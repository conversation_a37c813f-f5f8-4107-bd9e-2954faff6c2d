import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:referrals/deep_link/deep_link_uri_parser.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_referrals_api/index.dart';

class ReferralsWebNavigatorRouter extends ApplicationNavigatorRouter {
  final DeepLinkUriParserFacade _uriParser;

  ReferralsWebNavigatorRouter(this._uriParser)
      : super(
          initialApplicationId: ApplicationType.retail.id,
          applicationNavigatorConfigs: [
            ApplicationNavigatorConfig(ApplicationType.retail.id, {}),
          ],
        );

  @override
  Route<Object?> getRoute(RouteSettings settings) {
    if (settings.name == '/') return getInitialRoute();

    final config = settings.arguments;
    if (config == null) {
      return _handleDeepLink(settings) ?? getFallbackRoute();
    }

    return getFeatureRouter(config).getScreenRoute(settings);
  }

  @override
  NavigationRouter getFeatureRouter(Object? config) {
    if (config is FeatureNavigationConfig) {
      return _getNavigationRouter(config.id);
    } else if (config is ScreenNavigationConfig) {
      return _getNavigationRouter(config.feature);
    } else {
      throw Exception('Not supported navigation config: ${config.runtimeType}');
    }
  }

  @override
  Route<Object?> getInitialRoute() {
    const initialFeatureNavigationConfig = ReferralFeatureNavigationConfig(
      destination: ReferralsWebHomePageNavigationConfig(),
    );

    return getFeatureRouter(initialFeatureNavigationConfig).getScreenRoute(
      const RouteSettings(
        name: '/',
        arguments: initialFeatureNavigationConfig,
      ),
    );
  }

  @override
  Route<Object?> getFallbackRoute() {
    return getInitialRoute();
  }

  NavigationRouter _getNavigationRouter(String featureName) {
    return DependencyProvider.get<NavigationRouter>(
      instanceName: featureName,
    );
  }

  Route<Object?>? _handleDeepLink(RouteSettings settings) {
    final uri = Uri.tryParse(settings.name ?? '');
    if (uri == null) return null;

    final config = _uriParser.parse(uri);
    if (config == null) return null;

    return getFeatureRouter(config).getScreenRoute(
      RouteSettings(
        name: config.id,
        arguments: config,
      ),
    );
  }
}
