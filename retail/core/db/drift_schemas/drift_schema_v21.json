{"_meta": {"description": "This file contains a serialized version of schema entities for drift.", "version": "1.0.0"}, "options": {"store_date_time_values_as_text": false}, "entities": [{"id": 0, "references": [], "type": "table", "data": {"name": "transactions", "was_declared_in_moor": false, "columns": [{"name": "id", "getter_name": "id", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": 1, "max": 50}}]}, {"name": "product_type", "getter_name": "productType", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "account_id", "getter_name": "accountId", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": [{"allowed-lengths": {"min": 1, "max": 50}}]}, {"name": "transaction_type", "getter_name": "transactionType", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_identifier", "getter_name": "transactionIdentifier", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "status", "getter_name": "status", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_mode", "getter_name": "transactionMode", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_sub_type", "getter_name": "transactionSubType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "internal_transaction_status", "getter_name": "internalTransactionStatus", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_date_time", "getter_name": "transactionDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "debtor_name", "getter_name": "debtor<PERSON>ame", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "customer_id", "getter_name": "customerId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "core_banking_identifier", "getter_name": "coreBankingIdentifier", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "creditor_name", "getter_name": "creditorName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "description", "getter_name": "description", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "sub_description", "getter_name": "subDescription", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "logo_type", "getter_name": "logoType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "merchant_name", "getter_name": "merchantName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_image_key", "getter_name": "transactionImageKey", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_value", "getter_name": "amountValue", "moor_type": "double", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_amount", "getter_name": "localAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "exchange_rate", "getter_name": "exchangeRate", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "available_balance", "getter_name": "availableBalance", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_currency", "getter_name": "amountCurrency", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "execution_date", "getter_name": "executionDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "transaction_category", "getter_name": "transactionCategory", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_from_amount", "getter_name": "fxFromAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_to_amount", "getter_name": "fxToAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_from_amount_currency", "getter_name": "fxFromAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fx_to_amount_currency", "getter_name": "fxToAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order_id", "getter_name": "orderId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "portfolio_id", "getter_name": "portfolioId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_id", "getter_name": "instrumentId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "created_date_time", "getter_name": "createdDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order_side", "getter_name": "orderSide", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_name", "getter_name": "instrumentName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_symbol", "getter_name": "instrumentSymbol", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_exchange_id", "getter_name": "instrumentExchangeId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_image_url", "getter_name": "instrumentImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "order_type", "getter_name": "orderType", "moor_type": "string", "nullable": true, "customConstraints": "CHECK(order_type IN ('market', 'limit'))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "error_code", "getter_name": "errorCode", "moor_type": "int", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "error_message", "getter_name": "errorMessage", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_quantity", "getter_name": "executedQuantity", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_quantity", "getter_name": "estimatedQuantity", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_amount_value", "getter_name": "executedAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_amount_currency", "getter_name": "executedAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_net_amount_value", "getter_name": "executedNetAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "executed_net_amount_currency", "getter_name": "executedNetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_amount_value", "getter_name": "estimatedAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_amount_currency", "getter_name": "estimatedAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_net_amount_value", "getter_name": "estimatedNetAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_net_amount_currency", "getter_name": "estimatedNetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_amount_value", "getter_name": "commissionAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "commission_amount_currency", "getter_name": "commissionAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_commission_amount_value", "getter_name": "estimatedCommissionAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_commission_amount_currency", "getter_name": "estimatedCommissionAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "vat_value", "getter_name": "vatValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "vat_currency", "getter_name": "vatCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_vat_value", "getter_name": "estimatedVatValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_vat_currency", "getter_name": "estimatedVatCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "average_price_value", "getter_name": "averagePriceValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "average_price_currency", "getter_name": "averagePriceCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_price_value", "getter_name": "estimatedPriceValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "estimated_price_currency", "getter_name": "estimatedPriceCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "is_payment_proof_ready", "getter_name": "isPaymentProofReady", "moor_type": "bool", "nullable": false, "customConstraints": null, "defaultConstraints": "CHECK (\"is_payment_proof_ready\" IN (0, 1))", "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_target_country", "getter_name": "internationalTargetCountry", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_completed_date_time", "getter_name": "internationalCompletedDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_fee_total_amount", "getter_name": "internationalFeeTotalAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_fee_total_currency", "getter_name": "internationalFeeTotalCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_fee_charging_type", "getter_name": "internationalFeeChargingType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_swift_code", "getter_name": "internationalSwiftCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_account_number", "getter_name": "internationalAccountNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_purpose_description", "getter_name": "internationalPurposeDescription", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_purpose_code", "getter_name": "internationalPurposeCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_notes", "getter_name": "internationalNotes", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_bank_name", "getter_name": "internationalBankName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_target_amount", "getter_name": "internationalTargetAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_target_amount_currency", "getter_name": "internationalTargetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_transfer_fee", "getter_name": "internationalTransferFee", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_transfer_fee_currency", "getter_name": "internationalTransferFeeCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_description_Wio", "getter_name": "internationalFeeTypeDescriptionWio", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_amount_Wio", "getter_name": "internationalFeeTypeAmountWio", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_currency_Wio", "getter_name": "internationalFeeTypeCurrencyWio", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_description_WioCorrespondentBank", "getter_name": "internationalFeeTypeDescriptionWioCorrespondentBank", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_amount_WioCorrespondentBank", "getter_name": "internationalFeeTypeAmountWioCorrespondentBank", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_currency_WioCorrespondentBank", "getter_name": "internationalFeeTypeCurrencyWioCorrespondentBank", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_description_Wise", "getter_name": "internationalFeeTypeDescriptionWise", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_amount_Wise", "getter_name": "internationalFeeTypeAmountWise", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "international_feetype_currency_Wise", "getter_name": "internationalFeeTypeCurrencyWise", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_expiration_date", "getter_name": "fabExpirationDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_bank_name", "getter_name": "fabBankName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_total_fee_amount", "getter_name": "fabTotalFeeAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_total_fee_amount_currency", "getter_name": "fabTotalFeeAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "fab_updated_date", "getter_name": "fabUpdatedDate", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_purpose_description", "getter_name": "localPurposeDescription", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_purpose_code", "getter_name": "localPurposeCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_note", "getter_name": "localNote", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_target_country", "getter_name": "localTargetCountry", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_completed_date_time", "getter_name": "localCompletedDateTime", "moor_type": "dateTime", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_account_number", "getter_name": "localAccountNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_swift_code", "getter_name": "localSwiftCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_bank_name", "getter_name": "localBankName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_target_amount", "getter_name": "localTargetAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "local_target_amount_currency", "getter_name": "localTargetAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "token", "getter_name": "token", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "card_masked_pan", "getter_name": "cardMaskedPan", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "merchant_country_code", "getter_name": "merchantCountryCode", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "original_card_transaction_currency", "getter_name": "originalCardTransactionCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "original_card_transaction_amount", "getter_name": "originalCardTransactionAmount", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "earned_cash_back", "getter_name": "earned<PERSON>ashback", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "cash_back_percentage", "getter_name": "cashBackPercentage", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "cash_back_transaction_currency", "getter_name": "cashBackTransactionCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_tax_amount_value", "getter_name": "dividendTaxAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_tax_amount_currency", "getter_name": "dividendTaxAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_amount_value", "getter_name": "dividendAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_amount_currency", "getter_name": "dividendAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "net_dividend_amount_value", "getter_name": "netDividendAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "net_dividend_amount_currency", "getter_name": "netDividendAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_per_instrument_value", "getter_name": "amountPerInstrumentValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "amount_per_instrument_currency", "getter_name": "amountPerInstrumentCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "instrument_quantity", "getter_name": "instrumentQuantity", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_dividend_tax_type", "getter_name": "brokerDividendTaxType", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dividend_tax_rate", "getter_name": "dividendTaxRate", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_name", "getter_name": "acquirerInstrumentName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_symbol", "getter_name": "acquirerInstrumentSymbol", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_exchange_id", "getter_name": "acquirerInstrumentExchangeId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquirer_instrument_image_url", "getter_name": "acquirerInstrumentImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_name", "getter_name": "acquireeInstrumentName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_symbol", "getter_name": "acquireeInstrumentSymbol", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_exchange_id", "getter_name": "acquireeInstrumentExchangeId", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "acquiree_instrument_image_url", "getter_name": "acquireeInstrumentImageUrl", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_acquisition_account_amount_value", "getter_name": "brokerAcquisitionAccountAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_acquisition_account_amount_currency", "getter_name": "brokerAcquisitionAccountAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "broker_acquisition_position_delta", "getter_name": "brokerAcquisitionPositionDelta", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_company_name", "getter_name": "ipoCompanyName", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_subscription_number", "getter_name": "ipoSubscriptionNumber", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_value", "getter_name": "ipoLeverageValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_currency", "getter_name": "ipoLeverageCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_amount_value", "getter_name": "ipoAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_amount_currency", "getter_name": "ipoAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_total_amount_value", "getter_name": "ipoTotalAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_total_amount_currency", "getter_name": "ipoTotalAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_company_logo", "getter_name": "ipoCompanyLogo", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_refund_amount_value", "getter_name": "ipoRefundAmountValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_refund_amount_currency", "getter_name": "ipoRefundAmountCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_paid_value", "getter_name": "ipoLeveragePaidValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_paid_currency", "getter_name": "ipoLeveragePaidCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_fee_percentage", "getter_name": "ipoLeverageFeePercentage", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_fee_value", "getter_name": "ipoLeverageFeeValue", "moor_type": "double", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "ipo_leverage_fee_currency", "getter_name": "ipoLeverageFeeCurrency", "moor_type": "string", "nullable": true, "customConstraints": null, "default_dart": null, "default_client_dart": null, "dsl_features": []}, {"name": "dynamic_details", "getter_name": "dynamicDetails", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const TransactionDynamicDetailsConverter()", "dart_type_name": "List<DtoTransactionDynamicDetails>"}}, {"name": "linked_transaction_fee_details", "getter_name": "linkedTransactionFeeDetails", "moor_type": "string", "nullable": false, "customConstraints": null, "default_dart": "const Constant('')", "default_client_dart": null, "dsl_features": [], "type_converter": {"dart_expr": "const TransactionFeeDetailsConverter()", "dart_type_name": "List<TransactionFeeDetail>"}}], "is_virtual": false, "without_rowid": false, "constraints": [], "explicit_pk": ["id"]}}]}