// ignore_for_file: deprecated_member_use_from_same_package

import 'package:drift/drift.dart';
import 'package:drift_dev/api/migrations.dart';
import 'package:test/test.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_retail_db/database/database.dart';

import '../generated_migrations/schema.dart';
import '../test_entities.dart';

void main() {
  // Define verifier, connection, and database.
  late SchemaVerifier verifier;
  late DatabaseConnection connection;
  late RetailDatabase database;

  // Test data
  final mockTransactionEntities = TestEntities.getMockDBTransactionEntries();

  // Initialize verifier, connection, and database. Insert dummy transactions to
  // the database.
  setUpAll(() async {
    verifier = SchemaVerifier(GeneratedHelper());
    connection = await verifier.startAt(24);
    database = RetailDatabase(
      queryExecutor: connection,
      sqlStatementBuilder: const SQLStatementBuilder(),
    );
  });

  // Clear the database cache, close the database, and close the connection.
  tearDownAll(() async {
    await database.close();
    await connection.close();
  });

  tearDown(() => database.clearCache());

  group('Total spend for period >', () {
    test('Get total spend amount in time range', () async {
      // Arrange
      await database.insertAllTransactions(mockTransactionEntities);
      final expectedResult = TestEntities.getSpendTotalAmount();

      // Act
      final totalAmount = await database.getTotalAmountInRangeForSpend(
        start: DateTime(2023, 9, 11),
        end: DateTime(2023, 10, 5),
      );

      // Assert
      expect(totalAmount, expectedResult);
    });

    test(
      'Get total spend amount in time range for a known country (US)',
      () async {
        // Arrange
        await database.insertAllTransactions([
          TestEntities.getDummyTransactionEntity(
            id: '1',
            transactionDateTime: DateTime(2023, 9, 20),
            localAmount: 13.37,
            merchantCountryCode: 'US',
            transactionMode: TransactionMode.withdrawal,
          ),
          // Deposit transaction, will be skipped
          TestEntities.getDummyTransactionEntity(
            id: '2',
            transactionDateTime: DateTime(2023, 10, 2),
            localAmount: 42,
            merchantCountryCode: 'US',
          ),
          // Withdrawal transaction but outside the date range, will be skipped
          TestEntities.getDummyTransactionEntity(
            id: '3',
            transactionDateTime: DateTime(2023, 10, 8),
            localAmount: 100,
            transactionMode: TransactionMode.withdrawal,
            merchantCountryCode: 'US',
          ),
          // Withdrawal transaction but for different country, will be skipped
          TestEntities.getDummyTransactionEntity(
            id: '4',
            transactionDateTime: DateTime(2023, 9, 17),
            localAmount: 42,
            merchantCountryCode: 'KZ',
            transactionMode: TransactionMode.withdrawal,
          ),
        ]);

        // Act
        final totalAmount = await database.getTotalAmountInRangeForSpend(
          start: DateTime(2023, 9, 11),
          end: DateTime(2023, 10, 5),
          additionalFilters: {'merchant_country_code': 'US'},
        );

        // Assert
        expect(totalAmount, 13.37);
      },
    );

    test(
      'Get total spend amount in time range for unknown countries',
      () async {
        // Arrange
        await database.insertAllTransactions([
          TestEntities.getDummyTransactionEntity(
            id: '1',
            transactionDateTime: DateTime(2023, 9, 20),
            localAmount: 13,
          ),
          TestEntities.getDummyTransactionEntity(
            id: '2',
            transactionDateTime: DateTime(2023, 10, 2),
            localAmount: 42,
            transactionMode: TransactionMode.withdrawal,
          ),
          TestEntities.getDummyTransactionEntity(
            id: '3',
            transactionDateTime: DateTime(2023, 10, 8),
            localAmount: 100,
            transactionMode: TransactionMode.withdrawal,
          ),
        ]);

        // Act
        final totalAmount = await database.getTotalAmountInRangeForSpend(
          start: DateTime(2023, 9, 11),
          end: DateTime(2023, 10, 5),
          additionalFilters: {'merchant_country_code': null},
        );

        // Assert
        expect(totalAmount, 42);
      },
    );
  });

  test('Get spent categories in time range', () async {
    // Arrange
    await database.insertAllTransactions(mockTransactionEntities);
    final expectedResult = TestEntities.getSpendCategoryHighlights();

    // Act
    final categoryHighlights = await database.getCategoriesForSpend(
      start: DateTime(2023, 9, 15),
      end: DateTime(2023, 10, 7),
    );

    // Assert
    expect(categoryHighlights, expectedResult);
  });

  test('Get spent countries in time range', () async {
    // Arrange
    await database.insertAllTransactions(mockTransactionEntities);
    final expectedResult = TestEntities.getSpendCountryHighlights();

    // Act
    final countryHighlights = await database.getCountriesForSpend(
      start: DateTime(2023, 9, 27),
      end: DateTime(2023, 10, 12),
    );

    // Assert
    expect(countryHighlights, expectedResult);
  });
}
