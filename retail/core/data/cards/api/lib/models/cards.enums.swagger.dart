import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum SetCardLimitRequestType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DAILY')
  daily('DAILY'),
  @JsonValue('WEEKLY')
  weekly('WEEKLY'),
  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('OVERALL')
  overall('OVERALL');

  final String? value;

  const SetCardLimitRequestType(this.value);
}

enum UpdateCardSettingsRequestAtmPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const UpdateCardSettingsRequestAtmPayments(this.value);
}

enum UpdateCardSettingsRequestEcomPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const UpdateCardSettingsRequestEcomPayments(this.value);
}

enum UpdateCardSettingsRequestContactlessPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const UpdateCardSettingsRequestContactlessPayments(this.value);
}

enum UpdateCardSettingsRequestAbroadPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const UpdateCardSettingsRequestAbroadPayments(this.value);
}

enum SpendingLimitType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DAILY')
  daily('DAILY'),
  @JsonValue('WEEKLY')
  weekly('WEEKLY'),
  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('OVERALL')
  overall('OVERALL');

  final String? value;

  const SpendingLimitType(this.value);
}

enum CardImageCardType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PHYSICAL')
  physical('PHYSICAL'),
  @JsonValue('DIGITAL')
  digital('DIGITAL'),
  @JsonValue('VIRTUAL')
  virtual('VIRTUAL');

  final String? value;

  const CardImageCardType(this.value);
}

enum CardLimitType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DAILY')
  daily('DAILY'),
  @JsonValue('WEEKLY')
  weekly('WEEKLY'),
  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('OVERALL')
  overall('OVERALL');

  final String? value;

  const CardLimitType(this.value);
}

enum CardResponseType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PHYSICAL')
  physical('PHYSICAL'),
  @JsonValue('DIGITAL')
  digital('DIGITAL'),
  @JsonValue('VIRTUAL')
  virtual('VIRTUAL');

  final String? value;

  const CardResponseType(this.value);
}

enum CardResponseStatus {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('FREEZE')
  freeze('FREEZE'),
  @JsonValue('UNFREEZE')
  unfreeze('UNFREEZE'),
  @JsonValue('BLOCKED_BY_SUPPORT')
  blockedBySupport('BLOCKED_BY_SUPPORT'),
  @JsonValue('LOST')
  lost('LOST'),
  @JsonValue('STOLEN')
  stolen('STOLEN'),
  @JsonValue('TERMINATED')
  terminated('TERMINATED'),
  @JsonValue('PIN_BLOCKED')
  pinBlocked('PIN_BLOCKED');

  final String? value;

  const CardResponseStatus(this.value);
}

enum CardSettingsAtmPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const CardSettingsAtmPayments(this.value);
}

enum CardSettingsEcomPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const CardSettingsEcomPayments(this.value);
}

enum CardSettingsContactlessPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const CardSettingsContactlessPayments(this.value);
}

enum CardSettingsAbroadPayments {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ENABLED')
  enabled('ENABLED'),
  @JsonValue('DISABLED')
  disabled('DISABLED');

  final String? value;

  const CardSettingsAbroadPayments(this.value);
}

enum CarbonFootprintDonationRequestCurrency {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('ALL')
  all('ALL'),
  @JsonValue('DZD')
  dzd('DZD'),
  @JsonValue('ARS')
  ars('ARS'),
  @JsonValue('AUD')
  aud('AUD'),
  @JsonValue('BSD')
  bsd('BSD'),
  @JsonValue('BHD')
  bhd('BHD'),
  @JsonValue('BDT')
  bdt('BDT'),
  @JsonValue('AMD')
  amd('AMD'),
  @JsonValue('BBD')
  bbd('BBD'),
  @JsonValue('BMD')
  bmd('BMD'),
  @JsonValue('BTN')
  btn('BTN'),
  @JsonValue('BOB')
  bob('BOB'),
  @JsonValue('BWP')
  bwp('BWP'),
  @JsonValue('BZD')
  bzd('BZD'),
  @JsonValue('SBD')
  sbd('SBD'),
  @JsonValue('BND')
  bnd('BND'),
  @JsonValue('MMK')
  mmk('MMK'),
  @JsonValue('BIF')
  bif('BIF'),
  @JsonValue('KHR')
  khr('KHR'),
  @JsonValue('CAD')
  cad('CAD'),
  @JsonValue('CVE')
  cve('CVE'),
  @JsonValue('KYD')
  kyd('KYD'),
  @JsonValue('LKR')
  lkr('LKR'),
  @JsonValue('CLP')
  clp('CLP'),
  @JsonValue('CNY')
  cny('CNY'),
  @JsonValue('COP')
  cop('COP'),
  @JsonValue('KMF')
  kmf('KMF'),
  @JsonValue('CRC')
  crc('CRC'),
  @JsonValue('HRK')
  hrk('HRK'),
  @JsonValue('CUP')
  cup('CUP'),
  @JsonValue('CZK')
  czk('CZK'),
  @JsonValue('DKK')
  dkk('DKK'),
  @JsonValue('DOP')
  dop('DOP'),
  @JsonValue('SVC')
  svc('SVC'),
  @JsonValue('ETB')
  etb('ETB'),
  @JsonValue('ERN')
  ern('ERN'),
  @JsonValue('FKP')
  fkp('FKP'),
  @JsonValue('FJD')
  fjd('FJD'),
  @JsonValue('DJF')
  djf('DJF'),
  @JsonValue('GMD')
  gmd('GMD'),
  @JsonValue('GIP')
  gip('GIP'),
  @JsonValue('GTQ')
  gtq('GTQ'),
  @JsonValue('GNF')
  gnf('GNF'),
  @JsonValue('GYD')
  gyd('GYD'),
  @JsonValue('HTG')
  htg('HTG'),
  @JsonValue('HNL')
  hnl('HNL'),
  @JsonValue('HKD')
  hkd('HKD'),
  @JsonValue('HUF')
  huf('HUF'),
  @JsonValue('ISK')
  isk('ISK'),
  @JsonValue('INR')
  inr('INR'),
  @JsonValue('IDR')
  idr('IDR'),
  @JsonValue('IRR')
  irr('IRR'),
  @JsonValue('IQD')
  iqd('IQD'),
  @JsonValue('ILS')
  ils('ILS'),
  @JsonValue('JMD')
  jmd('JMD'),
  @JsonValue('JPY')
  jpy('JPY'),
  @JsonValue('KZT')
  kzt('KZT'),
  @JsonValue('JOD')
  jod('JOD'),
  @JsonValue('KES')
  kes('KES'),
  @JsonValue('KPW')
  kpw('KPW'),
  @JsonValue('KRW')
  krw('KRW'),
  @JsonValue('KWD')
  kwd('KWD'),
  @JsonValue('KGS')
  kgs('KGS'),
  @JsonValue('LAK')
  lak('LAK'),
  @JsonValue('LBP')
  lbp('LBP'),
  @JsonValue('LSL')
  lsl('LSL'),
  @JsonValue('LRD')
  lrd('LRD'),
  @JsonValue('LYD')
  lyd('LYD'),
  @JsonValue('MOP')
  mop('MOP'),
  @JsonValue('MWK')
  mwk('MWK'),
  @JsonValue('MYR')
  myr('MYR'),
  @JsonValue('MVR')
  mvr('MVR'),
  @JsonValue('MUR')
  mur('MUR'),
  @JsonValue('MXN')
  mxn('MXN'),
  @JsonValue('MNT')
  mnt('MNT'),
  @JsonValue('MDL')
  mdl('MDL'),
  @JsonValue('MAD')
  mad('MAD'),
  @JsonValue('OMR')
  omr('OMR'),
  @JsonValue('NAD')
  nad('NAD'),
  @JsonValue('NPR')
  npr('NPR'),
  @JsonValue('ANG')
  ang('ANG'),
  @JsonValue('AWG')
  awg('AWG'),
  @JsonValue('VUV')
  vuv('VUV'),
  @JsonValue('NZD')
  nzd('NZD'),
  @JsonValue('NIO')
  nio('NIO'),
  @JsonValue('NGN')
  ngn('NGN'),
  @JsonValue('NOK')
  nok('NOK'),
  @JsonValue('PKR')
  pkr('PKR'),
  @JsonValue('PAB')
  pab('PAB'),
  @JsonValue('PGK')
  pgk('PGK'),
  @JsonValue('PYG')
  pyg('PYG'),
  @JsonValue('PEN')
  pen('PEN'),
  @JsonValue('PHP')
  php('PHP'),
  @JsonValue('QAR')
  qar('QAR'),
  @JsonValue('RUB')
  rub('RUB'),
  @JsonValue('RWF')
  rwf('RWF'),
  @JsonValue('SHP')
  shp('SHP'),
  @JsonValue('SAR')
  sar('SAR'),
  @JsonValue('SCR')
  scr('SCR'),
  @JsonValue('SLL')
  sll('SLL'),
  @JsonValue('SGD')
  sgd('SGD'),
  @JsonValue('VND')
  vnd('VND'),
  @JsonValue('SOS')
  sos('SOS'),
  @JsonValue('ZAR')
  zar('ZAR'),
  @JsonValue('SSP')
  ssp('SSP'),
  @JsonValue('SZL')
  szl('SZL'),
  @JsonValue('SEK')
  sek('SEK'),
  @JsonValue('CHF')
  chf('CHF'),
  @JsonValue('SYP')
  syp('SYP'),
  @JsonValue('THB')
  thb('THB'),
  @JsonValue('TOP')
  top('TOP'),
  @JsonValue('TTD')
  ttd('TTD'),
  @JsonValue('AED')
  aed('AED'),
  @JsonValue('TND')
  tnd('TND'),
  @JsonValue('UGX')
  ugx('UGX'),
  @JsonValue('MKD')
  mkd('MKD'),
  @JsonValue('EGP')
  egp('EGP'),
  @JsonValue('GBP')
  gbp('GBP'),
  @JsonValue('TZS')
  tzs('TZS'),
  @JsonValue('USD')
  usd('USD'),
  @JsonValue('UYU')
  uyu('UYU'),
  @JsonValue('UZS')
  uzs('UZS'),
  @JsonValue('WST')
  wst('WST'),
  @JsonValue('YER')
  yer('YER'),
  @JsonValue('TWD')
  twd('TWD'),
  @JsonValue('VED')
  ved('VED'),
  @JsonValue('UYW')
  uyw('UYW'),
  @JsonValue('VES')
  ves('VES'),
  @JsonValue('MRU')
  mru('MRU'),
  @JsonValue('STN')
  stn('STN'),
  @JsonValue('CUC')
  cuc('CUC'),
  @JsonValue('ZWL')
  zwl('ZWL'),
  @JsonValue('BYN')
  byn('BYN'),
  @JsonValue('TMT')
  tmt('TMT'),
  @JsonValue('GHS')
  ghs('GHS'),
  @JsonValue('SDG')
  sdg('SDG'),
  @JsonValue('UYI')
  uyi('UYI'),
  @JsonValue('RSD')
  rsd('RSD'),
  @JsonValue('MZN')
  mzn('MZN'),
  @JsonValue('AZN')
  azn('AZN'),
  @JsonValue('RON')
  ron('RON'),
  @JsonValue('CHE')
  che('CHE'),
  @JsonValue('CHW')
  chw('CHW'),
  @JsonValue('TRY')
  $try('TRY'),
  @JsonValue('XAF')
  xaf('XAF'),
  @JsonValue('XCD')
  xcd('XCD'),
  @JsonValue('XOF')
  xof('XOF'),
  @JsonValue('XPF')
  xpf('XPF'),
  @JsonValue('ZMW')
  zmw('ZMW'),
  @JsonValue('SRD')
  srd('SRD'),
  @JsonValue('MGA')
  mga('MGA'),
  @JsonValue('COU')
  cou('COU'),
  @JsonValue('AFN')
  afn('AFN'),
  @JsonValue('TJS')
  tjs('TJS'),
  @JsonValue('AOA')
  aoa('AOA'),
  @JsonValue('BGN')
  bgn('BGN'),
  @JsonValue('CDF')
  cdf('CDF'),
  @JsonValue('BAM')
  bam('BAM'),
  @JsonValue('EUR')
  eur('EUR'),
  @JsonValue('MXV')
  mxv('MXV'),
  @JsonValue('UAH')
  uah('UAH'),
  @JsonValue('GEL')
  gel('GEL'),
  @JsonValue('BOV')
  bov('BOV'),
  @JsonValue('PLN')
  pln('PLN'),
  @JsonValue('BRL')
  brl('BRL'),
  @JsonValue('CLF')
  clf('CLF'),
  @JsonValue('USN')
  usn('USN');

  final String? value;

  const CarbonFootprintDonationRequestCurrency(this.value);
}

enum CardLimitResponseType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('DAILY')
  daily('DAILY'),
  @JsonValue('WEEKLY')
  weekly('WEEKLY'),
  @JsonValue('MONTHLY')
  monthly('MONTHLY'),
  @JsonValue('OVERALL')
  overall('OVERALL');

  final String? value;

  const CardLimitResponseType(this.value);
}

enum CardDetailsResponseType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PHYSICAL')
  physical('PHYSICAL'),
  @JsonValue('DIGITAL')
  digital('DIGITAL'),
  @JsonValue('VIRTUAL')
  virtual('VIRTUAL');

  final String? value;

  const CardDetailsResponseType(this.value);
}

enum ApiV1CardImageGetCardType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('PHYSICAL')
  physical('PHYSICAL'),
  @JsonValue('DIGITAL')
  digital('DIGITAL'),
  @JsonValue('VIRTUAL')
  virtual('VIRTUAL');

  final String? value;

  const ApiV1CardImageGetCardType(this.value);
}
