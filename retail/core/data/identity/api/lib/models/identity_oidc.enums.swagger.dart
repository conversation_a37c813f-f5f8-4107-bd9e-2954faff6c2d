import 'package:json_annotation/json_annotation.dart';
import 'package:collection/collection.dart';

enum TokenPost$RequestBodyGrantType {
  @JsonValue(null)
  swaggerGeneratedUnknown(null),

  @JsonValue('authorization_code')
  authorizationCode('authorization_code'),
  @JsonValue('refresh_token')
  refreshToken('refresh_token');

  final String? value;

  const TokenPost$RequestBodyGrantType(this.value);
}
