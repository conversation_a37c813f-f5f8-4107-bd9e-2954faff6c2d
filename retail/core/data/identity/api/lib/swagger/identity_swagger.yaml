openapi: 3.0.3

info:
  version: 1.19.4
  title: ${title}
  description: |
    Retail Identity Orchestrator Service for Wio Identity

servers:
  - url: /api/v1

security:
  - bearerAuth: [ ]

tags:
  - name: Unsecured
    description: API that doesn't require auth header.
  - name: Identity Management
    description: API for managing Users and Login.
  - name: TwoFA
    description: API for managing 2FA flows.
  - name: Devices
    description: API for managing Devices.

paths:
  /efr/configuration:
    get:
      tags:
        - TwoFA
        - Unsecured
      summary: Get Face Recognition Configuration
      operationId: getFaceRecognitionConfiguration
      responses:
        200:
          $ref: '#/components/responses/EfrConfiguration'

  /internal/users/whitelist:
    patch:
      summary: Update User by Identity
      description: Update user info by id.
      tags:
        - Identity Management
        - unsecured
      operationId: whiteListUser
      parameters:
        - $ref: '#/components/parameters/customerId'
      requestBody:
        $ref: '#/components/requestBodies/whiteListUserRequest'
      responses:
        200:
          $ref: '#/components/responses/OperationResponse'
        404:
          $ref: '#/components/responses/NotFoundError'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/closure:
    post:
      security: [ ]
      summary: Trigger account closure
      description: Triggers the closure of an account
      tags:
        - Identity Management
        - Unsecured
      operationId: accountClosure
      requestBody:
        $ref : '#/components/requestBodies/accountClosure'
      responses:
        200:
          $ref: '#/components/responses/AccountClosureResponse'
        default:
          $ref: '#/components/responses/UnknownError'

  /users:
    post:
      security: [ ]
      parameters:
        - $ref: '#/components/parameters/x-2fa-id'
      summary: Create User
      description: Create a new Identity User.
      tags:
        - Identity Management
        - Unsecured
      operationId: createUser
      requestBody:
        $ref: '#/components/requestBodies/CreateUserRequest'
      responses:
        201:
          $ref: '#/components/responses/UserResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /identity:
    post:
      security: [ ]
      parameters:
        - $ref: '#/components/parameters/x-2fa-id'
      summary: Create Identity
      description: Create a new core2fa and IMS user.
      tags:
        - Identity Management
        - Unsecured
      operationId: createIdentity
      requestBody:
        $ref: '#/components/requestBodies/CreateIdentityRequest'
      responses:
        201:
          $ref: '#/components/responses/UserResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/validate-closure:
    get:
      security: [ ]
      summary: Validate closure
      description: Validates if an account is eligible for closure
      tags:
        - Identity Management
        - Unsecured
      operationId: validateClosure
      parameters:
        - $ref: '#/components/parameters/userId'
      responses:
        '200':
          $ref: '#/components/responses/ValidateClosureResponse'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/users:
    get:
      summary: Get Users
      description: Uses query keys to retrieve a subset of existing users.
      tags:
        - Identity Management
      operationId: getUsers
      parameters:
        - $ref: '#/components/parameters/id'
        - $ref: '#/components/parameters/email'
        - $ref: '#/components/parameters/phone'
        - $ref: '#/components/parameters/customerId'
        - $ref: '#/components/parameters/coreTwoFaUserId'
        - $ref: '#/components/parameters/externalIdentityId'
        - $ref: '#/components/parameters/pagination_page_num'
        - $ref: '#/components/parameters/pagination_page_size'
        - $ref: '#/components/parameters/pagination_users_sort'
      responses:
        200:
          $ref: '#/components/responses/InternalUsersResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/users/{id}:
    get:
      summary: Get User by Identity user Id
      description: Retrieve info about user by id.
      tags:
        - Identity Management
      operationId: getUserById
      parameters:
        - $ref: '#/components/parameters/p_id'
      responses:
        200:
          $ref: '#/components/responses/InternalUserResponse'
        404:
          $ref: '#/components/responses/NotFoundError'
        default:
          $ref: '#/components/responses/UnknownError'
    patch:
      summary: Update User by Identity Customer Id
      description: Update user info by customer id.
      tags:
        - Identity Management
      operationId: updateUser
      parameters:
        - $ref: '#/components/parameters/c_id'
      requestBody:
        $ref: '#/components/requestBodies/UpdateUserRequest'
      responses:
        200:
          $ref: '#/components/responses/OperationResponse'
        404:
          $ref: '#/components/responses/NotFoundError'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/users/migrate-customers:
    post:
      summary: Internal Migrate Customers
      description: Internal Migrate Customers to Identity Users
      requestBody:
        $ref: '#/components/requestBodies/MigrateUsersRequest'
      tags:
        - Identity Management
      operationId: migrateCustomers
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        default:
          $ref: '#/components/responses/UnknownError'

  /session:
    post:
      security: [ ]
      parameters:
        - $ref: '#/components/parameters/x-2fa-id'
        - $ref: '#/components/parameters/x-device-id'
      summary: Create Session
      description: Authenticate user using credentials and create token based session.
      tags:
        - Identity Management
        - Unsecured
      operationId: createSession
      requestBody:
        $ref: '#/components/requestBodies/CreateSessionRequest'
      responses:
        201:
          $ref: '#/components/responses/SessionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'
    delete:
      summary: Delete Session
      description: Invalidate current user session.
      tags:
        - Identity Management
      operationId: deleteSession
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'
    put:
      summary: Renew Session
      description: Renew the session.
      tags:
        - Identity Management
      operationId: renewSession
      requestBody:
        $ref: '#/components/requestBodies/RenewSessionRequest'
      responses:
        200:
          $ref: '#/components/responses/SessionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /current-user:
    get:
      summary: Get Current User
      description: Retrieve info about current authenticated user.
      tags:
        - Identity Management
      operationId: getCurrentUser
      responses:
        200:
          $ref: '#/components/responses/UserResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /current-user/email:
    put:
      parameters:
        - $ref: '#/components/parameters/x-2fa-id'
      summary: Update Email
      description: Update Email for an authorized user
      tags:
        - Identity Management
      operationId: updateEmail
      requestBody:
        $ref: '#/components/requestBodies/UpdateEmailRequest'
      responses:
        200:
          $ref: '#/components/responses/OperationResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /current-user/mobile:
    put:
      parameters:
        - $ref: '#/components/parameters/x-2fa-id'
      summary: Update Mobile
      description: Update Mobile number for an authorized user
      tags:
        - Identity Management
      operationId: updateMobile
      requestBody:
        $ref: '#/components/requestBodies/UpdateMobileRequest'
      responses:
        200:
          $ref: '#/components/responses/OperationResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /token/introspect:
    post:
      security: [ ]
      summary: Introspect token
      description: Introspect token
      tags:
        - Identity Management
        - Unsecured
      operationId: introspectToken
      requestBody:
        $ref: '#/components/requestBodies/IntrospectTokenRequest'
      responses:
        201:
          $ref: '#/components/responses/IntrospectTokenResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/transactions:
    post:
      summary: Create Transaction
      description: Create 2FA Transaction.
      tags:
        - TwoFA
      operationId: createTransaction
      requestBody:
        $ref: '#/components/requestBodies/CreateTransactionRequest'
      responses:
        201:
          $ref: '#/components/responses/TransactionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

    get:
      summary: Get Transaction
      description: Get details about a 2FA transaction
      parameters:
        - $ref: '#/components/parameters/q_transaction_id'
      tags:
        - TwoFA
      operationId: getTransaction
      responses:
        200:
          $ref: '#/components/responses/TransactionResponse'

  /internal/initiate-direct-otp:
    post:
      summary: Initiate Transaction with direct OTP send
      description: Initiate Transaction with direct SMS/EMAIL OTP send
      tags:
        - TwoFA
      operationId: initiateDirectOtp
      requestBody:
        $ref: '#/components/requestBodies/SendDirectOtpRequest'
      responses:
        201:
          $ref: '#/components/responses/TransactionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/unsecure/initiate-otp/{customer_id}:
    post:
      summary: Initiate Transaction for OTP with customer id in request
      description: Initiate Transaction with SMS/EMAIL OTP sent to customer
      tags:
        - TwoFA
      operationId: initiateOtpWithCustomerId
      parameters:
        - $ref: '#/components/parameters/customer_id'
      requestBody:
        $ref: '#/components/requestBodies/SendOtpWithCustomerIdRequest'
      responses:
        201:
          $ref: '#/components/responses/TransactionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/transactions/{transaction_id}/verify:
    parameters:
      - $ref: '#/components/parameters/p_transaction_id'
    post:
      summary: Verify Transaction
      description: Verify 2FA Transaction.
      tags:
        - TwoFA
      operationId: verifyTransaction
      requestBody:
        $ref: '#/components/requestBodies/VerifyTransactionRequest'
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        404:
          $ref: '#/components/responses/NotFoundError'
        default:
          $ref: '#/components/responses/UnknownError'

  /transactions/{transaction_id}/finish:
    parameters:
      - $ref: '#/components/parameters/p_transaction_id'
    post:
      security: [ ]
      summary: Finish transaction by ID
      description: Transfer transaction to FINISHED status after it has been VERIFIED
      tags:
        - TwoFA
      operationId: finishTransaction
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        404:
          $ref: '#/components/responses/NotFoundError'
        default:
          $ref: '#/components/responses/UnknownError'

  /challenges/{challenge_id}/solve:
    parameters:
      - $ref: '#/components/parameters/x-2fa-id'
      - $ref: '#/components/parameters/p_challenge_id'
    post:
      security: [ ]
      summary: Solve Challenge
      description: Solve 2FA Challenge.
      tags:
        - TwoFA
        - Unsecured
      operationId: solveChallenge
      requestBody:
        $ref: '#/components/requestBodies/SolveChallengeRequest'
      responses:
        201:
          $ref: '#/components/responses/TransactionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        404:
          $ref: '#/components/responses/NotFoundError'
        default:
          $ref: '#/components/responses/UnknownError'

  /challenges/{challenge_id}/initiate:
    parameters:
      - $ref: '#/components/parameters/x-2fa-id'
      - $ref: '#/components/parameters/p_challenge_id'
    post:
      security: [ ]
      summary: Initiate Challenge
      description: Initiate Challenge for optional Challenge or for resend purposes.
      tags:
        - TwoFA
        - Unsecured
      operationId: initiateChallenge
      responses:
        200:
          $ref: '#/components/responses/TransactionResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        404:
          $ref: '#/components/responses/NotFoundError'
        default:
          $ref: '#/components/responses/UnknownError'

  /factors/passcode:
    parameters:
      - $ref: '#/components/parameters/x-2fa-id'
    put:
      summary: Upsert Passcode factor
      description: Upsert Passcode for an authorized user
      tags:
        - TwoFA
      operationId: upsertPasscode
      requestBody:
        $ref: '#/components/requestBodies/UpsertPasscodeRequest'
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /user/verify-mobile:
    parameters:
      - $ref: '#/components/parameters/x-2fa-id'
    post:
      summary: Verify mobile
      description: Verify mobile for existing individual
      security: [ ]
      tags:
        - Identity Management
        - Unsecured
      operationId: verifyMobile
      requestBody:
        $ref: '#/components/requestBodies/VerifyMobileRequest'
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'


  /factors/trusted-device:
    put:
      summary: Upsert Trusted Device factor
      description: Upsert Trusted Device factor for an authorized user
      tags:
        - TwoFA
      operationId: upsertTrustedDevice
      requestBody:
        $ref: '#/components/requestBodies/UpsertTrustedDeviceRequest'
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /factors/biometrics:
    put:
      summary: Upsert Biometrics factor
      description: Upsert Biometrics factor for an authorized user
      tags:
        - TwoFA
      operationId: upsertBiometrics
      requestBody:
        $ref: '#/components/requestBodies/UpsertBiometricsRequest'
      responses:
        204:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'
    get:
      summary: Get Biometrics Info
      description: Retrieve info about Biometrics factor.
      tags:
        - TwoFA
      operationId: getBiometrics
      responses:
        200:
          $ref: '#/components/responses/BiometricsResponse'
        404:
          $ref: '#/components/responses/NotFoundError'
        default:
          $ref: '#/components/responses/UnknownError'

  /factors/efr/liveness:
    parameters:
      - $ref: '#/components/parameters/x-2fa-id'
    post:
      security: [ ]
      summary: Check EFR liveness
      description: Check liveness of provided file through EFR
      tags:
        - TwoFA
        - Unsecured
      operationId: checkEfrLiveness
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
              properties:
                file:
                  description: Multipart file for liveness check.
                  type: string
                  format: binary
      responses:
        200:
          $ref: '#/components/responses/EfrLivenessResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/factors/passcode/verify:
    post:
      summary: Internal Verify Passcode factor
      description: Internal Verify Passcode for an authorized user
      tags:
        - TwoFA
      operationId: verifyPasscode
      requestBody:
        $ref: '#/components/requestBodies/VerifyPasscode'
      responses:
        200:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/factors/{customer_id}/unblock/{factor_type}:
    post:
      summary: Unblock factor by ID
      description: Unblock factor by factorID for given customerIdentifier
      tags:
        - TwoFA
        - Unsecured
      operationId: unblockFactor
      parameters:
        - $ref: '#/components/parameters/customer_id'
        - $ref: '#/components/parameters/factor_type'
      responses:
        200:
          $ref: '#/components/responses/NoContentResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/step-up-auth-factors:
    get:
      summary: Get factor types for step up auth
      description: Get 2 FA factor types to be used for relevant operation type
      operationId: getStepUpAuthFactors
      tags:
        - TwoFA
        - Unsecured
      parameters:
        - $ref: '#/components/parameters/amount'
        - $ref: '#/components/parameters/currency'
        - $ref: '#/components/parameters/step_up_type'
      responses:
        201:
          $ref: '#/components/responses/StepUpAuthResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

  /internal/external-user-info:
    post:
      summary: Bulk fetch external user info
      description: Get customer info belonging to other services for multiple customerIds
      operationId: getExternalUserInfo
      tags:
        - Identity Management
        - Unsecured
      requestBody:
        $ref: '#/components/requestBodies/ExternalUserInfoRequest'
      responses:
        200:
          $ref: '#/components/responses/ExternalUserInfoResponse'
        400:
          $ref: '#/components/responses/ValidationError'
        default:
          $ref: '#/components/responses/UnknownError'

components:
  schemas:
    EfrConfiguration:
      type: object
      properties:
        configurationData:
          type: string
          description: Face Recognition Configuration
        temporaryKey:
          type: object
          description: temporary key
          properties:
            code:
              type: number
              format: int64
            value:
              type: string
            expiry:
              type: string

    Email:
      type: string
      format: email
      description: Customer email id
    Phone:
      type: string
      description: Customer phone number
      pattern: '^(\+971)\d{7,9}$'
    Agreement:
      description: Legal agreement of Retail product.
      type: object
      required:
        - privacyPolicy
        - keyFactsStatement
      properties:
        privacyPolicy:
          $ref: '#/components/schemas/PrivacyPolicy'
        keyFactsStatement:
          $ref: '#/components/schemas/KeyFactsStatement'
        marketingNotificationsApproval:
          $ref: '#/components/schemas/MarketingNotificationsApproval'

    ApplyExternalIdentity:
      description: Apply External Identity
      type: object
      required:
        - type
        - authToken
      properties:
        type:
          $ref: '#/components/schemas/ExternalIdentityType'
        authToken:
          type: string
          description: Bearer token in base64

    ExternalIdentity:
      description: External Identity representation
      type: object
      required:
        - type
        - id
      properties:
        type:
          $ref: '#/components/schemas/ExternalIdentityType'
        id:
          type: string

    ExternalIdentityType:
      description: External Identity type
      type: string
      enum:
        - UAEPASS

    PrivacyPolicy:
      description: Wio Privacy Policy and Weo Standard terms.
      type: object
      required:
        - version
        - accepted
      properties:
        version:
          type: string
          description: The version of Wio Privacy Policy and Weo Standard terms.
        versionDate:
          type: string
          example: 2022-12-19 11:30:00.000
          x-field-extra-annotation: "@io.wio.retail.identity.orchestrator.validation.Timestamp"
          description: The version date of Wio Privacy Policy and Weo Standard terms.
        accepted:
          type: boolean
          description: Whether user accepted the Policy.

    KeyFactsStatement:
      description: Wio Key Facts Statement.
      type: object
      required:
        - version
        - accepted
      properties:
        version:
          type: string
          description: The version of Wio Key Facts Statement.
        versionDate:
          type: string
          example: 2022-12-19 11:30:00.000
          x-field-extra-annotation: "@io.wio.retail.identity.orchestrator.validation.Timestamp"
          description: The version date of Wio Key Facts Statement.
        accepted:
          type: boolean
          description: Whether user accepted the KFS.

    MarketingNotificationsApproval:
      title: MarketingNotificationsApproval
      type: object
      properties:
        accepted:
          type: boolean

    CreateIdentityUser:
      description: A representation of Create User entity.
      type: object
      required:
        - agreement
      properties:
        phone:
          $ref: '#/components/schemas/Phone'
        email:
          $ref: '#/components/schemas/Email'
        externalIdentity:
          $ref: '#/components/schemas/ApplyExternalIdentity'
        agreement:
          $ref: '#/components/schemas/Agreement'

    CreateIdentity:
      description: A representation of Create Identity entity.
      type: object
      required:
        - agreement
      properties:
        email:
          $ref: '#/components/schemas/Email'
        externalIdentity:
          $ref: '#/components/schemas/ApplyExternalIdentity'
        agreement:
          $ref: '#/components/schemas/Agreement'

    UpdateUser:
      description: Internal Update User entity.
      type: object
      properties:
        currentEmail:
          type: string
        newEmail:
          type: string
        currentPhone:
          type: string
        phone:
          $ref: '#/components/schemas/Phone'
        externalIdentities:
          type: array
          items:
            $ref: '#/components/schemas/ExternalIdentity'
        manualVerificationFlag:
          type: boolean

    whiteListUser:
      description: Internal Update User entity.
      type: object
      properties:
        currentEmail:
          type: string
        currentPhone:
          type: string
          description: Customer phone number
          pattern: '^(\+971)\d{7,9}$'
        reason:
          type: string
        source:
          type: string

    accountClosure:
      description: Closure request payload.
      type: object
      properties:
        userId:
          type: string
        iban:
          type: string
        extraInfo:
          type: object
          additionalProperties: true
        force:
          type: boolean

    IdentityUser:
      description: A representation of User entity.
      type: object
      required:
        - uuid
        - email
      properties:
        id:
          description: The ID of the User.
          type: string
          format: uuid
        customerIdentifier:
          description: The onboarding identifier of the User.
          type: string
        phone:
          $ref: '#/components/schemas/Phone'
        email:
          $ref: '#/components/schemas/Email'
        emailVerified:
          type: boolean
        availableProducts:
          description: The List available products of the User.
          type: array
          items:
            $ref: '#/components/schemas/Product'
        factors:
          description: The List of factor configurations of the User.
          type: array
          items:
            $ref: '#/components/schemas/TwoFaFactor'
        customerInfo:
          type: object
          properties:
            firstName:
              description: The first name of the User.
              type: string
            lastName:
              description: The last name of the User.
              type: string
            gender:
              description: The gender of the User.
              type: string
            customerReference:
              description: The onboarding reference of the User.
              type: string
            nationality:
              description: The nationality of the User.
              type: string
            dateOfBirth:
              description: The DOB of the User.
              type: string
            countryOfBirth:
              description: The country of birth of the User.
              type: string
            status:
              description: The status of the User.
              type: string
            roleType:
              description: Role of the current user (OWNER / PARTICIPANT)
              type: string

    AccountClosureResult:
      type: object
      properties:
        userId:
          type: string
        closeTriggered:
          type: boolean
        closed:
          type: boolean

    InternalIdentityUser:
      description: A representation of User entity.
      type: object
      required:
        - uuid
        - phone
        - email
      properties:
        id:
          description: The ID of the User.
          type: string
          format: uuid
        coreTwoFaUserId:
          description: The ID of the Core Two Fa User.
          type: string
          format: uuid
        customerIdentifier:
          description: The onboarding identifier of the User.
          type: string
        phone:
          $ref: '#/components/schemas/Phone'
        email:
          $ref: '#/components/schemas/Email'
        emailVerified:
          type: boolean
        availableProducts:
          description: The List available products of the User.
          type: array
          items:
            $ref: '#/components/schemas/Product'
        factors:
          description: The List of factor configurations of the User.
          type: array
          items:
            $ref: '#/components/schemas/TwoFaFactor'
        customerInfo:
          type: object
          properties:
            firstName:
              description: The first name of the User.
              type: string
            lastName:
              description: The last name of the User.
              type: string
            gender:
              description: The gender of the User.
              type: string
            customerReference:
              description: The onboarding reference of the User.
              type: string
            nationality:
              description: The nationality of the User.
              type: string
            dateOfBirth:
              description: The DOB of the User.
              type: string
            countryOfBirth:
              description: The country of birth of the User.
              type: string
            status:
              description: The status of the User.
              type: string

    Biometrics:
      description: A representation of Biometrics info.
      type: object
      required:
        - deviceId
        - biometricsEnabled
        - keyPairId
      properties:
        deviceId:
          description: Id of user's device.
          type: string
        biometricsEnabled:
          description: Returns if biometrics enabled for the user
          type: boolean
        keyPairId:
          description: The id of RSA key pair.
          type: string
          readOnly: true

    CreateSession:
      description: A representation of Create Session entity.
      type: object
      properties:
        email:
          $ref: '#/components/schemas/Email'
        externalIdentity:
          $ref: '#/components/schemas/ApplyExternalIdentity'

    IntrospectToken:
      description: A representation of introspect token request.
      type: object
      required:
        - token
      properties:
        token:
          description: Token
          type: string

    Session:
      description: A representation of Session entity.
      type: object
      required:
        - id
        - accessToken
        - refreshToken
      properties:
        id:
          type: string
          description: The ID of the session
        accessToken:
          type: string
          description: Access token
        refreshToken:
          type: string
          description: Refresh token

    IntrospectionResult:
      description: Result of introspection of token
      type: object
      required:
        - active
      properties:
        active:
          type: boolean
          description: Is token active

    RenewSession:
      description: A representation of Renew Session entity.
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string
          description: Refresh token

    ResultType:
      description: The type of the Result for the verification
      type: string
      enum:
        - OK
        - NOK

    OperationResult:
      description: A representation of operation for user update
      type: object
      required:
        - result
      properties:
        result:
          $ref: '#/components/schemas/ResultType'

    ValidateClosure:
      type: array
      items:
        type: object
        properties:
          userId:
            type: string
          domainId:
            type: string
          product:
            type: string
          currentStatus:
            type: string
          closeable:
            type: boolean
          reasons:
            type: array
            items:
              type: object
              properties:
                reasonCode:
                  type: string
                reasonDescription:
                  type: string

    UpsertPasscode:
      description: A representation of Upsert Passcode request.
      type: object
      required:
        - passcode
      properties:
        passcode:
          type: string
          description: Passcode

    UpsertTrustedDevice:
      description: A representation of Create Trusted Device request.
      type: object
      required:
        - deviceId
        - publicKey
        - keyPairId
      properties:
        deviceId:
          description: Id of trusted device.
          type: string
        publicKey:
          description: Base64-encoded public key.
          type: string
        keyPairId:
          description: Unique id for RSA key pair
          type: string

    UpsertBiometrics:
      description: A representation of Create Biometrics request.
      type: object
      required:
        - deviceId
        - biometricsEnabled
      properties:
        deviceId:
          description: Id of device where biometrics factor is enabled.
          type: string
        biometricsEnabled:
          description: Indicates if a user enables biometrics
          type: boolean

    VerifyMobile:
      description: A representation of verify mobile request.
      type: object
      required:
        - mobile
        - customerId
      properties:
        mobile:
          type: string
          description: Mobile
          pattern: '^(\+971)\d{9}$'
        customerId:
          type: string
          description: CustomerId

    UpdateEmail:
      description: A representation of Update Email request.
      type: object
      required:
        - newEmail
      properties:
        newEmail:
          $ref: '#/components/schemas/Email'

    UpdateMobile:
      description: A representation of Update Mobile request.
      type: object
      required:
        - newMobile
      properties:
        newMobile:
          $ref: '#/components/schemas/Phone'

    VerifyPasscode:
      description: A representation of Verify passcode request.
      type: object
      required:
        - passcode
        - customerId
      properties:
        customerId:
          description: Unique Identifier of customer
          type: string
        passcode:
          description: Passcode of the customer
          type: string

    MigrateUsers:
      description: A representation of User Migration request.
      type: object
      required:
        - dryRun
      properties:
        dryRun:
          description: Dry-run option for migration.
          type: boolean

    ExternalUserInfoParams:
      description: A representation of external user info request
      type: object
      required:
        - customerIds
      properties:
        customerIds:
          description: customer ids for which to fetch data
          type: array
          items:
            type: string
            example: ["userId1", "userId2"]

    ExternalUserInfo:
      description: A representation of external user info data
      type: object
      properties:
        customerId:
          description: customerId or customerRef
          type: string
        onboardingStatus:
          description: onboarding status of the user
          type: string

    TwoFaFactorType:
      description: The type of the Factor in the system.
      type: string
      enum:
        - SMS_OTP
        - EMAIL_OTP
        - PASSCODE
        - EFR
        - TRUSTED_DEVICE
        - BIOMETRICS

    DirectOtpFactorType:
      description: The type of the direct OTP Factor in the system.
      type: string
      enum:
        - SMS_OTP
        - EMAIL_OTP
    OtpFactorType:
      description: The type of the direct OTP Factor in the system.
      type: string
      enum:
        - SMS_OTP
        - EMAIL_OTP
    TwoFaChallengeType:
      description: The type of the challenge.
      type: string
      default: PRIMARY
      enum:
        - PRIMARY
        - BACKUP

    CreateTwoFaTransaction:
      description: A representation of Create Transaction entity.
      type: object
      required:
        - requestedScenarios
      properties:
        purpose:
          type: string
        requestedScenarios:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestedTwoFaScenario'

    SendDirectOtp:
      description: A representation of Send Direct Otp entity.
      type: object
      required:
        - factorType
        - factorValue
      properties:
        factorType:
          $ref: '#/components/schemas/DirectOtpFactorType'
        factorValue:
          description: Should be email or mobile number to send direct OTP
          type: string
        flow:
          description: Should be like Card info or send direct OTP for shared card
          type: string
        notificationContext:
          description: Should be like Card info or send direct OTP for shared card
          type: object
          additionalProperties:
            type: string


    SendOtpWithCustomerId:
      description: A representation of Send Otp entity.
      type: object
      required:
        - factorType
      properties:
        factorType:
          $ref: '#/components/schemas/OtpFactorType'

    TwoFaTransaction:
      description: A representation of Transaction entity.
      type: object
      required:
        - id
        - status
        - scenarios
      properties:
        id:
          description: The ID of the Transaction.
          type: string
          format: uuid
          readOnly: true
        status:
          readOnly: true
          allOf:
            - $ref: '#/components/schemas/TwoFaTransactionStatus'
        scenarios:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/TwoFaScenario'
        purpose:
          description: The purpose of the Transaction.
          type: string
        additionalFields:
          description: Additional fields related to the Transaction.
          type: object
          additionalProperties:
            type: string

    Product:
      type: object
      properties:
        name:
          description: The name of the product.
          type: string
        id:
          description: The id of the product.
          type: string

    TwoFaTransactionStatus:
      description: The status of the Transaction in the system.
      type: string
      enum:
        - INITIATED
        - IN_PROGRESS
        - FAILED
        - COMPLETED
        - VERIFIED
        - FINISHED
        - EXPIRED

    TwoFaScenario:
      description: A representation of scenario entity.
      type: object
      properties:
        strength:
          description: indicate the strength of scenario
          $ref: '#/components/schemas/Strength'
        challenges:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/TwoFaChallenge'

    Strength:
      description: Represents the strength of scenario
      type: string
      enum:
        - WEAK
        - STRONG

    ScreenText:
      description: The text to be displayed on screen
      type: object
      properties:
        title:
          type: string
        subtitle:
          type: string

    TwoFaChallenge:
      description: A representation of Challenge entity.
      type: object
      discriminator:
        propertyName: factorType
        mapping:
          SMS_OTP: '#/components/schemas/SmsOtpTwoFaChallenge'
          EMAIL_OTP: '#/components/schemas/EmailOtpTwoFaChallenge'
          PASSCODE: '#/components/schemas/PasscodeTwoFaChallenge'
          EFR: '#/components/schemas/EfrTwoFaChallenge'
          TRUSTED_DEVICE: '#/components/schemas/TrustedDeviceTwoFaChallenge'
          BIOMETRICS: '#/components/schemas/BiometricsTwoFaChallenge'
      required:
        - id
        - factorType
        - status
        - type
      properties:
        id:
          description: The ID of the Challenge.
          type: string
          format: uuid
        attemptsLeft:
          description: Attempts left to pass the challenge.
          type: integer
        factorType:
          $ref: '#/components/schemas/TwoFaFactorType'
        status:
          $ref: '#/components/schemas/TwoFaChallengeStatus'
        type:
          $ref: '#/components/schemas/TwoFaChallengeType'
        uiInfoEnabled:
          default: false
          type: boolean
        uiInfoDetails:
          $ref: '#/components/schemas/ScreenText'

    SmsOtpTwoFaChallenge:
      description: A representation of Sms Otp Challenge entity.
      allOf:
        - $ref: '#/components/schemas/TwoFaChallenge'
      type: object

    EmailOtpTwoFaChallenge:
      description: A representation of Email Otp Challenge entity.
      allOf:
        - $ref: '#/components/schemas/TwoFaChallenge'
      type: object

    PasscodeTwoFaChallenge:
      description: A representation of Passcode Challenge entity.
      allOf:
        - $ref: '#/components/schemas/TwoFaChallenge'
      type: object

    EfrTwoFaChallenge:
      description: A representation of Efr Challenge entity.
      allOf:
        - $ref: '#/components/schemas/TwoFaChallenge'
      type: object

    TrustedDeviceTwoFaChallenge:
      description: A representation of Trusted Device Challenge entity.
      allOf:
        - $ref: '#/components/schemas/TwoFaChallenge'
      type: object
      required:
        - deviceId
        - keyPairId
      properties:
        deviceId:
          description: The id of device which is expected to be trusted.
          type: string
          readOnly: true
        keyPairId:
          description: The id of RSA key pair which is expected to match with the one stored on mobile.
          type: string
          readOnly: true

    BiometricsTwoFaChallenge:
      description: A representation of Biometrics Challenge entity.
      allOf:
        - $ref: '#/components/schemas/TwoFaChallenge'
      type: object
      required:
        - deviceId
      properties:
        deviceId:
          description: The id of device which is expected to have biometrics enabled.
          type: string
          readOnly: true

    TwoFaChallengeStatus:
      description: The status of the Challenge in the system.
      type: string
      enum:
        - CREATED
        - INITIATED
        - IN_PROGRESS
        - FAILED
        - FACTOR_BLOCKED
        - SOLVED
        - EXPIRED

    TwoFaFactorStatus:
      description: The status of the Factor in the system.
      type: string
      enum:
        - ENABLED
        - DISABLED
        - BLOCKED
        - TEMPORARILY_DISABLED
        - TEMPORARILY_EXPIRED

    SolveTwoFaChallenge:
      description: A representation of Solve Challenge operation.
      type: object
      discriminator:
        propertyName: type
        mapping:
          SMS_OTP: '#/components/schemas/SolveTwoFaSmsOtpChallenge'
          EMAIL_OTP: '#/components/schemas/SolveTwoFaEmailOtpChallenge'
          PASSCODE: '#/components/schemas/SolveTwoFaPasscodeChallenge'
          TRUSTED_DEVICE: '#/components/schemas/SolveTwoFaTrustedDeviceChallenge'
          BIOMETRICS: '#/components/schemas/SolveTwoFaBiometricsChallenge'
          EFR: '#/components/schemas/SolveTwoFaEfrChallenge'
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/TwoFaFactorType'

    SolveTwoFaSmsOtpChallenge:
      description: A representation of Solve Sms Otp Challenge operation.
      allOf:
        - $ref: '#/components/schemas/SolveTwoFaChallenge'
      type: object
      required:
        - otp
      properties:
        otp:
          description: The received one time password.
          type: string

    SolveTwoFaEmailOtpChallenge:
      description: A representation of Solve Email Otp Challenge operation.
      allOf:
        - $ref: '#/components/schemas/SolveTwoFaChallenge'
      type: object
      required:
        - otp
      properties:
        otp:
          description: The received one time password.
          type: string

    SolveTwoFaPasscodeChallenge:
      description: A representation of Solve Passcode Challenge operation.
      allOf:
        - $ref: '#/components/schemas/SolveTwoFaChallenge'
      type: object
      required:
        - code
      properties:
        code:
          description: Passcode entered by user.
          type: string

    SolveTwoFaEfrChallenge:
      description: A representation of Solve Efr Challenge operation.
      allOf:
        - $ref: '#/components/schemas/SolveTwoFaChallenge'
      type: object
      required:
        - data
        - dataHash
        - tag
      properties:
        data:
          description: Image in base64 format.
          type: string
        dataHash:
          description: Signature generated by the EFR MobileSDK.
          type: string
        tag:
          description: Hash function name used in dataHash. Generated by EFR MobileSDK
          type: string

    SolveTwoFaTrustedDeviceChallenge:
      description: A representation of Solve Trusted Device Challenge operation.
      allOf:
        - $ref: '#/components/schemas/SolveTwoFaChallenge'
      type: object
      required:
        - signature
      properties:
        signature:
          description: Base64-encoded challenge id signed with trusted device's private key
          type: string

    SolveTwoFaBiometricsChallenge:
      description: A representation of Solve Biometrics Challenge operation.
      allOf:
        - $ref: '#/components/schemas/SolveTwoFaChallenge'
      type: object
      required:
        - success
      properties:
        success:
          description: Flag to indicate if the biometrics check was succeeded or failed
          type: boolean

    VerifyTwoFaTransaction:
      description: A representation of Verify Transaction request.
      type: object
      required:
        - requestedScenarios
      properties:
        purpose:
          type: string
        requestedScenarios:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestedTwoFaScenario'

    RequestedTwoFaScenario:
      description: A Requested Scenario object which is used for Transaction creation.
      type: object
      required:
        - requestedChallenges
      properties:
        requestedChallenges:
          type: array
          minItems: 1
          items:
            $ref: '#/components/schemas/RequestedTwoFaChallenge'

    RequestedTwoFaChallenge:
      description: A Requested Challenge object which is used for Transaction creation.
      type: object
      required:
        - factorType
      properties:
        initiate:
          description: Whether initiate the challenge right now or not.
          type: boolean
          default: true
        factorType:
          $ref: '#/components/schemas/TwoFaFactorType'

    EfrLiveness:
      description: Liveness response from EFR
      type: object
      required:
        - data
      properties:
        data:
          description: Liveness encrypted data
          type: string

    Error:
      type: object
      description: Representation of an Error that can appear using the application.
      properties:
        code:
          description: The code of an error that describes the Error.
          readOnly: true
          type: string
        description:
          description: The message of an error that describes the Error.
          type: string
          readOnly: true
        message:
          description: The message of an error.
          type: string
          readOnly: true
        subtext:
          description: The message of an error that describes the Error.
          type: string
          readOnly: true
        errorFormat:
          description: The message of an error without context.
          type: string
          readOnly: true
        context:
          description: The context of an error.
          type: object
          readOnly: true
          additionalProperties:
            type: object

    TwoFaFactor:
      description: A representation of Factor configuration for user.
      type: object
      discriminator:
        propertyName: factorType
        mapping:
          SMS_OTP: '#/components/schemas/TwoFaSmsOtpFactor'
          EMAIL_OTP: '#/components/schemas/TwoFaEmailOtpFactor'
          PASSCODE: '#/components/schemas/TwoFaPasscodeFactor'
          EFR: '#/components/schemas/TwoFaEfrFactor'
          BIOMETRICS: '#/components/schemas/TwoFaBiometricsFactor'
          TRUSTED_DEVICE: '#/components/schemas/TwoFaTrustedDeviceFactor'
      required:
        - factorType
        - status
      properties:
        factorType:
          $ref: '#/components/schemas/TwoFaFactorType'
        status:
          $ref: '#/components/schemas/TwoFaFactorStatus'

    TwoFaSmsOtpFactor:
      description: A representation of Sms Factor
      allOf:
        - $ref: '#/components/schemas/TwoFaFactor'
      type: object

    TwoFaEmailOtpFactor:
      description: A representation of Email Otp Factor
      allOf:
        - $ref: '#/components/schemas/TwoFaFactor'
      type: object

    TwoFaEfrFactor:
      description: A representation of Efr Factor
      allOf:
        - $ref: '#/components/schemas/TwoFaFactor'
      type: object

    TwoFaPasscodeFactor:
      description: A representation of Passcode Factor
      allOf:
        - $ref: '#/components/schemas/TwoFaFactor'
      type: object

    TwoFaBiometricsFactor:
      description: A representation of Biometrics Factor
      allOf:
        - $ref: '#/components/schemas/TwoFaFactor'
      type: object
      required:
        - deviceId
      properties:
        deviceId:
          description: The id of device which is expected to be trusted.
          type: string
          readOnly: true

    TwoFaTrustedDeviceFactor:
      description: A representation of Trusted device Factor
      allOf:
        - $ref: '#/components/schemas/TwoFaFactor'
      type: object
      required:
        - deviceId
        - publicKey
        - keyPairId
      properties:
        deviceId:
          description: The id of device which is expected to be trusted.
          type: string
          readOnly: true
        keyPairId:
          description: The id of RSA key pair which is expected to match with the one stored on mobile.
          type: string
          readOnly: true
        publicKey:
          description: The public key.
          type: string
          readOnly: true

    StepUpAuthenticationScenarioType:
      description: Type of step up authentication scenario
      type: string
      enum:
        - TRANSFER
        - CARD
        - INVOICE

    Currency:
      description: currency of a transaction
      type: string
      enum:
        - AED
        - USD
        - EUR
        - GBP
        - SAR
        - INR

  responses:
    EfrConfiguration:
      description: Get Configuration information
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EfrConfiguration"

    OperationResponse:
      description: The user was successfully changed
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/OperationResult"
    ValidateClosureResponse:
      description: Successful validation
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ValidateClosure"
    NoContentResponse:
      description: The request was successfully processed.
    UserResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/IdentityUser"
    AccountClosureResponse:
      description: Successful closure trigger
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AccountClosureResult"
    UsersResponse:
      description: OK
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Element-Count:
          $ref: '#/components/headers/X-Element-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "#/components/schemas/IdentityUser"
    InternalUserResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/InternalIdentityUser"
    InternalUsersResponse:
      description: OK
      headers:
        X-Total-Count:
          $ref: '#/components/headers/X-Total-Count'
        X-Element-Count:
          $ref: '#/components/headers/X-Element-Count'
        X-Total-Pages:
          $ref: '#/components/headers/X-Total-Pages'
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "#/components/schemas/InternalIdentityUser"
    SessionResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Session"
    IntrospectTokenResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/IntrospectionResult"
    TransactionResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/TwoFaTransaction"
    EfrLivenessResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EfrLiveness"
    BiometricsResponse:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Biometrics"
    StepUpAuthResponse:
      description: OK
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/RequestedTwoFaScenario'
    ExternalUserInfoResponse:
      description: OK
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ExternalUserInfo'
    ValidationError:
      description: The payload contains an error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFoundError:
      description: The specified resource was not found.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    UnknownError:
      description: The unknown error appeared. Check your payload or contact support.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'

  requestBodies:
    CreateUserRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CreateIdentityUser"
    CreateIdentityRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CreateIdentity"
    UpdateUserRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UpdateUser"

    whiteListUserRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/whiteListUser"

    accountClosure:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/accountClosure"

    CreateTransactionRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CreateTwoFaTransaction"
    SendDirectOtpRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/SendDirectOtp"
    SendOtpWithCustomerIdRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/SendOtpWithCustomerId"
    SolveChallengeRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/SolveTwoFaChallenge"
    VerifyTransactionRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/VerifyTwoFaTransaction"
    CreateSessionRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CreateSession"
    RenewSessionRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/RenewSession"
    IntrospectTokenRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/IntrospectToken"
    UpsertPasscodeRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UpsertPasscode"
    VerifyMobileRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/VerifyMobile"
    UpsertTrustedDeviceRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UpsertTrustedDevice"
    UpsertBiometricsRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UpsertBiometrics"
    UpdateEmailRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UpdateEmail"
    UpdateMobileRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UpdateMobile"
    VerifyPasscode:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/VerifyPasscode"
    MigrateUsersRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/MigrateUsers"
    ExternalUserInfoRequest:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ExternalUserInfoParams"
  headers:
    X-Total-Count:
      description: The total results for the particular query.
      schema:
        type: number
        format: int64
    X-Element-Count:
      description: The element for the current page.
      schema:
        type: number
        format: int64
    X-Total-Pages:
      description: The total amount of pages for the particular query.
      schema:
        type: number
        format: int64

  parameters:
    #Headers
    x-2fa-id:
      in: header
      name: x-2fa-id
      required: false
      description: >
        Identifier of the 2FA transaction. Is not required with first fail approach.
        In order to generate it, send request w/o it in place and it will fail with 2FA requirements.
        Pass all the challenges and then come back with it and execute business transaction.
      schema:
        type: string
        format: uuid

    x-device-id:
      in: header
      name: x-device-id
      required: false
      description: User device identifier
      schema:
        type: string

    # Transaction
    p_transaction_id:
      in: path
      name: transaction_id
      required: true
      schema:
        type: string
        format: uuid

    q_transaction_id:
      in: query
      name: transactionId
      required: true
      schema:
        type: string
        format: uuid

    # Challenge
    p_challenge_id:
      in: path
      name: challenge_id
      required: true
      schema:
        type: string
        format: uuid

    # User
    email:
      in: query
      name: email
      schema:
        type: string
    phone:
      in: query
      name: phone
      schema:
        type: string
    customerId:
      in: query
      name: customerId
      schema:
        type: string
    coreTwoFaUserId:
      in: query
      name: coreTwoFaUserId
      schema:
        type: string
        format: uuid
    externalIdentityId:
      in: query
      name: externalIdentityId
      schema:
        type: string
    userId:
      name: userId
      in: query
      required: true
      description: The user ID to validate for closure
      schema:
        type: string

    # Generic
    c_id:
      in: path
      name: id
      required: true
      schema:
        type: string
    p_id:
      in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
    id:
      in: query
      name: id
      schema:
        type: string
        format: uuid
    pagination_page_num:
      in: query
      name: page_num
      schema:
        type: integer
        default: 0
    pagination_page_size:
      in: query
      name: page_size
      schema:
        type: integer
        default: 10
      required: false
    pagination_users_sort:
      in: query
      name: sort
      style: form
      explode: false
      description: |
        In order to execute *asc*, you need to specify in the search *createdAt* parameter.
        In order to complete the *desc* sorting must be specified in the query parameter *-createdAt*
      schema:
        type: array
        items:
          type: string
          enum: [ "createdAt", "-createdAt" ]
    customer_id:
      name: customer_id
      in: path
      required: true
      schema:
        type: string
    factor_type:
      name: factor_type
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/TwoFaFactorType'
    step_up_type:
      name: scenario_type
      in: query
      required: true
      schema:
        $ref: '#/components/schemas/StepUpAuthenticationScenarioType'
    amount:
      name: amount
      in: query
      required: true
      schema:
        type: number
    currency:
      name: currency
      in: query
      required: true
      schema:
        $ref: '#/components/schemas/Currency'
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
