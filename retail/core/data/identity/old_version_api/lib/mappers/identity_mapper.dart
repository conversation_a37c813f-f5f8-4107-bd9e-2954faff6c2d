import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_identity_data_old_version_api/identity_models/biometrics_challenge_model.dart'
    as bio;
import 'package:wio_feature_identity_data_old_version_api/identity_models/extended_identity_user_model.dart';
import 'package:wio_feature_identity_data_old_version_api/identity_models/trusted_device_challenge_model.dart'
    as trusted;
import 'package:wio_feature_identity_data_old_version_api/identity_models/twofa_challenge_model.dart'
    as challenge;
import 'package:wio_feature_identity_data_old_version_api/identity_models/twofa_scenario_model.dart'
    as scenario;
import 'package:wio_feature_identity_data_old_version_api/index.dart' as dto;
import 'package:wio_feature_identity_data_old_version_api/models/overriden_models.dart'
    as dto;
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import 'package:wio_feature_user_api/domain/model/factor_config.dart' as user;

/// Class for mapping all the enums and models between data and domain layers
abstract class IdentityMapper {
  /// Map [TwoFactorAuthType] from data layer to domain layer
  TwoFactorAuthType toTwoFactorAuthType(dto.TwoFaFactorType type);

  /// Map [TwoFactorAuthType] from domain layer to data layer
  dto.TwoFaFactorType toTwoFaFactorTypeDto(TwoFactorAuthType type);

  /// Map [CreateUserArguments] to dto model
  dto.CreateIdentityUser toCreateIdentityUserDto(
    OldVersionCreateUserArguments arguments,
    String? uaePassToken,
  );

  /// Map [ExtendedIdentityUser] to domain model
  OldVersionCreateUserResponse toCreateUserResponse(
    ExtendedIdentityUser userIdentity,
  );

  /// Map [dto.TwoFaChallenge] to domain model
  TwoFactorChallenge twoFactorChallenge(challenge.TwoFaChallenge challenge);

  /// Map [dto.TwoFaChallengeStatus] to domain enum
  TwoFactorAuthChallengeStatus toTwoFactorAuthChallengeStatus(
    dto.TwoFaChallengeStatus status,
  );

  /// Map [dto.TwoFaTransactionStatus] to domain enum
  TwoFactorAuthTransactionStatus toTwoFactorAuthTransactionStatus(
    dto.TwoFaTransactionStatus status,
  );

  /// Map [Token] to domain model of session response
  Token toTokenResponse(dto.Session session);

  /// Map [UpsertTrustedDevice] to dto model
  dto.UpsertTrustedDevice toUpsertTrustedDeviceDto(
    UpsertTrustedDevice upsertTrustedDevice,
  );

  /// Map [dto.IdentityUser] to domain model
  UserIdentity toUserIdentity(dto.IdentityUser identityUser);

  /// Map [dto.Product] to domain model
  Product toProduct(dto.Product product);

  /// Map [dto.IdentityUser$CustomerInfo] to domain model
  CustomerInfo toCustomerInfo(
    dto.IdentityUser$CustomerInfo customerInfo,
  );

  /// Map [dto.TwoFaScenario] to domain model
  TwoFactorScenario toTwoFactorScenario(scenario.TwoFaScenario twoFaScenario);

  /// Map [dto.Biometrics] to domain model
  Biometrics toBiometrics(dto.Biometrics biometrics);

  /// Map [dto.TwoFaFactorStatus] to domain model
  TwoFactorStatus toTwoFactorStatus(dto.TwoFaFactorStatus status);

  /// Map [TwoFactorAuthType] to user domain model
  user.TwoFactorAuthType toUserTwoFactorAuthType(
    TwoFactorAuthType type,
  );

  /// Map [TwoFactorStatus] to user domain model
  user.TwoFactorStatus toUserTwoFactorStatus(TwoFactorStatus type);

  /// Map [dto.TwoFaTransactionStatus] to domain model
  TwoFactorTransactionStatus toTwoFactorTransactionStatus(
    dto.TwoFaTransactionStatus status,
  );
}

class IdentityMapperImpl implements IdentityMapper {
  final ErrorReporter _reporter;

  const IdentityMapperImpl(this._reporter);

  @override
  TwoFactorAuthType toTwoFactorAuthType(dto.TwoFaFactorType type) {
    return _reporter.executeWithReport(
      () {
        switch (type) {
          case dto.TwoFaFactorType.smsOtp:
            return TwoFactorAuthType.smsOtp;
          case dto.TwoFaFactorType.emailOtp:
            return TwoFactorAuthType.emailOtp;
          case dto.TwoFaFactorType.passcode:
            return TwoFactorAuthType.passcode;
          case dto.TwoFaFactorType.efr:
            return TwoFactorAuthType.efr;
          case dto.TwoFaFactorType.trustedDevice:
            return TwoFactorAuthType.trustedDevice;
          case dto.TwoFaFactorType.biometrics:
            return TwoFactorAuthType.biometrics;
          default:
            return TwoFactorAuthType.unknown;
        }
      },
    );
  }

  @override
  dto.TwoFaFactorType toTwoFaFactorTypeDto(TwoFactorAuthType type) {
    return _reporter.executeWithReport(
      () {
        switch (type) {
          case TwoFactorAuthType.smsOtp:
            return dto.TwoFaFactorType.smsOtp;
          case TwoFactorAuthType.emailOtp:
            return dto.TwoFaFactorType.emailOtp;
          case TwoFactorAuthType.passcode:
            return dto.TwoFaFactorType.passcode;
          case TwoFactorAuthType.efr:
            return dto.TwoFaFactorType.efr;
          case TwoFactorAuthType.trustedDevice:
            return dto.TwoFaFactorType.trustedDevice;
          case TwoFactorAuthType.biometrics:
            return dto.TwoFaFactorType.biometrics;
          default:
            return dto.TwoFaFactorType.swaggerGeneratedUnknown;
        }
      },
    );
  }

  @override
  dto.CreateIdentityUser toCreateIdentityUserDto(
    OldVersionCreateUserArguments arguments,
    String? uaePassToken,
  ) {
    return _reporter.executeWithReport(() {
      final keyFactStatement = arguments.agreements.keyFactsStatement;
      if (keyFactStatement == null) {
        throw ArgumentError(
          'key facts statement cannot be null for v1 agreements',
        );
      }
      return dto.CreateIdentityUser(
        phone: arguments.phone,
        email: arguments.email,
        agreement: dto.Agreement(
          privacyPolicy: dto.PrivacyPolicy(
            version: arguments.agreements.privacyPolicy.version,
            versionDate: arguments.agreements.privacyPolicy.versionDate,
            accepted: arguments.agreements.privacyPolicy.accepted,
          ),
          keyFactsStatement: dto.KeyFactsStatement(
            version: keyFactStatement.version,
            versionDate: keyFactStatement.versionDate,
            accepted: keyFactStatement.accepted,
          ),
        ),
        externalIdentity: uaePassToken != null
            ? dto.ApplyExternalIdentity(
                type: dto.ExternalIdentityType.uaepass,
                authToken: uaePassToken,
              )
            : null,
      );
    });
  }

  @override
  OldVersionCreateUserResponse toCreateUserResponse(
    ExtendedIdentityUser userIdentity,
  ) {
    return _reporter.executeWithReport(
      () => OldVersionCreateUserResponse(
        id: userIdentity.id ?? '',
        phone: userIdentity.phone,
        email: userIdentity.email,
        transactionId: userIdentity.transactionId,
        customerId: userIdentity.customerIdentifier,
      ),
    );
  }

  @override
  // ignore: long-method
  TwoFactorChallenge twoFactorChallenge(challenge.TwoFaChallenge challenge) {
    return _reporter.executeWithReport(
      () {
        switch (challenge.factorType) {
          case dto.TwoFaFactorType.smsOtp:
            return TwoFactorChallenge.smsOtp(
              id: challenge.id,
              factorAuthType: toTwoFactorAuthType(challenge.factorType),
              status: toTwoFactorAuthChallengeStatus(challenge.status),
              attemptsLeft: challenge.attemptsLeft,
            );
          case dto.TwoFaFactorType.emailOtp:
            return TwoFactorChallenge.emailOtp(
              id: challenge.id,
              factorAuthType: toTwoFactorAuthType(challenge.factorType),
              status: toTwoFactorAuthChallengeStatus(challenge.status),
              attemptsLeft: challenge.attemptsLeft,
            );
          case dto.TwoFaFactorType.passcode:
            return TwoFactorChallenge.passcode(
              id: challenge.id,
              factorAuthType: toTwoFactorAuthType(challenge.factorType),
              status: toTwoFactorAuthChallengeStatus(challenge.status),
              attemptsLeft: challenge.attemptsLeft,
            );
          case dto.TwoFaFactorType.efr:
            return TwoFactorChallenge.efr(
              id: challenge.id,
              factorAuthType: toTwoFactorAuthType(challenge.factorType),
              status: toTwoFactorAuthChallengeStatus(challenge.status),
              attemptsLeft: challenge.attemptsLeft,
            );
          case dto.TwoFaFactorType.trustedDevice:
            final trustedDeviceChallenge =
                challenge as trusted.TrustedDeviceTwoFaChallenge;
            return TwoFactorChallenge.trustedDevice(
              id: trustedDeviceChallenge.id,
              factorAuthType:
                  toTwoFactorAuthType(trustedDeviceChallenge.factorType),
              status:
                  toTwoFactorAuthChallengeStatus(trustedDeviceChallenge.status),
              deviceId: trustedDeviceChallenge.deviceId,
              keyPairId: trustedDeviceChallenge.keyPairId,
              attemptsLeft: challenge.attemptsLeft,
            );
          case dto.TwoFaFactorType.biometrics:
            final biometricChallenge =
                challenge as bio.BiometricsTwoFaChallenge;
            return TwoFactorChallenge.biometrics(
              id: biometricChallenge.id,
              factorAuthType:
                  toTwoFactorAuthType(biometricChallenge.factorType),
              status: toTwoFactorAuthChallengeStatus(biometricChallenge.status),
              deviceId: biometricChallenge.deviceId,
              attemptsLeft: challenge.attemptsLeft,
            );
          case dto.TwoFaFactorType.swaggerGeneratedUnknown:
            throw Exception('Unknown type of TwoFaFactorType');
        }
      },
    );
  }

  @override
  TwoFactorAuthChallengeStatus toTwoFactorAuthChallengeStatus(
    dto.TwoFaChallengeStatus status,
  ) {
    return _reporter.executeWithReport(
      () {
        switch (status) {
          case dto.TwoFaChallengeStatus.created:
            return TwoFactorAuthChallengeStatus.created;
          case dto.TwoFaChallengeStatus.initiated:
            return TwoFactorAuthChallengeStatus.initiated;
          case dto.TwoFaChallengeStatus.inProgress:
            return TwoFactorAuthChallengeStatus.inProgress;
          case dto.TwoFaChallengeStatus.failed:
            return TwoFactorAuthChallengeStatus.failed;
          case dto.TwoFaChallengeStatus.solved:
            return TwoFactorAuthChallengeStatus.solved;
          case dto.TwoFaChallengeStatus.expired:
            return TwoFactorAuthChallengeStatus.expired;
          case dto.TwoFaChallengeStatus.factorBlocked:
            return TwoFactorAuthChallengeStatus.factorBlocked;
          default:
            return TwoFactorAuthChallengeStatus.unknown;
        }
      },
    );
  }

  @override
  TwoFactorAuthTransactionStatus toTwoFactorAuthTransactionStatus(
    dto.TwoFaTransactionStatus status,
  ) {
    return _reporter.executeWithReport(
      () {
        switch (status) {
          case dto.TwoFaTransactionStatus.initiated:
            return TwoFactorAuthTransactionStatus.initiated;
          case dto.TwoFaTransactionStatus.inProgress:
            return TwoFactorAuthTransactionStatus.inProgress;
          case dto.TwoFaTransactionStatus.failed:
            return TwoFactorAuthTransactionStatus.failed;
          case dto.TwoFaTransactionStatus.completed:
            return TwoFactorAuthTransactionStatus.completed;
          case dto.TwoFaTransactionStatus.verified:
            return TwoFactorAuthTransactionStatus.verified;
          case dto.TwoFaTransactionStatus.expired:
            return TwoFactorAuthTransactionStatus.expired;
          default:
            return TwoFactorAuthTransactionStatus.unknown;
        }
      },
    );
  }

  @override
  Token toTokenResponse(dto.Session session) {
    return _reporter.executeWithReport(
      () => Token(
        id: session.id,
        accessToken: session.accessToken,
        refreshToken: session.refreshToken,
      ),
    );
  }

  @override
  dto.UpsertTrustedDevice toUpsertTrustedDeviceDto(
    UpsertTrustedDevice upsertTrustedDevice,
  ) {
    return _reporter.executeWithReport(
      () => dto.UpsertTrustedDevice(
        deviceId: upsertTrustedDevice.deviceId,
        publicKey: upsertTrustedDevice.publicKey,
        keyPairId: upsertTrustedDevice.keyPairId,
      ),
    );
  }

  @override
  UserIdentity toUserIdentity(dto.IdentityUser identityUser) {
    return _reporter.executeWithReport(
      () {
        final availableProducts =
            identityUser.availableProducts?.map((product) {
          return Product(name: product.name, id: product.id);
        }).toList();

        final customerInfoDto = identityUser.customerInfo;

        final factors = identityUser.factors?.map((factor) {
              String? deviceId;
              String? keyPairId;
              if (factor is dto.TwoFaTrustedDeviceFactor) {
                deviceId = factor.deviceId;
                keyPairId = factor.keyPairId;
              } else if (factor is dto.TwoFaBiometricsFactor) {
                deviceId = factor.deviceId;
              }

              return FactorConfig(
                status: toTwoFactorStatus(factor.status),
                type: toTwoFactorAuthType(factor.factorType),
                deviceId: deviceId,
                keyPairId: keyPairId,
              );
            }).toList() ??
            [];

        return UserIdentity(
          id: identityUser.id,
          customerIdentifier: identityUser.customerIdentifier,
          phone: identityUser.phone,
          email: identityUser.email,
          availableProducts: availableProducts,
          customerInfo:
              customerInfoDto != null ? toCustomerInfo(customerInfoDto) : null,
          isEmailVerified: identityUser.emailVerified ?? false,
          factors: factors,
        );
      },
    );
  }

  @override
  CustomerInfo toCustomerInfo(
    dto.IdentityUser$CustomerInfo customerInfo,
  ) {
    return _reporter.executeWithReport(
      () => CustomerInfo(
        firstName: customerInfo.firstName,
        lastName: customerInfo.lastName,
        gender: customerInfo.gender,
        customerReference: customerInfo.customerReference,
        nationality: customerInfo.nationality,
        dateOfBirth: customerInfo.dateOfBirth,
        countryOfBirth: customerInfo.countryOfBirth,
        status: customerInfo.status,
      ),
    );
  }

  @override
  Product toProduct(dto.Product product) {
    return _reporter.executeWithReport(
      () => Product(name: product.name, id: product.id),
    );
  }

  @override
  TwoFactorScenario toTwoFactorScenario(scenario.TwoFaScenario twoFaScenario) {
    return _reporter.executeWithReport(
      () {
        final challenges =
            twoFaScenario.challenges?.map(twoFactorChallenge).toList();

        return TwoFactorScenario(challenges: challenges ?? []);
      },
    );
  }

  @override
  Biometrics toBiometrics(dto.Biometrics biometrics) {
    return _reporter.executeWithReport(
      () => Biometrics(
        deviceId: biometrics.deviceId,
        biometricsEnabled: biometrics.biometricsEnabled,
        keyPairId: biometrics.keyPairId,
      ),
    );
  }

  @override
  TwoFactorStatus toTwoFactorStatus(dto.TwoFaFactorStatus status) {
    return _reporter.executeWithReport(
      () {
        switch (status) {
          case dto.TwoFaFactorStatus.enabled:
            return TwoFactorStatus.enabled;
          case dto.TwoFaFactorStatus.disabled:
            return TwoFactorStatus.disabled;
          case dto.TwoFaFactorStatus.blocked:
            return TwoFactorStatus.blocked;
          default:
            return TwoFactorStatus.unknown;
        }
      },
    );
  }

  @override
  user.TwoFactorAuthType toUserTwoFactorAuthType(TwoFactorAuthType type) {
    return _reporter.executeWithReport(
      () {
        switch (type) {
          case TwoFactorAuthType.smsOtp:
            return user.TwoFactorAuthType.smsOtp;
          case TwoFactorAuthType.emailOtp:
            return user.TwoFactorAuthType.emailOtp;
          case TwoFactorAuthType.passcode:
            return user.TwoFactorAuthType.passcode;
          case TwoFactorAuthType.efr:
            return user.TwoFactorAuthType.efr;
          case TwoFactorAuthType.trustedDevice:
            return user.TwoFactorAuthType.trustedDevice;
          case TwoFactorAuthType.biometrics:
            return user.TwoFactorAuthType.biometrics;
          default:
            return user.TwoFactorAuthType.unknown;
        }
      },
    );
  }

  @override
  user.TwoFactorStatus toUserTwoFactorStatus(TwoFactorStatus type) {
    return _reporter.executeWithReport(
      () {
        switch (type) {
          case TwoFactorStatus.blocked:
            return user.TwoFactorStatus.blocked;
          case TwoFactorStatus.disabled:
            return user.TwoFactorStatus.disabled;
          case TwoFactorStatus.enabled:
            return user.TwoFactorStatus.enabled;
          default:
            return user.TwoFactorStatus.unknown;
        }
      },
    );
  }

  @override
  TwoFactorTransactionStatus toTwoFactorTransactionStatus(
    dto.TwoFaTransactionStatus status,
  ) {
    return _reporter.executeWithReport(
      () {
        switch (status) {
          case dto.TwoFaTransactionStatus.initiated:
            return TwoFactorTransactionStatus.initiated;
          case dto.TwoFaTransactionStatus.expired:
            return TwoFactorTransactionStatus.expired;
          case dto.TwoFaTransactionStatus.inProgress:
            return TwoFactorTransactionStatus.inProgress;
          case dto.TwoFaTransactionStatus.failed:
            return TwoFactorTransactionStatus.failed;
          case dto.TwoFaTransactionStatus.completed:
            return TwoFactorTransactionStatus.completed;
          case dto.TwoFaTransactionStatus.verified:
            return TwoFactorTransactionStatus.verified;
          case dto.TwoFaTransactionStatus.finished:
            return TwoFactorTransactionStatus.finished;
          default:
            return TwoFactorTransactionStatus.unknown;
        }
      },
    );
  }
}
