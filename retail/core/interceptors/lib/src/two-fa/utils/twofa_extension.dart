import 'package:dio/dio.dart';
import 'package:wio_retail_interceptors/src/two-fa/two_factor_auth_interceptor.dart';

extension TwoFaInterceptorResponseExt on Response<Object?> {
  Response<Object?> get getResponseWithTransactionId {
    if (!requestOptions.headers.containsKey(TwoFaConstants.headerKey2FaId)) {
      return this;
    }

    if (data is Map<String, dynamic>) {
      final dataField = data as Map<String, dynamic>;
      // ignore: cascade_invocations, avoid-dynamic
      dataField.addAll(<String, dynamic>{
        TwoFaConstants.transactionId:
            requestOptions.headers[TwoFaConstants.headerKey2FaId],
      });
      data = dataField;
    }

    return this;
  }
}
