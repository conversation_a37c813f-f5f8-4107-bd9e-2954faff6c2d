// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'two_factor_auth_interceptor_analytics.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$TwoFactorChallengesAnalytics {
  List<List<String>> get scenarios => throw _privateConstructorUsedError;
  String get url => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $TwoFactorChallengesAnalyticsCopyWith<TwoFactorChallengesAnalytics>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TwoFactorChallengesAnalyticsCopyWith<$Res> {
  factory $TwoFactorChallengesAnalyticsCopyWith(
          TwoFactorChallengesAnalytics value,
          $Res Function(TwoFactorChallengesAnalytics) then) =
      _$TwoFactorChallengesAnalyticsCopyWithImpl<$Res>;
  $Res call({List<List<String>> scenarios, String url});
}

/// @nodoc
class _$TwoFactorChallengesAnalyticsCopyWithImpl<$Res>
    implements $TwoFactorChallengesAnalyticsCopyWith<$Res> {
  _$TwoFactorChallengesAnalyticsCopyWithImpl(this._value, this._then);

  final TwoFactorChallengesAnalytics _value;
  // ignore: unused_field
  final $Res Function(TwoFactorChallengesAnalytics) _then;

  @override
  $Res call({
    Object? scenarios = freezed,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      scenarios: scenarios == freezed
          ? _value.scenarios
          : scenarios // ignore: cast_nullable_to_non_nullable
              as List<List<String>>,
      url: url == freezed
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
abstract class _$$_TwoFactorChallengesAnalyticsCopyWith<$Res>
    implements $TwoFactorChallengesAnalyticsCopyWith<$Res> {
  factory _$$_TwoFactorChallengesAnalyticsCopyWith(
          _$_TwoFactorChallengesAnalytics value,
          $Res Function(_$_TwoFactorChallengesAnalytics) then) =
      __$$_TwoFactorChallengesAnalyticsCopyWithImpl<$Res>;
  @override
  $Res call({List<List<String>> scenarios, String url});
}

/// @nodoc
class __$$_TwoFactorChallengesAnalyticsCopyWithImpl<$Res>
    extends _$TwoFactorChallengesAnalyticsCopyWithImpl<$Res>
    implements _$$_TwoFactorChallengesAnalyticsCopyWith<$Res> {
  __$$_TwoFactorChallengesAnalyticsCopyWithImpl(
      _$_TwoFactorChallengesAnalytics _value,
      $Res Function(_$_TwoFactorChallengesAnalytics) _then)
      : super(_value, (v) => _then(v as _$_TwoFactorChallengesAnalytics));

  @override
  _$_TwoFactorChallengesAnalytics get _value =>
      super._value as _$_TwoFactorChallengesAnalytics;

  @override
  $Res call({
    Object? scenarios = freezed,
    Object? url = freezed,
  }) {
    return _then(_$_TwoFactorChallengesAnalytics(
      scenarios: scenarios == freezed
          ? _value._scenarios
          : scenarios // ignore: cast_nullable_to_non_nullable
              as List<List<String>>,
      url: url == freezed
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_TwoFactorChallengesAnalytics extends _TwoFactorChallengesAnalytics {
  const _$_TwoFactorChallengesAnalytics(
      {required final List<List<String>> scenarios, required this.url})
      : _scenarios = scenarios,
        super._();

  final List<List<String>> _scenarios;
  @override
  List<List<String>> get scenarios {
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_scenarios);
  }

  @override
  final String url;

  @override
  String toString() {
    return 'TwoFactorChallengesAnalytics(scenarios: $scenarios, url: $url)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TwoFactorChallengesAnalytics &&
            const DeepCollectionEquality()
                .equals(other._scenarios, _scenarios) &&
            const DeepCollectionEquality().equals(other.url, url));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_scenarios),
      const DeepCollectionEquality().hash(url));

  @JsonKey(ignore: true)
  @override
  _$$_TwoFactorChallengesAnalyticsCopyWith<_$_TwoFactorChallengesAnalytics>
      get copyWith => __$$_TwoFactorChallengesAnalyticsCopyWithImpl<
          _$_TwoFactorChallengesAnalytics>(this, _$identity);
}

abstract class _TwoFactorChallengesAnalytics
    extends TwoFactorChallengesAnalytics {
  const factory _TwoFactorChallengesAnalytics(
      {required final List<List<String>> scenarios,
      required final String url}) = _$_TwoFactorChallengesAnalytics;
  const _TwoFactorChallengesAnalytics._() : super._();

  @override
  List<List<String>> get scenarios => throw _privateConstructorUsedError;
  @override
  String get url => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_TwoFactorChallengesAnalyticsCopyWith<_$_TwoFactorChallengesAnalytics>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TwoFactorUrlAnalytics {
  String get url => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $TwoFactorUrlAnalyticsCopyWith<TwoFactorUrlAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TwoFactorUrlAnalyticsCopyWith<$Res> {
  factory $TwoFactorUrlAnalyticsCopyWith(TwoFactorUrlAnalytics value,
          $Res Function(TwoFactorUrlAnalytics) then) =
      _$TwoFactorUrlAnalyticsCopyWithImpl<$Res>;
  $Res call({String url});
}

/// @nodoc
class _$TwoFactorUrlAnalyticsCopyWithImpl<$Res>
    implements $TwoFactorUrlAnalyticsCopyWith<$Res> {
  _$TwoFactorUrlAnalyticsCopyWithImpl(this._value, this._then);

  final TwoFactorUrlAnalytics _value;
  // ignore: unused_field
  final $Res Function(TwoFactorUrlAnalytics) _then;

  @override
  $Res call({
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      url: url == freezed
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
abstract class _$$_TwoFactorUrlAnalyticsCopyWith<$Res>
    implements $TwoFactorUrlAnalyticsCopyWith<$Res> {
  factory _$$_TwoFactorUrlAnalyticsCopyWith(_$_TwoFactorUrlAnalytics value,
          $Res Function(_$_TwoFactorUrlAnalytics) then) =
      __$$_TwoFactorUrlAnalyticsCopyWithImpl<$Res>;
  @override
  $Res call({String url});
}

/// @nodoc
class __$$_TwoFactorUrlAnalyticsCopyWithImpl<$Res>
    extends _$TwoFactorUrlAnalyticsCopyWithImpl<$Res>
    implements _$$_TwoFactorUrlAnalyticsCopyWith<$Res> {
  __$$_TwoFactorUrlAnalyticsCopyWithImpl(_$_TwoFactorUrlAnalytics _value,
      $Res Function(_$_TwoFactorUrlAnalytics) _then)
      : super(_value, (v) => _then(v as _$_TwoFactorUrlAnalytics));

  @override
  _$_TwoFactorUrlAnalytics get _value =>
      super._value as _$_TwoFactorUrlAnalytics;

  @override
  $Res call({
    Object? url = freezed,
  }) {
    return _then(_$_TwoFactorUrlAnalytics(
      url: url == freezed
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$_TwoFactorUrlAnalytics extends _TwoFactorUrlAnalytics {
  const _$_TwoFactorUrlAnalytics({required this.url}) : super._();

  @override
  final String url;

  @override
  String toString() {
    return 'TwoFactorUrlAnalytics(url: $url)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TwoFactorUrlAnalytics &&
            const DeepCollectionEquality().equals(other.url, url));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(url));

  @JsonKey(ignore: true)
  @override
  _$$_TwoFactorUrlAnalyticsCopyWith<_$_TwoFactorUrlAnalytics> get copyWith =>
      __$$_TwoFactorUrlAnalyticsCopyWithImpl<_$_TwoFactorUrlAnalytics>(
          this, _$identity);
}

abstract class _TwoFactorUrlAnalytics extends TwoFactorUrlAnalytics {
  const factory _TwoFactorUrlAnalytics({required final String url}) =
      _$_TwoFactorUrlAnalytics;
  const _TwoFactorUrlAnalytics._() : super._();

  @override
  String get url => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_TwoFactorUrlAnalyticsCopyWith<_$_TwoFactorUrlAnalytics> get copyWith =>
      throw _privateConstructorUsedError;
}
