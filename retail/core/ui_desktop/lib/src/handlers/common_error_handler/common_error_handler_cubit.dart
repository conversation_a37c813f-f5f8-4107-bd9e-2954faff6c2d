import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_common_feature_2fa_baas_api/baas_two_factor_auth_api.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_core_ui_desktop/src/handlers/common_error_handler/common_error_handler_state.dart';
import 'package:wio_feature_core_ui_desktop/src/handlers/common_error_handler/error_mapper.dart';
import 'package:wio_feature_core_ui_desktop/src/handlers/common_error_handler/error_type.dart';
import 'package:wio_feature_error_domain_api/index.dart';

/// Server send that request when all 2FA scenarios blocked or unavailable

const _toastErrorMessage = 'toast_error_message';

class CommonErrorHandlerCubit extends Cubit<CommonErrorHandlerState> {
  final AnalyticsEventTracker _tracker;
  final ErrorReporter _errorReporter;
  final NavigationProvider _navigationProvider;
  final AuthInteractor _authInteractor;

  CommonErrorHandlerCubit({
    required AnalyticsEventTracker tracker,
    required ErrorReporter errorReporter,
    required NavigationProvider navigationProvider,
    required AuthInteractor authInteractor,
  })  : _tracker = tracker,
        _errorReporter = errorReporter,
        _navigationProvider = navigationProvider,
        _authInteractor = authInteractor,
        super(const CommonErrorHandlerState.initial());

  void handleError(Object? error) {
    final errorObject = _getCorrectErrorObject(error);

    if (errorObject == null) return;

    if (error != null) {
      /// reporting toast errors to datadog
      _errorReporter.reportError(
        error,
        properties: errorObject.getSentryProperties(),
      );
    }

    /// tracking toast errors for mixpanel
    _tracker.track(
      AnalyticsEvent.simple(
        _toastErrorMessage,
        properties: errorObject.getMixPanelProperties(),
      ),
    );

    if (_isImplicitlyActions(errorObject)) {
      return _navigateToFeature(
        (errorObject as ErrorImplicitlyActions).errorType,
      );
    } else {
      emit(CommonErrorHandlerState.showError(error: errorObject));
      emit(const CommonErrorHandlerState.initial());
    }
  }

  bool _isImplicitlyActions(ErrorMapper mapper) {
    return mapper.maybeMap(
      implicitlyActions: (_) => true,
      orElse: () => false,
    );
  }

  void _navigateToFeature(ImplicitlyActions type) {
    switch (type) {
      case ImplicitlyActions.forgotPasscode:
        _navigationProvider
            .navigateTo(
              const ConsentAuthorizationFeatureNavigationConfig(
                destination: ForgotPasscodeErrorScreenNavigationConfig(),
              ),
            )
            .ignore();
        break;
      case ImplicitlyActions.logout:
        _authInteractor.logout();
        _navigationProvider
            .removeStackAndPush(const AuthFeatureNavigationConfig())
            .ignore();
        break;
      case ImplicitlyActions.blocked:
        _navigationProvider
            .navigateTo(
              const ConsentAuthorizationFeatureNavigationConfig(
                destination: OtpBlockedErrorScreenNavigationConfig(),
              ),
            )
            .ignore();
        break;
    }
  }

  ErrorMapper? _getCorrectErrorObject(Object? error) {
    if (error is ApiException) {
      return _getApiExceptionObject(error);
    } else if (error is NoInternetConnectionException) {
      return const ErrorMapper.type(
        errorType: ErrorType.networkConnectionLost,
      );
    } else if (error is TimeoutException) {
      return const ErrorMapper.type(
        errorType: ErrorType.timeoutException,
      );
    } else if (error is RestApiUnknownException) {
      return _restApiUnknownExceptionHandler(error);
    } else if (error is TwoFaCancelledException) {
      return _twoFaCancelledException(error);
    } else if (error is TwoFaGeneralException) {
      return const ErrorMapper.type(
        errorType: ErrorType.twoFactorFailedTryAgain,
      );
    } else if (error is RestApiException<Object>) {
      return _getErrorObject(error);
    } else if (error is String && error.isNotEmpty) {
      return ErrorMapper.message(message: error);
    } else {
      return const ErrorMapper.type(
        errorType: ErrorType.commonErrorMessage,
      );
    }
  }

  ErrorMapper _getErrorObject(RestApiException<Object> exception) {
    final localExceptionData = exception.response?.data;
    if (localExceptionData is Map<String, dynamic>) {
      final message = (exception.response?.data
          as Map<String, dynamic>)['message'] as String?;
      if (message != null && message.isNotEmpty) {
        return ErrorMapper.message(
          message: message,
          correlationId: exception.correlationId,
          serverCode: exception.statusCode.toString(),
        );
      }
    }

    return ErrorMapper.type(
      errorType: ErrorType.commonErrorMessage,
      correlationId: exception.correlationId,
      serverCode: exception.statusCode.toString(),
    );
  }

  ErrorMapper _getApiExceptionObject(
    ApiException<Object?> exception,
  ) {
    final message = exception.message;
    if (message != null && message.isNotEmpty) {
      return ErrorMapper.message(
        message: message,
        serverCode: exception.code,
      );
    }

    return ErrorMapper.type(
      errorType: ErrorType.commonErrorMessage,
      serverCode: exception.code,
      correlationId: exception.id,
    );
  }

  ErrorMapper? _twoFaCancelledException(
    TwoFaCancelledException error,
  ) {
    final errorCode = error.twoFactorErrorCode;
    if (errorCode == TwoFactorErrorCode.blocked) {
      return ErrorMapper.implicitlyActions(
        errorType: ImplicitlyActions.blocked,
        serverCode: error.twoFactorErrorCode.toString(),
      );
    } else if (errorCode == TwoFactorErrorCode.failed) {
      return ErrorMapper.type(
        errorType: ErrorType.twoFactorFailedTryAgain,
        serverCode: error.twoFactorErrorCode.toString(),
      );
    } else if (errorCode == TwoFactorErrorCode.forgotPasscode) {
      return ErrorMapper.implicitlyActions(
        errorType: ImplicitlyActions.forgotPasscode,
        serverCode: error.twoFactorErrorCode.toString(),
      );
    } else if (errorCode == TwoFactorErrorCode.logout) {
      return ErrorMapper.implicitlyActions(
        errorType: ImplicitlyActions.logout,
        serverCode: error.twoFactorErrorCode.toString(),
      );
    }

    return null;
  }

  ErrorMapper? _restApiUnknownExceptionHandler(
    RestApiUnknownException error,
  ) {
    if (error.error is TwoFaCancelledException) {
      return _twoFaCancelledException(error.error as TwoFaCancelledException);
    } else if (error.error is TwoFaGeneralException) {
      return ErrorMapper.type(
        errorType: ErrorType.twoFactorFailedTryAgain,
        correlationId: error.correlationId,
      );
    } else {
      return ErrorMapper.type(
        errorType: ErrorType.commonErrorMessage,
        correlationId: error.correlationId,
      );
    }
  }
}
