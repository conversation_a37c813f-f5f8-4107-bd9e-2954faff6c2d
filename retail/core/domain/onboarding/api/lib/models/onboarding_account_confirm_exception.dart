import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';

part 'onboarding_account_confirm_exception.freezed.dart';

@freezed
class OnboardingAccountConfirmException
    with _$OnboardingAccountConfirmException
    implements HttpRequestException {
  const factory OnboardingAccountConfirmException.faceMismatch() =
      _OnboardingAccountConfirmFaceMismatchException;

  const factory OnboardingAccountConfirmException.otherEFRError() =
      _OnboardingAccountConfirmOtherEFRErrorException;

  const factory OnboardingAccountConfirmException.systemError({
    required String message,
  }) = _OnboardingAccountConfirmSystemErrorException;

  const factory OnboardingAccountConfirmException.duplicateCustomerRejected({
    required String message,
  }) = _OnboardingAccountConfirmDuplicateCustomerRejectedException;

  const factory OnboardingAccountConfirmException.duplicateCustomerActive({
    required String message,
    required String email,
  }) = _OnboardingAccountConfirmDuplicateCustomerActiveException;

  @override
  String toString() => 'OnboardingAccountConfirmException';
}
