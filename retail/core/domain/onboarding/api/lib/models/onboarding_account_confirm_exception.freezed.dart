// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_account_confirm_exception.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OnboardingAccountConfirmException {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() faceMismatch,
    required TResult Function() otherEFRError,
    required TResult Function(String message) systemError,
    required TResult Function(String message) duplicateCustomerRejected,
    required TResult Function(String message, String email)
        duplicateCustomerActive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? faceMismatch,
    TResult? Function()? otherEFRError,
    TResult? Function(String message)? systemError,
    TResult? Function(String message)? duplicateCustomerRejected,
    TResult? Function(String message, String email)? duplicateCustomerActive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? faceMismatch,
    TResult Function()? otherEFRError,
    TResult Function(String message)? systemError,
    TResult Function(String message)? duplicateCustomerRejected,
    TResult Function(String message, String email)? duplicateCustomerActive,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _OnboardingAccountConfirmFaceMismatchException value)
        faceMismatch,
    required TResult Function(
            _OnboardingAccountConfirmOtherEFRErrorException value)
        otherEFRError,
    required TResult Function(
            _OnboardingAccountConfirmSystemErrorException value)
        systemError,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)
        duplicateCustomerRejected,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)
        duplicateCustomerActive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult? Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult? Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingAccountConfirmExceptionCopyWith<$Res> {
  factory $OnboardingAccountConfirmExceptionCopyWith(
          OnboardingAccountConfirmException value,
          $Res Function(OnboardingAccountConfirmException) then) =
      _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
          OnboardingAccountConfirmException>;
}

/// @nodoc
class _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
        $Val extends OnboardingAccountConfirmException>
    implements $OnboardingAccountConfirmExceptionCopyWith<$Res> {
  _$OnboardingAccountConfirmExceptionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$OnboardingAccountConfirmFaceMismatchExceptionImplCopyWith<
    $Res> {
  factory _$$OnboardingAccountConfirmFaceMismatchExceptionImplCopyWith(
          _$OnboardingAccountConfirmFaceMismatchExceptionImpl value,
          $Res Function(_$OnboardingAccountConfirmFaceMismatchExceptionImpl)
              then) =
      __$$OnboardingAccountConfirmFaceMismatchExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OnboardingAccountConfirmFaceMismatchExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
        _$OnboardingAccountConfirmFaceMismatchExceptionImpl>
    implements
        _$$OnboardingAccountConfirmFaceMismatchExceptionImplCopyWith<$Res> {
  __$$OnboardingAccountConfirmFaceMismatchExceptionImplCopyWithImpl(
      _$OnboardingAccountConfirmFaceMismatchExceptionImpl _value,
      $Res Function(_$OnboardingAccountConfirmFaceMismatchExceptionImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$OnboardingAccountConfirmFaceMismatchExceptionImpl
    implements _OnboardingAccountConfirmFaceMismatchException {
  const _$OnboardingAccountConfirmFaceMismatchExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingAccountConfirmFaceMismatchExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() faceMismatch,
    required TResult Function() otherEFRError,
    required TResult Function(String message) systemError,
    required TResult Function(String message) duplicateCustomerRejected,
    required TResult Function(String message, String email)
        duplicateCustomerActive,
  }) {
    return faceMismatch();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? faceMismatch,
    TResult? Function()? otherEFRError,
    TResult? Function(String message)? systemError,
    TResult? Function(String message)? duplicateCustomerRejected,
    TResult? Function(String message, String email)? duplicateCustomerActive,
  }) {
    return faceMismatch?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? faceMismatch,
    TResult Function()? otherEFRError,
    TResult Function(String message)? systemError,
    TResult Function(String message)? duplicateCustomerRejected,
    TResult Function(String message, String email)? duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (faceMismatch != null) {
      return faceMismatch();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _OnboardingAccountConfirmFaceMismatchException value)
        faceMismatch,
    required TResult Function(
            _OnboardingAccountConfirmOtherEFRErrorException value)
        otherEFRError,
    required TResult Function(
            _OnboardingAccountConfirmSystemErrorException value)
        systemError,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)
        duplicateCustomerRejected,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)
        duplicateCustomerActive,
  }) {
    return faceMismatch(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult? Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult? Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
  }) {
    return faceMismatch?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (faceMismatch != null) {
      return faceMismatch(this);
    }
    return orElse();
  }
}

abstract class _OnboardingAccountConfirmFaceMismatchException
    implements OnboardingAccountConfirmException {
  const factory _OnboardingAccountConfirmFaceMismatchException() =
      _$OnboardingAccountConfirmFaceMismatchExceptionImpl;
}

/// @nodoc
abstract class _$$OnboardingAccountConfirmOtherEFRErrorExceptionImplCopyWith<
    $Res> {
  factory _$$OnboardingAccountConfirmOtherEFRErrorExceptionImplCopyWith(
          _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl value,
          $Res Function(_$OnboardingAccountConfirmOtherEFRErrorExceptionImpl)
              then) =
      __$$OnboardingAccountConfirmOtherEFRErrorExceptionImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$OnboardingAccountConfirmOtherEFRErrorExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
        _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl>
    implements
        _$$OnboardingAccountConfirmOtherEFRErrorExceptionImplCopyWith<$Res> {
  __$$OnboardingAccountConfirmOtherEFRErrorExceptionImplCopyWithImpl(
      _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl _value,
      $Res Function(_$OnboardingAccountConfirmOtherEFRErrorExceptionImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl
    implements _OnboardingAccountConfirmOtherEFRErrorException {
  const _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() faceMismatch,
    required TResult Function() otherEFRError,
    required TResult Function(String message) systemError,
    required TResult Function(String message) duplicateCustomerRejected,
    required TResult Function(String message, String email)
        duplicateCustomerActive,
  }) {
    return otherEFRError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? faceMismatch,
    TResult? Function()? otherEFRError,
    TResult? Function(String message)? systemError,
    TResult? Function(String message)? duplicateCustomerRejected,
    TResult? Function(String message, String email)? duplicateCustomerActive,
  }) {
    return otherEFRError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? faceMismatch,
    TResult Function()? otherEFRError,
    TResult Function(String message)? systemError,
    TResult Function(String message)? duplicateCustomerRejected,
    TResult Function(String message, String email)? duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (otherEFRError != null) {
      return otherEFRError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _OnboardingAccountConfirmFaceMismatchException value)
        faceMismatch,
    required TResult Function(
            _OnboardingAccountConfirmOtherEFRErrorException value)
        otherEFRError,
    required TResult Function(
            _OnboardingAccountConfirmSystemErrorException value)
        systemError,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)
        duplicateCustomerRejected,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)
        duplicateCustomerActive,
  }) {
    return otherEFRError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult? Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult? Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
  }) {
    return otherEFRError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (otherEFRError != null) {
      return otherEFRError(this);
    }
    return orElse();
  }
}

abstract class _OnboardingAccountConfirmOtherEFRErrorException
    implements OnboardingAccountConfirmException {
  const factory _OnboardingAccountConfirmOtherEFRErrorException() =
      _$OnboardingAccountConfirmOtherEFRErrorExceptionImpl;
}

/// @nodoc
abstract class _$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWith<
    $Res> {
  factory _$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWith(
          _$OnboardingAccountConfirmSystemErrorExceptionImpl value,
          $Res Function(_$OnboardingAccountConfirmSystemErrorExceptionImpl)
              then) =
      __$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWithImpl<$Res>
    extends _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
        _$OnboardingAccountConfirmSystemErrorExceptionImpl>
    implements
        _$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWith<$Res> {
  __$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWithImpl(
      _$OnboardingAccountConfirmSystemErrorExceptionImpl _value,
      $Res Function(_$OnboardingAccountConfirmSystemErrorExceptionImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$OnboardingAccountConfirmSystemErrorExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OnboardingAccountConfirmSystemErrorExceptionImpl
    implements _OnboardingAccountConfirmSystemErrorException {
  const _$OnboardingAccountConfirmSystemErrorExceptionImpl(
      {required this.message});

  @override
  final String message;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingAccountConfirmSystemErrorExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWith<
          _$OnboardingAccountConfirmSystemErrorExceptionImpl>
      get copyWith =>
          __$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWithImpl<
                  _$OnboardingAccountConfirmSystemErrorExceptionImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() faceMismatch,
    required TResult Function() otherEFRError,
    required TResult Function(String message) systemError,
    required TResult Function(String message) duplicateCustomerRejected,
    required TResult Function(String message, String email)
        duplicateCustomerActive,
  }) {
    return systemError(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? faceMismatch,
    TResult? Function()? otherEFRError,
    TResult? Function(String message)? systemError,
    TResult? Function(String message)? duplicateCustomerRejected,
    TResult? Function(String message, String email)? duplicateCustomerActive,
  }) {
    return systemError?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? faceMismatch,
    TResult Function()? otherEFRError,
    TResult Function(String message)? systemError,
    TResult Function(String message)? duplicateCustomerRejected,
    TResult Function(String message, String email)? duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (systemError != null) {
      return systemError(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _OnboardingAccountConfirmFaceMismatchException value)
        faceMismatch,
    required TResult Function(
            _OnboardingAccountConfirmOtherEFRErrorException value)
        otherEFRError,
    required TResult Function(
            _OnboardingAccountConfirmSystemErrorException value)
        systemError,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)
        duplicateCustomerRejected,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)
        duplicateCustomerActive,
  }) {
    return systemError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult? Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult? Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
  }) {
    return systemError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (systemError != null) {
      return systemError(this);
    }
    return orElse();
  }
}

abstract class _OnboardingAccountConfirmSystemErrorException
    implements OnboardingAccountConfirmException {
  const factory _OnboardingAccountConfirmSystemErrorException(
          {required final String message}) =
      _$OnboardingAccountConfirmSystemErrorExceptionImpl;

  String get message;
  @JsonKey(ignore: true)
  _$$OnboardingAccountConfirmSystemErrorExceptionImplCopyWith<
          _$OnboardingAccountConfirmSystemErrorExceptionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWith<
    $Res> {
  factory _$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWith(
          _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl value,
          $Res Function(
                  _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl)
              then) =
      __$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWithImpl<
          $Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWithImpl<
        $Res>
    extends _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
        _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl>
    implements
        _$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWith<
            $Res> {
  __$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWithImpl(
      _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl _value,
      $Res Function(
              _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl)
          _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(
        _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl
    implements _OnboardingAccountConfirmDuplicateCustomerRejectedException {
  const _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl(
      {required this.message});

  @override
  final String message;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWith<
          _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl>
      get copyWith =>
          __$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWithImpl<
                  _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() faceMismatch,
    required TResult Function() otherEFRError,
    required TResult Function(String message) systemError,
    required TResult Function(String message) duplicateCustomerRejected,
    required TResult Function(String message, String email)
        duplicateCustomerActive,
  }) {
    return duplicateCustomerRejected(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? faceMismatch,
    TResult? Function()? otherEFRError,
    TResult? Function(String message)? systemError,
    TResult? Function(String message)? duplicateCustomerRejected,
    TResult? Function(String message, String email)? duplicateCustomerActive,
  }) {
    return duplicateCustomerRejected?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? faceMismatch,
    TResult Function()? otherEFRError,
    TResult Function(String message)? systemError,
    TResult Function(String message)? duplicateCustomerRejected,
    TResult Function(String message, String email)? duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (duplicateCustomerRejected != null) {
      return duplicateCustomerRejected(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _OnboardingAccountConfirmFaceMismatchException value)
        faceMismatch,
    required TResult Function(
            _OnboardingAccountConfirmOtherEFRErrorException value)
        otherEFRError,
    required TResult Function(
            _OnboardingAccountConfirmSystemErrorException value)
        systemError,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)
        duplicateCustomerRejected,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)
        duplicateCustomerActive,
  }) {
    return duplicateCustomerRejected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult? Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult? Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
  }) {
    return duplicateCustomerRejected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (duplicateCustomerRejected != null) {
      return duplicateCustomerRejected(this);
    }
    return orElse();
  }
}

abstract class _OnboardingAccountConfirmDuplicateCustomerRejectedException
    implements OnboardingAccountConfirmException {
  const factory _OnboardingAccountConfirmDuplicateCustomerRejectedException(
          {required final String message}) =
      _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl;

  String get message;
  @JsonKey(ignore: true)
  _$$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImplCopyWith<
          _$OnboardingAccountConfirmDuplicateCustomerRejectedExceptionImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWith<
    $Res> {
  factory _$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWith(
          _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl value,
          $Res Function(
                  _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl)
              then) =
      __$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWithImpl<
          $Res>;
  @useResult
  $Res call({String message, String email});
}

/// @nodoc
class __$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWithImpl<
        $Res>
    extends _$OnboardingAccountConfirmExceptionCopyWithImpl<$Res,
        _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl>
    implements
        _$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWith<
            $Res> {
  __$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWithImpl(
      _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl _value,
      $Res Function(
              _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl)
          _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? email = null,
  }) {
    return _then(_$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl
    implements _OnboardingAccountConfirmDuplicateCustomerActiveException {
  const _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl(
      {required this.message, required this.email});

  @override
  final String message;
  @override
  final String email;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, email);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWith<
          _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl>
      get copyWith =>
          __$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWithImpl<
                  _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() faceMismatch,
    required TResult Function() otherEFRError,
    required TResult Function(String message) systemError,
    required TResult Function(String message) duplicateCustomerRejected,
    required TResult Function(String message, String email)
        duplicateCustomerActive,
  }) {
    return duplicateCustomerActive(message, email);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? faceMismatch,
    TResult? Function()? otherEFRError,
    TResult? Function(String message)? systemError,
    TResult? Function(String message)? duplicateCustomerRejected,
    TResult? Function(String message, String email)? duplicateCustomerActive,
  }) {
    return duplicateCustomerActive?.call(message, email);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? faceMismatch,
    TResult Function()? otherEFRError,
    TResult Function(String message)? systemError,
    TResult Function(String message)? duplicateCustomerRejected,
    TResult Function(String message, String email)? duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (duplicateCustomerActive != null) {
      return duplicateCustomerActive(message, email);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _OnboardingAccountConfirmFaceMismatchException value)
        faceMismatch,
    required TResult Function(
            _OnboardingAccountConfirmOtherEFRErrorException value)
        otherEFRError,
    required TResult Function(
            _OnboardingAccountConfirmSystemErrorException value)
        systemError,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)
        duplicateCustomerRejected,
    required TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)
        duplicateCustomerActive,
  }) {
    return duplicateCustomerActive(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult? Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult? Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult? Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
  }) {
    return duplicateCustomerActive?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_OnboardingAccountConfirmFaceMismatchException value)?
        faceMismatch,
    TResult Function(_OnboardingAccountConfirmOtherEFRErrorException value)?
        otherEFRError,
    TResult Function(_OnboardingAccountConfirmSystemErrorException value)?
        systemError,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerRejectedException value)?
        duplicateCustomerRejected,
    TResult Function(
            _OnboardingAccountConfirmDuplicateCustomerActiveException value)?
        duplicateCustomerActive,
    required TResult orElse(),
  }) {
    if (duplicateCustomerActive != null) {
      return duplicateCustomerActive(this);
    }
    return orElse();
  }
}

abstract class _OnboardingAccountConfirmDuplicateCustomerActiveException
    implements OnboardingAccountConfirmException {
  const factory _OnboardingAccountConfirmDuplicateCustomerActiveException(
          {required final String message, required final String email}) =
      _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl;

  String get message;
  String get email;
  @JsonKey(ignore: true)
  _$$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImplCopyWith<
          _$OnboardingAccountConfirmDuplicateCustomerActiveExceptionImpl>
      get copyWith => throw _privateConstructorUsedError;
}
