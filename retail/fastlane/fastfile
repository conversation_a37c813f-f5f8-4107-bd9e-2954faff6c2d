#! /usr/bin/env ruby

$APP_PROPS = 'mobile/app.properties'
$APP_VERSION_KEY = 'app_version'
$APP_OBFUSCATION_MAP_PATH = 'symbols/obfuscation-map.json'
$APP_DEBUG_INFO_PATH = 'symbols'

default_platform(:ios)

# Android lanes

platform :android do
  desc "Deploys Retail Android Develop to App Center"
  lane :deploy_android_develop_app_center do |options|
    deploy_app_center(
      api_token: options[:api_token],
      owner_name: options[:owner_name],
      app_name: "retail-android-develop",
      destinations: options[:destinations],
      file: options[:path]
    )
  end

  desc "Deploys Retail Android Pre to App Center"
  lane :deploy_android_pre_app_center do |options|
    deploy_app_center(
      api_token: options[:api_token],
      owner_name: options[:owner_name],
      app_name: "retail-android-pre",
      destinations: options[:destinations],
      file: options[:path]
    )
  end

  desc "Deploys Retail Android Pre to App Distribution"
  lane :deploy_android_non_prod_app_distribution do |options|
	firebase_app_distribution(
	  app: options[:app],
	  android_artifact_type: "APK",
	  android_artifact_path: options[:path],
	  service_credentials_file: options[:serviceAccountFile],
	  groups: options[:groups],
	  release_notes: get_release_notes()
	)
  end

  desc "Builds and exports development Artifact"
  lane :build_android_dev do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
    build_android(
      target: "lib/main_dev.dart",
      app_version: app_version,
      build_number: build_number,
      flavor: "dev",
      datadog_client_token: options[:datadog_client_token],
      datadog_app_id: options[:datadog_app_id],
      type: options[:type]
    )
  end

  desc "Builds and exports pre-production Artifact"
  lane :build_android_pre do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
    build_android(
      target: "lib/main_pre.dart",
      app_version: app_version,
      build_number: build_number,
      flavor: "dev",
      datadog_client_token: options[:datadog_client_token],
      datadog_app_id: options[:datadog_app_id],
      type: options[:type]
    )
  end

  desc "Builds and exports production AAB. app_integrity_key must be provided for integrity check"
  lane :build_android_google_store do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
    app_integrity_key = options[:app_integrity_key]
    build_android(
      target: "lib/main_prod.dart",
      app_version: app_version,
      build_number: build_number,
      flavor: "prod",
      app_integrity_key: options[:app_integrity_key],
      datadog_client_token: options[:datadog_client_token],
      datadog_app_id: options[:datadog_app_id]
    )
  end

  desc "Builds and exports Android Artifact"
  private_lane :build_android do |options|
    target = options[:target]
    app_version = options[:app_version]
    build_number = options[:build_number]
    flavor = options[:flavor]
    app_integrity_key = options[:app_integrity_key]
    datadog_client_token = options[:datadog_client_token]
    datadog_app_id = options[:datadog_app_id]
    type = options[:type] || "appbundle"

    Dir.chdir("../mobile") do
      flutter_build_command = "flutter build #{type} " +
        "-t #{target} --release " +
        "--build-name=#{app_version} " +
        "--build-number=#{build_number} " +
        "--dart-define=DATADOG_CLIENT_TOKEN=#{datadog_client_token} " +
        "--dart-define=DATADOG_APP_ID=#{datadog_app_id} " +
        "--flavor #{flavor} " +
        "--obfuscate --split-debug-info=#{$APP_DEBUG_INFO_PATH} " +
        "--extra-gen-snapshot-options=--save-obfuscation-map=#{$APP_OBFUSCATION_MAP_PATH} "

      unless app_integrity_key.nil?
        flutter_build_command += "--dart-define=consumer_secret=#{app_integrity_key} "
      end

      sh(flutter_build_command)
    end
  end

  desc "Upload symbols to Datadog"
    lane :upload_symbols_to_datadog do |options|
      app_version = get_retail_app_version()
      build_number = number_of_commits()
        Dir.chdir("../mobile") do
            require 'json'

            ENV["DATADOG_API_KEY"] = options[:datadog_api_key]
            ENV["DATADOG_SITE"] = options[:datadog_site]
            dart_symbols_location = $APP_DEBUG_INFO_PATH
            is_publish_to_store = options[:is_publish_to_store]

            if is_publish_to_store
                flavor = 'production'
                android_mapping_location = "./build/app/outputs/mapping/prodRelease/mapping.txt"
                service_name = "retail-production"
            else
                flavor = 'preprod'
                android_mapping_location = "./build/app/outputs/mapping/devRelease/mapping.txt"
                service_name = "retail-preprod"
            end

            sh("npm install -g @datadog/datadog-ci")
            sh("datadog-ci flutter-symbols upload --flavor=#{flavor} --service-name #{service_name} --dart-symbols-location #{dart_symbols_location} --android-mapping-location #{android_mapping_location} --version #{app_version}+#{build_number}")
        end
    end
end

# iOS lanes

platform :ios do
  desc "Deploys Retail iOS Develop to App Center"
  lane :deploy_ios_develop_app_center do |options|
    deploy_app_center(
      api_token: options[:api_token],
      owner_name: options[:owner_name],
      app_name: "retail-ios-develop",
      destinations: options[:destinations],
      file: options[:path]
    )
  end

  desc "Deploys Retail iOS Pre to App Center"
  lane :deploy_ios_pre_app_center do |options|
    deploy_app_center(
      api_token: options[:api_token],
      owner_name: options[:owner_name],
      app_name: "retail-ios-pre",
      destinations: options[:destinations],
      file: options[:path]
    )
  end

  desc "Deploys Retail iOS Pre to Firebase App Distribution"
  lane :deploy_ios_pre_firebase_app_distribution do |options|
	firebase_app_distribution(
	  app: options[:appId],
	  ipa_path: options[:path],
	  service_credentials_file: options[:serviceAccountFile],
	  groups: options[:groups],
	  release_notes: get_release_notes()
	)
  end

  desc "Builds and exports development IPA"
  lane :build_ios_dev do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
    build_ios(
      target: "lib/main_dev.dart",
      app_version: app_version,
      build_number: build_number,
      flavor: "dev",
      export_options_plist: "ios/export_options_dev.plist",
      datadog_client_token: options[:datadog_client_token],
      datadog_app_id: options[:datadog_app_id]
    )
  end

  desc "Builds and exports pre-production IPA"
  lane :build_ios_pre do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
    build_ios(
      target: "lib/main_pre.dart",
      app_version: app_version,
      build_number: build_number,
      flavor: "dev",
      export_options_plist: "ios/export_options_dev.plist",
      datadog_client_token: options[:datadog_client_token],
      datadog_app_id: options[:datadog_app_id]
    )
  end

  desc "Builds and exports production IPA"
  lane :build_ios_app_store do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
    build_ios(
      target: "lib/main_prod.dart",
      app_version: app_version,
      build_number: build_number,
      flavor: "prod",
      export_options_plist: "ios/export_options_prod.plist",
      datadog_client_token: options[:datadog_client_token],
      datadog_app_id: options[:datadog_app_id]
    )
  end

  desc "Builds and exports IPA"
  private_lane :build_ios do |options|
    target = options[:target]
    app_version = options[:app_version]
    build_number = options[:build_number]
    flavor = options[:flavor]
    export_options_plist = options[:export_options_plist]
    datadog_client_token = options[:datadog_client_token]
    datadog_app_id = options[:datadog_app_id]

    Dir.chdir("../mobile") do
      flutter_build_command = "flutter build ipa " +
        "-t #{target} --release " +
        "--build-name=#{app_version} " +
        "--build-number=#{build_number} " +
        "--dart-define=DATADOG_CLIENT_TOKEN=#{datadog_client_token} " +
        "--dart-define=DATADOG_APP_ID=#{datadog_app_id} " +
        "--flavor #{flavor} " +
        "--export-options-plist=#{export_options_plist} " +
        "--obfuscate --split-debug-info=#{$APP_DEBUG_INFO_PATH} " +
        "--extra-gen-snapshot-options=--save-obfuscation-map=#{$APP_OBFUSCATION_MAP_PATH} " +
        "--verbose"

      sh(flutter_build_command)

      sh("zip -r build/ios/app.dSYM.zip build/ios/archive/Runner.xcarchive/dSYMs")
    end
  end

  desc "Upload symbols to Datadog"
  lane :upload_symbols_to_datadog do |options|
    app_version = get_retail_app_version()
    build_number = number_of_commits()
      Dir.chdir("../mobile") do
          require 'json'

          ENV["DATADOG_API_KEY"] = options[:datadog_api_key]
          ENV["DATADOG_SITE"] = options[:datadog_site]
          dart_symbols_location = $APP_DEBUG_INFO_PATH
          is_publish_to_store = options[:is_publish_to_store]

          if is_publish_to_store
              flavor = 'production'
              service_name = "retail-production"
          else
              flavor = 'preprod'
              service_name = "retail-preprod"
          end

          sh("npm install -g @datadog/datadog-ci")
          sh("datadog-ci flutter-symbols upload --flavor=#{flavor} --service-name #{service_name} --dart-symbols-location #{dart_symbols_location} --ios-dsyms-location ./build/ios/app.dSYM.zip --version #{app_version}+#{build_number}")
      end
  end
end

# Common lanes

def get_retail_app_version()
    return get_properties_value(
        key: $APP_VERSION_KEY,
        path: $APP_PROPS,
    )
end

desc "Deploys the build to App Center"
lane :deploy_app_center do |options|
    appcenter_upload(
         api_token: options[:api_token],
         owner_name: options[:owner_name],
         owner_type: "organization",
         app_name: options[:app_name],
         file: options[:file],
         destinations: options[:destinations],
         notify_testers: false,
         release_notes: get_release_notes()
     )
 end

desc "Gets changelog from commits for the release notes"
lane :get_release_notes do
  branch = branch = ENV['BUILD_SOURCEBRANCHNAME'] || git_branch.strip

  if branch == "develop" || branch.start_with?("release/")
    sh("cd ../.. && dart run tooling/code_tools/bin/reporters/get_release_changelog.dart --product Retail").strip
  else
    commit_hash = sh("git rev-parse HEAD").strip
    last_commit_msg = sh("git log -1 --pretty=%B").strip
    note = "WIP branch: #{branch}\nCommit Hash:(#{commit_hash})\nCommit Message: #{last_commit_msg}"
    note
  end
end

desc "Fetches Localise data"
lane :localise do
    sh("cd ../mobile && flutter pub run ui:fetch_loco --config=loco.yaml --skip=true")
end
