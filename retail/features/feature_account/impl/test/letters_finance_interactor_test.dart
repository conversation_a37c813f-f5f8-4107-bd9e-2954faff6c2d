import 'package:feature_account_impl/data/mappers/letters_accounts_mapper.dart';
import 'package:feature_account_impl/domain/letters_finance_interactor_impl.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_faq_api/faq_api.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

import 'account_mocks.dart';

void main() {
  late LettersFinanceInteractor lettersFinanceInteractor;
  late MockAccountInteractor mockAccountInteractor;
  late LettersAccountsMapper lettersAccountsMapper;
  late MockSavingSpaceInteractor mockSavingSpaceInteractor;

  setUp(() {
    mockAccountInteractor = MockAccountInteractor();
    mockSavingSpaceInteractor = MockSavingSpaceInteractor();
    final errorReporter = FakeErrorReporter();
    lettersAccountsMapper = LettersAccountsMapper(errorReporter: errorReporter);
    lettersFinanceInteractor = LettersFinanceInteractorImpl(
      accountInteractor: mockAccountInteractor,
      savingSpaceInteractor: mockSavingSpaceInteractor,
      lettersAccountsMapper: lettersAccountsMapper,
    );
  });

  test(
      'when fetchLettersAccounts are called then accountInteractor and '
      'savingSpaceInteractor are called', () async {
    when(
      () => mockAccountInteractor.getAccountDetails(
        areClosedAccountsIncluded: anyNamed('areClosedAccountsIncluded'),
      ),
    ).justAnswerAsync(
      [
        mockAccountDetails.copyWith(
          nickname: 'name1',
          availableBalance: Money.fromNumWithCurrency(10, Currency.aed),
        ),
        mockAccountDetails.copyWith(
          id: 'id2',
          nickname: 'name2',
          availableBalance: Money.fromNumWithCurrency(30, Currency.aed),
        ),
      ],
    );
    when(
      () => mockSavingSpaceInteractor.getSavingsAccounts(),
    ).justAnswerAsync(
      [
        SavingSpace(
          id: 'id3',
          name: 'name3',
          balance: Money.fromNumWithCurrency(40, Currency.aed),
          image: const SavingSpaceImage.local(
            name: 'name3',
            path: 'path3',
          ),
        ),
        FixedDeposit(
          id: 'id4',
          name: 'name4',
          balance: Money.fromNumWithCurrency(50, Currency.aed),
          image: const SavingSpaceImage.local(
            name: 'name3',
            path: 'path3',
          ),
          tenorInMonths: 3,
          maturityDate: DateTime(2024, 2, 3),
        ),
      ],
    );

    final result = await lettersFinanceInteractor.fetchLettersAccounts();

    verify(() => mockSavingSpaceInteractor.getSavingsAccounts()).calledOnce;
    verify(
      () => mockAccountInteractor.getAccountDetails(
        areClosedAccountsIncluded: true,
      ),
    ).calledOnce;

    expect(result, [
      LettersAccountSummary(
        accountDetails: const LettersAccountDetails(
          accountId: 'id',
          accountType: 'CURRENT_ACCOUNT',
        ),
        accountName: 'name1',
        availableBalance: Money.fromNumWithCurrency(10, Currency.aed),
        creationDate: DateTime(2022),
      ),
      LettersAccountSummary(
        accountDetails: const LettersAccountDetails(
          accountId: 'id2',
          accountType: 'CURRENT_ACCOUNT',
        ),
        accountName: 'name2',
        availableBalance: Money.fromNumWithCurrency(30, Currency.aed),
        creationDate: DateTime(2022),
      ),
      LettersAccountSummary(
        accountDetails: const LettersAccountDetails(
          accountId: 'id3',
          accountType: 'REGULAR_SAVINGS',
        ),
        accountName: 'name3',
        availableBalance: Money.fromNumWithCurrency(40, Currency.aed),
      ),
      LettersAccountSummary(
        accountDetails: const LettersAccountDetails(
          accountId: 'id4',
          accountType: 'FIXED_DEPOSIT',
        ),
        accountName: 'name4',
        availableBalance: Money.fromNumWithCurrency(50, Currency.aed),
      ),
    ]);
  });
}
