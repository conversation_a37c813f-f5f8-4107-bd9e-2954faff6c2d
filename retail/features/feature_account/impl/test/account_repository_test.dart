import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_account_impl/data/account_repository_impl.dart';
import 'package:feature_account_impl/data/account_service.dart';
import 'package:feature_account_impl/data/mappers/account_exception_mapper.dart';
import 'package:feature_account_impl/data/mappers/account_mapper.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_account_data_api/account_data_api.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

import 'account_mocks.dart';

void main() {
  late AccountService accountService;
  late AccountMapper accountMapper;
  late FeatureToggleProvider featureToggleProvider;
  late FamilyBankingFeatureController familyBankingController;
  late AccountRepository accountRepository;

  setUp(() {
    accountService = MockAccountService();
    accountMapper = MockAccountMapper();
    featureToggleProvider = MockFeatureToggleProvider();
    familyBankingController = _MockFamilyBankingController();
    accountRepository = AccountRepositoryImpl(
      service: accountService,
      mapper: accountMapper,
      exceptionMapper: const AccountExceptionMapper(),
      featureToggles: featureToggleProvider,
      familyBankingController: familyBankingController,
    );

    when(
      () => featureToggleProvider.get(
        AccountFeatureToggles.isLocalBalanceEnabled,
      ),
    ).thenReturn(false);

    when(
      () => featureToggleProvider.get(
        FamilyBankingFeatureToggles.isFamilyAccountCheckEnabled,
      ),
    ).thenReturn(true);
  });

  setUpAll(() {
    registerFallbackValue(CurrencyFake());
    registerFallbackValue(FxTransferResponseFake());
    registerFallbackValue(FxTransferRequestFake());
  });

  // Test data
  final mockDate = DateTime.now();
  final mockAccountDetailsResponseRetail = AccountDetailsResponseRetail(
    id: 'id',
    name: 'name',
    accountNickName: 'nickname',
    beneficiaryName: 'beneficiaryName',
    type: AccountDetailsResponseRetailType.fixedDeposit,
    encodedKey: 'encodedKey',
    state: AccountDetailsResponseRetailState.approved,
    currencyCode: AccountDetailsResponseRetailCurrencyCode.usd,
    creationDate: mockDate,
    bankName: 'bankName',
    bankAddress: 'bankAddress',
  );

  test(
    'Should get FxTransferResponse from service and map it to domain '
    'FXTransferDetails',
    () async {
      const mockFxTransferRequestBuyCurrency = FxTransferRequestBuyCurrency.usd;
      const mockFxTransferRequestSellCurrency =
          FxTransferRequestSellCurrency.aed;
      const mockFxTransferRequestDealtCurrency =
          FxTransferRequestDealtCurrency.aoa;

      const mockFxTransferRequest = FxTransferRequest(
        transactionKey: 'transactionKey',
        debitAccountId: 'debitAccountId',
        creditAccountId: 'creditAccountId',
        buyCurrency: FxTransferRequestBuyCurrency.usd,
        sellCurrency: FxTransferRequestSellCurrency.aed,
        dealtCurrency: FxTransferRequestDealtCurrency.aoa,
        dealtAmount: 1.00,
        contraAmount: 1.00,
      );
      const mockFxTransferResponse = FxTransferResponse();

      when(() => accountMapper.mapFXBuyTransferCurrency(any()))
          .thenReturn(mockFxTransferRequestBuyCurrency);
      when(() => accountMapper.mapFXSellTransferCurrency(any()))
          .thenReturn(mockFxTransferRequestSellCurrency);
      when(() => accountMapper.mapFXDealtTransferCurrency(any()))
          .thenReturn(mockFxTransferRequestDealtCurrency);
      when(() => accountMapper.mapFXTransferDetails(any()))
          .thenReturn(mockFXTransferDetails);
      when(() => accountService.performTransfer(any()))
          .justAnswerAsync(mockFxTransferResponse);

      final result = await accountRepository.performTransfer(
        'transactionKey',
        'debitAccountId',
        'creditAccountId',
        mockCurrency,
        mockCurrency,
        mockCurrency,
        1.00,
        1.00,
      );

      expect(result, mockFXTransferDetails);
      verify(() => accountService.performTransfer(mockFxTransferRequest));
      verify(() => accountMapper.mapFXTransferDetails(any()));
    },
  );

  final mockAccountsDetailsListResponseRetail =
      AccountsDetailsListResponseRetail(
    accounts: [
      mockAccountDetailsResponseRetail,
    ],
  );
  final mockAccountDetailsList = [mockAccountDetails];

  test(
    'Should get AccountsDetailsListResponseRetail '
    'from service and map it to list of AccountDetails',
    () async {
      // Arrange
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);
      when(() => accountMapper.mapAccountDetailsList(any()))
          .thenReturn(mockAccountDetailsList);

      // Act
      final result = await accountRepository.getAccountDetails(
        areClosedAccountsIncluded: false,
      );

      // Assert
      expect(result, mockAccountDetailsList);
      verify(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      );
      verify(() => accountMapper.mapAccountDetailsList(any()));
    },
  );

  test(
    'Should get AccountsDetailsListResponseRetail '
    'from service and map it to list of AccountDetails',
    () async {
      // Arrange
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: true,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);
      when(() => accountMapper.mapAccountDetailsList(any()))
          .thenReturn(mockAccountDetailsList);

      // Act
      final result = await accountRepository.getAccountDetails(
        areClosedAccountsIncluded: true,
      );

      // Assert
      expect(result, mockAccountDetailsList);
      verify(
        () => accountService.getAccounts(
          includeClosedAccounts: true,
          includeLocalBalance: false,
        ),
      );
      verify(() => accountMapper.mapAccountDetailsList(any()));
    },
  );

  test(
    'Should get AccountsDetailsListResponseRetail from service '
    'and return empty list if request returned null',
    () async {
      // Arrange
      const mockAccountsDetailsListResponseRetail =
          AccountsDetailsListResponseRetail();
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);

      // Act
      final result = await accountRepository.getAccountDetails(
        areClosedAccountsIncluded: false,
      );

      // Assert
      expect(result, isEmpty);
      verify(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      );
      verifyNever(() => accountMapper.mapAccountDetailsList(any()));
    },
  );

  group('observeAccounts', () {
    test('emits correct values when reloading', () async {
      // Arrange
      // Mock the service and mapper responses
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);
      when(() => accountMapper.mapAccountDetailsList(any()))
          .thenReturn(mockAccountDetailsList);

      // Act
      final stream = accountRepository.observeAccounts(reload: true);
      await expectLater(stream, emitsInOrder([mockAccountDetailsList]));

      // Assert
      // Verify that the service and mapper were called
      verify(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).calledOnce;
      verify(
        () => accountMapper.mapAccountDetailsList(any()),
      ).calledOnce;
    });

    test('emits correct values when not reloading', () async {
      // Mock the service and mapper responses
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: true,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);
      when(() => accountMapper.mapAccountDetailsList(any()))
          .thenReturn(mockAccountDetailsList);

      // Testing the scenario reload = false
      final stream = accountRepository.observeAccounts();

      // Do not emit anything as reload  = false
      await expectLater(stream, emitsInOrder([]));

      // Verify that the service wasn't called again for observeAccounts
      // with reload = false
      verifyNever(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      );
      verifyNever(() => accountMapper.mapAccountDetailsList(any()));
    });
  });

  group('observeAccount', () {
    test('emits correct account details when account is found', () async {
      // Mock the service and mapper responses
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);
      when(() => accountMapper.mapAccountDetailsList(any()))
          .thenReturn(mockAccountDetailsList);

      final stream = accountRepository
          .observeAccount(mockAccountDetailsList.first.id, reload: true);

      await expectLater(
        stream,
        emitsInOrder([mockAccountDetailsList.first]),
      );

      // Verify that the service and mapper were called
      verify(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).calledOnce;
      verify(() => accountMapper.mapAccountDetailsList(any())).calledOnce;
    });

    test('emits nothing when account is not found', () async {
      // Mock the service and mapper responses
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(const AccountsDetailsListResponseRetail());
      when(() => accountMapper.mapAccountDetailsList(any())).thenReturn([]);

      final stream =
          accountRepository.observeAccount(randomString(), reload: true);

      // Expect no emissions since the account isn't found
      await expectLater(stream, emitsDataInOrder([]));
    });
  });

  test(
    'refreshAccounts calls getAccountDetails',
    () async {
      when(
        () => featureToggleProvider.get(
          AccountFeatureToggles.isLocalBalanceEnabled,
        ),
      ).thenReturn(true);
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: true,
        ),
      ).justAnswerAsync(mockAccountsDetailsListResponseRetail);
      when(() => accountMapper.mapAccountDetailsList(any()))
          .thenReturn(mockAccountDetailsList);

      await accountRepository.refreshAccounts();

      verify(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: true,
        ),
      );
      verify(() => accountMapper.mapAccountDetailsList(any()));
    },
  );

  test(
    'Create Currency Account',
    () async {
      const currency = 'AED';
      const name = 'name';
      const request = CreateAccountRequestRetail(
        name: name,
        currencyCode: CreateAccountRequestRetailCurrencyCode.aed,
      );
      when(
        () => accountMapper.mapCreateAccountRequest(
          currency: currency,
          name: name,
        ),
      ).thenReturn(request);
      when(() => accountService.createCurrencyAccount(request))
          .justCompleteAsync();

      await accountRepository.createCurrencyAccount(
        currency: currency,
        name: name,
      );

      verify(
        () => accountMapper.mapCreateAccountRequest(
          currency: currency,
          name: name,
        ),
      ).calledOnce;
      verify(() => accountService.createCurrencyAccount(request)).calledOnce;
    },
  );

  group('Available currencies >', () {
    final expectedCurrency = AvailableCurrency(
      currency: Currency.aed,
      available: true,
      maxAccountCount: 1,
    );
    const currency = AccountCurrency(
      currency: AccountCurrencyCurrency.aed,
      available: true,
      maxAccountCount: 1,
    );

    test('get available currencies (v1)', () async {
      // Arrange
      final currencies = [currency];
      when(accountService.getAccountCurrenciesV1).justAnswerAsync(currencies);
      when(
        () => accountMapper.mapAvailableCurrency(currency),
      ).thenReturn(expectedCurrency);
      when(
        () => featureToggleProvider.get(
          SavingSpacesFeatureToggles.isMultiCurrencyFixedDepositEnabled,
        ),
      ).thenReturn(false);

      // Act
      await accountRepository.getAvailableCurrencies();

      // Assert
      verify(accountService.getAccountCurrenciesV1).calledOnce;
      verify(() => accountMapper.mapAvailableCurrency(currency)).calledOnce;
    });

    test('get available currencies (v2)', () async {
      // Arrange
      const currencies = Currencies(
        currentAccount: [currency],
        fixedDeposit: [currency],
        regularSavings: [currency],
      );
      final expected = AvailableCurrencies(
        currentAccount: [expectedCurrency],
        savingSpaces: [expectedCurrency],
        fixedSavingSpaces: [expectedCurrency],
        familyFixedSavingSpaces: [],
      );

      when(accountService.getAccountCurrenciesV2).justAnswerAsync(currencies);
      when(
        () => accountMapper.mapAvailableCurrencies(currencies),
      ).thenReturn(expected);
      when(
        () => featureToggleProvider.get(
          SavingSpacesFeatureToggles.isMultiCurrencyFixedDepositEnabled,
        ),
      ).thenReturn(true);

      // Act
      await accountRepository.getAvailableCurrencies();

      // Assert
      verify(accountService.getAccountCurrenciesV2).calledOnce;
      verify(() => accountMapper.mapAvailableCurrencies(currencies)).calledOnce;
    });
  });

  group('closeMultiCurrencyAccount', () {
    test('should call closeMultiCurrencyAccount on the repository', () async {
      // Arrange
      const accountId = 'id';
      when(() => accountService.closeMultiCurrencyAccount(accountId))
          .thenAnswer((_) async {});

      // Act
      await accountRepository.closeMultiCurrencyAccount(accountId);

      // Assert
      verify(() => accountService.closeMultiCurrencyAccount(accountId))
          .called(1);
    });
  });

  group('validateMultiCurrencyAccount', () {
    test('should return MultiCurrencyAccountClosureValidation', () async {
      // Arrange
      const accountId = 'id';
      const dto = AccountClosureValidationResponse(
        userId: '',
        domainId: 'domain123',
        closeable: true,
        currentStatus: AccountClosureValidationResponseCurrentStatus.active,
        reasons: [],
      );
      final expectedResult = MultiCurrencyAccountClosureValidation(
        status: MultiCurrencyAccountClosureValidationStatus(
          domainId: 'domain123',
          closeable: true,
          currentStatus: AccountState.active,
          reasons: [],
          userId: '',
        ),
        flow: MultiCurrencyAccountClosureFlow.balanceZero,
      );
      when(() => accountService.validateMultiCurrencyAccountClosure(accountId))
          .thenAnswer((_) async => dto);

      when(() => accountMapper.mapValidateMultiCurrencyAccount(dto))
          .thenReturn(expectedResult);

      // Act
      final result = await accountRepository
          .validateMultiCurrencyAccountClosure(accountId);

      // Assert
      expect(result, expectedResult);
      verify(
        () => accountService.validateMultiCurrencyAccountClosure(accountId),
      ).called(1);
    });
  });

  group('Toggling family banking >', () {
    void stubAccountsResponse({
      required bool withFamilyAccount,
      required bool withPocket,
      AccountState state = AccountState.active,
    }) {
      when(
        () => accountService.getAccounts(
          includeClosedAccounts: false,
          includeLocalBalance: false,
        ),
      ).justAnswerAsync(
        AccountsDetailsListResponseRetail(
          accounts: [
            mockAccountDetailsResponseRetail,
            if (withFamilyAccount)
              mockAccountDetailsResponseRetail.copyWith(
                type: AccountDetailsResponseRetailType.jointCurrentAccount,
              ),
            if (withPocket)
              mockAccountDetailsResponseRetail.copyWith(
                type: AccountDetailsResponseRetailType.pocket,
              ),
          ],
        ),
      );

      when(
        () => accountMapper.mapAccountDetailsList(any()),
      ).thenReturn(
        [
          mockAccountDetails,
          if (withFamilyAccount) ...[
            mockAccountDetails.copyWith(
              type: AccountType.jointCurrentAccount,
              state: AccountState.withdrawn,
            ),
            mockAccountDetails.copyWith(
              type: AccountType.jointCurrentAccount,
              state: state,
            ),
            mockAccountDetails.copyWith(
              type: AccountType.jointCurrentAccount,
              state: AccountState.closed,
            ),
          ],
          if (withPocket) ...[
            mockAccountDetails.copyWith(
              type: AccountType.pocketAccount,
              state: AccountState.withdrawn,
            ),
            mockAccountDetails.copyWith(
              type: AccountType.pocketAccount,
              state: state,
            ),
            mockAccountDetails.copyWith(
              type: AccountType.pocketAccount,
              state: AccountState.closed,
            ),
          ],
        ],
      );
    }

    final testCases = [
      (
        description:
            'enables family banking if account list has active family accounts',
        withFamilyAccount: true,
        withPocket: false,
      ),
      (
        description:
            'enables family banking if account list has active pockets',
        withFamilyAccount: false,
        withPocket: true,
      ),
      (
        description: 'enables family banking if account list has both active '
            'family accounts and pockets',
        withFamilyAccount: true,
        withPocket: true,
      ),
    ];

    for (final it in testCases) {
      test(
        it.description,
        () async {
          // Arrange
          stubAccountsResponse(
            withFamilyAccount: it.withFamilyAccount,
            withPocket: it.withPocket,
          );

          // Act
          await accountRepository.getAccountDetails(
            areClosedAccountsIncluded: false,
          );

          // Assert
          verify(familyBankingController.enable).calledOnce;
        },
      );
    }

    final activeAccountCases = [
      AccountState.approved,
      AccountState.active,
      AccountState.pendingApproval,
      AccountState.matured,
      AccountState.activeInArrears,
      AccountState.locked,
      AccountState.dormant,
    ];

    for (final state in activeAccountCases) {
      test(
        'enables family banking if any account is in active state: '
        '${state.name}',
        () async {
          // Arrange
          stubAccountsResponse(
            withFamilyAccount: true,
            withPocket: true,
            state: state,
          );

          // Act
          await accountRepository.getAccountDetails(
            areClosedAccountsIncluded: false,
          );

          // Assert
          verify(familyBankingController.enable).calledOnce;
        },
      );
    }

    final inactiveAccountCases = [
      AccountState.withdrawn,
      AccountState.closed,
      AccountState.closedRejected,
      AccountState.closedWrittenOff,
      AccountState.unknown,
    ];

    for (final state in inactiveAccountCases) {
      test(
        'does not enable family banking if no account is in active state: '
        '${state.name}',
        () async {
          // Arrange
          stubAccountsResponse(
            withFamilyAccount: true,
            withPocket: true,
            state: state,
          );

          // Act
          await accountRepository.getAccountDetails(
            areClosedAccountsIncluded: false,
          );

          // Assert
          verify(familyBankingController.reset).calledOnce;
        },
      );
    }

    test(
      'resets family banking if the account list has no family account '
      'or pocket',
      () async {
        // Arrange
        stubAccountsResponse(withFamilyAccount: false, withPocket: false);

        // Act
        await accountRepository.getAccountDetails(
          areClosedAccountsIncluded: false,
        );

        // Assert
        verify(familyBankingController.reset).calledOnce;
      },
    );

    test(
      'does not interact with the controller if FF is disabled',
      () async {
        // Arrange
        stubAccountsResponse(withFamilyAccount: true, withPocket: true);
        when(
          () => featureToggleProvider.get(
            FamilyBankingFeatureToggles.isFamilyAccountCheckEnabled,
          ),
        ).thenReturn(false);

        // Act
        await accountRepository.getAccountDetails(
          areClosedAccountsIncluded: false,
        );

        // Assert
        verifyZeroInteractions(familyBankingController);
      },
    );
  });
}

class _MockFamilyBankingController extends Mock
    implements FamilyBankingFeatureController {}
