import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_account_ui/src/navigation/bottom_sheets/shared_account_closure_bottom_sheet_config.dart';
import 'package:wio_feature_contact_support_api/contact_support_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

/// Handles the closure flow for different types of accounts.
/// This class encapsulates the validation
///  and navigation logic for account closure.
class AccountClosureHandler {
  final AccountInteractor _accountInteractor;
  final FamilyBankingAccountInteractor _familyAccountInteractor;
  final NavigationProvider _navigationProvider;
  final Logger _logger;
  final LoadingProvider _loadingProvider;
  final CommonErrorHandler _commonErrorHandler;
  final FeatureToggleProvider _featureToggleProvider;
  final FamilyBankingFlow _familyBankingFlow;

  AccountClosureHandler({
    required AccountInteractor accountInteractor,
    required FamilyBankingAccountInteractor familyAccountInteractor,
    required NavigationProvider navigationProvider,
    required LoadingProvider loadingProvider,
    required Logger logger,
    required CommonErrorHandler commonErrorHandler,
    required FeatureToggleProvider featureToggleProvider,
    required FamilyBankingFlow familyBankingFlow,
  })  : _accountInteractor = accountInteractor,
        _familyAccountInteractor = familyAccountInteractor,
        _navigationProvider = navigationProvider,
        _loadingProvider = loadingProvider,
        _logger = logger,
        _commonErrorHandler = commonErrorHandler,
        _featureToggleProvider = featureToggleProvider,
        _familyBankingFlow = familyBankingFlow;

  /// Initiates the closure flow for the given account.
  /// The flow varies based on the account type
  Future<void> call(
    AccountDetails account,
    List<AccountDetails> allAccounts,
  ) async {
    _logger.debug('Starting account closure flow for ${account.id}');
    final isSharedAccountClosureEnabled = _featureToggleProvider.get<bool>(
      FamilyBankingFeatureToggles.isAccountClosureEnabled,
    );

    return switch (account.type) {
      AccountType.currentAccount =>
        _handleCurrentAccountClosure(account, allAccounts),
      AccountType.pocketAccount ||
      AccountType.jointCurrentAccount when isSharedAccountClosureEnabled =>
        _handleSharedAccountClosure(account),
      _ => throw UnsupportedError(
          'Account type ${account.type} closure not supported',
        ),
    };
  }

  Future<void> _handleCurrentAccountClosure(
    AccountDetails account,
    List<AccountDetails> allAccounts,
  ) async {
    try {
      _loadingProvider.loading(true);
      final validation = await _accountInteractor
          .validateMultiCurrencyAccountClosure(account.id);

      _loadingProvider.loading(false);

      await switch (validation.flow) {
        MultiCurrencyAccountClosureFlow.balanceZero =>
          _showCloseConfirmation(account),
        MultiCurrencyAccountClosureFlow.balanceNotZero =>
          _showMoveMoneyBottomSheet(account, allAccounts),
        MultiCurrencyAccountClosureFlow.balanceMinus =>
          _showMinBalanceBottomSheet(),
        MultiCurrencyAccountClosureFlow.closureBlocked =>
          _showBlockedBottomSheet(),
        MultiCurrencyAccountClosureFlow.linkedAccount =>
          _showLinkedAccountBottomSheet(),
        MultiCurrencyAccountClosureFlow.unknown =>
          throw const FormatException('Unknown Closure Flow'),
      };
    } on Exception catch (e) {
      _logger.error('Failed to validate current account closure: $e', error: e);
      _commonErrorHandler.handleError(e);
    } finally {
      _loadingProvider.loading(false);
    }
  }

  Future<void> _handleSharedAccountClosure(AccountDetails account) async {
    try {
      _loadingProvider.loading(true);
      final validation = await _familyAccountInteractor
          .validateSharedAccountClosure(account.id);

      _loadingProvider.loading(false);

      await switch (validation) {
        SharedAccountClosable() => _showSharedAccountCloseConfirmation(
            account,
          ),
        SharedAccountNotClosable() => _showSharedAccountNotCloseableBottomSheet(
            validation,
            account,
          ),
      };
    } on Exception catch (e) {
      _loadingProvider.loading(false);
      _logger.error('Failed to validate joint account closure: $e', error: e);
      _commonErrorHandler.handleError(e);
    } finally {
      _loadingProvider.loading(false);
    }
  }

  Future<void> _showCloseConfirmation(AccountDetails account) {
    return _navigationProvider.showBottomSheet(
      MultiCurrencyAccountClosureBottomSheetConfig.close(
        accountId: account.id,
        accountName: account.nickname ?? account.name,
      ),
    );
  }

  Future<void> _showMoveMoneyBottomSheet(
    AccountDetails account,
    List<AccountDetails> allAccounts,
  ) {
    return _navigationProvider.showBottomSheet(
      MultiCurrencyAccountClosureBottomSheetConfig.fx(
        currentAccount: account,
        accounts: allAccounts,
      ),
    );
  }

  Future<void> _showMinBalanceBottomSheet() {
    return _navigationProvider.showBottomSheet(
      const MultiCurrencyAccountClosureBottomSheetConfig.negativeBalance(),
    );
  }

  Future<void> _showBlockedBottomSheet() {
    return _navigationProvider.showBottomSheet(
      const MultiCurrencyAccountClosureBottomSheetConfig.blocked(),
    );
  }

  Future<void> _showLinkedAccountBottomSheet() {
    return _navigationProvider.showBottomSheet(
      const MultiCurrencyAccountClosureBottomSheetConfig.linked(),
    );
  }

  Future<void> _showSharedAccountCloseConfirmation(AccountDetails account) {
    return _navigationProvider.showBottomSheet<void>(
      SharedAccountClosureBottomSheetConfig.confirmation(
        account: account,
      ),
    );
  }

  Future<void> _showSharedAccountNotCloseableBottomSheet(
    SharedAccountNotClosable validation,
    AccountDetails account,
  ) {
    final firstReason = validation.reasons.firstOrNull;
    final firstReasonCode = firstReason?.code;

    if (firstReasonCode == null) {
      _logger.error('No reason code found for shared account closure');
    }

    return switch (firstReasonCode) {
      // Account has negative balance
      SharedAccountClosureReasonCode.accountHasNegativeBalance =>
        _showSharedAccountBalanceNotZeroBottomSheet(false, account),
      // Account has positive balance
      SharedAccountClosureReasonCode.accountHasPositiveBalance =>
        _showSharedAccountBalanceNotZeroBottomSheet(true, account),
      // Current user needs to approve the closure request
      SharedAccountClosureReasonCode.closureRequestExistsForYou =>
        _showSharedAccountClosureRequestExistForYouBottomSheet(
          validation.requestId,
          account,
        ),
      // Co-owner needs to approve the closure request
      SharedAccountClosureReasonCode.closureRequestExistsForCoOwner =>
        _showSharedAccountClosureRequestExistForCoOwnerBottomSheet(account),
      // Family fixed deposit or regular savings account not closed
      SharedAccountClosureReasonCode.familyFixedDepositNotClosed ||
      SharedAccountClosureReasonCode.regularSavingsAccountNotClosed =>
        _showSharedSavingsSpaceNotClosedBottomSheet(),
      SharedAccountClosureReasonCode.sharedPocketNotClosed =>
        _showSharedAccountClosureExistingPocketBottomSheet(),
      // Account Closure is blocked for some reason
      _ => _showSharedAccountClosureBlockedBottomSheet(account),
    };
  }

  Future<void> _showSharedAccountBalanceNotZeroBottomSheet(
    bool hasPositiveBalance,
    AccountDetails account,
  ) async {
    final result = await _navigationProvider
        .showBottomSheet<SharedAccountClosureBottomSheetResult?>(
      SharedAccountClosureBottomSheetConfig.nonZeroBalance(
        hasPositiveBalance: hasPositiveBalance,
        account: account,
      ),
    );

    await _handleSharedAccountBalanceResult(result, account);
  }

  Future<void> _handleSharedAccountBalanceResult(
    SharedAccountClosureBottomSheetResult? result,
    AccountDetails account,
  ) async {
    await switch (result) {
      SharedAccountClosureBottomSheetResult.goToMoveMoney =>
        _navigationProvider.push(
          MoveMoneyNavigationConfig(
            accountId: account.id,
            transferMode: TransferMoneyMode.withdrawal,
            screenOrigin: MoveMoneyScreenOrigin.accountsScreen,
          ),
        ),
      SharedAccountClosureBottomSheetResult.goToAddMoney =>
        _navigationProvider.push(
          MoveMoneyNavigationConfig(
            accountId: account.id,
            transferMode: TransferMoneyMode.deposit,
            screenOrigin: MoveMoneyScreenOrigin.accountsScreen,
          ),
        ),
      _ => null,
    };
  }

  Future<void> _showSharedAccountClosureRequestExistForYouBottomSheet(
    String? requestId,
    AccountDetails account,
  ) async {
    final result = await _navigationProvider
        .showBottomSheet<SharedAccountClosureBottomSheetResult?>(
      SharedAccountClosureBottomSheetConfig.requestAlreadyReceived(
        account: account,
      ),
    );

    return switch (result) {
      SharedAccountClosureBottomSheetResult.goToReviewRequest
          when requestId != null =>
        _goToReviewRequest(requestId),
      _ => null,
    };
  }

  Future<void> _goToReviewRequest(
    String requestId,
  ) async {
    return _familyBankingFlow.openAccountClosureRequest(requestId);
  }

  Future<void> _showSharedAccountClosureRequestExistForCoOwnerBottomSheet(
    AccountDetails account,
  ) {
    return _navigationProvider.showBottomSheet<void>(
      SharedAccountClosureBottomSheetConfig.requestAlreadyInitiated(
        account: account,
      ),
    );
  }

  Future<void> _showSharedSavingsSpaceNotClosedBottomSheet() async {
    final result = await _navigationProvider
        .showBottomSheet<SharedAccountClosureBottomSheetResult?>(
      const SharedAccountClosureBottomSheetConfig.existingFamilyFixedDeposit(),
    );

    return switch (result) {
      SharedAccountClosureBottomSheetResult.goToSavingSpaces =>
        _familyBankingFlow.openFamilyHub(
          initialTab: FamilyTab.savingSpaces,
        ),
      _ => null,
    };
  }

  Future<void> _showSharedAccountClosureExistingPocketBottomSheet() async {
    return _navigationProvider.showBottomSheet<void>(
      const SharedAccountClosureBottomSheetConfig.existingPocket(),
    );
  }

  Future<void> _showSharedAccountClosureBlockedBottomSheet(
    AccountDetails account,
  ) async {
    final result = await _navigationProvider
        .showBottomSheet<SharedAccountClosureBottomSheetResult?>(
      SharedAccountClosureBottomSheetConfig.blocked(
        account: account,
      ),
    );

    return switch (result) {
      SharedAccountClosureBottomSheetResult.goToContactUs =>
        _navigationProvider.showBottomSheet(
          const ContactSupportBottomSheetConfig(),
        ),
      _ => null,
    };
  }
}
