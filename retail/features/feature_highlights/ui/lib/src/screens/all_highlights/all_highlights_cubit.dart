import 'dart:async';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_highlights_api/highlights_api.dart';
import 'package:wio_feature_highlights_ui/src/common/models/highlight_type.dart';
import 'package:wio_feature_highlights_ui/src/navigation/highlight_details_screen_navigation_config.dart';
import 'package:wio_feature_highlights_ui/src/utility/month_helper.dart';

part 'all_highlights_cubit.freezed.dart';
part 'all_highlights_state.dart';

class AllHighlightsCubit extends BaseCubit<AllHighlightsState> {
  static const _pageSize = 20;

  final AllHighlightsParams _params;
  final ExhaustStreamExecutor _paginationExecutor;
  final HighlightsInteractor _highlightsInteractor;
  final Logger _logger;
  final NavigationProvider _navigation;
  final HighlightType _highlightType;

  AllHighlightsCubit({
    required AllHighlightsParams params,
    required NavigationProvider navigation,
    required ExhaustStreamExecutor exhaustStreamExecutor,
    required HighlightType highlightType,
    required HighlightsInteractor highlightsInteractor,
    required Logger logger,
  })  : _params = params,
        _highlightType = highlightType,
        _highlightsInteractor = highlightsInteractor,
        _logger = logger,
        _navigation = navigation,
        _paginationExecutor = exhaustStreamExecutor,
        super(const AllHighlightsState.loading()) {
    _logger.debug('Created AllHighlightsCubit#$_highlightType');
  }

  void init() {
    final selectedDate = state.mapOrNull(idle: (s) => s.currentDateTime);
    emit(const AllHighlightsState.loading());
    _loadData(endDateTimeForce: selectedDate ?? _params.selectedDate);
  }

  void onPaginate() {
    state.mapOrNull(
      idle: (s) {
        if (s.hasReachMax) return;

        _logger.debug('Paginating to next page');
        _paginationExecutor.run(_onPaginate().toStream);
      },
    );
  }

  void onHighlightPressed(HighlightModel highlight) {
    _navigation.push(
      HighlightDetailsScreenNavigationConfig(
        type: _params.pageType,
        highlight: highlight,
        monthDateTime:
            state.mapOrNull<DateTime?>(idle: (s) => s.currentDateTime),
      ),
    );
  }

  Future<void> applyFilters({
    required DateTime? dateTime,
  }) async {
    emit(const AllHighlightsState.loading());
    await runZoned(
      () => _loadData(endDateTimeForce: dateTime),
      zoneValues: {#dateTime: dateTime},
    );
  }

  Future<void> _loadData({
    DateTime? endDateTimeForce,
  }) async {
    final itemsCount =
        state.mapOrNull(idle: (i) => i.highlights.itemsCount) ?? 0;
    final endDateTime = _resolveEndDateTime(endDateTimeForce);
    final isPaginating = itemsCount > 0;

    try {
      final result = await _loadHighlights(
        startDateTime: MonthHelper.getFirstDayOf(endDateTime),
        endDateTime: endDateTime,
        pageIndex: (itemsCount / _pageSize).ceil(),
        pageSize: _pageSize,
      );

      if (isPaginating) {
        state.mapOrNull(
          idle: (s) => emit(
            s.copyWith(
              highlights: s.highlights.add(result),
              hasReachMax: result.itemsCount < _pageSize,
            ),
          ),
        );
      } else {
        emit(
          AllHighlightsState.idle(
            currentDateTime: endDateTime,
            highlights: result,
            hasReachMax: result.itemsCount < _pageSize,
            topItem: await _resolveTopItem(result, endDateTime),
          ),
        );
      }
    } on Object catch (ex) {
      _logger.debug('Fetch error: $ex');

      if (isPaginating) {
        rethrow;
      } else {
        emit(const AllHighlightsState.failed());
      }
    }
  }

  DateTime _resolveEndDateTime(DateTime? initialDateTime) {
    final forceToUseDateTime = Zone.current[#dateTime] as DateTime?;
    if (forceToUseDateTime != null) return forceToUseDateTime;

    final dateTime =
        state.mapOrNull<DateTime?>(idle: (i) => i.currentDateTime) ??
            initialDateTime ??
            DateTime.now();

    return MonthHelper.getLastDayOf(dateTime);
  }

  Future<Highlights> _loadHighlights({
    required DateTime startDateTime,
    required DateTime endDateTime,
    required int pageIndex,
    required int pageSize,
  }) async {
    switch (_highlightType) {
      case HighlightType.category:
        return Highlights.categories(
          await _highlightsInteractor.getCategories(
            type: _params.pageType,
            startDateTime: startDateTime,
            endDateTime: endDateTime,
            pageIndex: pageIndex,
            pageSize: pageSize,
          ),
        );
      case HighlightType.country:
        return Highlights.countries(
          await _highlightsInteractor.getCountries(
            type: _params.pageType,
            startDateTime: startDateTime,
            endDateTime: endDateTime,
            pageIndex: pageIndex,
            pageSize: pageSize,
          ),
        );
    }
  }

  Future<AllHighlightTopItem?> _resolveTopItem(
    Highlights result,
    DateTime currentDateTime,
  ) async {
    try {
      final currentMonthTop = _resolveTop(result.items);
      final prevMonthResult =
          await _resolvePrevMonth(currentMonthTop, currentDateTime);

      return AllHighlightTopItem(
        percentage: _calculateGrowth(
          currentMonthTop.amount,
          prevMonthResult.amount,
        ),
        highlight: currentMonthTop,
      );
    } on Object catch (ex) {
      _logger.debug('Could not resolve top item: $ex');

      return null;
    }
  }

  Future<HighlightModel> _resolvePrevMonth(
    HighlightModel currentMonthResult,
    DateTime currentDateTime,
  ) async {
    final startDate = MonthHelper.getStartOfLastMonth(currentDateTime);

    return currentMonthResult.map(
      category: (it) async => _resolveTop(
        await _highlightsInteractor.getCategories(
          type: _params.pageType,
          startDateTime: startDate,
          endDateTime: MonthHelper.getLastDayOf(startDate),
          pageIndex: 0,
          pageSize: 1,
          categories: {it.transactionCategory},
        ),
      ),
      country: (it) async => _resolveTop(
        await _highlightsInteractor.getCountries(
          type: _params.pageType,
          startDateTime: MonthHelper.getStartOfLastMonth(currentDateTime),
          endDateTime: MonthHelper.getLastDayOf(startDate),
          pageIndex: 0,
          pageSize: 1,
          countryCodes: {it.countryCode},
        ),
      ),
    );
  }

  HighlightModel _resolveTop(List<HighlightModel> items) {
    final nonIgnoredItem = items
        .where((i) => !_shouldIgnoreInTopCalculation(i))
        .cast<HighlightModel>();

    if (nonIgnoredItem.isEmpty) throw Exception();
    if (nonIgnoredItem.length == 1) return nonIgnoredItem.first;

    return items
        .where((i) => !_shouldIgnoreInTopCalculation(i))
        .cast<HighlightModel>()
        .reduce((a, b) => a.amount > b.amount ? a : b);
  }

  bool _shouldIgnoreInTopCalculation(HighlightModel item) {
    if (item is CategoryHighlight) {
      return item.transactionCategory == TransactionCategory.unknown;
    } else if (item is CountryHighlight) {
      return item.countryCode.isEmpty;
    }

    return false;
  }

  double _calculateGrowth(Money money1, Money money2) {
    final (money2double, money1double) =
        (double.parse(money2.stringAmount), double.parse(money1.stringAmount));

    if (money2double > money1double) {
      return money2double / money1double * 100;
    } else {
      return -money1double / money2double * 100;
    }
  }

  Future<void> _onPaginate() async {
    await state.mapOrNull<Future<void>>(
      idle: (s) async {
        try {
          await _loadData();
        } on Object {
          emit(const AllHighlightsState.couldNotPaginate());
          emit(s);
        }
      },
    );
  }

  @override
  String toString() => 'AllHighlightsCubit';
}
