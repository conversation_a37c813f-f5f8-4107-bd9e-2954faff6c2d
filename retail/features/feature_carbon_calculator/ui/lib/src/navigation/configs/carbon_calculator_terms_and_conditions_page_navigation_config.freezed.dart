// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'carbon_calculator_terms_and_conditions_page_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$CarbonCalculatorTermsAndConditionsPageNavigationConfig {}

/// @nodoc
abstract class $CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWith<
    $Res> {
  factory $CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWith(
          CarbonCalculatorTermsAndConditionsPageNavigationConfig value,
          $Res Function(CarbonCalculatorTermsAndConditionsPageNavigationConfig)
              then) =
      _$CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl<$Res,
          CarbonCalculatorTermsAndConditionsPageNavigationConfig>;
}

/// @nodoc
class _$CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl<$Res,
        $Val extends CarbonCalculatorTermsAndConditionsPageNavigationConfig>
    implements
        $CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWith<$Res> {
  _$CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$_CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWith<
    $Res> {
  factory _$$_CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWith(
          _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig value,
          $Res Function(
                  _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig)
              then) =
      __$$_CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl<
          $Res>;
}

/// @nodoc
class __$$_CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl<
        $Res>
    extends _$CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl<
        $Res, _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig>
    implements
        _$$_CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWith<
            $Res> {
  __$$_CarbonCalculatorTermsAndConditionsPageNavigationConfigCopyWithImpl(
      _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig _value,
      $Res Function(_$_CarbonCalculatorTermsAndConditionsPageNavigationConfig)
          _then)
      : super(_value, _then);
}

/// @nodoc

class _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig
    extends _CarbonCalculatorTermsAndConditionsPageNavigationConfig {
  const _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig() : super._();

  @override
  String toString() {
    return 'CarbonCalculatorTermsAndConditionsPageNavigationConfig()';
  }
}

abstract class _CarbonCalculatorTermsAndConditionsPageNavigationConfig
    extends CarbonCalculatorTermsAndConditionsPageNavigationConfig {
  const factory _CarbonCalculatorTermsAndConditionsPageNavigationConfig() =
      _$_CarbonCalculatorTermsAndConditionsPageNavigationConfig;
  const _CarbonCalculatorTermsAndConditionsPageNavigationConfig._() : super._();
}
