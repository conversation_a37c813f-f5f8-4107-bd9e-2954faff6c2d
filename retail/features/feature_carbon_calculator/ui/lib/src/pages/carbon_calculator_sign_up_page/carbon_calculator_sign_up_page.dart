import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_carbon_calculator_ui/l10n/carbon_calculator.g.dart';
import 'package:wio_feature_carbon_calculator_ui/src/pages/carbon_calculator_sign_up_page/carbon_calculator_sign_up_cubit.dart';
import 'package:wio_feature_carbon_calculator_ui/src/pages/carbon_calculator_sign_up_page/carbon_calculator_sign_up_state.dart';

class CarbonCalculatorSignUpPage
    extends BasePage<CarbonCalculatorSignUpState, CarbonCalculatorSignUpCubit> {
  const CarbonCalculatorSignUpPage({super.key});

  @override
  Widget buildPage(
    BuildContext context,
    CarbonCalculatorSignUpCubit bloc,
    CarbonCalculatorSignUpState state,
  ) {
    return const _Body();
  }

  @override
  CarbonCalculatorSignUpCubit createBloc() =>
      DependencyProvider.get<CarbonCalculatorSignUpCubit>();
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final l10n = CarbonCalculatorLocalizations.of(context);
    final cubit = context.read<CarbonCalculatorSignUpCubit>();

    return Scaffold(
      appBar: const TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
        ),
      ),
      backgroundColor: context.colorStyling.background1,
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Label(
              model: LabelModel(
                text: l10n.carbonCalculatorSignUpTitle,
                textStyle: CompanyTextStylePointer.h2medium,
                color: CompanyColorPointer.primary3,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s3),
            Label(
              model: LabelModel(
                text: l10n.carbonCalculatorSignUpSubTitle,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.secondary1,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s6),
            ListDetailsContainer(
              model: ListDetailsContainerModel(
                items: [
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.carbonCalculatorSignUpAmount,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.money,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.carbonCalculatorSignUpCategory,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.user,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.carbonCalculatorSignUpCurrency,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.payments,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
            const _AgreementButton(),
            const SizedBox(height: 24.0),
            Button(
              model: ButtonModel(
                title: l10n.carbonCalculatorSignUpConfirm,
              ),
              onPressed: cubit.signUp,
            ),
            Space.fromSpacingVertical(Spacing.s2),
          ],
        ),
      ),
    );
  }
}

class _AgreementButton extends StatelessWidget {
  const _AgreementButton();

  @override
  Widget build(BuildContext context) {
    final l10n = CarbonCalculatorLocalizations.of(context);

    return CompanyRichText(
      CompanyRichTextModel(
        text: l10n.byTappingAgreement(
          l10n.termsAndConditions,
          l10n.carbonCalculatorSignUpConfirm,
        ),
        highlightedTextModels: [
          HighlightedTextModel(
            l10n.termsAndConditions,
            onTap: context
                .read<CarbonCalculatorSignUpCubit>()
                .onOpenTermsAndConditions,
          ),
        ],
        normalStyle: CompanyTextStylePointer.b3,
        normalTextColor: CompanyColorPointer.secondary4,
        accentStyle: CompanyTextStylePointer.b3,
        accentTextColor: CompanyColorPointer.primary1,
      ),
    );
  }
}
