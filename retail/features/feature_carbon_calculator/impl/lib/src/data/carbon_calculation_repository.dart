import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_carbon_calculator_api/carbon_calculator_api.dart';
import 'package:wio_feature_carbon_calculator_impl/src/data/carbon_calculation_mapper.dart';
import 'package:wio_feature_carbon_calculator_impl/src/data/carbon_calculation_service.dart';

import 'package:wio_feature_carbon_calculator_impl/src/data/carbon_calculator_data_source.dart';

abstract interface class CarbonCalculationRepository {
  Future<CarbonCalculationConfig> getConfig();

  Future<void> donate({
    required Money amount,
    required String sourceAccountId,
  });

  Future<double> getTotalDonation();
}

class CarbonCalculationRepositoryImpl implements CarbonCalculationRepository {
  final CarbonCalculationService _service;
  final CarbonCalculatorDataSource _localDataSource;
  final CarbonCalculationMapper _mapper;

  const CarbonCalculationRepositoryImpl({
    required CarbonCalculationService service,
    required CarbonCalculatorDataSource localDataSource,
    required CarbonCalculationMapper mapper,
  })  : _service = service,
        _localDataSource = localDataSource,
        _mapper = mapper;

  @override
  Future<CarbonCalculationConfig> getConfig() async {
    final response = await _service.getConfigs();
    final result = _mapper.mapToConfig(response);

    return result;
  }

  @override
  Future<void> donate({
    required Money amount,
    required String sourceAccountId,
  }) {
    final request = _mapper.mapToDonationRequest(
      amount: amount,
      accountId: sourceAccountId,
    );

    return _service.donate(request);
  }

  @override
  Future<double> getTotalDonation() => _localDataSource.getTotalDonations();
}
