import 'package:collection/collection.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_data_api/account_data_api.dart' as dtos;
import 'package:wio_feature_carbon_calculator_api/carbon_calculator_api.dart';
import 'package:wio_feature_cards_data_api/cards_data_api.dart' as cards_dto;

abstract interface class CarbonCalculationMapper {
  CarbonCalculationConfig mapToConfig(List<dtos.MyMoneyConfig> response);

  cards_dto.CarbonFootprintDonationRequest mapToDonationRequest({
    required Money amount,
    required String accountId,
  });
}

class CarbonCalculationMapperImpl implements CarbonCalculationMapper {
  static const _treeCostAmountKey = 'tree_planting_cost_amount';
  static const _treeCostCurrencyKey = 'tree_planting_cost_currency';
  static const _tncEnglishUrlKey = 'terms_conditions_url_en';
  static const _tncArabicUrlKey = 'terms_conditions_url_ar';

  final ErrorReporter _reporter;

  const CarbonCalculationMapperImpl(this._reporter);

  @override
  CarbonCalculationConfig mapToConfig(List<dtos.MyMoneyConfig> response) {
    return _reporter.executeWithReport(
      () {
        final pairs = {for (final item in response) item.key: item.$value};
        final treePrice = _mapTreePlantingPrice(pairs);
        final termsAndConditions = _mapTermsAndConditions(pairs);

        return CarbonCalculationConfig(
          treePlantingCost: treePrice,
          termsAndConditions: termsAndConditions,
        );
      },
    );
  }

  @override
  cards_dto.CarbonFootprintDonationRequest mapToDonationRequest({
    required Money amount,
    required String accountId,
  }) {
    return _reporter.executeWithReport(
      () => cards_dto.CarbonFootprintDonationRequest(
        amount: double.parse(amount.stringAmount),
        accountId: accountId,
        // Currency is optional anyway
        currency: cards_dto.CarbonFootprintDonationRequestCurrency.values
            .firstWhereOrNull((it) => it.value == amount.currency.code),
      ),
    );
  }

  Money _mapTreePlantingPrice(Map<String, String> config) {
    final treePriceAmount = ArgumentError.checkNotNull(
      config[_treeCostAmountKey],
      'No data for tree planting cost found: amount is missing',
    );
    final treePriceCurrency = ArgumentError.checkNotNull(
      config[_treeCostCurrencyKey],
      'No data for tree planting cost found: currency is missing',
    ).toUpperCase();

    return Money.parse(treePriceAmount, code: treePriceCurrency);
  }

  CarbonCalculatorDocument _mapTermsAndConditions(Map<String, String> config) {
    final enUrl = ArgumentError.checkNotNull(
      config[_tncEnglishUrlKey],
      'No URL found for T&C in English',
    );
    final arUrl = ArgumentError.checkNotNull(
      config[_tncArabicUrlKey],
      'No URL found for T&C in Arabic',
    );

    return CarbonCalculatorDocument(enUrl: enUrl, arUrl: arUrl);
  }
}
