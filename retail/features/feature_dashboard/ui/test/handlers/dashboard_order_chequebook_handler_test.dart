import 'dart:ui';

import 'package:fake_async/fake_async.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_order_chequebook_handler.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_feature_user_api/domain/model/customer_info.dart';
import 'package:wio_feature_user_api/domain/model/user.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  const accountId = 'accountId';

  late MockChequebookInteractor interactor;
  late MockAddressInteractor addressInteractor;
  late MockUserInteractor userInteractor;
  late MockNavigationProvider navigationProvider;
  late MockCustomerAddressFeatureNavigationFactory addressFeatureNavFactory;
  late MockChequebookAnalytics analytics;
  late MockCommonErrorHandler commonErrorHandler;
  late MockToastMessageProvider toastMessageProvider;
  late MockFeatureToggleProvider featureToggleProvider;
  late MockLogger logger;
  late DashboardOrderChequebookHandler handler;
  late CardsLocalizations localizations;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await CardsLocalizations.load(const Locale('en'));
  });

  setUp(() {
    interactor = MockChequebookInteractor();
    addressInteractor = MockAddressInteractor();
    userInteractor = MockUserInteractor();
    navigationProvider = MockNavigationProvider();
    addressFeatureNavFactory = MockCustomerAddressFeatureNavigationFactory();
    analytics = MockChequebookAnalytics();
    commonErrorHandler = MockCommonErrorHandler();
    toastMessageProvider = MockToastMessageProvider();
    featureToggleProvider = MockFeatureToggleProvider();
    logger = MockLogger();

    handler = DashboardOrderChequebookHandler(
      interactor: interactor,
      addressInteractor: addressInteractor,
      userInteractor: userInteractor,
      navigationProvider: navigationProvider,
      addressFeatureNavigationFactory: addressFeatureNavFactory,
      analytics: analytics,
      commonErrorHandler: commonErrorHandler,
      toastMessageProvider: toastMessageProvider,
      featureToggleProvider: featureToggleProvider,
      localizations: localizations,
      logger: logger,
    );

    when(() => navigationProvider.navigateTo(any())).justCompleteAsync();
  });

  group('init method', () {
    test('disabled autofilled ff initiates order chequebook flow', () async {
      when(
        () => featureToggleProvider.get(
          CardsFeatureToggles.isChequebookOrderAutofilled,
        ),
      ).thenReturn(false);

      await handler.init(accountId);

      verify(() {
        navigationProvider.navigateTo(
          const CardsFeatureNavigationConfig.chequebook(accountId: accountId),
        );
      }).calledOnce;
    });

    group('autofilled initiate address and name', () {
      test(
        'enabled autofilled, initiate address and name autofill success',
        () async {
          final navigationConfig = FeatureNavigationConfigFake();
          final details = TestEntities.chequebookInitiateResult();
          const address = CustomerAddress();

          when(
            () => featureToggleProvider.get(
              CardsFeatureToggles.isChequebookOrderAutofilled,
            ),
          ).thenReturn(true);
          when(() => interactor.initiate(accountId)).justAnswerAsync(details);
          when(userInteractor.getUser).justAnswerAsync(
            const User(customerInfo: CustomerInfo()),
          );
          when(
            () => addressFeatureNavFactory
                .navigateToDeliveryAddressSelectionScreen(),
          ).thenReturn(navigationConfig);
          when(
            () => navigationProvider.navigateTo(navigationConfig),
          ).justAnswerAsync(address);

          fakeAsync(
            (async) {
              handler.init(accountId);

              async.elapse(const Duration(seconds: 1));

              verifyNever(() {
                navigationProvider.navigateTo(
                  const CardsFeatureNavigationConfig.chequebook(
                    accountId: accountId,
                  ),
                );
              });

              verify(
                () {
                  navigationProvider.navigateTo(
                    CardsFeatureNavigationConfig.chequebookOrderReview(
                      accountId: accountId,
                      chequebookDetails: details,
                      address: address,
                      nameOnChequebook: '',
                    ),
                  );
                },
              ).calledOnce;
            },
          );
        },
      );

      test(
        'enabled autofilled, closed address selection interrupts execution',
        () async {
          final navigationConfig = FeatureNavigationConfigFake();
          final details = TestEntities.chequebookInitiateResult();

          when(
            () => featureToggleProvider.get(
              CardsFeatureToggles.isChequebookOrderAutofilled,
            ),
          ).thenReturn(true);
          when(() => interactor.initiate(accountId)).justAnswerAsync(details);
          when(userInteractor.getUser).justAnswerAsync(
            const User(customerInfo: CustomerInfo()),
          );
          when(
            addressFeatureNavFactory.navigateToDeliveryAddressSelectionScreen,
          ).thenReturn(navigationConfig);
          when(
            () => navigationProvider.navigateTo(navigationConfig),
          ).justAnswerEmptyAsync();

          fakeAsync(
            (async) {
              handler.init(accountId);
              async.elapse(const Duration(seconds: 1));
              verify(() => navigationProvider.navigateTo(navigationConfig))
                  .calledOnce;
              verifyNoMoreInteractions(navigationProvider);
            },
          );
        },
      );

      test(
        'enabled autofilled, empty name interrupts execution',
        () async {
          final navigationConfig = FeatureNavigationConfigFake();
          final details = TestEntities.chequebookInitiateResult();
          const address = CustomerAddress();

          when(
            () => featureToggleProvider.get(
              CardsFeatureToggles.isChequebookOrderAutofilled,
            ),
          ).thenReturn(true);
          when(() => interactor.initiate(accountId)).justAnswerAsync(details);
          when(userInteractor.getUser).justAnswerEmptyAsync();
          when(
            addressFeatureNavFactory.navigateToDeliveryAddressSelectionScreen,
          ).thenReturn(navigationConfig);
          when(
            () => navigationProvider.navigateTo(navigationConfig),
          ).justAnswerAsync(address);

          fakeAsync(
            (async) {
              handler.init(accountId);
              async.elapse(const Duration(seconds: 1));
              verify(() => navigationProvider.navigateTo(navigationConfig))
                  .calledOnce;
              verifyNoMoreInteractions(navigationProvider);
            },
          );
        },
      );

      test(
        'enabled autofilled, empty initiate response interrupts execution',
        () async {
          final navigationConfig = FeatureNavigationConfigFake();
          const address = CustomerAddress();

          when(
            () => featureToggleProvider.get(
              CardsFeatureToggles.isChequebookOrderAutofilled,
            ),
          ).thenReturn(true);
          when(() => interactor.initiate(accountId)).justThrowAsync(
            Exception(),
          );
          when(userInteractor.getUser).justAnswerAsync(
            const User(customerInfo: CustomerInfo()),
          );
          when(
            addressFeatureNavFactory.navigateToDeliveryAddressSelectionScreen,
          ).thenReturn(navigationConfig);
          when(
            () => navigationProvider.navigateTo(navigationConfig),
          ).justAnswerAsync(address);

          fakeAsync(
            (async) {
              handler.init(accountId);
              async.elapse(const Duration(seconds: 1));
              verifyZeroInteractions(navigationProvider);
            },
          );
        },
      );

      test(
        'InitChequebookException on init open error toast',
        () async {
          when(
            () => featureToggleProvider.get(
              CardsFeatureToggles.isChequebookOrderAutofilled,
            ),
          ).thenReturn(true);
          when(() => interactor.initiate(accountId)).justThrowAsync(
            const ChequebookException.init('description'),
          );

          fakeAsync(
            (async) {
              handler.init(accountId);
              async.elapse(const Duration(seconds: 1));
              verify(
                () => toastMessageProvider.showRetailMobileThemedToastMessage(
                  NotificationToastMessageConfiguration.error('description'),
                ),
              ).calledOnce;
            },
          );
        },
      );

      test(
        'Not eligible status on init open warn toast',
        () async {
          when(
            () => featureToggleProvider.get(
              CardsFeatureToggles.isChequebookOrderAutofilled,
            ),
          ).thenReturn(true);
          when(() => interactor.initiate(accountId)).justAnswerAsync(
            const ChequebookInitiateResult(
              status: InitiateStatus.notEligible(reason: 'reason'),
              firstOrder: true,
              fees: CreationFees(),
              chequebookLeafsAmount: [],
            ),
          );

          fakeAsync(
            (async) {
              handler.init(accountId);
              async.elapse(const Duration(seconds: 1));
              verify(
                () => toastMessageProvider.showRetailMobileThemedToastMessage(
                  NotificationToastMessageConfiguration.warning('reason'),
                ),
              ).calledOnce;
            },
          );
        },
      );
    });
  });
}
