import 'dart:async';

import 'package:common_feature_fx_api/feature_fx_api.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_balance_data.dart';
import 'package:neobroker_models/models.dart';
import 'package:portfolio_api/index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

class DashboardBalanceHandler {
  final SavingSpacesInteractor _savingSpacesInteractor;
  final PortfolioInteractor _portfolioInteractor;
  final RateInteractor _rateInteractor;
  final Logger _logger;

  DashboardBalanceHandler(
    this._savingSpacesInteractor,
    this._portfolioInteractor,
    this._rateInteractor,
    this._logger,
  );

  Future<DashboardBalanceData> getBalanceData({
    required List<AccountDetails> accounts,
    required ProductHubItem? brokerProduct,
    List<SavingsAccount> savingSpaces = const [],
  }) async {
    try {
      final aedAccountId = accounts.aedAccountId;

      if (aedAccountId == null) {
        throw Exception('Could not get customer AED account');
      }

      final balances = await Future.wait(
        [
          _getForeignAccountsInAed(accounts, aedAccountId),
          _getSavingsBalance(savingSpaces),
          _getInvestmentsBalance(brokerProduct, aedAccountId),
        ],
      );

      final foreignAccountsInAed = balances[0] as Map<String, Money>;
      final savingsBalance = balances[1] as DashboardBalance;
      final investmentsBalance = balances[2] as DashboardBalance;

      final accountsBalance = _getAccountsBalance(
        accounts,
        foreignAccountsInAed,
      );

      return DashboardBalanceData(
        accountsBalance: accountsBalance,
        savingsBalance: savingsBalance,
        investmentsBalance: investmentsBalance,
        foreignAccountsInAed: foreignAccountsInAed,
      );
    } on Object catch (error) {
      _logger.debug(error.toString());
      rethrow;
    }
  }

  Future<Map<String, Money>> _getForeignAccountsInAed(
    List<AccountDetails> accounts,
    String aedAccountId,
  ) async {
    final nonAedAccounts = accounts.excludeCurrency(Currency.aed);
    if (nonAedAccounts.isEmpty) {
      return <String, Money>{};
    }

    // Accounts now have localAvailableBalance field which is their
    // available balance converted to AED.
    // This is done so to avoid calling rates API from FE.
    final localBalances = {
      for (final account in nonAedAccounts)
        if (account.localAvailableBalance case final localBalance?)
          account.id: localBalance,
    };

    // This generally should always be true but extra checks never hurt.
    if (localBalances.length == nonAedAccounts.length) {
      return localBalances;
    }

    try {
      final currencySet =
          nonAedAccounts.map((it) => it.availableBalance.currency).toSet();
      final allRates = await _getAllRates(currencySet, aedAccountId);
      final foreignDoubleRates = _convertRatesToMap(allRates, currencySet);
      return _convertAccountsToAed(nonAedAccounts, foreignDoubleRates);
    } on Object catch (e, st) {
      _logger.error(
        '_getForeignAccountsInAed exception',
        error: e,
        stackTrace: st,
        loggerName: WioDomain.fx.name,
      );
      return {};
    }
  }

  Future<List<Rate>> _getAllRates(
    Set<Currency> currencySet,
    String aedAccountId,
  ) {
    final couples = currencySet
        .map((currency) => CurrencyCouple(Currency.aed, currency))
        .toList();

    return _rateInteractor.getRatesByAccount(couples, aedAccountId);
  }

  Map<String, double> _convertRatesToMap(
    List<Rate> allRates,
    Set<Currency> currencySet,
  ) {
    final currenciesRates = <String, double>{};
    for (final rate in allRates) {
      if (currencySet.contains(rate.sellCurrency) &&
          rate.buyCurrency == Currency.aed) {
        currenciesRates[rate.sellCurrency.code] = rate.rate;
      }
    }

    return currenciesRates;
  }

  Map<String, Money> _convertAccountsToAed(
    List<AccountDetails> accounts,
    Map<String, double> currenciesRates,
  ) {
    final foreignAccountsInAed = <String, Money>{};
    for (final account in accounts) {
      final balance = account.availableBalance;
      final currencyCode = balance.currency.code;
      final doubleBalance = double.tryParse(balance.stringAmount);
      final conversionRate = currenciesRates[currencyCode];

      if (doubleBalance == null || conversionRate == null) {
        throw Exception(
          'Error parsing balance of account with ID ${account.id}',
        );
      }

      foreignAccountsInAed[account.id] = Money.fromNumWithCurrency(
        doubleBalance * conversionRate,
        Currency.aed,
      );
    }

    return foreignAccountsInAed;
  }

  Future<DashboardBalance> _getSavingsBalance(
    List<SavingsAccount> spaces,
  ) async {
    if (spaces.isEmpty) {
      return const DashboardBalance.notOnboarded();
    }

    try {
      final value = await _savingSpacesInteractor.getTotalBalance(spaces);
      return DashboardBalance.value(amount: value);
    } on Object catch (e, st) {
      _logger.error(
        '_getSavingsBalance exception',
        stackTrace: st,
        loggerName: WioDomain.savingSpaces.name,
      );
    }

    return const DashboardBalance.error();
  }

  Future<DashboardBalance> _getInvestmentsBalance(
    ProductHubItem? brokerProduct,
    String aedAccountId,
  ) async {
    try {
      if (brokerProduct == null || brokerProduct.status == ItemStatus.blocked) {
        return const DashboardBalance.notAvailable();
      }

      final isActiveBroker = brokerProduct.status == ItemStatus.active;

      if (!isActiveBroker || brokerProduct.status.isBrokerAccountAbsent) {
        return const DashboardBalance.notOnboarded();
      }

      final brokerId = brokerProduct.externalProductId;

      if (brokerId == null) throw Exception('brokerId can not be null');

      // TODO(rauan): We need to fetch invest portfolios in advance to show
      // portfolios filter in transactions, this piece of code will be removed
      // when `portfolioType` field will be added to broker transactions
      _portfolioInteractor.getPortfolios().ignore();

      // Retrieve all portfolios data
      final balance = await _portfolioInteractor.getAedBalance();

      if (isActiveBroker && balance == null) {
        throw Exception(
          'When the broker is active, the investment balance cannot be null',
        );
      }
      if (balance == null) {
        return const DashboardBalance.error();
      }
      final balanceAmount = num.tryParse(balance.toMoney().stringAmount);

      return DashboardBalance.value(
        amount: Money.fromNumWithCurrency(balanceAmount ?? 0, Currency.aed),
      );
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Cannot get investments balance.\n${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.broker.name,
      );

      return const DashboardBalance.error();
    }
  }

  DashboardBalance _getAccountsBalance(
    List<AccountDetails> accounts,
    Map<String, Money> foreignAccountsInAed,
  ) {
    final aedAccounts = _getAedAccountsTotalBalance(accounts);
    final nonAedAccounts = accounts.excludeCurrency(Currency.aed);

    if (nonAedAccounts.isNotEmpty && foreignAccountsInAed.isEmpty) {
      return const DashboardBalance.error();
    }

    final foreignAccounts = _getForeignAccountsTotalBalance(
      foreignAccountsInAed,
    );

    return DashboardBalance.value(amount: aedAccounts + foreignAccounts);
  }

  Money _getAedAccountsTotalBalance(List<AccountDetails> accounts) {
    return accounts
        .where((account) => account.type != AccountType.regularSavings)
        .where((account) => account.availableBalance.currency == Currency.aed)
        .fold<Money>(
          Money.fromNumWithCurrency(0.0, Currency.aed),
          (sum, current) => sum + current.availableBalance,
        );
  }

  Money _getForeignAccountsTotalBalance(
    Map<String, Money> foreignAccountsInAed,
  ) {
    return foreignAccountsInAed.values.fold<Money>(
      Money.fromNumWithCurrency(0.0, Currency.aed),
      (sum, current) => sum + current,
    );
  }
}
