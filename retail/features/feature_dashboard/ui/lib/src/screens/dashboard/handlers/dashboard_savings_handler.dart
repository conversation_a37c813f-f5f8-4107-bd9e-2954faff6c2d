import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:rxdart/rxdart.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

class DashboardSavingsHandler {
  final SavingSpacesInteractor _savingsInteractor;
  final SavingSpaceTrackingInteractor _trackingInteractor;
  final FeatureToggleProvider _featureToggles;
  final Logger _logger;

  const DashboardSavingsHandler({
    required SavingSpacesInteractor savingsInteractor,
    required SavingSpaceTrackingInteractor trackingInteractor,
    required FeatureToggleProvider featureToggles,
    required Logger logger,
  })  : _savingsInteractor = savingsInteractor,
        _trackingInteractor = trackingInteractor,
        _featureToggles = featureToggles,
        _logger = logger;

  bool get _isSavingsPromoEnabled => _featureToggles.get(
        DashboardFeatureToggles.isSavingsInterestPromoEnabled,
      );

  Future<List<SavingsAccount>> getSavingSpaces() async {
    try {
      return await _savingsInteractor.getSavingsAccounts();
    } on Object catch (error, stack) {
      _logger.info(
        'Fetching saving spaces failed',
        error: error,
        stackTrace: stack,
      );

      if (error is NoInternetConnectionException) {
        rethrow;
      }

      return const [];
    }
  }

  Future<SavingsPromo?> getSavingsPromo({
    required Money totalBalance,
    required List<SavingsAccount> savingSpaces,
  }) {
    if (!_isSavingsPromoEnabled) {
      return Future.value();
    }

    return _savingsInteractor
        .getSavingsPromo(
          savingSpaces: savingSpaces.whereType<SavingSpace>().toList(),
          totalBalance: totalBalance,
        )
        .toStream()
        .timeout(const Duration(seconds: 5), onTimeout: (sink) => sink.close())
        .logError(_logger)
        .onErrorReturn(null)
        .firstOrNull;
  }

  void hideSavingsPromo(SavingsPromo promo) {
    _trackingInteractor.markSavingsPromoViewed(promo);
  }
}
