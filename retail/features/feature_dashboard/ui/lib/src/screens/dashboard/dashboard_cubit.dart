// ignore_for_file: long-method, deprecated_member_use
import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_wio_feature_kyc_api/kyc_api.dart';
import 'package:connectivity_api/index.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:feature_dashboard_ui/l10n/dashboard_localization.g.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/analytics/dashboard_analytics.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/dashboard_screen.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/dashboard_state.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/delegates/additional_info_delegate.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/delegates/lending_delegate.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_balance_data.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_balance_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_family_banking_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_navigation_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_order_chequebook_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_savings_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_to_do_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/models/to_do_item.dart';
import 'package:feature_required_actions_api/feature_required_actions_api.dart';
import 'package:flutter/cupertino.dart';
import 'package:neobroker_models/models.dart';
import 'package:rxdart/rxdart.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide SavingSpace;
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_broker_api/index.dart';
import 'package:wio_common_feature_customer_address_api/domain/customer_address_interactor.dart';
import 'package:wio_common_feature_ipo_api/index.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_wealth_management_api/handlers/wealth_management_starter_handler.dart';
import 'package:wio_critical_notification_api/feature_critical_notification_api.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_account_maintenance_api/account_maintenance_api.dart';
import 'package:wio_feature_carbon_calculator_api/state_controller/carbon_calculator_state.dart';
import 'package:wio_feature_carbon_calculator_api/state_controller/carbon_calculator_state_controller.dart';
import 'package:wio_feature_deposits_api/feature_deposits_api.dart';
import 'package:wio_feature_easy_cash_api/easy_cash_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_liveness_api/domain/models/liveness_result.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_user_api/index.dart';
import 'package:wio_retail_analytics_api/index.dart';

class DashboardCubit extends BaseCubit<DashboardState> {
  static const _currentScreen = 'retail_dashboard_screen';
  static const _lendingProductType = ProductType.creditCard;
  static final _emptyBalanceInAed = Money.fromNumWithCurrency(0, Currency.aed);
  static const _pricingApiTimeout = Duration(seconds: 5);

  final _refreshTransactionsStream = PublishSubject<void>();

  final DashboardAnalytics _analytics;
  final DashboardNavigationHandler _navigationHandler;
  final DashboardBalanceHandler _balanceHandler;
  final DashboardToDoHandler _toDoHandler;
  final AccountInteractor _accountInteractor;
  final CustomerIdInteractor _customerIdInteractor;
  final ToastMessageProvider _toastMessageProvider;
  final CommonLocalizations _commonLocalizations;
  final DashboardLocalizations _dashboardLocalizations;
  final FeatureToggleProvider _featureToggles;
  final UserInteractor _userInteractor;
  final Logger _logger;
  final PerformanceMonitor _performanceMonitor;
  final LendingDelegate _lendingDelegate;
  final CommonErrorHandler _commonErrorHandler;
  final RfiHandler _rfiHandler;
  final DashboardSavingsHandler _savingsHandler;
  final DashboardOrderChequebookHandler _chequebookHandler;
  final PassportVerificationFlowService _passportVerificationFlowService;
  final CarbonCalculatorStateController _stateController;
  final ChequebookInteractor _chequebookInteractor;
  final IpoInteractor _ipoInteractor;
  final NPSSInteractor _npssInteractor;
  final EducationalInfoHandler _coachMarkHandler;
  final BrokerUserInteractor _brokerUserInteractor;
  final KycInteractor _kycInteractor;
  final ConnectivityInteractor _connectivityInteractor;
  final PricingPlanInteractor _planInteractor;
  final ExhaustStreamExecutor _exhaustStreamExecutor;
  final CriticalNotificationInteractor _criticalNotificationInteractor;
  final AdditionalInfoDelegate _additionalInfoDelegate;
  final UpdateEmiratesIdDelegate _updateEmiratesIdDelegate;
  final CriticalNotificationAnalytics _criticalNotificationAnalytics;
  final RequiredActionsInteractor _requiredActionsInteractor;
  final ModalDelegatesRunner _modalDelegatesRunner;
  final CustomerAddressInteractor _customerAddressInteractor;
  final EasyCashInteractor _easyCashInteractor;
  final ReportingConfigurator _reportingConfigurator;
  final CustomerSubscriptionInteractor _customerSubscriptionInteractor;
  final BackendDrivenFlowInteractor _backendDrivenFlowInteractor;
  final DashboardFamilyBankingHandler _familyBankingHandler;
  final SplitTransactionsInteractor _splitTransactionsInteractor;
  final BrokerProductInteractor _brokerProductInteractor;
  final WealthManagementStarterHandler _wealthManagementStarterHandler;

  Timer? _updateScreenTimer;
  StreamSubscription<User?>? _userSubscription;
  StreamSubscription<EasyCashData?>? _easyCashSubscription;
  StreamSubscription<DashboardState>? _lendingSummarySubscription;

  DashboardCubit({
    required IpoInteractor ipoInteractor,
    required DashboardAnalytics analytics,
    required DashboardNavigationHandler navigationHandler,
    required DashboardBalanceHandler balanceHandler,
    required DashboardToDoHandler dashboardToDoHandler,
    required AccountInteractor accountInteractor,
    required RfiHandler rfiHandler,
    required CustomerIdInteractor customerIdInteractor,
    required ToastMessageProvider toastMessageProvider,
    required CommonLocalizations commonLocalizations,
    required DashboardLocalizations dashboardLocalizations,
    required FeatureToggleProvider featureToggles,
    required UserInteractor userInteractor,
    required LendingDelegate lendingDelegate,
    required Logger logger,
    required PerformanceMonitor performanceMonitor,
    required CommonErrorHandler commonErrorHandler,
    required DashboardSavingsHandler savingsHandler,
    required DashboardOrderChequebookHandler dashboardOrderChequebookHandler,
    required PassportVerificationFlowService passportVerificationFlowService,
    required CarbonCalculatorStateController stateController,
    required ChequebookInteractor chequebookInteractor,
    required NPSSInteractor npssInteractor,
    required EducationalInfoHandler coachMarkHandler,
    required BrokerUserInteractor brokerUserInteractor,
    required KycInteractor kycInteractor,
    required ConnectivityInteractor connectivityInteractor,
    required PricingPlanInteractor planInteractor,
    required ExhaustStreamExecutor exhaustStreamExecutor,
    required CriticalNotificationInteractor criticalNotificationInteractor,
    required AdditionalInfoDelegate additionalInfoDelegate,
    required UpdateEmiratesIdDelegate updateEmiratesIdDelegate,
    required CriticalNotificationAnalytics criticalNotificationAnalytics,
    required RequiredActionsInteractor requiredActionsInteractor,
    required ModalDelegatesRunner modalDelegatesRunner,
    required CustomerAddressInteractor customerAddressInteractor,
    required EasyCashInteractor easyCashInteractor,
    required ReportingConfigurator reportingConfigurator,
    required CustomerSubscriptionInteractor customerSubscriptionInteractor,
    required BackendDrivenFlowInteractor backendDrivenFlowInteractor,
    required DashboardFamilyBankingHandler familyBankingHandler,
    required SplitTransactionsInteractor splitTransactionsInteractor,
    required BrokerProductInteractor brokerProductInteractor,
    required WealthManagementStarterHandler wealthManagementStarterHandler,
  })  : _analytics = analytics,
        _navigationHandler = navigationHandler,
        _balanceHandler = balanceHandler,
        _toDoHandler = dashboardToDoHandler,
        _accountInteractor = accountInteractor,
        _customerIdInteractor = customerIdInteractor,
        _toastMessageProvider = toastMessageProvider,
        _commonLocalizations = commonLocalizations,
        _ipoInteractor = ipoInteractor,
        _dashboardLocalizations = dashboardLocalizations,
        _featureToggles = featureToggles,
        _userInteractor = userInteractor,
        _logger = logger,
        _performanceMonitor = performanceMonitor,
        _lendingDelegate = lendingDelegate,
        _commonErrorHandler = commonErrorHandler,
        _savingsHandler = savingsHandler,
        _rfiHandler = rfiHandler,
        _passportVerificationFlowService = passportVerificationFlowService,
        _chequebookHandler = dashboardOrderChequebookHandler,
        _stateController = stateController,
        _chequebookInteractor = chequebookInteractor,
        _npssInteractor = npssInteractor,
        _coachMarkHandler = coachMarkHandler,
        _brokerUserInteractor = brokerUserInteractor,
        _kycInteractor = kycInteractor,
        _connectivityInteractor = connectivityInteractor,
        _planInteractor = planInteractor,
        _exhaustStreamExecutor = exhaustStreamExecutor,
        _criticalNotificationInteractor = criticalNotificationInteractor,
        _additionalInfoDelegate = additionalInfoDelegate,
        _updateEmiratesIdDelegate = updateEmiratesIdDelegate,
        _criticalNotificationAnalytics = criticalNotificationAnalytics,
        _requiredActionsInteractor = requiredActionsInteractor,
        _modalDelegatesRunner = modalDelegatesRunner,
        _customerAddressInteractor = customerAddressInteractor,
        _easyCashInteractor = easyCashInteractor,
        _reportingConfigurator = reportingConfigurator,
        _customerSubscriptionInteractor = customerSubscriptionInteractor,
        _backendDrivenFlowInteractor = backendDrivenFlowInteractor,
        _familyBankingHandler = familyBankingHandler,
        _splitTransactionsInteractor = splitTransactionsInteractor,
        _brokerProductInteractor = brokerProductInteractor,
        _wealthManagementStarterHandler = wealthManagementStarterHandler,
        super(const DashboardState.loading()) {
    analytics.dashboardOpened();
    _listenForUpdateEvents();
    _listenForPendingInvitationTrigger();

    // Listen to the accounts updates
    _observeAccounts();

    _observerCarbonCalculatorState();
    _listenCriticalNotifications();
    _observeChequebookState();
    _observeNpssEnrollmentStatus();
    _userSubscription = _userInteractor.currentUser
        .where((user) => user != null)
        .distinct()
        .listen(
      (user) {
        state.mapOrNull(
          loaded: (state) async {
            final showPasscodeSetUp = await _shouldShowSetUpPasscode(user);
            emit(state.copyWith(shouldShowSetUpPasscode: showPasscodeSetUp));
          },
        );
      },
    );

    if (_shouldShowModals) {
      safeExecute(
        _modalDelegatesRunner.run(
          delegates: [
            _additionalInfoDelegate,
            _updateEmiratesIdDelegate,
          ],
        ),
      ).then((result) {
        if (result is EidUpdateResult) {
          _onUpdateExpiredEmiratesId(result.updated);
        }
      });
    }

    _checkForRequiredAction();

    _easyCashSubscription =
        _easyCashInteractor.observeEasyCashOffer().listenSafe(
              this,
              onData: _handleEasyCashData,
              onError: (error) => _logger.error(
                'Error while listening easy cash offer',
                error: error,
                loggerName: WioDomain.lending.name,
              ),
            );
  }

  bool get _isIpoEnabled => _featureToggles.get(
        IpoFeatureToggles.isIpoEnabled,
      );

  bool get isNpsFeedbackEnabled =>
      _featureToggles.get<bool>(DashboardFeatureToggles.isNpsFeedbackEnabled);

  bool get _isIpoCardVisible => _featureToggles.get(
        IpoFeatureToggles.isIpoBannerEnabled,
      );

  bool get _isDepositsEnabled =>
      _featureToggles.get(DepositsFeatureToggles.isCashDepositFeatureEnabled) ||
      _featureToggles
          .get(DepositsFeatureToggles.isChequeDepositsFeatureEnabled);

  bool get _isNpssEnabled =>
      _featureToggles.get(PaymentsFeatureToggle.isNPSSEnabled);

  bool get _isInvestPromoBannerEnabled =>
      _featureToggles.get(BrokerFeatureToggles.isInvestPromoBannerEnabled);

  bool get _isChequebookDeliveryStatusEnabled => _featureToggles
      .get(CardsFeatureToggles.isChequebookDeliveryTrackerEnabled);

  bool get _isCriticalNotificationsEnabled => _featureToggles.get(
        CriticalNotificationFeatureToggles.isCriticalNotificationEnabled,
      );

  bool get _shouldShowModals => _featureToggles.get(
        DashboardFeatureToggles.areModalScreensEnabled,
      );

  bool get _isRequiredActionsEnabled => _featureToggles.get(
        RequiredActionsFeatureToggles.isRequiredActionsFeatureEnabled,
      );

  bool get _isLendingPersonalLoanEnabled => _featureToggles.get(
        LendingFeatureToggles.isLendingPersonalLoanEnabled,
      );

  bool get _isLendingAutoLoanEnabled => _featureToggles.get(
        LendingFeatureToggles.isLendingAutoLoanEnabled,
      );

  bool get _isAccountRestrictionFlowEnabled => _featureToggles.get(
        BackendDrivenFlowFeatureToggles.isAccountRestrictionEnabled,
      );

  bool get _isSplitTransactionsEnabled => _featureToggles.get(
        LoanFeatureToggles.isSplitTransactionLoanEnabled,
      );

  bool get _isSalaryPlanPromoEnabled => _featureToggles.get(
        DashboardFeatureToggles.isSalaryPlanPromoEnabled,
      );

  int? get dueDays => state.mapOrNull(loaded: (it) => it.easyCashData?.dueDays);

  double? get maxEasyCashLimitPercentage =>
      state.mapOrNull(loaded: (it) => it.easyCashData?.maxLimitPercentage);

  EasyCashData? get _easyCashData =>
      state.mapOrNull(loaded: (it) => it.easyCashData);

  bool get isEnableCriticalNotificationV2Design => _featureToggles.get<bool>(
        CriticalNotificationFeatureToggles.isCriticalNotificationV2Enabled,
      );

  DashboardFeatureTogglesState get _dashboardFeatures =>
      DashboardFeatureTogglesState(
        isIpoCardVisible: _isIpoCardVisible,
        isDepositsEnabled: _isDepositsEnabled,
        isChequebookDeliveryStatusEnabled: _isChequebookDeliveryStatusEnabled,
        isInvestPromoBannerEnabled: _isInvestPromoBannerEnabled,
        isCriticalNotificationsEnabled: _isCriticalNotificationsEnabled,
        isLendingPersonalLoanEnabled: _isLendingPersonalLoanEnabled,
        isLendingAutoLoanEnabled: _isLendingAutoLoanEnabled,
        isSalaryPlanPromoEnabled: _isSalaryPlanPromoEnabled,
      );

  Stream<void> get refreshTransactionsStream =>
      _refreshTransactionsStream.stream;

  bool get isLendingForeignCreditReportEnabled =>
      _featureToggles.get(LendingFeatureToggles.isForeignCreditReportEnabled);

  bool get isLendingApplicationTrackerEntryPointEnabled => _featureToggles.get(
        LendingFeatureToggles.isApplicationTrackerEntryPointEnabled,
      );

  @override
  String toString() {
    return 'Dashboard_cubit';
  }

  Future<void> init({bool withLoading = true}) async {
    final pageLoadTrace = _performanceMonitor.startPageLoadTrace(
      'DashboardPage',
    );
    try {
      if (withLoading) {
        emit(const DashboardState.loading());
      }

      unawaited(_refreshInstallments());

      final noParamsRequests = await Future.wait(
        [
          _customerIdInteractor.getCustomerId(),
          _savingsHandler.getSavingSpaces(),
          _userInteractor.getUser(),
          _lendingDelegate.getLendingGuideStage(),
          _fetchCheckbookStatuses(),
          _getAccountDetail(),
          _lendingDelegate.isLendingPromoCardDismissed(),
          _fetchCriticalNotifications(),
          _getPlanInfo(),
          _getAllPlans(),
          _fetchUnreadNotificationsCount(),
          _loadUserMarketAccess(),
          _lendingDelegate.getLendingConfiguration(_lendingProductType),
          _lendingSummaryWithAutopay,
          checkForBackendDrivenFlows(),
        ],
        eagerError: true,
      );

      if (isClosed) return;

      final customerId = noParamsRequests[0] as String?;
      final savingSpaces = noParamsRequests[1] as List<SavingsAccount>;
      final user = noParamsRequests[2] as User?;
      final lendingGuideStage = noParamsRequests[3] as LendingGuideStage;
      final statuses = noParamsRequests[4] as List<ChequebookDeliveryStatus>;
      final accounts = noParamsRequests[5] as List<AccountDetails>;
      final isLendingPromoCardDismissed = noParamsRequests[6] as bool;
      final notifications = noParamsRequests[7] as List<CriticalNotification>;
      final planInfo = noParamsRequests[8] as SubscriptionPlanInfo?;
      final allPlans = noParamsRequests[9] as List<SubscriptionPlanInfo>;
      final unreadNotifications = noParamsRequests[10] as int;
      final marketAccess = noParamsRequests[11] as UserMarketAccess;
      final lendingConfiguration =
          noParamsRequests[12] as LendingConfiguration?;
      final lendingSummary = noParamsRequests[13] as LendingSummaryWithAutopay?;

      final paramsRequests = await Future.wait(
        [
          _getBrokerProduct(customerId),
          _shouldShowSetUpPasscode(user),
        ],
        eagerError: true,
      );

      if (isClosed) return;

      final brokerProduct = paramsRequests[0] as ProductHubItem?;
      final showPasscodeSetUp = paramsRequests[1] as bool;

      final balances = await _balanceHandler.getBalanceData(
        accounts: accounts,
        brokerProduct: brokerProduct,
        savingSpaces: savingSpaces,
      );
      _logger.debug(
        'Saving spaces : $savingSpaces',
        loggerName: WioDomain.savingSpaces.name,
      );

      final savingsPromo = await balances.accountsBalance.mapOrNull(
        value: (it) => _savingsHandler.getSavingsPromo(
          totalBalance: it.amount,
          savingSpaces: savingSpaces,
        ),
      );

      _logger.debug(
        'Saving spaces promo: $savingsPromo',
        loggerName: WioDomain.savingSpaces.name,
      );
      final coachMarkType = await _getUpdatedCoachMarkStatus(
        balances,
        savingsPromo,
        lendingGuideStage,
      );

      if (isClosed) return;

      _refreshTransactionsStream.add(null);
      unawaited(_updateNPSSEnrollmentStatus(pullToRefresh: !withLoading));

      // To-do items
      final todoData = ToDoData(
        user: user,
        totalBalance: balances.total,
        accounts: accounts,
        savingsAccounts: savingSpaces,
      );
      _fetchToDoItems(todoData).ignore();
      final toDoItems =
          state.mapOrNull(loaded: (loaded) => loaded.toDoItems) ?? [];
      emit(
        DashboardState.loaded(
          user: user,
          accounts: accounts,
          toDoItems: toDoItems,
          balance: balances,
          brokerProduct: brokerProduct,
          featureToggles: _dashboardFeatures,
          savingsPromo: savingsPromo,
          savingSpaces: savingSpaces,
          shouldShowSetUpPasscode: showPasscodeSetUp,
          isDebitRestricted: accounts.isDebitRestricted,
          lendingGuideStage: lendingGuideStage,
          chequebookDeliveryStatuses: statuses,
          coachMarkToShowIfAny: coachMarkType,
          isLendingPromoCardDismissed: isLendingPromoCardDismissed,
          notifications: notifications,
          unreadNotifications: unreadNotifications,
          currentPlan: planInfo,
          allPlans: allPlans,
          easyCashData: _easyCashData,
          marketAccess: marketAccess,
          lendingConfiguration: lendingConfiguration,
          lendingSummary: lendingSummary,
        ),
      );

      unawaited(_fetchIpos());

      if (planInfo != null) {
        final lastUserPlan =
            await _customerSubscriptionInteractor.getCustomerSubscriptionPlan();
        final shouldFetchNewConfiguration = lastUserPlan != planInfo.name;
        if (shouldFetchNewConfiguration) {
          await _saveCustomerPlanType(planInfo.name);
          await _fetchFeatureToggles();
        }
      }

      await _refreshLendingSummary(withLoading);
      await showNPSFeedback();

      // Observe lending summary information
      if (withLoading) {
        _subscribeToLendingSummary();
      }
    } on Object catch (e, stacktrace) {
      _logger.error(
        'Retail dashboard exception',
        error: e,
        stackTrace: stacktrace,
        loggerName: WioDomain.core.name,
      );
      if (e is NoInternetConnectionException || withLoading) {
        _logger.debug(
          e.toString(),
          stackTrace: stacktrace,
          loggerName: WioDomain.core.name,
        );
        safeEmit(const DashboardState.error());
      }
    } finally {
      pageLoadTrace.finish();
    }
  }

  Future<SubscriptionPlanInfo?> _getPlanInfo() async {
    try {
      return await _planInteractor
          .getSubscriptionPlanInfo()
          .timeout(_pricingApiTimeout);
    } on Exception catch (error, stackTrace) {
      _logger.info(
        'Current plan info failed to fetch:',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.pricingPlan.name,
      );

      return null;
    }
  }

  Future<List<SubscriptionPlanInfo>> _getAllPlans() async {
    try {
      return await _planInteractor
          .getSubscriptionPlans()
          .timeout(_pricingApiTimeout);
    } on Exception catch (error, stackTrace) {
      _logger.info(
        'Error in fetching plans:',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.pricingPlan.name,
      );

      return const [];
    }
  }

  Future<void> _fetchToDoItems(ToDoData data) async {
    try {
      final todoItems = await _toDoHandler.getToDoItems(
        data,
        state.mapOrNull(
          loaded: (loaded) => loaded.marketAccess,
        ),
      );
      state.mapOrNull(
        loaded: (loaded) {
          final lendingSummary = loaded.lendingSummary;
          if (lendingSummary == null) {
            safeEmit(loaded.copyWith(toDoItems: todoItems));
          } else {
            safeEmit(
              loaded.copyWith(
                toDoItems: _toDoHandler.updateOnCreditOffers(
                  items: todoItems,
                  canCreateApplication: lendingSummary
                      .applicationEntryPointStatus.canCreateApplication,
                ),
              ),
            );
          }
        },
      );
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Could not fetch To Do items.\n${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );
    }
  }

  Future<List<ChequebookDeliveryStatus>> _fetchCheckbookStatuses() async {
    try {
      final statuses = await _chequebookInteractor.getDeliveryStatus();

      return statuses.where((status) => status.isVisible).toList();
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Could not get Checkbook Delivery statuses.\n${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.payments.name,
      );

      return [];
    }
  }

  Future<NPSSCustomerEnrollmentStatus?> _updateNPSSEnrollmentStatus({
    bool pullToRefresh = false,
  }) async {
    if (!_isNpssEnabled || !pullToRefresh) return null;

    try {
      final model = await _npssInteractor.refreshEnrollmentStatus();

      return model.status;
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Could not get npss enrollments status\n${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.payments.name,
      );

      return null;
    }
  }

  Future<List<CriticalNotification>> _fetchCriticalNotifications() async {
    if (!_isCriticalNotificationsEnabled) return [];

    try {
      final response = await _criticalNotificationInteractor.getNotifications(
        priority: NotificationPriority.critical,
        closed: false,
        size: 10000,
      );

      _criticalNotificationAnalytics.onFetchNotificationsSuccess(
        response.notifications.length,
      );

      return response.notifications;
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Could not get critical notification',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.notification.name,
      );

      return [];
    }
  }

  Future<void> _updateUnreadNotificationsCount() async {
    final unreadNotifications = await _fetchUnreadNotificationsCount();

    state.mapOrNull(
      loaded: (loaded) {
        if (unreadNotifications != loaded.unreadNotifications) {
          safeEmit(loaded.copyWith(unreadNotifications: unreadNotifications));
        }
      },
    );
  }

  Future<void> _updateCriticalNotifications() async {
    final notifications = await _fetchCriticalNotifications();

    state.mapOrNull(
      loaded: (loaded) {
        if (notifications.length != loaded.notifications.length) {
          safeEmit(loaded.copyWith(notifications: notifications));
        }
      },
    );
  }

  Future<int> _fetchUnreadNotificationsCount() async {
    if (!_isCriticalNotificationsEnabled) return 0;

    try {
      return await _criticalNotificationInteractor.unreadCount();
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Could not get unread notifications count',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.notification.name,
      );

      return 0;
    }
  }

  void _refreshTodos(
    CarbonCalculatorState carbonCalculatorState,
  ) {
    state.mapOrNull(
      loaded: (it) {
        final updateItems = carbonCalculatorState.mapOrNull(
          disabled: (_) {
            final toDoItems = it.toDoItems.toList()
              ..add(const ToDoItem.carbonCalculator());

            return toDoItems;
          },
          onboarded: (_) {
            if (it.toDoItems.isNotEmpty) {
              return it.toDoItems
                  .where(
                    (element) => element != const ToDoItem.carbonCalculator(),
                  )
                  .toList();
            }
          },
        );

        // To avoid multiple emits
        if (updateItems != null && it.toDoItems.length != updateItems.length) {
          safeEmit(it.copyWith(toDoItems: updateItems));
        }
      },
    );
  }

  void _observeChequebookState() {
    _chequebookInteractor
        .isFirstChequebookOrderAllowedStream()
        .doOnData((value) {
      if (!isClosed) {
        state.mapOrNull(
          loaded: (state) {
            final todoItems = _toDoHandler.updateOnChequebookRequestSubmit(
              chequebookOrderAllowed: value,
              items: state.toDoItems,
            );
            emit(state.copyWith(toDoItems: todoItems));
          },
        );
      }
    }).listenSafe(this);
  }

  void _observeNpssEnrollmentStatus() {
    if (_isNpssEnabled) {
      _npssInteractor.enrollmentStatusStream.listenSafe(
        this,
        onData: (value) {
          if (!isClosed) {
            state.mapOrNull(
              loaded: (state) {
                final todoItems = _toDoHandler.updateOnNpssEnrollmentsStatus(
                  status: value.status,
                  items: state.toDoItems,
                );
                safeEmit(state.copyWith(toDoItems: todoItems));
              },
            );
          }
        },
        onError: (_) {},
      );
    }
  }

  void _observerCarbonCalculatorState() {
    _stateController.carbonCalculatorStateStream.doOnData((event) {
      if (!isClosed) {
        switch (event) {
          case CarbonCalculatorStateOnBoarded():
          case CarbonCalculatorStateDisabled():
            _refreshTodos(event);
          default:
            break; // Do nothing
        }
      }
    }).listenSafe(this);
  }

  void _listenCriticalNotifications() {
    _criticalNotificationInteractor.refreshNotificationsStream.doOnData((_) {
      _logger.debug(
        '*** Before Updating notifications ***',
        loggerName: WioDomain.notification.value,
      );
      if (isClosed) {
        return;
      }
      _logger.debug(
        '*** Updating notifications ***',
        loggerName: WioDomain.notification.value,
      );
      _updateCriticalNotifications();
    }).listenSafe(this);
  }

  Future<void> _refreshLendingSummary(bool withLoading) async {
    if (!withLoading) {
      //it would be called only on pull to refresh or similar interactions
      await _lendingDelegate.refreshLendingSummary();
    }
  }

  Future<void> _refreshInstallments() async {
    try {
      if (_isSplitTransactionsEnabled) {
        return await _splitTransactionsInteractor.refreshInstallments();
      }
    } on Object catch (e, st) {
      _logger.error(
        'Error while refreshing Installments',
        error: e,
        stackTrace: st,
      );
      return;
    }
  }

  @override
  Future<void> close() {
    _refreshTransactionsStream.close();
    _updateScreenTimer?.cancel();
    _userSubscription?.cancel();
    _easyCashSubscription?.cancel();
    _lendingSummarySubscription?.cancel();
    return super.close();
  }

  void showSetUpPasscodeBottomSheet() {
    _navigationHandler.showSetUpPasscodeBottomSheet();
  }

  void goToCreditDashboard({
    required LoanAccount account,
    required LendingDashboardNavigationEntryPoint entryPoint,
  }) {
    state.mapOrNull(
      loaded: (loadedState) {
        _navigationHandler.navigateToCreditDashboard(
          account: account,
          entryPoint: entryPoint,
        );
      },
    );
  }

  void goToLoanDashboard({
    required LoanAccount account,
  }) {
    state.mapOrNull(
      loaded: (_) => _navigationHandler.navigateToLoanDashboard(
        account: account,
      ),
    );
  }

  Future<void> showNPSFeedback() async {
    if (!isNpsFeedbackEnabled) return;

    await _navigationHandler.pushNpsFeedback();
  }

  void showAutopayFromSavingSpaceInfo(DateTime autopayDate) {
    state.mapOrNull(
      loaded: (it) => _navigationHandler
          .showAutopayFromSavingSpaceInfoBottomSheet(autopayDate),
    );
  }

  Future<void> goToSpend() {
    return _navigationHandler.navigateToSpend();
  }

  Future<void> goToSpendTab() {
    return _navigationHandler.navigateToSpendTab();
  }

  Future<void> updateLendingGuideProgress(
    LendingGuideStage updatedStage,
  ) async {
    await state.mapOrNull(
      loaded: (loaded) async {
        final stage =
            await _lendingDelegate.updateLendingGuideStage(updatedStage);
        safeEmit(loaded.copyWith(lendingGuideStage: stage));
      },
    );
  }

  void navigateToCurrencyExchange() {
    state.mapOrNull(
      loaded: (state) => _navigationHandler.navigateToCurrencyExchange(
        state.accounts,
      ),
    );
  }

  void navigateToPayments() {
    state.mapOrNull(
      loaded: (state) => _navigationHandler.navigateToPaymentsV2(),
    );
  }

  void navigateToBroker() {
    state.mapOrNull(
      loaded: (state) async {
        try {
          loading(true);
          final customerId = await _customerIdInteractor.getCustomerId();
          if (customerId == null) {
            throw Exception('Customer id cannot be null');
          }
          final broker =
              await _brokerProductInteractor.getBroker(customerId: customerId);

          if (isClosed) return;

          emit(state.copyWith(brokerProduct: broker));
          await _navigationHandler.startProduct(broker, customerId);
          _updateScreen();
        } on Object catch (error) {
          _logger.debug(error.toString(), loggerName: WioDomain.broker.name);
          _commonErrorHandler.handleError(error);
        } finally {
          loading(false);
        }
      },
    );
  }

  void navigateToIPO() {
    _analytics.exploreIpo();
    if (_isIpoEnabled) {
      _navigationHandler.navigateToIPO();
    }
  }

  void navigateToSavingSpace() {
    // NOTE: we need to show the loading indicator because the saving space
    // flow may load subscription plans for its tutorials.
    _navigationHandler
        .navigateToSavingSpace()
        .toStream()
        .withLoading(this)
        .complete();
  }

  void changeSubscriptionPlan() {
    _navigationHandler.navigateToPlanSelection();
  }

  void navigateToAllTransactions() {
    _navigationHandler.navigateToAllTransactions();
  }

  void navigateToSettings() => _navigationHandler.navigateToSettings();

  void navigateToCashDeposits() => _navigationHandler.navigateToCashDeposits();

  void navigateToChequeDeposits() =>
      _navigationHandler.navigateToChequeDeposits();

  void navigateToRequestMoney() => _navigationHandler.navigateToRequestMoney();

  Future<void> navigateToAddMoney() async {
    final showSavingSpace = state.maybeMap(
      loaded: (it) => it.savingSpaces.isNotEmpty,
      orElse: () => false,
    );

    final result = await _navigationHandler.showAddMoneyBottomSheet(
      showSavingSpace: showSavingSpace,
    );

    result?.mapOrNull(
      bankTransfer: (_) => navigateToAccountDetails(),
      savingSpace: (_) => navigateToSavingSpace(),
      chequeDeposit: (_) => navigateToChequeDeposits(),
      requestMoney: (_) => navigateToRequestMoney(),
    );
  }

  void navigateToAccountDetails() {
    state.mapOrNull(
      loaded: (state) => _navigationHandler.navigateToAccountDetails(
        state.accounts,
      ),
    );
  }

  void openAccountsBottomSheet() {
    state.mapOrNull(
      loaded: (state) async {
        final result = await _navigationHandler.openAccountsBottomSheet(
          state.accounts,
          state.accountsBalance,
          state.balance.foreignAccountsInAed,
          state.easyCashData,
        );

        await result?.map(
          pickedAccount: (it) {
            _navigationHandler.navigateToAccount(state.accounts, it.account);
          },
          openNewAccount: (it) async {
            final result = await _navigationHandler.openNewAccountBottomSheet(
              it.currencies,
            );

            result?.mapOrNull(
              checkAccount: (it) {
                assert(
                  it.accounts.contains(it.newAccount),
                  'Account ${it.newAccount.id} is not '
                  'in the provided account list',
                );

                final updatedBalance = state.balance.copyWith(
                  foreignAccountsInAed: {
                    ...state.balance.foreignAccountsInAed,
                    it.newAccount.id: _emptyBalanceInAed,
                  },
                );

                emit(
                  state.copyWith(
                    accounts: it.accounts,
                    balance: updatedBalance,
                  ),
                );
                _navigationHandler.navigateToAccount(
                  it.accounts,
                  it.newAccount,
                );
              },
            );
          },
        );
      },
    );
  }

  Future<void> onPassportVerificationCardPressed() async {
    final result = await _passportVerificationFlowService.runWithoutPrompt();

    if (result == PassportVerificationFlowResult.featureToggleDisabled) {
      return;
    }

    if (result == PassportVerificationFlowResult.ocrFailure) {
      return;
    }

    if (state is! DashboardLoadedState) {
      return;
    }

    final accounts = await _accountInteractor.getAccountDetails();

    state.mapOrNull(
      loaded: (loadedState) {
        emit(
          loadedState.copyWith(
            toDoItems: _toDoHandler.filter<ToDoItem, PassportVerificationItem>(
              loadedState.toDoItems,
            ),
            isDebitRestricted: accounts.isDebitRestricted,
          ),
        );
      },
    );
  }

  void changePasscode() {
    state.mapOrNull(
      loaded: (state) async {
        try {
          final result = await _toDoHandler.changePasscode();
          if (isClosed) return;
          if (result) {
            emit(
              state.copyWith(
                toDoItems: _toDoHandler.filter<ToDoItem, PasscodeForgottenItem>(
                  state.toDoItems,
                ),
              ),
            );
          }
        } on Object catch (error) {
          _logger.debug(error.toString(), loggerName: WioDomain.identity.name);
          _commonErrorHandler.handleError(error);
        }
      },
    );
  }

  void customerFeedbackPressed() {
    state.mapOrNull(
      loaded: (state) async => _toDoHandler.customerFeedbackFlow(),
    );
  }

  Future<void> onInboxButtonPressed() async {
    await _navigationHandler.navigateToCriticalNotifications();
    _updateUnreadNotificationsCount().ignore();
    _updateCriticalNotifications().ignore();
  }

  void requiredInformationPressed() {
    state.mapOrNull(
      loaded: (state) async {
        final rfiList = _kycInteractor.getRfiList();
        if (rfiList != null && rfiList.isNotEmpty) {
          final updatedRfiList = await _rfiHandler.handleRfi(
            rfiList,
          );

          if (updatedRfiList.isEmpty) {
            return;
          }

          final submittedCount = updatedRfiList.fold(0, (acc, model) {
            if (model.submitted) {
              acc++;
            }

            return acc;
          });

          if (submittedCount == rfiList.length) {
            emit(
              state.copyWith(
                toDoItems: _toDoHandler
                    .filter<ToDoItem, RequiredInformationItem>(state.toDoItems),
              ),
            );
            _toastMessageProvider.showToastMessage(
              NotificationToastMessageConfiguration.success(
                _dashboardLocalizations.rfiSubmitted,
              ),
            );
          } else {
            _kycInteractor.updateRfiList(updatedRfiList);
          }
        } else {
          _commonErrorHandler
              .handleError(_commonLocalizations.common_error_message);
        }
      },
    );
  }

  void updateEmiratesIdPressed() {
    state.mapOrNull(
      loaded: (state) async {
        final isUpdated = await _navigationHandler.navigateToEmiratesIdUpdate();
        _onUpdateExpiredEmiratesId(isUpdated);
      },
    );
  }

  void _onUpdateExpiredEmiratesId(bool? updated) {
    if (updated == null) {
      return;
    }
    state.mapOrNull(
      loaded: (state) async {
        if (updated) {
          emit(
            state.copyWith(
              toDoItems: _toDoHandler
                  .filter<ToDoItem, UpdateEmiratesIdItem>(state.toDoItems),
            ),
          );
          _toastMessageProvider.showToastMessage(
            NotificationToastMessageConfiguration.success(
              _dashboardLocalizations.emiratesIdUpdatedSuccessfully,
            ),
          );
        }
      },
    );
  }

  void customerSurveyPressed() {
    state.mapOrNull(
      loaded: (state) async {
        final result = await _toDoHandler.customerSurvey();

        if (result) {
          safeEmit(
            state.copyWith(
              toDoItems: _toDoHandler.filter<ToDoItem, CustomerSurvey>(
                state.toDoItems,
              ),
            ),
          );
        }
      },
    );
  }

  void onNotificationPressed(CriticalNotification notification) {
    state.mapOrNull(
      loaded: (loaded) async {
        _readNotification(notification).ignore();

        await _navigationHandler.showNotificationDetailsBottomSheet(
          notification,
          _currentScreen,
        );
      },
    );
  }

  void closeNotification(CriticalNotification notification) {
    state.mapOrNull(
      loaded: (loaded) async {
        if (await _closeNotification(notification)) {
          safeEmit(
            loaded.copyWith(
              notifications: List.from(loaded.notifications)
                ..remove(notification),
            ),
          );
        }
      },
    );
  }

  void verifyEmail() {
    state.mapOrNull(
      loaded: (state) async {
        try {
          final user = state.user;
          if (user == null) {
            return;
          }
          loading(true);
          final isSuccess = await _toDoHandler.verifyEmail(user);
          if (!isSuccess) throw Exception('Email verification failed');

          _logger.debug(
            'Email verified.',
            loggerName: WioDomain.identity.name,
          );
          await _refreshLendingSummary(false);
          safeEmit(
            state.copyWith(
              user: state.user?.copyWith(isEmailVerified: true),
              toDoItems: _toDoHandler.filter<ToDoItem, EmailVerificationItem>(
                state.toDoItems,
              ),
            ),
          );
        } on Object catch (error, stackTrace) {
          _logger.debug(
            error.toString(),
            stackTrace: stackTrace,
            loggerName: WioDomain.identity.name,
          );

          _commonErrorHandler.handleError(error);
        } finally {
          _navigationHandler.popUntilFirstRoute();
          loading(false);
        }
      },
    );
  }

  Future<void> onEnableBiometryStatus() async {
    try {
      if (await _toDoHandler.isAvailableBiometric()) {
        loading(true);
        await _toDoHandler.upsertBiometrics();

        if (isClosed) return;

        state.mapOrNull(
          loaded: (state) => emit(
            state.copyWith(
              toDoItems: _toDoHandler.filter<ToDoItem, BiometricsItem>(
                state.toDoItems,
              ),
            ),
          ),
        );
        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.success(
            _dashboardLocalizations.biometryEnabledToastMessage,
          ),
        );
      } else {
        await _toDoHandler.openDeviceSettings();
      }
    } on Object catch (error) {
      _logger.debug(error.toString(), loggerName: WioDomain.identity.name);
      _commonErrorHandler.handleError(error);
    } finally {
      loading(false);
    }
  }

  Future<void> onSetupIdentity() async {
    final result = await _navigationHandler.navigateToLivenessFeature();

    result.map(
      success: (_) {
        state.mapOrNull(
          loaded: (state) => emit(
            state.copyWith(
              toDoItems:
                  _toDoHandler.filter<ToDoItem, EFRSetupItem>(state.toDoItems),
            ),
          ),
        );

        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.success(
            _dashboardLocalizations.efrUploadSuccessfully,
          ),
        );
      },
      failed: (value) {
        if (value.code == LivenessChallengeErrorCode.onBackPressed) {
          return;
        }
        _commonErrorHandler
            .handleError(_commonLocalizations.common_error_message);
      },
    );
  }

  Future<void> handleApplicationEntryPointTap() async {
    await state.mapOrNull(
      loaded: (value) => _lendingDelegate
          .handleApplicationEntryPointInteraction(value.lendingSummary),
    );
  }

  void handleCreditDecisionInProgressCtaPressed(
    BannerInfo bannerInfo,
    LendingApplication application,
  ) {
    state.mapOrNull(
      loaded: (value) =>
          _lendingDelegate.handleCreditDecisionInProgressCtaPressed(
        bannerInfo,
        application,
      ),
    );
  }

  void onProcessingPersonalLoanTap() =>
      _navigationHandler.showProcessingLoanAccountBottomSheet();

  void onCreditToDoPressed() {
    handleApplicationEntryPointTap().then(
      (_) =>
          _refreshLendingSummary(false).asStream().withLoading(this).complete(),
    );
  }

  void onChequebookOrderPressed() {
    state.mapOrNull(
      loaded: (state) async {
        loading(true);
        try {
          final accountId = state.accounts.aedAccountId;
          if (accountId != null) await _chequebookHandler.init(accountId);
        } on Exception catch (e, st) {
          _logger.error(
            'Chequebook order init exception',
            error: e,
            stackTrace: st,
            loggerName: WioDomain.payments.name,
          );
        } finally {
          loading(false);
        }
      },
    );
  }

  void onSavingsPromoPressed() {
    return state.maybeMap<void>(
      loaded: (state) => state.savingsPromo?.map(
        noMoney: (it) => it.status.map(
          active: (_) => navigateToAccountDetails(),
          inactive: (_) => _navigationHandler.navigateToSubscriptionPlans(),
        ),
        noSavings: (it) => it.status.map(
          active: (_) => _handleCreateSavingSpace(),
          inactive: (_) => _navigationHandler.navigateToSubscriptionPlans(),
        ),
        notEnoughSavings: (it) => it.status.map(
          active: (_) => _handleSelectSavingSpace(
            // Won't be empty at this stage
            state.savingSpaces.whereType<SavingSpace>().toList(),
          ),
          inactive: (_) => _navigationHandler.navigateToSubscriptionPlans(),
        ),
        applied: (it) => it.status.map(
          active: (_) => onSavingsPromoViewed(),
          inactive: (_) => _navigationHandler.navigateToSubscriptionPlans(),
        ),
      ),
      orElse: () {},
    );
  }

  void onSavingsPromoViewed() {
    return state.maybeMap<void>(
      loaded: (it) {
        final promo = it.savingsPromo;
        if (promo != null && promo.highlighted) {
          emit(it.copyWith(savingsPromo: promo.copyWith(highlighted: false)));
          _savingsHandler.hideSavingsPromo(promo);
          getNextCoachMarkIfAny();
        }
      },
      orElse: () {},
    );
  }

  Future<void> onRestrictedDebitPressed() =>
      _navigationHandler.showRestrictedDebitBottomSheet();

  void onTabChanged(DashboardTab tab) {
    state.mapOrNull(
      loaded: (_) async {
        _lendingDelegate.trackDashboardTabSwitched(tab);
        await _updateNextCoachMarkIfNeeded(tab: tab);
      },
    );
  }

  Future<void> onReferralsToDoPressed() {
    return _navigationHandler.navigateToReferrals();
  }

  void onCarbonCalculatorToDoPressed() {
    return _navigationHandler.navigateToCarbonCalculator();
  }

  void onInstantPaymentTodoPressed() {
    _navigationHandler.navigateToInstantPayments();
    _observeNpssEnrollmentStatus();
  }

  Future<void> onCryptoToDoPressed() async {
    _exhaustStreamExecutor.run(
      () => Stream<void>.fromFuture(
        _onCryptoToDoPressed(),
      ).withLoading(this),
    );
  }

  Future<void> _onCryptoToDoPressed() async {
    final isAddressValid = await _isAddressValid();

    if (!isAddressValid) {
      return;
    }
    return _navigateToCryptoOnboarding();
  }

  Future<void> onWealthUnqualifiedToDoPressed() =>
      _wealthManagementStarterHandler.startWealthManagement();

  Future<bool> _isAddressValid() async {
    try {
      final address = await _customerAddressInteractor.getPrimaryAddress();

      if (address != null) {
        return true;
      }

      final result = await _navigationHandler.navigateToAddress();

      // extra fail safe to ensure that the address is saved in BE
      final checkAddress = await _customerAddressInteractor.getPrimaryAddress();

      if (result is AddressSuccessResult && checkAddress != null) {
        return true;
      }
      return false;
    } on Object catch (error) {
      _logger.debug(error.toString(), loggerName: WioDomain.broker.name);
      _commonErrorHandler.handleError(error);
      return false;
    }
  }

  Future<void> onUaeSecuritiesToDoPressed() async {
    _exhaustStreamExecutor.run(
      () => Stream<void>.fromFuture(
        _onUaeSecuritiesToDoPressed(),
      ).withLoading(this),
    );
  }

  Future<void> _onUaeSecuritiesToDoPressed() async {
    final isAddressValid = await _isAddressValid();

    if (!isAddressValid) {
      return;
    }
    return _handlePassportForUaeOnboarding();
  }

  Future<void> _navigateToCryptoOnboarding() async {
    state.mapOrNull(
      loaded: (state) =>
          _navigationHandler.navigateToCryptoOnboarding(state.marketAccess),
    );
  }

  Future<void> _handlePassportForUaeOnboarding() async {
    final info = await _kycInteractor.getCustomerScreeningInfo();
    final passportStatus = info.screeningStatusOnPassport;

    if (passportStatus == PassportVerificationStatus.approved) {
      return _handleEidForUaeOnboarding();
    }

    final passportFlowResult =
        await _navigationHandler.navigateToPassportFlow();

    if (passportFlowResult == PassportVerificationFlowResult.success) {
      return _handleEidForUaeOnboarding();
    }
  }

  Future<void> _handleEidForUaeOnboarding() async {
    if (_shouldShowModals) {
      final result = await safeExecute(
        _modalDelegatesRunner.run(
          delegates: [
            _updateEmiratesIdDelegate,
          ],
        ),
      );

      if (result is EidUpdateResult) {
        final updated = result.updated;
        if (updated == null) {
          return;
        }
        _onUpdateExpiredEmiratesId(updated);

        if (updated) {
          return _navigateToUaeOnboarding();
        }
      }
      if (result is VoidResult) {
        return _navigateToUaeOnboarding();
      }
    }
  }

  Future<void> _navigateToUaeOnboarding() async {
    state.mapOrNull(
      loaded: (state) =>
          _navigationHandler.navigateToUaeOnboarding(state.marketAccess),
    );
  }

  void onOpenTermDeposit() {
    state.mapOrNull(
      loaded: (it) {
        assert(
          it.toDoItems.contains(const ToDoItem.termDeposit()),
          'TODO item for term deposit is not available',
        );

        _navigationHandler.navigateToTermDepositCreation();
      },
    );
  }

  void initIfConnected() {
    if (_hasConnection()) init(withLoading: false);
  }

  bool _hasConnection() {
    try {
      return _connectivityInteractor.isConnected ?? false;
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Error checking internet connection: ${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );

      return false;
    }
  }

  Future<void> dismissLendingPromoCard() async {
    await state.mapOrNull(
      loaded: (loadedState) async {
        await _lendingDelegate.dismissLendingPromoCard();

        emit(loadedState.copyWith(isLendingPromoCardDismissed: true));
      },
    );
  }

  Future<void> showCreditTutorial() => _lendingDelegate.showCreditTutorial();

  Future<void> getNextCoachMarkIfAny() async {
    // Delay is needed as in next rendering cycle pipeline will be updating
    // the position of existing widget and after the positions gets updated
    // then new coach mark will be displayed. Currently during the update
    // process of widgets position, target and follower shown wrongly.
    Future<void>.delayed(
      const Duration(milliseconds: 300),
      _updateNextCoachMarkIfNeeded,
    );
  }

  Future<void> _updateNextCoachMarkIfNeeded({
    DashboardTab tab = DashboardTab.myMoney,
  }) async {
    final currentState = state.mapOrNull(
      loaded: (it) => it,
    );
    if (currentState == null) return;

    final coachMarkType = await _getUpdatedCoachMarkStatus(
      currentState.balance,
      currentState.savingsPromo,
      currentState.lendingGuideStage,
    );
    safeEmit(
      currentState.copyWith(
        coachMarkToShowIfAny: coachMarkType,
        currentTab: tab,
      ),
    );
  }

  Future<List<AccountDetails>> _getAccountDetail() async {
    try {
      return await _accountInteractor.getAccountDetails();
    } on Object catch (error) {
      _logger.debug(error.toString(), loggerName: WioDomain.accounts.name);
      if (error is NoInternetConnectionException) {
        rethrow;
      }

      return [];
    }
  }

  void _observeAccounts() {
    _accountInteractor.observeAccounts().doOnError((error, st) {
      _logger.error(
        error.toString(),
        error: error,
        stackTrace: st,
        loggerName: WioDomain.accounts.name,
      );
    }).listenSafe(
      this,
      onData: (accounts) {
        state.mapOrNull(
          loaded: (it) async {
            // Need to fetch new balance data and
            // update dashboard state accordingly
            final balances = await _balanceHandler.getBalanceData(
              accounts: accounts,
              brokerProduct: it.brokerProduct,
              savingSpaces: it.savingSpaces,
            );

            safeEmit(it.copyWith(accounts: accounts, balance: balances));
          },
        );
      },
      onError: (_) {},
    );
  }

  Future<void> _readNotification(CriticalNotification notification) async {
    try {
      if (notification.read == true) return;

      final read = await _criticalNotificationInteractor.read(
        [notification.id],
      );

      if (!read) throw Exception('Could not read notification');
      _criticalNotificationAnalytics.readNotification(
        notification: notification,
        fromScreen: _currentScreen,
        isSuccess: true,
      );

      state.mapOrNull(
        loaded: (loaded) => safeEmit(
          loaded.copyWith(
            notifications: loaded.notifications
                .map(
                  (item) => item.id == notification.id
                      ? notification.copyWith(read: true)
                      : item,
                )
                .toList(),
          ),
        ),
      );
      _updateUnreadNotificationsCount().ignore();
    } on Object catch (error, stackTrace) {
      _criticalNotificationAnalytics.readNotification(
        notification: notification,
        fromScreen: _currentScreen,
        isSuccess: false,
      );
      _logger.error(
        '${notification.id}\n${error.toString()}',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  Future<bool> _closeNotification(CriticalNotification notification) async {
    try {
      loading(true);
      if (!await _criticalNotificationInteractor.close(notification.id)) {
        throw Exception('Could not close notification');
      }
      _criticalNotificationAnalytics.closeNotification(
        notification: notification,
        fromScreen: _currentScreen,
        isSuccess: true,
      );

      return true;
    } on Object catch (error, stackTrace) {
      _criticalNotificationAnalytics.closeNotification(
        notification: notification,
        fromScreen: _currentScreen,
        isSuccess: false,
      );
      _logger.debug(
        'Could not close notification with id: ${notification.id}\n'
        '${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.notification.name,
      );
      _commonErrorHandler.handleError(error);

      return false;
    } finally {
      loading(false);
    }
  }

  Future<ProductHubItem?> _getBrokerProduct(String? customerId) async {
    try {
      final broker = await _brokerProductInteractor.getBrokerFromCache();

      if (broker != null) {
        return broker;
      } else {
        if (customerId == null) return null;
        return _brokerProductInteractor.getBroker(customerId: customerId);
      }
    } on Object catch (error) {
      _logger
        ..debug(
          'Cannot get products by customerId',
          loggerName: WioDomain.core.name,
        )
        ..debug(
          error.toString(),
          loggerName: WioDomain.core.name,
        );
      if (error is NoInternetConnectionException) {
        rethrow;
      }

      return null;
    }
  }

  Future<bool> _shouldShowSetUpPasscode(User? user) async {
    final isFirstSession = _userInteractor.isFirstSession();
    final hasPasscodeStatus = user?.hasPasscodeFactor ?? false;
    final showPasscodeSetUp = !hasPasscodeStatus && !isFirstSession;

    return showPasscodeSetUp;
  }

  Future<EducationalInfoType> _getUpdatedCoachMarkStatus(
    DashboardBalanceData balance,
    SavingsPromo? promo,
    LendingGuideStage? lendingGuideStage,
  ) async {
    final accountBalance = balance.accountsBalance.mapOrNull(
      value: (it) => it.amount,
    );
    _logger
      ..debug(
        'accountBalance: $accountBalance',
        loggerName: WioDomain.core.name,
      )
      ..debug(
        'lendingGuideStage: $lendingGuideStage',
        loggerName: WioDomain.core.name,
      );

    try {
      final coachMarkType = await _coachMarkHandler.getCoachMarkVisibility(
        savingsPromo: promo,
        lendingGuideStage: lendingGuideStage,
      );

      _logger.debug(
        'possible coachMark if any: $coachMarkType',
        loggerName: WioDomain.core.name,
      );

      return coachMarkType;
    } on Object {
      return EducationalInfoType.none;
    }
  }

  void _updateScreen() {
    _updateScreenTimer?.cancel();
    _updateScreenTimer = Timer(
      const Duration(seconds: 2),
      () => init(withLoading: false),
    );
  }

  void _handleCreateSavingSpace() => _navigationHandler
      .navigateToSavingSpaceCreation()
      .toStream()
      .withLoading(this)
      .whereNotNull()
      .doOnData((_) => _updateScreen())
      .asyncMap(
        (result) => result.mapOrNull(
          addMoney: (it) => _handleAddMoneyToSavingSpace(it.savingSpace),
          showDetails: (it) => _navigationHandler.navigateToSavingSpaceDetails(
            it.savingSpace,
          ),
        ),
      )
      .complete();

  void _handleSelectSavingSpace(List<SavingSpace> spaces) => _navigationHandler
      .navigateToSavingSpaceSelection(spaces)
      .toStream()
      .whereNotNull()
      .asyncMap(
        (result) => result.map(
          createNew: (_) => _handleCreateSavingSpace(),
          select: (it) => _handleAddMoneyToSavingSpace(it.space),
        ),
      )
      .complete();

  void _handleAddMoneyToSavingSpace(SavingSpace space) => _navigationHandler
      .navigateToAddMoneyToSavingSpace(space)
      .toStream()
      .whereNotNull()
      .doOnData((_) => _updateScreen())
      .asyncMap(
        (result) => result.mapOrNull(
          showDetails: (_) =>
              _navigationHandler.navigateToSavingSpaceDetails(space),
        ),
      )
      .complete();

  void _listenForUpdateEvents() {
    Rx.merge([
      _navigationHandler.fxNotificationChannel.fxEventStream.mapTo(null),
      _navigationHandler.ipoNotificationChannel.ipoEventStream.mapTo(null),
      _navigationHandler.onboardingNotificationChannel.onboardingEventStream
          .mapTo(null),
      _navigationHandler.accountNotificationChannel.accountEventStream,
    ]).listenSafe(
      this,
      onData: (_) {
        _logger.debug(
          'Reloading dashboard data',
          loggerName: WioDomain.core.name,
        );
        _updateScreen();
      },
    );
  }

  void _listenForPendingInvitationTrigger() {
    stream
        .whereType<DashboardLoadedState>()
        .take(1) // just once
        .exhaustMap(
          (_) => _familyBankingHandler
              .getPendingInvitation()
              .asStream()
              .whereNotNull()
              .where((_) => !isClosed && state is DashboardLoadedState)
              .asyncMap(_familyBankingHandler.showPendingInvitation),
        )
        .listenSafe(this);
  }

  void _handleEasyCashData(EasyCashData? easyCashData) {
    if (easyCashData != null) {
      state.mapOrNull(
        loaded: (it) {
          safeEmit(
            it.copyWith(
              easyCashData: easyCashData,
            ),
          );
        },
      );
    }
  }

  Future<void> _saveCustomerPlanType(String planName) async {
    try {
      await _customerSubscriptionInteractor
          .saveCustomerSubscriptionPlan(planName);
      _reportingConfigurator.setUserProperties(
        property: 'customer_plan',
        value: planName,
      );
    } on Object catch (ex, stackTrace) {
      _logger.error(
        'Failed to save the customer plan',
        error: ex,
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );
    }
  }

  Future<void> _fetchIpos() async {
    try {
      await state.mapOrNull(
        loaded: (it) async {
          final ipos = await _ipoInteractor.getIpos();
          safeEmit(
            it.copyWith(isAnyActiveIpos: ipos.upcomingIpos.isNotEmpty),
          );
        },
      );
    } on Object catch (ex, stackTrace) {
      _logger.error(
        'Failed to fetch IPOs',
        error: ex,
        stackTrace: stackTrace,
        loggerName: WioDomain.broker.name,
      );
    }
  }

  Future<UserMarketAccess> _loadUserMarketAccess() async {
    try {
      return await _brokerUserInteractor.getUserMarketAccess();
    } on Object catch (_) {
      _logger.debug(
        'Error fetching user features',
        loggerName: WioDomain.core.name,
      );
      return const UserMarketAccess(
        usMarketStatus: UserFeatureStatus.unknown,
        uaeMarketStatus: UserFeatureStatus.unknown,
        cryptoMarketStatus: UserFeatureStatus.unknown,
      );
    }
  }

  /// This will check if the user has come pending actions to complete
  /// or any account restriction.
  /// If the user has, the BE will return the BE-driven flow
  @visibleForTesting
  Future<void> checkForBackendDrivenFlows() async {
    if (!_isAccountRestrictionFlowEnabled) return;

    try {
      final flow = await _backendDrivenFlowInteractor.getUserActionFlowSteps();
      if (flow.isNotEmpty) {
        await _navigationHandler.navigateToBackendDrivenFlow(flow);
      }
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Error while fetching backend driven flows',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.acquisition.name,
      );
    }
  }

  Future<void> _fetchFeatureToggles() async {
    try {
      await _featureToggles.fetchConfigurations();
    } on Object catch (ex, stackTrace) {
      _logger.error(
        "Can't fetch Remote config",
        error: ex,
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );
    }
  }

  void _subscribeToLendingSummary() {
    _lendingSummarySubscription?.cancel();
    _lendingSummarySubscription = _lendingDelegate
        .observeLendingSummary()
        .skip(1)
        .distinct()
        .doOnError((error, st) {
          _logger.error(
            error.toString(),
            error: error,
            stackTrace: st,
            loggerName: WioDomain.lending.name,
          );
        })
        .map(
          (data) => state.mapOrNull(
            loaded: (state) {
              final lendingSummary = data.content;

              final updatedToDoItems = _toDoHandler.updateOnCreditOffers(
                items: state.toDoItems,
                canCreateApplication: lendingSummary
                        ?.applicationEntryPointStatus.canCreateApplication ??
                    false,
              );

              return state.copyWith(
                toDoItems: updatedToDoItems,
                lendingSummary: lendingSummary,
              );
            },
          ),
        )
        .whereNotNull()
        .listen(safeEmit);
  }

  Future<void> _checkForRequiredAction() async {
    try {
      if (_isRequiredActionsEnabled) {
        await safeExecute(_requiredActionsInteractor.fetchRequiredAction())
            .then((requiredAction) {
          if (requiredAction != null && !isClosed) {
            _navigationHandler.navigateToRequiredAction(requiredAction);
          }
        });
      }
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Failed to fetch required actions',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  Future<LendingSummaryWithAutopay?> get _lendingSummaryWithAutopay async {
    try {
      final data = await _lendingDelegate.observeLendingSummary().firstOrNull;

      return data?.content;
    } on Exception catch (error, stackTrace) {
      _logger.error(
        'Error while fetching lending summary',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.lending.name,
      );

      return null;
    }
  }
}
