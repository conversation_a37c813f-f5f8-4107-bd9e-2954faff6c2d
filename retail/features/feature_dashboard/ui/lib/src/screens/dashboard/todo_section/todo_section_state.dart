import 'package:feature_dashboard_ui/src/screens/dashboard/models/to_do_item.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_api/models/account_details.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_user_api/domain/model/user.dart';

part 'todo_section_state.freezed.dart';

@freezed
class TodoSectionState with _$TodoSectionState {
  const TodoSectionState._();

  const factory TodoSectionState.loading() = TodoSectionLoadingState;

  const factory TodoSectionState.loaded({
    required List<ToDoItem> toDoItems,
    User? user,
    Money? totalBalance,
    @Default(<AccountDetails>[]) List<AccountDetails> accounts,
    @Default(<SavingsAccount>[]) List<SavingsAccount> savingsAccounts,
    UserMarketAccess? userMarketAccess,
  }) = TodoSectionLoadedState;

  const factory TodoSectionState.error() = TodoSectionErrorState;
}

extension TodoSectionLoadedStateExtensions on TodoSectionLoadedState {
  String? get aedAccountId => accounts.aedAccountId;

  bool get hasDebitRestrictions => accounts.isDebitRestricted;
}
