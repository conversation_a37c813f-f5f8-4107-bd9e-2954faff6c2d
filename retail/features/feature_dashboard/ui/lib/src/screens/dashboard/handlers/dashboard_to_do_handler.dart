// ignore_for_file: long-method, deprecated_member_use

import 'package:collection/collection.dart';
import 'package:common_bottom_sheet_api/common_bottom_sheet_api.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:feature_dashboard_ui/l10n/dashboard_localization.g.dart';
import 'package:feature_dashboard_ui/src/common/constants.dart';
import 'package:feature_dashboard_ui/src/common/extensions.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/models/to_do_item.dart';
import 'package:feature_device_identity_api/index.dart';
import 'package:feature_neobroker_onboarding_api/index.dart';
import 'package:feature_todo_actions_api/feature_todo_actions_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:neobroker_models/models.dart';
import 'package:portfolio_api/index.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_wealth_management_api/feature_toggles/wealth_management_feature_toggles.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_api/models/account_details.dart';
import 'package:wio_feature_biometrics_api/biometrics_api.dart';
import 'package:wio_feature_carbon_calculator_api/carbon_calculator_api.dart';
import 'package:wio_feature_customer_feedback_api/index.dart';
import 'package:wio_feature_email_api/index.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_passcode_api/index.dart';
import 'package:wio_feature_pricing_plan_api/domain/pricing_plan_interactor.dart';
import 'package:wio_feature_referrals_api/feature_toggles/referrals_feature_toggles.dart';
import 'package:wio_feature_referrals_api/index.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_user_api/domain/model/factor_config.dart'
    as factor_model;
import 'package:wio_feature_user_api/index.dart';

part 'dashboard_to_do_handler.freezed.dart';

enum FeatureToggleToDoItems {
  passcodeForgotten,
  emailVerification,
  biometrics,
  setupEFR,
  referrals,
  customerFeedback,
  customerSurvey,
  emiratesIdUpdate,
  instantTransfers,
  uaeSecurities,
  rfi,
  npsFeedback,
  crypto,
  wealthManagement,
}

class DashboardToDoHandler {
  final BindDeviceInteractor _bindDeviceInteractor;
  final BiometricInteractor _biometricInteractor;
  final CarbonCalculatorOnboardingInteractor
      _carbonCalculatorOnboardingInteractor;
  final ChequebookInteractor _chequebookInteractor;
  final CustomerFeedbackInteractor _customerFeedbackInteractor;
  final EmailInteractor _emailInteractor;
  final KycInteractor _kycInteractor;
  final NPSSInteractor _npssInteractor;
  final PricingPlanInteractor _pricingInteractor;
  final ReferralsInteractor _referralsInteractor;
  final TodoActionsInteractor _todoActionsInteractor;
  final UserInteractor _userInteractor;
  // ignore: deprecated_member_use
  final BottomSheetProvider _bottomSheetProvider;
  final CompanyPermissionResolver _permissionResolver;
  final CommonLocalizations _commonLocalizations;
  final NavigationProvider _navigationProvider;
  final PlatformInfo _platformInfo;
  final FeatureToggleProvider _featureToggleProvider;
  final Logger _logger;
  final DashboardLocalizations _dashboardLocalizations;
  final BrokerUserInteractor _brokerUserInteractor;
  final FeatureEligibilityProvider _featureEligibilityProvider;

  DashboardToDoHandler({
    required TodoActionsInteractor todoActionsInteractor,
    // ignore: deprecated_member_use
    required BottomSheetProvider bottomSheetProvider,
    required CompanyPermissionResolver permissionResolver,
    required ReferralsInteractor referralsInteractor,
    required CommonLocalizations commonLocalizations,
    required BiometricInteractor biometricInteractor,
    required NavigationProvider navigationProvider,
    required EmailInteractor emailInteractor,
    required UserInteractor userInteractor,
    required PlatformInfo platformInfo,
    required KycInteractor kycInteractor,
    required NPSSInteractor npssInteractor,
    required FeatureToggleProvider featureToggleProvider,
    required Logger logger,
    required CustomerFeedbackInteractor customerFeedbackInteractor,
    required DashboardLocalizations dashboardLocalizations,
    required BindDeviceInteractor bindDeviceInteractor,
    required CarbonCalculatorOnboardingInteractor
        carbonCalculatorOnboardingInteractor,
    required ChequebookInteractor chequebookInteractor,
    required PricingPlanInteractor pricingInteractor,
    required BrokerUserInteractor brokerUserInteractor,
    required FeatureEligibilityProvider featureEligibilityProvider,
  })  : _todoActionsInteractor = todoActionsInteractor,
        _bottomSheetProvider = bottomSheetProvider,
        _permissionResolver = permissionResolver,
        _referralsInteractor = referralsInteractor,
        _commonLocalizations = commonLocalizations,
        _biometricInteractor = biometricInteractor,
        _navigationProvider = navigationProvider,
        _emailInteractor = emailInteractor,
        _userInteractor = userInteractor,
        _platformInfo = platformInfo,
        _kycInteractor = kycInteractor,
        _npssInteractor = npssInteractor,
        _featureToggleProvider = featureToggleProvider,
        _logger = logger,
        _customerFeedbackInteractor = customerFeedbackInteractor,
        _dashboardLocalizations = dashboardLocalizations,
        _bindDeviceInteractor = bindDeviceInteractor,
        _carbonCalculatorOnboardingInteractor =
            carbonCalculatorOnboardingInteractor,
        _chequebookInteractor = chequebookInteractor,
        _pricingInteractor = pricingInteractor,
        _brokerUserInteractor = brokerUserInteractor,
        _featureEligibilityProvider = featureEligibilityProvider;

  Future<List<ToDoItem>> getToDoItems(
    ToDoData data,
    UserMarketAccess? userMarketAccess,
  ) async {
    final toDoList = <ToDoItem>[];

    CustomerScreeningInfo? customerScreeningInfo;

    if (_isEnabledByFeatureToggle(
      FeatureToggleToDoItems.rfi,
    )) {
      try {
        final rfiList = await _kycInteractor.fetchRfi();
        if (rfiList.isNotEmpty) {
          toDoList.add(const ToDoItem.requiredInformation());
        }
      } on Object catch (e, st) {
        _logger.error(
          'failed to fetch Rfi',
          error: e,
          stackTrace: st,
          loggerName: WioDomain.acquisition.name,
        );
      }
    }

    final npssEnabled =
        _isEnabledByFeatureToggle(FeatureToggleToDoItems.instantTransfers);

    if (npssEnabled) {
      try {
        final status = _npssInteractor.enrollmentStatus?.status;
        if (status?.canBeEnrolled ?? false) {
          toDoList.add(const ToDoItem.instantTransfers());
        }
      } on Object catch (e, st) {
        _logger.error(
          'instant payments failure',
          error: e,
          stackTrace: st,
          loggerName: WioDomain.payments.name,
        );
      }
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.emiratesIdUpdate)) {
      try {
        customerScreeningInfo = await _kycInteractor.getCustomerScreeningInfo();

        if (_shouldShowEmiratesIdUpdateToDo(customerScreeningInfo)) {
          toDoList.add(const ToDoItem.updateEmiratesId());
        }
      } on Object catch (e, st) {
        _logger.error(
          'fetching customer screening info failure',
          error: e,
          stackTrace: st,
          loggerName: WioDomain.acquisition.name,
        );
      }
    }

    final firstAedAccountId = data.accounts.aedAccountId;
    if (firstAedAccountId != null) {
      try {
        if (await _chequebookInteractor.isFirstChequebookOrderAllowed()) {
          toDoList.add(const ToDoItem.orderChequebook());
        }
      } on Object catch (e, st) {
        _logger.debug(
          'Chequebook eligibility todo item failure',
          error: e,
          stackTrace: st,
          loggerName: WioDomain.payments.name,
        );
      }
    }

    final customerSurvey =
        await _customerFeedbackInteractor.getAlreadyAnsweredOnCustomerSurvey();
    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.customerSurvey) &&
        !customerSurvey) {
      toDoList.add(const ToDoItem.customerSurvey());
    }
    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.customerFeedback)) {
      toDoList.add(const ToDoItem.customerFeedback());
    }

    final user = data.user;
    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.emailVerification) &&
        user != null &&
        !user.isEmailVerified) {
      toDoList.add(const ToDoItem.emailVerification());
    }

    try {
      if (_kycInteractor.isPassportVerificationEnabled) {
        final passportVerificationInfo = customerScreeningInfo ??
            await _kycInteractor.getCustomerScreeningInfo();

        if (passportVerificationInfo.needsPassportVerification) {
          toDoList.add(const ToDoItem.passportVerification());
        }
      }
    } on Object catch (e, st) {
      _logger.error(
        'fetching passport verification todo item failure',
        error: e,
        stackTrace: st,
        loggerName: WioDomain.acquisition.name,
      );
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.setupEFR) &&
        user != null &&
        user.factors.doesFactorNotExistForUser(
          factor_model.TwoFactorAuthType.efr,
        )) {
      toDoList.add(const ToDoItem.setupEFR());
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.biometrics) &&
        await _shouldShowBiometricsToDoAction()) {
      toDoList.add(const ToDoItem.biometrics());
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.passcodeForgotten) &&
        await _todoActionsInteractor.isPasscodeForgotten()) {
      toDoList.add(const ToDoItem.passcodeForgotten());
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.referrals) &&
        await _isReferralsAvailable()) {
      toDoList.add(const ToDoItem.shareReferralCode());
    }

    if (!await _isCarbonCalculatorEnrolled()) {
      toDoList.add(const ToDoItem.carbonCalculator());
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.uaeSecurities) &&
        _kycInteractor.isPassportVerificationEnabled &&
        await _isUaeSecuritiesInactive(
          userMarketAccess,
        )) {
      toDoList.add(const ToDoItem.uaeSecruities());
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.crypto) &&
        await _isCryptoInactive(userMarketAccess)) {
      toDoList.add(const ToDoItem.crypto());
    }

    if (await _getHasTermDepositAction(data)) {
      toDoList.add(const ToDoItem.termDeposit());
    }

    if (_isEnabledByFeatureToggle(FeatureToggleToDoItems.wealthManagement) &&
        await _isWealthUnqualifiedEnabled(userMarketAccess) &&
        await _isUserWealthEligible()) {
      toDoList.add(const ToDoItem.wealthManagement());
    }

    return toDoList;
  }

  List<ToDoItem> updateOnChequebookRequestSubmit({
    required bool chequebookOrderAllowed,
    required List<ToDoItem> items,
  }) {
    final result = items.where((item) => item is! OrderChequebookItem).toList();
    if (chequebookOrderAllowed) {
      result.add(const ToDoItem.orderChequebook());
    }

    return result;
  }

  List<ToDoItem> updateOnNpssEnrollmentsStatus({
    required NPSSCustomerEnrollmentStatus status,
    required List<ToDoItem> items,
  }) {
    final result =
        items.where((item) => item is! InstantTransfersItem).toList();
    if (status.canBeEnrolled) {
      result.add(const ToDoItem.instantTransfers());
    }

    return result;
  }

  List<ToDoItem> updateOnCreditOffers({
    required List<ToDoItem> items,
    required bool canCreateApplication,
  }) {
    final creditItemExists = items.any((it) => it is LendingItem);
    final shouldAddCreditToDoItem = canCreateApplication && !creditItemExists;
    final shouldRemoveCreditToDoItem =
        !canCreateApplication && creditItemExists;

    if (shouldAddCreditToDoItem) {
      return [
        const ToDoItem.lending(),
        ...items,
      ];
    }

    if (shouldRemoveCreditToDoItem) {
      return filter<ToDoItem, LendingItem>(items);
    }

    return items;
  }

  Future<void> customerFeedbackFlow() {
    return _navigationProvider.navigateTo(
      CustomerFeedbackFeatureNavigationConfig(
        triggerPoint: DashboardConstants.customerFeedbackTriggerPoint,
        title: _dashboardLocalizations.customerFeedbackDashboardTitle,
        highlightedTitle:
            _dashboardLocalizations.customerFeedbackDashboardHighlightTitle,
        skipSavingTriggerStatus: true,
      ),
    );
  }

  Future<bool> npsFeedbackFlow(NPSValidationStrategy validationStrategy) async {
    final result = await _navigationProvider.push<bool>(
      NpsFeedbackFlowNavigationConfig(
        validationStrategy,
      ),
    );

    return result ?? false;
  }

  Future<bool> customerSurvey() async {
    final result = await _navigationProvider
        .push<bool?>(const CustomerSurveyScreenNavigationConfig());

    return result ?? false;
  }

  Future<bool> verifyEmail(User user) async {
    try {
      final status = await _emailInteractor.changeEmail(email: user.email);

      if (status != ChangeEmailResult.success) {
        return false;
      }
      await _emailInteractor.saveEmail(user.email);
      await _userInteractor.updateUser(user.copyWith(isEmailVerified: true));

      return true;
    } on Object catch (error, stackTrace) {
      _logger.debug(
        error.toString(),
        stackTrace: stackTrace,
        loggerName: WioDomain.identity.name,
      );

      return false;
    }
  }

  List<T> filter<T, E extends T>(List<T> items) {
    return items.where((item) => item is! E).toList();
  }

  Future<bool> changePasscode() async {
    await _todoActionsInteractor.setPasscodeForgotStatus(status: false);
    final result = await _navigationProvider.navigateTo(
      const PasscodeFeatureNavigationConfig(
        destination: ChangePasscodePageNavigationConfig(),
      ),
    ) as bool?;

    return result ?? false;
  }

  Future<void> upsertBiometrics() async {
    final deviceBound = await _bindDeviceInteractor.bindDeviceIfItIsNotBound();

    if (deviceBound) {
      await _biometricInteractor.upsertBiometrics(biometricsEnabled: true);
    } else {
      throw Exception("Device is not bound, can't enable biometrics");
    }
  }

  Future<bool> isAvailableBiometric() async {
    return _biometricInteractor.isBiometryAvailable();
  }

  Future<bool> isBiometricsEnabled() async {
    return _biometricInteractor.isBiometricsEnabled();
  }

  Future<void> openDeviceSettings() async {
    final configuration = InfoBottomSheetConfiguration(
      title: _commonLocalizations.requiredBiometricAccess,
      paragraphs: [_commonLocalizations.needPermissionToBiometrics],
      buttons: [
        ButtonInfoConfiguration(
          title: _commonLocalizations.allowAccess,
          type: ButtonType.primary,
        ),
      ],
    );

    final result =
        // ignore: deprecated_member_use
        await _bottomSheetProvider.showBottomSheet<InfoBottomSheetResponse>(
      configuration,
      'BiometricAccessInfoBottomSheet',
    );

    if (result != null) {
      await _permissionResolver.openDeviceSettings();
    }
  }

  Future<bool> _shouldShowBiometricsToDoAction() async {
    final isBiometricEnabled = await _biometricInteractor.isBiometricsEnabled();
    final isBiometricAvailable =
        await _biometricInteractor.isBiometryAvailable();

    var isNeedToShowBiometrics = true;
    if (_platformInfo.isAndroid && !isBiometricAvailable) {
      isNeedToShowBiometrics = false;
    }
    final isBiometricActive = isBiometricEnabled && isBiometricAvailable;

    return !isBiometricActive && isNeedToShowBiometrics;
  }

  Future<bool> _isReferralsAvailable() async {
    try {
      final isPeerToPeerReferralsEnabled = _featureToggleProvider.get(
        ReferralsFeatureToggles.isPeerToPeerReferralsEnabled,
      );

      if (!isPeerToPeerReferralsEnabled) return false;

      final campaigns = await _referralsInteractor.getCampaigns();

      if (campaigns.isEmpty) return false;

      final referralInfo = await _referralsInteractor.getCode(
        campaignId: campaigns.first.id,
      );

      return referralInfo.referralCode.isNotEmpty;
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Could not get referral codes.\n${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );

      return false;
    }
  }

  Future<bool> _isCarbonCalculatorEnrolled() async {
    try {
      final carbonCalculatorStatus = await _carbonCalculatorOnboardingInteractor
          .getCarbonCalculatorStatus();

      return carbonCalculatorStatus.isEnabled;
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Could not carbon calculator status.\n${error.toString()}',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.cards.name,
      );

      // If we can't get the status, we assume that the user is enrolled
      // to hide carbon calculator item
      return true;
    }
  }

  Future<bool> _isUaeSecuritiesInactive(
    UserMarketAccess? userMarketAccess,
  ) async {
    try {
      final marketAccess =
          userMarketAccess ?? await _brokerUserInteractor.getUserMarketAccess();

      return marketAccess.uaeMarketIsInactive;
    } on Object catch (e, st) {
      _logger.error(
        'fetching broker user market access todo item failure',
        error: e,
        stackTrace: st,
        loggerName: WioDomain.broker.name,
      );

      return false;
    }
  }

  Future<bool> _isCryptoInactive(UserMarketAccess? userMarketAccess) async {
    try {
      final marketAccess =
          userMarketAccess ?? await _brokerUserInteractor.getUserMarketAccess();

      return marketAccess.cryptoMarketIsInactive;
    } on Object catch (e, st) {
      _logger.error(
        'fetching broker user market access todo item failure',
        error: e,
        stackTrace: st,
        loggerName: WioDomain.broker.name,
      );

      return false;
    }
  }

  Future<bool> _isWealthUnqualifiedEnabled(
    UserMarketAccess? userMarketAccess,
  ) async {
    try {
      final marketAccess =
          userMarketAccess ?? await _brokerUserInteractor.getUserMarketAccess();

      return marketAccess.isWealthManagementUnqualifiedInactive;
    } on Object catch (e, st) {
      _logger.error(
        'fetching broker user market access todo item failure',
        error: e,
        stackTrace: st,
      );

      return false;
    }
  }

  Future<bool> _isUserWealthEligible() async {
    try {
      final wealthEligible =
          await _featureEligibilityProvider.getFeatureEligibility(
        onboardingType: OnboardingType.wealthManagementUnqualified,
      );

      return wealthEligible;
    } on Object catch (e, st) {
      _logger.error(
        'fetching wealth eligibility in retail todo failed',
        error: e,
        stackTrace: st,
      );

      return false;
    }
  }

  Future<bool> _getHasTermDepositAction(ToDoData data) async {
    _logger.debug(
      'Checking todo item for term deposit',
      loggerName: WioDomain.pricingPlan.name,
    );

    final totalBalance = data.totalBalance;
    if (totalBalance == null || !totalBalance.isPositive) {
      return false;
    }

    final hasDeposits = data.savingsAccounts.any((it) => it is FixedDeposit);
    if (hasDeposits) {
      return false;
    }

    try {
      final currentPlan = await _pricingInteractor.getSubscriptionPlanInfo();

      return currentPlan.isFixedTermDepositAvailable;
    } on Object catch (error, stackTrace) {
      _logger.info(
        'Could not create TODO action for term deposit',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.pricingPlan.name,
      );
    }

    return false;
  }

  bool _isEnabledByFeatureToggle(FeatureToggleToDoItems toDoItem) {
    switch (toDoItem) {
      case FeatureToggleToDoItems.rfi:
        return _featureToggleProvider.get(DashboardFeatureToggles.isRfiEnabled);
      case FeatureToggleToDoItems.passcodeForgotten:
        return _featureToggleProvider
            .get(DashboardFeatureToggles.isPasscodeItemEnabledInTODOActions);
      case FeatureToggleToDoItems.emailVerification:
        return _featureToggleProvider
            .get(DashboardFeatureToggles.isEmailItemEnabledInTODOActions);
      case FeatureToggleToDoItems.biometrics:
        return _featureToggleProvider
            .get(DashboardFeatureToggles.isBiometricItemEnabledInTODOActions);
      case FeatureToggleToDoItems.setupEFR:
        return _featureToggleProvider
            .get(DashboardFeatureToggles.isEFRItemEnabledInTODOActions);
      case FeatureToggleToDoItems.referrals:
        return _featureToggleProvider
            .get(DashboardFeatureToggles.isReferralsEnabledInTODOActions);
      case FeatureToggleToDoItems.customerFeedback:
        return _featureToggleProvider.get(
          DashboardFeatureToggles.isCustomerFeedbackEnabledInTODOActions,
        );
      case FeatureToggleToDoItems.customerSurvey:
        return _featureToggleProvider.get(
          DashboardFeatureToggles.isCustomerSurveyInTODOActions,
        );
      case FeatureToggleToDoItems.emiratesIdUpdate:
        return _featureToggleProvider.get(
          DashboardFeatureToggles.showEmiratesIdUpdateInTodo,
        );
      case FeatureToggleToDoItems.instantTransfers:
        return _featureToggleProvider.get(
          PaymentsFeatureToggle.isNPSSEnabled,
        );
      case FeatureToggleToDoItems.uaeSecurities:
        return _featureToggleProvider
            .get(PortfolioFeatureToggles.isUaeSecuritiesEnabled);
      case FeatureToggleToDoItems.npsFeedback:
        return _featureToggleProvider.get(
          DashboardFeatureToggles.isNpsFeedbackEnabled,
        );
      case FeatureToggleToDoItems.crypto:
        return _featureToggleProvider.get(
          DashboardFeatureToggles.isRetailCryptoToDoEnabled,
        );
      case FeatureToggleToDoItems.wealthManagement:
        return _featureToggleProvider
            .get(WealthManagementFeatureToggles.isWealthManagementEnabled);
    }
  }

  bool _shouldShowEmiratesIdUpdateToDo(
    CustomerScreeningInfo customerScreeningInfo,
  ) {
    final documents = customerScreeningInfo.kycDocumentDetails;
    if (documents == null) {
      return false;
    }

    final eidDocumentToUpdate = documents.firstWhereOrNull(
      (document) =>
          document.reviewEnabled &&
          document.documentType == KycDocumentType.emiratesId,
    );

    // If eidDocumentToUpdate != null, it means we need to show EIDA in to-do
    return eidDocumentToUpdate != null;
  }
}

@freezed
class ToDoData with _$ToDoData {
  const ToDoData._();

  const factory ToDoData({
    User? user,
    Money? totalBalance,
    @Default(<AccountDetails>[]) List<AccountDetails> accounts,
    @Default(<SavingsAccount>[]) List<SavingsAccount> savingsAccounts,
  }) = _ToDoData;
}
