part of '../dashboard_screen.dart';

class _MyMoneyDistributionItems extends StatefulWidget {
  final DashboardLoadedState state;

  const _MyMoneyDistributionItems({required this.state});

  @override
  State<_MyMoneyDistributionItems> createState() =>
      _MyMoneyDistributionItemsState();
}

class _MyMoneyDistributionItemsState extends State<_MyMoneyDistributionItems> {
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<DashboardCubit>();

    final isBalanceHidden = context.select<BalancePrivacyCubit, bool>(
      (cubit) => cubit.state == BalancePrivacyState.hidden,
    );

    final shouldShowSetUpPasscode = widget.state.shouldShowSetUpPasscode;

    final accountsDistributionModel = _getAccountsDistributionModel(
      context: context,
      isBalanceHidden: isBalanceHidden,
    );

    final savingDistributionModel = _getSavingDistributionModel(
      context: context,
      isBalanceHidden: isBalanceHidden,
    );

    final investDistributionModel = _getInvestDistributionModel(
      context: context,
      isBalanceHidden: isBalanceHidden,
    );

    return FadeInSlideHorizontalAnimation(
      child: SizedBox(
        height: 80.0,
        child: SingleChildScrollView(
          key: DashboardScreen.distributionItemsListKey,
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              Space.fromSpacingHorizontal(Spacing.s5),
              ...[
                DistributionItemButton(
                  semanticsLabel: _getSemanticsLabel(
                    accountsDistributionModel,
                    isBalanceHidden: isBalanceHidden,
                  ),
                  key: DashboardScreen.accountsButtonKey,
                  model: accountsDistributionModel,
                  onTap: shouldShowSetUpPasscode
                      ? cubit.showSetUpPasscodeBottomSheet
                      : cubit.openAccountsBottomSheet,
                ),
                DistributionItemButton(
                  semanticsLabel: _getSemanticsLabel(
                    savingDistributionModel,
                    isBalanceHidden: isBalanceHidden,
                  ),
                  key: DashboardScreen.savingsButtonKey,
                  model: savingDistributionModel,
                  onTap: shouldShowSetUpPasscode
                      ? cubit.showSetUpPasscodeBottomSheet
                      : cubit.navigateToSavingSpace,
                ),
                if (investDistributionModel != null)
                  DistributionItemButton(
                    semanticsLabel: _getSemanticsLabel(
                      investDistributionModel,
                      isBalanceHidden: isBalanceHidden,
                    ),
                    key: DashboardScreen.investmentsButtonKey,
                    model: investDistributionModel,
                    onTap: shouldShowSetUpPasscode
                        ? cubit.showSetUpPasscodeBottomSheet
                        : cubit.navigateToBroker,
                  ),
              ].map(
                (item) => Padding(
                  padding: const EdgeInsetsDirectional.only(end: 16.0),
                  child: item,
                ),
              ),
              Space.fromSpacingHorizontal(Spacing.s5),
            ],
          ),
        ),
      ),
    );
  }

  String _getSemanticsLabel(
    SmallHorizontalCardModel model, {
    required bool isBalanceHidden,
  }) {
    return model.map(
      figure: (m) {
        final amountLabel = m.figureModel.moneyOrNull?.semanticsLabel;
        final base = '${m.title} balance';
        return isBalanceHidden || amountLabel == null
            ? base
            : '$base: $amountLabel';
      },
      text: (m) => '${m.title}: ${m.value}',
    );
  }

  SmallHorizontalCardModel _getAccountsDistributionModel({
    required BuildContext context,
    required bool isBalanceHidden,
  }) {
    final l10n = DashboardLocalizations.of(context);
    final accountsBalance = widget.state.balance.accountsBalance;

    return accountsBalance.maybeMap(
      value: (value) => SmallHorizontalCardModel.figure(
        title: l10n.accountsLabel,
        figureModel: FigureModel.b1OneLineOneSize(
          value.amount,
          colorScheme: FigureColorScheme.light,
          shouldSnap: isBalanceHidden,
        ),
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.business_wallet,
        ),
      ),
      orElse: () => SmallHorizontalCardModel.figure(
        title: l10n.accountsLabel,
        figureModel: _emptyFigureModel,
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.business_wallet,
        ),
      ),
    );
  }

  SmallHorizontalCardModel _getSavingDistributionModel({
    required BuildContext context,
    required bool isBalanceHidden,
  }) {
    final l10n = DashboardLocalizations.of(context);
    final savingsBalance = widget.state.balance.savingsBalance;

    final savingsIcon =
        CompanyPictogramPointer.business_savings.toGraphicAsset();

    return savingsBalance.map(
      notOnboarded: (_) => SmallHorizontalCardModel.text(
        title: l10n.savingsLabel,
        value: l10n.savingButtonTitle,
        icon: savingsIcon,
      ),
      notAvailable: (_) => SmallHorizontalCardModel.text(
        title: l10n.savingsLabel,
        value: l10n.savingButtonTitle,
        icon: savingsIcon,
      ),
      value: (value) => SmallHorizontalCardModel.figure(
        title: l10n.savingsLabel,
        figureModel: FigureModel.b1OneLineOneSize(
          value.amount,
          colorScheme: FigureColorScheme.light,
          shouldSnap: isBalanceHidden,
        ),
        icon: savingsIcon,
      ),
      error: (error) => SmallHorizontalCardModel.figure(
        title: l10n.savingsLabel,
        figureModel: _emptyFigureModel,
        icon: savingsIcon,
      ),
    );
  }

  SmallHorizontalCardModel? _getInvestDistributionModel({
    required BuildContext context,
    required bool isBalanceHidden,
  }) {
    final l10n = DashboardLocalizations.of(context);

    final investIcon =
        CompanyPictogramPointer.business_invest_right.toGraphicAsset();

    final investmentsBalance = widget.state.balance.investmentsBalance;

    return investmentsBalance.mapOrNull(
      notOnboarded: (_) => SmallHorizontalCardModel.text(
        title: l10n.investmentsLabel,
        value: l10n.investButtonTitle,
        icon: investIcon,
      ),
      notAvailable: (_) => null,
      value: (value) => SmallHorizontalCardModel.figure(
        title: l10n.investmentsLabel,
        figureModel: FigureModel.b1OneLineOneSize(
          value.amount,
          colorScheme: FigureColorScheme.light,
          shouldSnap: isBalanceHidden,
        ),
        icon: investIcon,
      ),
      error: (_) => SmallHorizontalCardModel.figure(
        title: l10n.investmentsLabel,
        figureModel: _emptyFigureModel,
        icon: investIcon,
      ),
    );
  }

  FigureModel get _emptyFigureModel =>
      FigureModel.b1EmptyMoney(colorScheme: FigureColorScheme.light);
}
