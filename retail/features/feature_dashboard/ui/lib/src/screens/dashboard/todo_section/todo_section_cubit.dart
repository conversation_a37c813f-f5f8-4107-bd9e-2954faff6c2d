import 'dart:async';

import 'package:common_wio_feature_kyc_api/kyc_api.dart';
import 'package:feature_dashboard_ui/l10n/dashboard_localization.g.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_navigation_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/handlers/dashboard_to_do_handler.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/models/to_do_item.dart';
import 'package:feature_dashboard_ui/src/screens/dashboard/todo_section/todo_section_state.dart';
import 'package:neobroker_models/models.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_customer_address_api/domain/customer_address_interactor.dart';
import 'package:wio_common_feature_wealth_management_api/handlers/wealth_management_starter_handler.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';
import 'package:wio_feature_liveness_api/domain/models/liveness_result.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_user_api/index.dart';

class TodoSectionCubit extends BaseCubit<TodoSectionState> {
  final DashboardToDoHandler _toDoHandler;
  final ToastMessageProvider _toastMessageProvider;
  final CommonLocalizations _commonLocalizations;
  final DashboardLocalizations _dashboardLocalizations;
  final Logger _logger;
  final CommonErrorHandler _commonErrorHandler;
  final RfiHandler _rfiHandler;
  final KycInteractor _kycInteractor;
  final DashboardNavigationHandler _navigationHandler;
  final CustomerAddressInteractor _customerAddressInteractor;
  final WealthManagementStarterHandler _wealthManagementStarterHandler;
  final ExhaustStreamExecutor _exhaustStreamExecutor;

  // Additional dependencies needed for data fetching
  final UserInteractor _userInteractor;
  final AccountInteractor _accountInteractor;
  final BrokerUserInteractor _brokerUserInteractor;

  TodoSectionCubit({
    required DashboardToDoHandler toDoHandler,
    required ToastMessageProvider toastMessageProvider,
    required CommonLocalizations commonLocalizations,
    required DashboardLocalizations dashboardLocalizations,
    required Logger logger,
    required CommonErrorHandler commonErrorHandler,
    required RfiHandler rfiHandler,
    required KycInteractor kycInteractor,
    required DashboardNavigationHandler navigationHandler,
    required CustomerAddressInteractor customerAddressInteractor,
    required WealthManagementStarterHandler wealthManagementStarterHandler,
    required ExhaustStreamExecutor exhaustStreamExecutor,
    required UserInteractor userInteractor,
    required AccountInteractor accountInteractor,
    required BrokerUserInteractor brokerUserInteractor,
  })  : _toDoHandler = toDoHandler,
        _toastMessageProvider = toastMessageProvider,
        _commonLocalizations = commonLocalizations,
        _dashboardLocalizations = dashboardLocalizations,
        _logger = logger,
        _commonErrorHandler = commonErrorHandler,
        _rfiHandler = rfiHandler,
        _kycInteractor = kycInteractor,
        _navigationHandler = navigationHandler,
        _customerAddressInteractor = customerAddressInteractor,
        _wealthManagementStarterHandler = wealthManagementStarterHandler,
        _exhaustStreamExecutor = exhaustStreamExecutor,
        _userInteractor = userInteractor,
        _accountInteractor = accountInteractor,
        _brokerUserInteractor = brokerUserInteractor,
        super(const TodoSectionState.loading());

  Future<void> init() async {
    try {
      emit(const TodoSectionState.loading());

      await _fetchAllDataAndTodoItems();
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Failed to initialize TODO section',
        error: error,
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );
      emit(const TodoSectionState.error());
    }
  }

  Future<void> _fetchAllDataAndTodoItems() async {
    final (
      user,
      accounts,
      userMarketAccess,
    ) = await (
      _userInteractor.getUser(),
      _accountInteractor.getAccountDetails(),
      _brokerUserInteractor.getUserMarketAccess().catchError(
            (_) => const UserMarketAccess(
              usMarketStatus: UserFeatureStatus.unknown,
              uaeMarketStatus: UserFeatureStatus.unknown,
              cryptoMarketStatus: UserFeatureStatus.unknown,
            ),
          ),
    ).wait;

    // TODO(amolchan): Temp solution, need to think more about it
    final totalBalance = _calculateTotalBalanceFromAccounts(accounts);

    // Get savings accounts - for now empty,
    // could be fetched separately if needed
    final savingsAccounts = <SavingsAccount>[];

    // Now fetch TODO items with the gathered data
    await _fetchToDoItems(
      accounts: accounts,
      savingsAccounts: savingsAccounts,
      user: user,
      totalBalance: totalBalance,
      userMarketAccess: userMarketAccess,
    );
  }

  Money? _calculateTotalBalanceFromAccounts(List<AccountDetails> accounts) {
    if (accounts.isEmpty) return null;

    // Sum all AED accounts - this is a simplified approach
    final aedAccounts = accounts.where(
      (account) => account.availableBalance.currency == Currency.aed,
    );

    if (aedAccounts.isEmpty) return null;

    return aedAccounts.fold<Money>(
      Money.fromNumWithCurrency(0.0, Currency.aed),
      (sum, current) => sum + current.availableBalance,
    );
  }

  /// Fetch TODO items from handler
  Future<void> _fetchToDoItems({
    required List<AccountDetails> accounts,
    required List<SavingsAccount> savingsAccounts,
    User? user,
    Money? totalBalance,
    UserMarketAccess? userMarketAccess,
  }) async {
    try {
      // Create ToDoData for handler compatibility
      final todoData = ToDoData(
        user: user,
        totalBalance: totalBalance,
        accounts: accounts,
        savingsAccounts: savingsAccounts,
      );

      final todoItems = await _toDoHandler.getToDoItems(
        todoData,
        userMarketAccess,
      );

      emit(
        TodoSectionState.loaded(
          toDoItems: todoItems,
          user: user,
          totalBalance: totalBalance,
          accounts: accounts,
          savingsAccounts: savingsAccounts,
          userMarketAccess: userMarketAccess,
        ),
      );
    } on Object catch (error, stackTrace) {
      _logger.debug(
        'Could not fetch To Do items.\\n${error.toString()}',
        stackTrace: stackTrace,
        loggerName: WioDomain.core.name,
      );
      emit(const TodoSectionState.error());
    }
  }

  /// Refresh TODO items by re-fetching all data
  Future<void> refresh() async {
    await init();
  }

  // MOVED METHODS FROM DASHBOARD CUBIT - TODO ACTION HANDLERS

  /// Handle change passcode TODO item
  void changePasscode() {
    state.mapOrNull(
      loaded: (state) async {
        try {
          final result = await _toDoHandler.changePasscode();
          if (isClosed) return;
          if (result) {
            emit(
              state.copyWith(
                toDoItems: _toDoHandler.filter<ToDoItem, PasscodeForgottenItem>(
                  state.toDoItems,
                ),
              ),
            );
          }
        } on Object catch (error) {
          _logger.debug(error.toString(), loggerName: WioDomain.identity.name);
          _commonErrorHandler.handleError(error);
        }
      },
    );
  }

  /// Handle customer feedback TODO item
  void customerFeedbackPressed() {
    state.mapOrNull(
      loaded: (state) async => _toDoHandler.customerFeedbackFlow(),
    );
  }

  /// Handle required information TODO item
  void requiredInformationPressed() {
    state.mapOrNull(
      loaded: (state) async {
        final rfiList = _kycInteractor.getRfiList();
        if (rfiList != null && rfiList.isNotEmpty) {
          final updatedRfiList = await _rfiHandler.handleRfi(rfiList);

          if (updatedRfiList.isEmpty) {
            return;
          }

          final submittedCount = updatedRfiList.fold(0, (acc, model) {
            if (model.submitted) {
              acc++;
            }
            return acc;
          });

          if (submittedCount == rfiList.length) {
            emit(
              state.copyWith(
                toDoItems: _toDoHandler
                    .filter<ToDoItem, RequiredInformationItem>(state.toDoItems),
              ),
            );
            _toastMessageProvider.showToastMessage(
              NotificationToastMessageConfiguration.success(
                _dashboardLocalizations.rfiSubmitted,
              ),
            );
          } else {
            _kycInteractor.updateRfiList(updatedRfiList);
          }
        } else {
          _commonErrorHandler
              .handleError(_commonLocalizations.common_error_message);
        }
      },
    );
  }

  /// Handle update Emirates ID TODO item
  void updateEmiratesIdPressed() {
    state.mapOrNull(
      loaded: (state) async {
        final isUpdated = await _navigationHandler.navigateToEmiratesIdUpdate();
        _onUpdateExpiredEmiratesId(isUpdated);
      },
    );
  }

  /// Handle Emirates ID update result
  void _onUpdateExpiredEmiratesId(bool? updated) {
    if (updated == null) return;

    state.mapOrNull(
      loaded: (state) async {
        if (updated) {
          emit(
            state.copyWith(
              toDoItems: _toDoHandler
                  .filter<ToDoItem, UpdateEmiratesIdItem>(state.toDoItems),
            ),
          );
          _toastMessageProvider.showToastMessage(
            NotificationToastMessageConfiguration.success(
              _dashboardLocalizations.emiratesIdUpdatedSuccessfully,
            ),
          );
        }
      },
    );
  }

  /// Handle customer survey TODO item
  void customerSurveyPressed() {
    state.mapOrNull(
      loaded: (state) async {
        final result = await _toDoHandler.customerSurvey();

        if (result) {
          emit(
            state.copyWith(
              toDoItems: _toDoHandler.filter<ToDoItem, CustomerSurvey>(
                state.toDoItems,
              ),
            ),
          );
        }
      },
    );
  }

  /// Handle email verification TODO item
  void verifyEmail() {
    state.mapOrNull(
      loaded: (state) async {
        try {
          final user = state.user;
          if (user == null) return;

          loading(true);
          final isSuccess = await _toDoHandler.verifyEmail(user);
          if (!isSuccess) throw Exception('Email verification failed');

          _logger.debug(
            'Email verified.',
            loggerName: WioDomain.identity.name,
          );

          emit(
            state.copyWith(
              toDoItems: _toDoHandler.filter<ToDoItem, EmailVerificationItem>(
                state.toDoItems,
              ),
            ),
          );
        } on Object catch (error, stackTrace) {
          _logger.debug(
            error.toString(),
            stackTrace: stackTrace,
            loggerName: WioDomain.identity.name,
          );
          _commonErrorHandler.handleError(error);
        } finally {
          loading(false);
        }
      },
    );
  }

  /// Handle biometrics enable TODO item
  Future<void> onEnableBiometryStatus() async {
    try {
      if (await _toDoHandler.isAvailableBiometric()) {
        loading(true);
        await _toDoHandler.upsertBiometrics();

        if (isClosed) return;

        state.mapOrNull(
          loaded: (state) => emit(
            state.copyWith(
              toDoItems: _toDoHandler.filter<ToDoItem, BiometricsItem>(
                state.toDoItems,
              ),
            ),
          ),
        );
        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.success(
            _dashboardLocalizations.biometryEnabledToastMessage,
          ),
        );
      } else {
        await _toDoHandler.openDeviceSettings();
      }
    } on Object catch (error) {
      _logger.debug(error.toString(), loggerName: WioDomain.identity.name);
      _commonErrorHandler.handleError(error);
    } finally {
      loading(false);
    }
  }

  /// Handle setup identity (EFR) TODO item
  Future<void> onSetupIdentity() async {
    final result = await _navigationHandler.navigateToLivenessFeature();

    result.map(
      success: (_) {
        state.mapOrNull(
          loaded: (state) => emit(
            state.copyWith(
              toDoItems:
                  _toDoHandler.filter<ToDoItem, EFRSetupItem>(state.toDoItems),
            ),
          ),
        );

        _toastMessageProvider.showToastMessage(
          NotificationToastMessageConfiguration.success(
            _dashboardLocalizations.efrUploadSuccessfully,
          ),
        );
      },
      failed: (value) {
        if (value.code == LivenessChallengeErrorCode.onBackPressed) {
          return;
        }
        _commonErrorHandler
            .handleError(_commonLocalizations.common_error_message);
      },
    );
  }

  /// Handle referrals TODO item
  Future<void> onReferralsToDoPressed() {
    return _navigationHandler.navigateToReferrals();
  }

  /// Handle carbon calculator TODO item
  void onCarbonCalculatorToDoPressed() {
    _navigationHandler.navigateToCarbonCalculator();
  }

  /// Handle instant payment TODO item
  void onInstantPaymentTodoPressed() {
    _navigationHandler.navigateToInstantPayments();
  }

  /// Handle crypto TODO item
  Future<void> onCryptoToDoPressed() async {
    _exhaustStreamExecutor.run(
      () => Stream<void>.fromFuture(
        _onCryptoToDoPressed(),
      ).withLoading(this),
    );
  }

  /// Internal crypto TODO handler
  Future<void> _onCryptoToDoPressed() async {
    final isAddressValid = await _isAddressValid();
    if (!isAddressValid) return;
    return _navigateToCryptoOnboarding();
  }

  /// Handle UAE securities TODO item
  Future<void> onUaeSecuritiesToDoPressed() async {
    _exhaustStreamExecutor.run(
      () => Stream<void>.fromFuture(
        _onUaeSecuritiesToDoPressed(),
      ).withLoading(this),
    );
  }

  /// Internal UAE securities TODO handler
  Future<void> _onUaeSecuritiesToDoPressed() async {
    final isAddressValid = await _isAddressValid();
    if (!isAddressValid) return;
    return _handlePassportForUaeOnboarding();
  }

  /// Handle wealth management TODO item
  Future<void> onWealthUnqualifiedToDoPressed() =>
      _wealthManagementStarterHandler.startWealthManagement();

  /// Handle term deposit TODO item
  void onOpenTermDeposit() {
    state.mapOrNull(
      loaded: (state) {
        assert(
          state.toDoItems.contains(const ToDoItem.termDeposit()),
          'TODO item for term deposit is not available',
        );
        _navigationHandler.navigateToTermDepositCreation();
      },
    );
  }

  /// Handle passport verification TODO item
  Future<void> onPassportVerificationCardPressed() async {
    // This method would need PassportVerificationFlowService
    // For now, just remove the item
    state.mapOrNull(
      loaded: (state) => emit(
        state.copyWith(
          toDoItems: _toDoHandler.filter<ToDoItem, PassportVerificationItem>(
            state.toDoItems,
          ),
        ),
      ),
    );
  }

  // HELPER METHODS

  /// Check if address is valid for broker operations
  Future<bool> _isAddressValid() async {
    try {
      final address = await _customerAddressInteractor.getPrimaryAddress();

      if (address != null) {
        return true;
      }

      final result = await _navigationHandler.navigateToAddress();

      // Extra fail safe to ensure that the address is saved in BE
      final checkAddress = await _customerAddressInteractor.getPrimaryAddress();

      if (result is AddressSuccessResult && checkAddress != null) {
        return true;
      }
      return false;
    } on Object catch (error) {
      _logger.debug(error.toString(), loggerName: WioDomain.broker.name);
      _commonErrorHandler.handleError(error);
      return false;
    }
  }

  /// Navigate to crypto onboarding
  Future<void> _navigateToCryptoOnboarding() async {
    state.mapOrNull(
      loaded: (state) => _navigationHandler.navigateToCryptoOnboarding(
        state.userMarketAccess ??
            const UserMarketAccess(
              usMarketStatus: UserFeatureStatus.unknown,
              uaeMarketStatus: UserFeatureStatus.unknown,
              cryptoMarketStatus: UserFeatureStatus.unknown,
            ),
      ),
    );
  }

  /// Handle passport for UAE onboarding
  Future<void> _handlePassportForUaeOnboarding() async {
    final info = await _kycInteractor.getCustomerScreeningInfo();
    final passportStatus = info.screeningStatusOnPassport;

    if (passportStatus == PassportVerificationStatus.approved) {
      return _handleEidForUaeOnboarding();
    }

    final passportFlowResult =
        await _navigationHandler.navigateToPassportFlow();

    if (passportFlowResult == PassportVerificationFlowResult.success) {
      return _handleEidForUaeOnboarding();
    }
  }

  /// Handle Emirates ID for UAE onboarding
  Future<void> _handleEidForUaeOnboarding() async {
    // This would need ModalDelegatesRunner logic
    // For now, navigate to UAE onboarding directly
    return _navigateToUaeOnboarding();
  }

  /// Navigate to UAE onboarding
  Future<void> _navigateToUaeOnboarding() async {
    state.mapOrNull(
      loaded: (state) => _navigationHandler.navigateToUaeOnboarding(
        state.userMarketAccess ??
            const UserMarketAccess(
              usMarketStatus: UserFeatureStatus.unknown,
              uaeMarketStatus: UserFeatureStatus.unknown,
              cryptoMarketStatus: UserFeatureStatus.unknown,
            ),
      ),
    );
  }

  /// Update TODO items when external changes occur (e.g., chequebook, NPSS)
  void updateToDoItems(List<ToDoItem> newToDoItems) {
    state.mapOrNull(
      loaded: (state) => emit(
        state.copyWith(toDoItems: newToDoItems),
      ),
    );
  }

  @override
  String toString() => 'TodoSectionCubit';
}
