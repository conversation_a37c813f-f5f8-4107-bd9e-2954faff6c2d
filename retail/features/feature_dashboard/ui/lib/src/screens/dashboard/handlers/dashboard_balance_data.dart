import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';

part 'dashboard_balance_data.freezed.dart';

/// Class contains all balances for dashboard
@freezed
class DashboardBalanceData with _$DashboardBalanceData {
  const DashboardBalanceData._();

  const factory DashboardBalanceData({
    required DashboardBalance accountsBalance,
    required DashboardBalance savingsBalance,
    required DashboardBalance investmentsBalance,
    @Default(<String, Money>{}) Map<String, Money> foreignAccountsInAed,
  }) = _DashboardBalanceData;

  Money? get total {
    final zero = Money.fromNum(0, code: Currency.aed.code);
    final balances = [
      accountsBalance.mapOrNull(value: (value) => value.amount),
      savingsBalance.mapOrNull(
        value: (value) => value.amount,
        notAvailable: (_) => zero,
        notOnboarded: (_) => zero,
      ),
      investmentsBalance.mapOrNull(
        value: (value) => value.amount,
        notAvailable: (_) => zero,
        notOnboarded: (_) => zero,
      ),
    ];

    return balances.isEmpty || balances.any((balance) => balance == null)
        ? null
        : balances.nonNulls.reduce((sum, balance) => sum + balance);
  }
}

@freezed
class DashboardBalance with _$DashboardBalance {
  const DashboardBalance._();

  /// Feature is not available
  const factory DashboardBalance.notAvailable() = DashboardBalanceNotAvailable;

  /// Feature is available but balance is not possible to get
  const factory DashboardBalance.notOnboarded() = DashboardBalanceNotOnboarded;

  const factory DashboardBalance.value({
    required Money amount,
  }) = DashboardBalanceValue;

  const factory DashboardBalance.error() = DashboardBalanceError;
}
