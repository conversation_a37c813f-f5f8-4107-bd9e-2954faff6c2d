import 'package:di/di.dart';
import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:feature_dashboard_ui/l10n/dashboard_localization.g.dart';
import 'package:feature_dashboard_ui/src/common/extensions.dart';
import 'package:feature_dashboard_ui/src/screens/account_details/account_details_cubit.dart';
import 'package:feature_dashboard_ui/src/screens/account_details/account_details_state.dart';
import 'package:feature_dashboard_ui/src/screens/account_details/widgets/account_details_view.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';

part 'widgets/common_widgets.dart';

part 'widgets/loaded_content.dart';

part 'widgets/loading_content.dart';

class AccountDetailsScreen
    extends BasePage<AccountDetailsState, AccountDetailsCubit> {
  static const body = ValueKey('accountDetailsScreenBodyKey');

  final List<AccountDetails>? accounts;
  final AccountSelector? selector;

  const AccountDetailsScreen({
    this.accounts,
    this.selector,
    super.key,
  });

  @override
  AccountDetailsCubit createBloc() =>
      DependencyProvider.get<AccountDetailsCubit>();

  @override
  void initBloc(AccountDetailsCubit bloc) => bloc.initialize(
        selector: selector,
        accounts: accounts,
      );

  @override
  Widget buildPage(
    BuildContext context,
    AccountDetailsCubit bloc,
    AccountDetailsState state,
  ) =>
      const _AccountDetailsContent();
}

class _AccountDetailsContent extends StatelessWidget {
  const _AccountDetailsContent();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<AccountDetailsCubit>().state;

    return Scaffold(
      appBar: const TopNavigation(
        TopNavigationModel(state: TopNavigationState.positive),
      ),
      body: FixedButtonsPageLayout(
        contentPadding: FixedButtonsPageLayout.defaultContentPadding.copyWith(
          top: 6,
        ),
        model: const FixedButtonsScrollablePageLayoutModel(),
        child: switch (state) {
          LoadingAccountDetailsState() => const _LoadingContent(),
          final IdleAccountDetailsState it when it.accounts.isNotEmpty =>
            LoadedContent(state: it),
          ErrorAccountDetailsState() => const _ErrorContent(),
          _ => const Space.shrink(),
        },
      ),
    );
  }
}

class _AccountsHeader extends StatelessWidget {
  final AccountDetails currentAccount;

  const _AccountsHeader({
    required this.currentAccount,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = DashboardLocalizations.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Label(
            model: LabelModel(
              text: l10n.accountDetailsTitle,
              textStyle: CompanyTextStylePointer.h2medium,
              color: CompanyColorPointer.primary3,
              overflow: LabelTextOverflow.fade,
              maxLines: 1,
            ),
          ),
        ),
        AnimatedSwitcher(
          duration: const Duration(milliseconds: 500),
          child: RichTile(
            key: ValueKey(currentAccount.id),
            model: RichTileModel(
              mainModel: TileModel.flag(
                flag: currentAccount.availableBalance.currency.flag,
                shape: TileBoxShape.circle,
                size: TileSize.extraLarge,
              ),
              secondaryModel: currentAccount.type.iconModel(),
              mainSize: 64.0,
            ),
          ),
        ),
      ],
    );
  }
}

class _AccountActions extends StatelessWidget {
  const _AccountActions();

  @override
  Widget build(BuildContext context) {
    final l10n = DashboardLocalizations.of(context);
    final cubit = context.read<AccountDetailsCubit>();
    final isAccountConfirmationEnable = cubit.state.maybeMap(
      orElse: () => false,
      idle: (it) => it.areAccountsWithConfirmationLetterEnabled,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (!isAccountConfirmationEnable)
          Button(
            model: ButtonModel(
              title: l10n.accountConfirmationLetterButtonTitle,
              type: ButtonType.secondary,
              graphicAssetPointer: CompanyIconPointer.download.toGraphicAsset(),
              iconSize: CompanyIconSize.medium,
            ),
            onPressed: cubit.onGetConfirmationLetter,
          ),
        Space.fromSpacingVertical(Spacing.s3),
        Button(
          model: ButtonModel(title: l10n.copyOrShareButtonTitle),
          onPressed: cubit.copyOrShareDetails,
        ),
      ],
    );
  }
}

class _ErrorContent extends StatelessWidget {
  const _ErrorContent();

  @override
  Widget build(BuildContext context) {
    final l10n = CommonLocalizations.of(context);

    return Center(
      child: GenericError(
        GenericErrorModel(
          title: l10n.genericErrorComponentTitle,
          subtitle: l10n.genericErrorComponentSubtitle,
          buttonLabel: l10n.genericErrorComponentTryAgainButton,
        ),
        onPressed: context.read<AccountDetailsCubit>().onRetry,
      ),
    );
  }
}
