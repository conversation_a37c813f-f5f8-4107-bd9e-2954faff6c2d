import 'package:feature_countries_api/data/country_repository.dart';
import 'package:feature_countries_api/model/country_model.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';

class CountryRepositoryImpl implements CountryRepository {
  @override
  // ignore: long-method
  Future<List<CountryModel>> getCountries() async {
    return [
// Afghanistan
      CountryModel(code: 'AF', code3: 'AFG', flag: FlagPointer.AF),
// Albania
      CountryModel(code: 'AL', code3: 'ALB', flag: FlagPointer.AL),
// Algeria
      CountryModel(code: 'DZ', code3: 'DZA', flag: FlagPointer.DZ),
// Andorra
      CountryModel(code: 'AD', code3: 'AND', flag: FlagPointer.AD),
// Angola
      CountryModel(code: 'AO', code3: 'AGO', flag: FlagPointer.AO),
// Anguilla
      CountryModel(code: 'AI', code3: 'AIA', flag: FlagPointer.AI),
// Antigua and Barbuda
      CountryModel(code: 'AG', code3: 'ATG', flag: FlagPointer.AG),
// Argentina
      CountryModel(code: 'AR', code3: 'ARG', flag: FlagPointer.AR),
// Armenia
      CountryModel(code: 'AM', code3: 'ARM', flag: FlagPointer.AM),
// Aruba
      CountryModel(code: 'AW', code3: 'ABW', flag: FlagPointer.AW),
// Australia
      CountryModel(code: 'AU', code3: 'AUS', flag: FlagPointer.AU),
// Austria
      CountryModel(code: 'AT', code3: 'AUT', flag: FlagPointer.AT),
// Azerbaijan
      CountryModel(code: 'AZ', code3: 'AZE', flag: FlagPointer.AZ),
// Bahamas
      CountryModel(code: 'BS', code3: 'BHS', flag: FlagPointer.BS),
// Bahrain
      CountryModel(code: 'BH', code3: 'BHR', flag: FlagPointer.BH),
// Bangladesh
      CountryModel(code: 'BD', code3: 'BGD', flag: FlagPointer.BD),
// Barbados
      CountryModel(code: 'BB', code3: 'BRB', flag: FlagPointer.BB),
// Belarus
      CountryModel(code: 'BY', code3: 'BLR', flag: FlagPointer.BY),
// Belgium
      CountryModel(code: 'BE', code3: 'BEL', flag: FlagPointer.BE),
// Belize
      CountryModel(code: 'BZ', code3: 'BLZ', flag: FlagPointer.BZ),
// Benin
      CountryModel(code: 'BJ', code3: 'BEN', flag: FlagPointer.BJ),
// Bermuda
      CountryModel(code: 'BM', code3: 'BMU', flag: FlagPointer.BM),
// Bhutan
      CountryModel(code: 'BT', code3: 'BTN', flag: FlagPointer.BT),
// Bolivia
      CountryModel(code: 'BO', code3: 'BOL', flag: FlagPointer.BO),
// Bosnia and Herzegovina
      CountryModel(code: 'BA', code3: 'BIH', flag: FlagPointer.BA),
// Botswana
      CountryModel(code: 'BW', code3: 'BWA', flag: FlagPointer.BW),
// Brazil
      CountryModel(
        code: 'BR',
        code3: 'BRA',
        flag: FlagPointer.BR,
        isTinMandatory: true,
      ),
// British Virgin Islands
      CountryModel(code: 'VG', code3: 'VGB', flag: FlagPointer.VG),
// Brunei Darussalam
      CountryModel(code: 'BN', code3: 'BRN', flag: FlagPointer.BN),
// Bulgaria
      CountryModel(code: 'BG', code3: 'BGR', flag: FlagPointer.BG),
// Burkina Faso
      CountryModel(code: 'BF', code3: 'BFA', flag: FlagPointer.BF),
// Burundi
      CountryModel(code: 'BI', code3: 'BDI', flag: FlagPointer.BI),
// Cambodia
      CountryModel(code: 'KH', code3: 'KHM', flag: FlagPointer.KH),
// Cameroon
      CountryModel(code: 'CM', code3: 'CMR', flag: FlagPointer.CM),
// Canada
      CountryModel(code: 'CA', code3: 'CAN', flag: FlagPointer.CA),
// Cape Verde
      CountryModel(code: 'CV', code3: 'CPV', flag: FlagPointer.CV),
// Cayman Islands
      CountryModel(code: 'KY', code3: 'CYM', flag: FlagPointer.KY),
// Central African Republic
      CountryModel(code: 'CF', code3: 'CAF', flag: FlagPointer.CF),
// Chad
      CountryModel(code: 'TD', code3: 'TCD', flag: FlagPointer.TD),
// Chile
      CountryModel(code: 'CL', code3: 'CHL', flag: FlagPointer.CL),
// China
      CountryModel(
        code: 'CN',
        code3: 'CHN',
        flag: FlagPointer.CN,
        isTinMandatory: true,
      ),
// Hong Kong
      CountryModel(code: 'HK', code3: 'HKG', flag: FlagPointer.HK),
// Macao
      CountryModel(code: 'MO', code3: 'MAC', flag: FlagPointer.MO),
// Curaçao
      CountryModel(code: 'CW', code3: 'CUW', flag: FlagPointer.CW),
// Colombia
      CountryModel(code: 'CO', code3: 'COL', flag: FlagPointer.CO),
// Comoros
      CountryModel(code: 'KM', code3: 'COM', flag: FlagPointer.KM),
// Congo-Brazzaville
      CountryModel(code: 'CG', code3: 'COG', flag: FlagPointer.CG),
// Congo-Kinshasa
      CountryModel(code: 'CD', code3: 'COD', flag: FlagPointer.CD),
// Cook Islands
      CountryModel(code: 'CK', code3: 'COK', flag: FlagPointer.CK),
// Costa Rica
      CountryModel(code: 'CR', code3: 'CRI', flag: FlagPointer.CR),
// Côte d'Ivoire
      CountryModel(code: 'CI', code3: 'CIV', flag: FlagPointer.CI),
// Croatia
      CountryModel(code: 'HR', code3: 'HRV', flag: FlagPointer.HR),
// Cyprus
      CountryModel(code: 'CY', code3: 'CYP', flag: FlagPointer.CY),
// Czech Republic
      CountryModel(code: 'CZ', code3: 'CZE', flag: FlagPointer.CZ),
// Denmark
      CountryModel(
        code: 'DK',
        code3: 'DNK',
        flag: FlagPointer.DK,
        isTinMandatory: true,
      ),
// Djibouti
      CountryModel(code: 'DJ', code3: 'DJI', flag: FlagPointer.DJ),
// Dominica
      CountryModel(code: 'DM', code3: 'DMA', flag: FlagPointer.DM),
// Dominican Republic
      CountryModel(code: 'DO', code3: 'DOM', flag: FlagPointer.DO),
// Ecuador
      CountryModel(code: 'EC', code3: 'ECU', flag: FlagPointer.EC),
// Egypt
      CountryModel(code: 'EG', code3: 'EGY', flag: FlagPointer.EG),
// El Salvador
      CountryModel(code: 'SV', code3: 'SLV', flag: FlagPointer.SV),
// Equatorial Guinea
      CountryModel(code: 'GQ', code3: 'GNQ', flag: FlagPointer.GQ),
// Eritrea
      CountryModel(code: 'ER', code3: 'ERI', flag: FlagPointer.ER),
// Estonia
      CountryModel(code: 'EE', code3: 'EST', flag: FlagPointer.EE),
// Ethiopia
      CountryModel(code: 'ET', code3: 'ETH', flag: FlagPointer.ET),
// Faroe Islands
      CountryModel(code: 'FO', code3: 'FRO', flag: FlagPointer.FO),
// Fiji
      CountryModel(code: 'FJ', code3: 'FJI', flag: FlagPointer.FJ),
// Finland
      CountryModel(code: 'FI', code3: 'FIN', flag: FlagPointer.FI),
// France
      CountryModel(code: 'FR', code3: 'FRA', flag: FlagPointer.FR),
// Gabon
      CountryModel(code: 'GA', code3: 'GAB', flag: FlagPointer.GA),
// Gambia
      CountryModel(code: 'GM', code3: 'GMB', flag: FlagPointer.GM),
// Georgia
      CountryModel(code: 'GE', code3: 'GEO', flag: FlagPointer.GE),
// Germany
      CountryModel(
        code: 'DE',
        code3: 'DEU',
        flag: FlagPointer.DE,
        isTinMandatory: true,
      ),
// Ghana
      CountryModel(code: 'GH', code3: 'GHA', flag: FlagPointer.GH),
// Gibraltar
      CountryModel(code: 'GI', code3: 'GIB', flag: FlagPointer.GI),
// Greece
      CountryModel(code: 'GR', code3: 'GRC', flag: FlagPointer.GR),
// Grenada
      CountryModel(code: 'GD', code3: 'GRD', flag: FlagPointer.GD),
// Guatemala
      CountryModel(code: 'GT', code3: 'GTM', flag: FlagPointer.GT),
// Guernsey
      CountryModel(code: 'GG', code3: 'GGY', flag: FlagPointer.GG),
// Guinea
      CountryModel(code: 'GN', code3: 'GIN', flag: FlagPointer.GN),
// Guinea-Bissau
      CountryModel(code: 'GW', code3: 'GNB', flag: FlagPointer.GW),
// Guyana
      CountryModel(code: 'GY', code3: 'GUY', flag: FlagPointer.GY),
// Haiti
      CountryModel(code: 'HT', code3: 'HTI', flag: FlagPointer.HT),
// Honduras
      CountryModel(code: 'HN', code3: 'HND', flag: FlagPointer.HN),
// Hungary
      CountryModel(code: 'HU', code3: 'HUN', flag: FlagPointer.HU),
// Iceland
      CountryModel(code: 'IS', code3: 'ISL', flag: FlagPointer.IS),
// India
      CountryModel(code: 'IN', code3: 'IND', flag: FlagPointer.IN),
// Indonesia
      CountryModel(
        code: 'ID',
        code3: 'IDN',
        flag: FlagPointer.ID,
        isTinMandatory: true,
      ),
// Iraq
      CountryModel(code: 'IQ', code3: 'IRQ', flag: FlagPointer.IQ),
// Ireland
      CountryModel(code: 'IE', code3: 'IRL', flag: FlagPointer.IE),
// Isle of Man
      CountryModel(code: 'IM', code3: 'IMN', flag: FlagPointer.IM),
// Israel
      CountryModel(
        code: 'IL',
        code3: 'ISR',
        flag: FlagPointer.IL,
        isTinMandatory: true,
      ),
// Italy
      CountryModel(
        code: 'IT',
        code3: 'ITA',
        flag: FlagPointer.IT,
        isTinMandatory: true,
      ),
// Jamaica
      CountryModel(code: 'JM', code3: 'JAM', flag: FlagPointer.JM),
// Japan
      CountryModel(code: 'JP', code3: 'JPN', flag: FlagPointer.JP),
// Jersey
      CountryModel(code: 'JE', code3: 'JEY', flag: FlagPointer.JE),
// Jordan
      CountryModel(code: 'JO', code3: 'JOR', flag: FlagPointer.JO),
// Kazakhstan
      CountryModel(code: 'KZ', code3: 'KAZ', flag: FlagPointer.KZ),
// Kenya
      CountryModel(code: 'KE', code3: 'KEN', flag: FlagPointer.KE),
// South Korea
      CountryModel(code: 'KR', code3: 'KOR', flag: FlagPointer.KR),
// Kuwait
      CountryModel(code: 'KW', code3: 'KWT', flag: FlagPointer.KW),
// Kyrgyzstan
      CountryModel(code: 'KG', code3: 'KGZ', flag: FlagPointer.KG),
// Lao
      CountryModel(code: 'LA', code3: 'LAO', flag: FlagPointer.LA),
// Latvia
      CountryModel(code: 'LV', code3: 'LVA', flag: FlagPointer.LV),
// Lebanon
      CountryModel(code: 'LB', code3: 'LBN', flag: FlagPointer.LB),
// Lesotho
      CountryModel(code: 'LS', code3: 'LSO', flag: FlagPointer.LS),
// Liberia
      CountryModel(code: 'LR', code3: 'LBR', flag: FlagPointer.LR),
// Libya
      CountryModel(code: 'LY', code3: 'LBY', flag: FlagPointer.LY),
// Liechtenstein
      CountryModel(code: 'LI', code3: 'LIE', flag: FlagPointer.LI),
// Lithuania
      CountryModel(code: 'LT', code3: 'LTU', flag: FlagPointer.LT),
// Luxembourg
      CountryModel(code: 'LU', code3: 'LUX', flag: FlagPointer.LU),
// Macedonia
      CountryModel(code: 'MK', code3: 'MKD', flag: FlagPointer.MK),
// Madagascar
      CountryModel(code: 'MG', code3: 'MDG', flag: FlagPointer.MG),
// Malawi
      CountryModel(code: 'MW', code3: 'MWI', flag: FlagPointer.MW),
// Malaysia
      CountryModel(code: 'MY', code3: 'MYS', flag: FlagPointer.MY),
// Maldives
      CountryModel(code: 'MV', code3: 'MDV', flag: FlagPointer.MV),
// Mali
      CountryModel(code: 'ML', code3: 'MLI', flag: FlagPointer.ML),
// Malta
      CountryModel(code: 'MT', code3: 'MLT', flag: FlagPointer.MT),
// Marshall Islands
      CountryModel(code: 'MH', code3: 'MHL', flag: FlagPointer.MH),
// Mauritania
      CountryModel(code: 'MR', code3: 'MRT', flag: FlagPointer.MR),
// Mauritius
      CountryModel(code: 'MU', code3: 'MUS', flag: FlagPointer.MU),
// Mexico
      CountryModel(code: 'MX', code3: 'MEX', flag: FlagPointer.MX),
// Moldova
      CountryModel(code: 'MD', code3: 'MDA', flag: FlagPointer.MD),
// Monaco
      CountryModel(code: 'MC', code3: 'MCO', flag: FlagPointer.MC),
// Mongolia
      CountryModel(code: 'MN', code3: 'MNG', flag: FlagPointer.MN),
// Montenegro
      CountryModel(code: 'ME', code3: 'MNE', flag: FlagPointer.ME),
// Montserrat
      CountryModel(code: 'MS', code3: 'MSR', flag: FlagPointer.MS),
// Morocco
      CountryModel(code: 'MA', code3: 'MAR', flag: FlagPointer.MA),
// Mozambique
      CountryModel(code: 'MZ', code3: 'MOZ', flag: FlagPointer.MZ),
// Myanmar
      CountryModel(code: 'MM', code3: 'MMR', flag: FlagPointer.MM),
// Namibia
      CountryModel(code: 'NA', code3: 'NAM', flag: FlagPointer.NA),
// Nauru
      CountryModel(code: 'NR', code3: 'NRU', flag: FlagPointer.NR),
// Nepal
      CountryModel(code: 'NP', code3: 'NPL', flag: FlagPointer.NP),
// Netherlands
      CountryModel(
        code: 'NL',
        code3: 'NLD',
        flag: FlagPointer.NL,
        isTinMandatory: true,
      ),
// New Zealand
      CountryModel(code: 'NZ', code3: 'NZL', flag: FlagPointer.NZ),
// Nicaragua
      CountryModel(code: 'NI', code3: 'NIC', flag: FlagPointer.NI),
// Niger
      CountryModel(code: 'NE', code3: 'NER', flag: FlagPointer.NE),
// Nigeria
      CountryModel(code: 'NG', code3: 'NGA', flag: FlagPointer.NG),
// Niue
      CountryModel(code: 'NU', code3: 'NIU', flag: FlagPointer.NU),
// Norway
      CountryModel(code: 'NO', code3: 'NOR', flag: FlagPointer.NO),
// Oman
      CountryModel(code: 'OM', code3: 'OMN', flag: FlagPointer.OM),
// Pakistan
      CountryModel(code: 'PK', code3: 'PAK', flag: FlagPointer.PK),
// Palau
      CountryModel(code: 'PW', code3: 'PLW', flag: FlagPointer.PW),
// Panama
      CountryModel(code: 'PA', code3: 'PAN', flag: FlagPointer.PA),
// Papua New Guinea
      CountryModel(code: 'PG', code3: 'PNG', flag: FlagPointer.PG),
// Paraguay
      CountryModel(code: 'PY', code3: 'PRY', flag: FlagPointer.PY),
// Peru
      CountryModel(code: 'PE', code3: 'PER', flag: FlagPointer.PE),
// Philippines
      CountryModel(code: 'PH', code3: 'PHL', flag: FlagPointer.PH),
// Poland
      CountryModel(code: 'PL', code3: 'POL', flag: FlagPointer.PL),
// Portugal
      CountryModel(
        code: 'PT',
        code3: 'PRT',
        flag: FlagPointer.PT,
        isTinMandatory: true,
      ),
// Qatar
      CountryModel(code: 'QA', code3: 'QAT', flag: FlagPointer.QA),
// Romania
      CountryModel(code: 'RO', code3: 'ROU', flag: FlagPointer.RO),
// Russia
      CountryModel(
        code: 'RU',
        code3: 'RUS',
        flag: FlagPointer.RU,
        isTinMandatory: true,
      ),
// Rwanda
      CountryModel(code: 'RW', code3: 'RWA', flag: FlagPointer.RW),
// Saint Kitts and Nevis
      CountryModel(code: 'KN', code3: 'KNA', flag: FlagPointer.KN),
// Saint Lucia
      CountryModel(code: 'LC', code3: 'LCA', flag: FlagPointer.LC),
// Sint Maarten
      CountryModel(code: 'SX', code3: 'SXM', flag: FlagPointer.SX),
// Saint Vincent and Grenadines
      CountryModel(code: 'VC', code3: 'VCT', flag: FlagPointer.VC),
// Samoa
      CountryModel(code: 'WS', code3: 'WSM', flag: FlagPointer.WS),
// San Marino
      CountryModel(code: 'SM', code3: 'SMR', flag: FlagPointer.SM),
// Sao Tome and Principe
      CountryModel(code: 'ST', code3: 'STP', flag: FlagPointer.ST),
// Saudi Arabia
      CountryModel(code: 'SA', code3: 'SAU', flag: FlagPointer.SA),
// Senegal
      CountryModel(code: 'SN', code3: 'SEN', flag: FlagPointer.SN),
// Serbia
      CountryModel(code: 'RS', code3: 'SRB', flag: FlagPointer.RS),
// Seychelles
      CountryModel(code: 'SC', code3: 'SYC', flag: FlagPointer.SC),
// Sierra Leone
      CountryModel(code: 'SL', code3: 'SLE', flag: FlagPointer.SL),
// Singapore
      CountryModel(code: 'SG', code3: 'SGP', flag: FlagPointer.SG),
// Slovakia
      CountryModel(code: 'SK', code3: 'SVK', flag: FlagPointer.SK),
// Slovenia
      CountryModel(code: 'SI', code3: 'SVN', flag: FlagPointer.SI),
// Solomon Islands
      CountryModel(code: 'SB', code3: 'SLB', flag: FlagPointer.SB),
// Somalia
      CountryModel(code: 'SO', code3: 'SOM', flag: FlagPointer.SO),
// South Africa
      CountryModel(code: 'ZA', code3: 'ZAF', flag: FlagPointer.ZA),
// Spain
      CountryModel(
        code: 'ES',
        code3: 'ESP',
        flag: FlagPointer.ES,
        isTinMandatory: true,
      ),
// Sri Lanka
      CountryModel(code: 'LK', code3: 'LKA', flag: FlagPointer.LK),
// Sudan
      CountryModel(code: 'SD', code3: 'SDN', flag: FlagPointer.SD),
// Suriname
      CountryModel(code: 'SR', code3: 'SUR', flag: FlagPointer.SR),
// Swaziland
      CountryModel(code: 'SZ', code3: 'SWZ', flag: FlagPointer.SZ),
// Sweden
      CountryModel(code: 'SE', code3: 'SWE', flag: FlagPointer.SE),
// Switzerland
      CountryModel(code: 'CH', code3: 'CHE', flag: FlagPointer.CH),
// Taiwan
      CountryModel(code: 'TW', code3: 'TWN', flag: FlagPointer.TW),
// Tajikistan
      CountryModel(code: 'TJ', code3: 'TJK', flag: FlagPointer.TJ),
// Tanzania
      CountryModel(code: 'TZ', code3: 'TZA', flag: FlagPointer.TZ),
// Thailand
      CountryModel(code: 'TH', code3: 'THA', flag: FlagPointer.TH),
// Timor-Leste
      CountryModel(code: 'TL', code3: 'TLS', flag: FlagPointer.TL),
// Togo
      CountryModel(code: 'TG', code3: 'TGO', flag: FlagPointer.TG),
// Tonga
      CountryModel(code: 'TO', code3: 'TON', flag: FlagPointer.TO),
// Trinidad and Tobago
      CountryModel(code: 'TT', code3: 'TTO', flag: FlagPointer.TT),
// Tunisia
      CountryModel(code: 'TN', code3: 'TUN', flag: FlagPointer.TN),
// Turkey
      CountryModel(
        code: 'TR',
        code3: 'TUR',
        flag: FlagPointer.TR,
        isTinMandatory: true,
      ),
// Turkmenistan
      CountryModel(code: 'TM', code3: 'TKM', flag: FlagPointer.TM),
// Turks and Caicos Islands
      CountryModel(code: 'TC', code3: 'TCA', flag: FlagPointer.TC),
// Uganda
      CountryModel(code: 'UG', code3: 'UGA', flag: FlagPointer.UG),
// Ukraine
      CountryModel(code: 'UA', code3: 'UKR', flag: FlagPointer.UA),
// United Arab Emirates
      CountryModel(code: 'AE', code3: 'ARE', flag: FlagPointer.AE),
// United Kingdom
      CountryModel(code: 'GB', code3: 'GBR', flag: FlagPointer.GB),
// United States of America
      CountryModel(
        code: 'US',
        code3: 'USA',
        flag: FlagPointer.US,
        isTinMandatory: true,
      ),
// Uruguay
      CountryModel(code: 'UY', code3: 'URY', flag: FlagPointer.UY),
// Uzbekistan
      CountryModel(code: 'UZ', code3: 'UZB', flag: FlagPointer.UZ),
// Vanuatu
      CountryModel(code: 'VU', code3: 'VUT', flag: FlagPointer.VU),
// Venezuela
      CountryModel(code: 'VE', code3: 'VEN', flag: FlagPointer.VE),
// Vietnam
      CountryModel(code: 'VN', code3: 'VNM', flag: FlagPointer.VN),
// Yemen
      CountryModel(code: 'YE', code3: 'YEM', flag: FlagPointer.YE),
// Zambia
      CountryModel(code: 'ZM', code3: 'ZMB', flag: FlagPointer.ZM),
// Zimbabwe
      CountryModel(code: 'ZW', code3: 'ZWE', flag: FlagPointer.ZW),
    ];
  }
}
