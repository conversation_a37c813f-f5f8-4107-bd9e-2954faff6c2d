import 'dart:async';

import 'package:connectivity_ui/index.dart';
import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:instruments_api/domain/model/instrument_search_params.dart';
import 'package:instruments_ui/index.dart';
import 'package:instruments_ui/screens/instruments_search/instruments_search_actions.dart';
import 'package:neobroker_models/models.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_rewards_ui/l10n/rewards_localization.g.dart';
import 'package:wio_feature_rewards_ui/src/common/widgets/rewards_error_widget.dart';
import 'package:wio_feature_rewards_ui/src/screens/rewards_auto_invest_pick_company/cubit/rewards_auto_invest_pick_company_cubit.dart';
import 'package:wio_feature_rewards_ui/src/screens/rewards_auto_invest_pick_company/cubit/rewards_auto_invest_pick_company_state.dart';
import 'package:wio_tab_helper_ui/index.dart';

part 'widgets/rewards_auto_invest_pick_company_instruments.dart';
part 'widgets/rewards_auto_invest_pick_company_loaded_view.dart';
part 'widgets/rewards_auto_invest_pick_company_loading_view.dart';

class RewardsAutoInvestPickCompanyPage extends StatelessWidget {
  const RewardsAutoInvestPickCompanyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
          backgroundColor: CompanyColorPointer.background1,
        ),
      ),
      body: _RewardsAutoInvestPickCompanyChangeBody(),
    );
  }
}

class _RewardsAutoInvestPickCompanyChangeBody extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RewardsAutoInvestPickCompanyCubit,
        RewardsAutoInvestPickCompanyState>(
      builder: (_, state) {
        return state.map(
          initial: (_) => const SizedBox.shrink(),
          loading: (_) => const _RewardsAutoInvestPickCompanyLoadingView(),
          idle: (_) => const _RewardsAutoInvestPickCompanyLoadedView(),
          failed: (_) => RewardsErrorWidget(
            callback: context.read<RewardsAutoInvestPickCompanyCubit>().init,
          ),
        );
      },
    );
  }
}
