import 'package:flutter/material.dart';

class ListBoxPlaceHolder extends StatelessWidget {
  final double height;

  const ListBoxPlaceHolder({
    required this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.black,
      ),
    );
  }
}
