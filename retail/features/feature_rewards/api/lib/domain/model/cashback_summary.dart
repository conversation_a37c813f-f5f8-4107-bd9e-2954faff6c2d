import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';

part 'cashback_summary.freezed.dart';

@freezed
class CashbackSummary with _$CashbackSummary {
  const factory CashbackSummary({
    required Money earnedTotalCashback,
    required Money totalCashbackLimit,
    required Money earnedCreditCashback,
    required Money creditCashbackLimit,
  }) = _CashbackSummary;
}
