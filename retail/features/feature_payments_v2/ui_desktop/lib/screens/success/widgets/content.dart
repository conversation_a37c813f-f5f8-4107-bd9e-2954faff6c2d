import 'package:feature_payments_ui_desktop/feature_payments_ui_desktop.dart';
import 'package:flutter/material.dart' hide Checkbox;
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_retail_desktop/ui_kit_retail_desktop.dart';

class SuccessContent extends StatelessWidget {
  final bool isCompactView;

  const SuccessContent({required this.isCompactView, super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsUiDesktopLocalizations.of(context);
    final colorStyling = context.colorStyling;
    final dividerColor = CompanyColorPointer.secondary3.colorOf(context);

    return Column(
      crossAxisAlignment:
          isCompactView ? CrossAxisAlignment.center : CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isCompactView)
          Icon(
            CompanyPictograms.validation_success,
            size: 180,
            color: colorStyling.primary4,
          ),
        Padding(
          padding: EdgeInsets.only(top: isCompactView ? 56 : 0),
          child: Label(
            model: LabelModel(
              text: l10n.npssEnrollmentSuccessTitle,
              textStyle: CompanyTextStylePointer.h1,
              color: CompanyColorPointer.primary4,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 8),
          child: Label(
            model: LabelModel(
              text: l10n.npssAuthSuccessSubtitle,
              textStyle: CompanyTextStylePointer.h3,
              color: CompanyColorPointer.primary4,
            ),
          ),
        ),
        Divider(color: dividerColor, height: 48),
        Label(
          model: LabelModel(
            text: l10n.npssEnrollmentSuccessText,
            color: CompanyColorPointer.secondary5,
          ),
        ),
        Divider(color: dividerColor, height: 48),
      ],
    );
  }
}
