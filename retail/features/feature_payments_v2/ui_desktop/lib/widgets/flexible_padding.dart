import 'package:flutter/material.dart';

class FlexiblePadding extends StatelessWidget {
  final int leftSpacerFlex;
  final int rightSpacerFlex;
  final int contentFlex;
  final Widget child;

  const FlexiblePadding({
    required this.child,
    this.leftSpacerFlex = 1,
    this.rightSpacerFlex = 1,
    this.contentFlex = 5,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final content = [
      Spacer(flex: leftSpacerFlex),
      Flexible(flex: contentFlex, child: child),
      Spacer(flex: rightSpacerFlex),
    ];

    return Row(children: content);
  }
}
