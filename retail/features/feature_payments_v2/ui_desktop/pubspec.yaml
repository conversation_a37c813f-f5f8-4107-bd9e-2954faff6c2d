name: feature_payments_ui_desktop
publish_to: none
version: 0.0.1
environment: 
  sdk: '>=3.6.0 <4.0.0'
flutter: 
  uses-material-design: true
dependencies: 
  collection: 1.19.0
  common_feature_analytics_ui: 
    path: ../../../../common/features/feature_analytics/ui
  common_feature_toggle_api: 
    path: ../../../../common/tools/feature_toggle/api
  di: 
    path: ../../../../core/di
  feature_auth_api: 
    path: ../../feature_auth/api
  flutter: 
    sdk: flutter
  flutter_bloc: 9.0.0
  flutter_localizations: 
    sdk: flutter
  flutter_svg: 2.0.17
  freezed_annotation: 2.4.4
  intl: 0.19.0
  ui_kit_legacy_core: 
    path: ../../../../ui_kit_legacy/ui_kit_core
  ui_kit_legacy_retail_desktop: 
    path: ../../../../ui_kit_legacy/ui_kit_retail_desktop
  url_launcher: 6.3.1
  wio_app_core_api:
    path: ../../../../core/app_core/api
  wio_common_feature_payments_api: 
    path: ../../../../common/features/feature_payments/api
  wio_common_feature_payments_impl: 
    path: ../../../../common/features/feature_payments/impl
  wio_core_navigation_api: 
    path: ../../../../core/navigation/api
  wio_core_navigation_ui: 
    path: ../../../../core/navigation/ui
  wio_feature_account_api: 
    path: ../../../../common/features/feature_account/api
  wio_feature_core_ui_desktop: 
    path: ../../../core/ui_desktop
  wio_feature_error_domain_api: 
    path: ../../../../common/features/feature_error/api
  wio_feature_payments_v2_api:
    path: ../api
  wio_feature_retail_identity_domain_api: 
    path: ../../../../common/features/feature_retail_identity/api
dev_dependencies:
  bloc_test: 10.0.0
  build_runner: 2.4.14
  core_lints:
    path: ../../../../tooling/core_lints
  fake_async: 1.3.1
  flutter_test:
    sdk: flutter
  freezed: 2.5.7
  mocktail: 1.0.4
  tests:
    path: ../../../../core/tests/impl
  tests_ui:
    path: ../../../../core/tests/ui
  url_launcher_platform_interface: 2.3.2
