import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/currency_selection/cubit/add_bene_currency_selection_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/currency_selection/cubit/add_bene_currency_selection_cubit_state.dart';

import '../../../factories.dart';
import '../../../feature_payments_mocks.dart';

void main() {
  late Logger logger;
  late PaymentInteractor paymentInteractor;
  late MockCommonErrorHandler mockCommonErrorHandler;
  late AddBeneCurrencySelectionCubit cubit;
  late AddBeneFlowScreenController controller;

  final delegate = MockAddBeneCurrencySelectionDelegate();

  const countrySelected = PaymentCountry.localCountry;

  final supportedCurrencies = [
    CurrencyFactory.rand(),
    CurrencyFactory.rand(),
    CurrencyFactory.rand(),
  ];

  setUp(() {
    mockCommonErrorHandler = MockCommonErrorHandler();
    paymentInteractor = MockPaymentInteractor();
    logger = MockLogger();
    controller = MockAddBenFlowScreenController();

    cubit = AddBeneCurrencySelectionCubit(
      logger: logger,
      interactor: paymentInteractor,
      delegate: delegate,
      commonErrorHandler: mockCommonErrorHandler,
    )..initializeStepScreen(controller);

    when(
      () => paymentInteractor.getSupportedCurrencies(
        countryCode: countrySelected.code,
      ),
    ).justAnswerAsync(supportedCurrencies);
  });

  group('Initialization', () {
    blocTest<AddBeneCurrencySelectionCubit, AddBeneCurrencySelectionState>(
      'Init',
      seed: () => const AddBeneCurrencySelectionState.loading(),
      build: () => cubit,
      act: (cubit) => cubit.initialize(countrySelected),
      verify: (cubit) {
        expect(
          cubit.state,
          AddBeneCurrencySelectionState.loaded(
            selectedCountry: countrySelected,
            supportedCurrencies: supportedCurrencies,
          ),
        );
      },
    );

    blocTest<AddBeneCurrencySelectionCubit, AddBeneCurrencySelectionState>(
      'throws after init',
      build: () => cubit,
      act: (cubit) => cubit.initialize(countrySelected),
      setUp: () {
        when(
          () => paymentInteractor.getSupportedCurrencies(
            countryCode: countrySelected.code,
          ),
        ).justThrowAsync(Exception('Smth went wrong'));
      },
      verify: (cubit) {
        expect(
          cubit.state,
          const AddBeneCurrencySelectionState.failure(),
        );
      },
    );

    blocTest<AddBeneCurrencySelectionCubit, AddBeneCurrencySelectionState>(
      'on Country Selected',
      // Arrange
      build: () => cubit,
      seed: () => AddBeneCurrencySelectionState.loaded(
        selectedCountry: countrySelected,
        supportedCurrencies: supportedCurrencies,
      ),
      setUp: () => when(
        () => delegate.onCurrencySelected(
          countrySelected,
          supportedCurrencies.first,
        ),
      ).justComplete(),

      // Act
      act: (cubit) => cubit.onCurrencySelected(
        supportedCurrencies.first,
      ),

      // Assert
      verify: (cubit) {
        verify(
          () => delegate.onCurrencySelected(
            countrySelected,
            supportedCurrencies.first,
          ),
        ).calledOnce;
      },
    );
  });
}
