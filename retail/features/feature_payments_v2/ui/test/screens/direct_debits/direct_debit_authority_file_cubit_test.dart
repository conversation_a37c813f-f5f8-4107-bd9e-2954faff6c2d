import 'dart:convert';

import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/direct_debits/direct_debit_authority_details/widgets/direct_debit_authority_file/cubit/direct_debit_authority_file_cubit.dart';

import '../../factories.dart';
import '../../feature_payments_mocks.dart';

void main() {
  late MockLogger logger;
  late MockDirectDebitsInteractor directDebitsInteractor;
  late DirectDebitAuthorityFileCubit cubit;

  final directDebitAuthorityFile = DirectDebitAuthorityFile(
    contentBase64: base64Decode('dGVzdCBjb250ZW50'),
    name: 'test_file.pdf',
    type: 'application/pdf',
    size: 12,
  );

  final debit = DirectDebitsFactory.directDebitAuthorityDetails();

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
  });

  setUp(() {
    logger = MockLogger();
    directDebitsInteractor = MockDirectDebitsInteractor();

    cubit = DirectDebitAuthorityFileCubit(
      directDebitsInteractor: directDebitsInteractor,
      logger: logger,
      debit: debit,
    );
  });

  group('DirectDebitAuthorityFileCubit', () {
    test('initial state should be loading', () {
      expect(cubit.state, isA<DirectDebitAuthorityFileLoadingState>());
    });

    test('toString should return correct string', () {
      expect(cubit.toString(), equals('DirectDebitAuthorityFileCubit'));
    });

    blocTest<DirectDebitAuthorityFileCubit, DirectDebitAuthorityFileState>(
      'emits [DirectDebitAuthorityFileLoadedState] when init succeeds',
      build: () {
        when(() => directDebitsInteractor.getDirectDebitAuthorityFile(any()))
            .thenAnswer((_) async => directDebitAuthorityFile);

        return cubit;
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        isA<DirectDebitAuthorityFileLoadedState>(),
      ],
      verify: (_) {
        verify(
          () => directDebitsInteractor.getDirectDebitAuthorityFile(debit.id),
        ).called(1);
      },
    );

    blocTest<DirectDebitAuthorityFileCubit, DirectDebitAuthorityFileState>(
      'emits [DirectDebitAuthorityFileFailedState] when init fails',
      build: () {
        when(() => directDebitsInteractor.getDirectDebitAuthorityFile(any()))
            .thenThrow(Exception('Test error'));
        when(
          () => logger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).thenReturn(null);

        return cubit;
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        isA<DirectDebitAuthorityFileFailedState>(),
      ],
      verify: (cubit) {
        verify(
          () => directDebitsInteractor.getDirectDebitAuthorityFile(debit.id),
        ).called(1);
        verify(
          () => logger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(1);
      },
    );

    blocTest<DirectDebitAuthorityFileCubit, DirectDebitAuthorityFileState>(
      'emits [DirectDebitAuthorityFileEmptyState] '
      'when captureMode is not paperBased',
      build: () {
        final internetBankingDebit = debit.copyWith(
          captureMode: DirectDebitAuthorityCaptureMode.internetBanking,
        );

        return DirectDebitAuthorityFileCubit(
          directDebitsInteractor: directDebitsInteractor,
          logger: logger,
          debit: internetBankingDebit,
        );
      },
      act: (cubit) => cubit.init(),
      expect: () => [
        isA<DirectDebitAuthorityFileEmptyState>(),
      ],
      verify: (_) {
        verifyNever(
          () => directDebitsInteractor.getDirectDebitAuthorityFile(any()),
        );
      },
    );

    test('should be disposed correctly', () async {
      await cubit.close();
      // Should not throw
    });
  });
}
