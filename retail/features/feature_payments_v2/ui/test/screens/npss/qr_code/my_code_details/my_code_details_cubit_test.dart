import 'dart:io';

import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/configs/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/cubit/my_code_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/cubit/my_code_details_state.dart';

import '../../../../factories.dart';
import '../../../../feature_payments_mocks.dart';

void main() {
  late MyCodeDetailsCubit cubit;
  late Logger mockLogger;
  late MockShareProvider mockShareProvider;
  late MockNPSSInteractor mockNPSSInteractor;
  late MockCommonErrorHandler mockCommonErrorHandler;
  late PaymentsLocalizations l10n;
  late MockWioPaymentsAnalytics mockAnalytics;
  late MockToastMessageProvider toastMessageProvider;
  late MockNavigationProvider navigationProvider;

  final aedAccount = AccountFactory.rand();

  setUp(() {
    mockLogger = MockLogger();
    mockShareProvider = MockShareProvider();
    mockNPSSInteractor = MockNPSSInteractor();
    mockCommonErrorHandler = MockCommonErrorHandler();
    l10n = MockPaymentsLocalizations();
    mockAnalytics = MockWioPaymentsAnalytics();
    toastMessageProvider = MockToastMessageProvider();
    navigationProvider = MockNavigationProvider();

    cubit = MyCodeDetailsCubit(
      logger: mockLogger,
      shareProvider: mockShareProvider,
      npssInteractor: mockNPSSInteractor,
      commonErrorHandler: mockCommonErrorHandler,
      l10n: l10n,
      analytics: mockAnalytics,
      toastMessageProvider: toastMessageProvider,
      navigationProvider: navigationProvider,
    );
  });

  group('MyCodeDetailsCubit', () {
    test('initial state is loading', () {
      expect(cubit.state, const MyCodeDetailsState.loading());
    });

    test('init subscribes to enrollment status', () async {
      when(() => mockNPSSInteractor.getDefaultAvailableAccount())
          .thenAnswer((_) async => aedAccount);
      when(
        () => mockNPSSInteractor.enrollmentStatusStream,
      ).thenAnswer(
        (invocation) => Stream.value(
          const NPSSCustomerStatusData(
            status: NPSSCustomerEnrollmentStatus.enrolled,
            accounts: [],
            staticQrCode: 'staticQrCode',
          ),
        ),
      );

      await cubit.init();
      verify(() => mockNPSSInteractor.enrollmentStatusStream).called(1);
    });

    group('captureAndShareQrCode', () {
      test('shares QR code successfully', () async {
        final mockFile = File('test.png');
        final captureImage = Future.value(mockFile);

        when(
          () => l10n.qrCodeShareMessage,
        ).thenReturn('Hey! You can scan this to send me the money. Thanks!');

        when(
          () => mockShareProvider.shareFile(
            any(),
            text: any(named: 'text'),
            mimeType: any(named: 'mimeType'),
          ),
        ).thenAnswer((_) async {});

        await cubit.captureAndShareQrCode(() => captureImage);

        verify(
          () => mockShareProvider.shareFile(
            [mockFile.path],
            text: 'Hey! You can scan this to send me the money. Thanks!',
            mimeType: 'image/png',
          ),
        ).called(1);
      });

      test('handles error when sharing fails', () async {
        final mockFile = File('test.png');
        final captureImage = Future.value(mockFile);

        when(
          () => l10n.qrCodeShareMessage,
        ).thenReturn('Hey! You can scan this to send me the money. Thanks!');

        when(
          () => mockShareProvider.shareFile(
            any(),
            text: any(named: 'text'),
            mimeType: any(named: 'mimeType'),
          ),
        ).thenThrow(Exception('Share failed'));

        await cubit.captureAndShareQrCode(() => captureImage);

        verify(
          () => mockLogger.error(
            'Failed to share QR code',
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(1);
      });
    });

    group('onTryAgainPressed', () {
      test('refreshes enrollment status successfully', () async {
        when(() => mockNPSSInteractor.refreshEnrollmentStatus())
            .justAnswerAsync(
          const NPSSCustomerStatusData(
            status: NPSSCustomerEnrollmentStatus.enrolled,
            accounts: [],
            staticQrCode: 'staticQrCode',
          ),
        );

        await cubit.onTryAgainPressed();

        verify(() => mockNPSSInteractor.refreshEnrollmentStatus()).called(1);
      });

      test('handles error when refresh fails', () async {
        final error = Exception('Refresh failed');
        when(() => mockNPSSInteractor.refreshEnrollmentStatus())
            .thenThrow(error);
        when(() => mockCommonErrorHandler.errorMessage(error))
            .thenReturn('Error message');

        await cubit.onTryAgainPressed();

        verify(() => mockCommonErrorHandler.handleError(error)).called(1);
        expect(
          cubit.state,
          MyCodeDetailsState.failure(errorMessage: 'Error message'),
        );
      });
    });
  });

  blocTest<MyCodeDetailsCubit, MyCodeDetailsState>(
    '''asks confirmation to set default acount and call set default account of interactor''',
    setUp: () {
      registerFallbackValue(
        const PaymentsBottomSheetConfig.npssSetDefaultAccountConfirmation(),
      );
      registerFallbackValue(
        NotificationToastMessageConfiguration.warning(randomString()),
      );
      when(
        () => toastMessageProvider.showRetailMobileThemedToastMessage(any()),
      ).justComplete();
      when(
        () => navigationProvider.showBottomSheet(
          const PaymentsBottomSheetConfig.npssSetDefaultAccountConfirmation(),
        ),
      ).justAnswerAsync(
        NPSSSetDefaultAccountConfirmationBottomSheetResult(agree: true),
      );
      when(
        () => mockNPSSInteractor.setAaniDefaultAccount(any()),
      ).justCompleteAsync();
      when(
        () => mockNPSSInteractor.refreshEnrollmentStatus(),
      ).justAnswerAsync(
        NPSSCustomerStatusData(
          status: NPSSCustomerEnrollmentStatus.enrolled,
          accounts: [
            NPSSAccount(
              iban: randomString(),
              currency: Currency.aed,
              isDefaultAccount: true,
            ),
          ],
          staticQrCode: 'staticQrCode',
        ),
      );
    },
    build: () => cubit,
    seed: () => MyCodeDetailsState.loaded(
      staticQrCode: 'staticQrCode',
      accountDetails: aedAccount,
      isWioAccountDefault: false,
    ),
    act: (cubit) => cubit.onDefaultAccountTogglePressed(),
    verify: (cubit) {
      verify(
        () => navigationProvider.showBottomSheet(
          const PaymentsBottomSheetConfig.npssSetDefaultAccountConfirmation(),
        ),
      );
      verify(() => mockNPSSInteractor.setAaniDefaultAccount(any()));
      verify(() => mockNPSSInteractor.refreshEnrollmentStatus());
    },
  );
}
