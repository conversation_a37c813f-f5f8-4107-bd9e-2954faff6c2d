import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/navigation/configs/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/all_beneficiaries/cubit/all_beneficiaries_cubit.dart';

import '../../../factories.dart';
import '../../../feature_payments_mocks.dart';
import '../../../mock_data.dart';

void main() {
  late NavigationProvider navigationProvider;
  late WioPaymentsAnalytics analytics;
  late Logger logger;
  late AllBeneficiariesCubit cubit;
  late FeatureToggleProvider featureToggleProvider;
  late BeneficiaryInteractor beneficiaryInteractor;

  final beneficiariesA = [
    InternationalBeneficiaryFactory.rand().copyWith(accountHolderName: 'a1'),
    InternationalBeneficiaryFactory.rand().copyWith(accountHolderName: 'a2'),
  ];

  final beneficiaries = [
    ...beneficiariesA,
    InternationalBeneficiaryFactory.rand().copyWith(
      accountHolderName: 'b1',
      nickname: 'nb',
    ),
    InternationalBeneficiaryFactory.rand().copyWith(
      accountHolderName: 'c1',
      nickname: 'nc',
    ),
  ];

  setUp(() {
    navigationProvider = MockNavigationProvider();
    analytics = MockWioPaymentsAnalytics();
    logger = MockLogger();
    featureToggleProvider = MockFeatureToggleProvider();
    beneficiaryInteractor = MockBeneficiaryInteractor();

    cubit = AllBeneficiariesCubit(
      beneficiaryInteractor: beneficiaryInteractor,
      navigation: navigationProvider,
      logger: logger,
      analytics: analytics,
      featureToggleProvider: featureToggleProvider,
    );

    when(
      () => featureToggleProvider
          .get<bool>(PaymentsFeatureToggle.isAddBeneficiaryFlowEnabled),
    ).thenReturn(true);
  });

  blocTest<AllBeneficiariesCubit, AllBeneficiariesState>(
    'onBeneficiarySelected',
    build: () => cubit,
    setUp: () {
      when(
        () => navigationProvider.push(
          InternationalBeneficiaryDetailsNavigationConfig(
            beneficiary: MockData.germanyEurBeneficiary,
            beneficiaryType: MockData.germanyEurBeneficiary.type,
          ),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.onBeneficiarySelected(
      MockData.germanyEurBeneficiary,
    ),
    verify: (cubit) {
      verify(() => analytics.beneSelection()).calledOnce;
    },
  );

  blocTest<AllBeneficiariesCubit, AllBeneficiariesState>(
    'onSearch',
    build: () => cubit,
    seed: () => AllBeneficiariesState.loaded(
      all: beneficiaries,
      filtered: beneficiaries,
    ),
    setUp: () {
      when(
        () => navigationProvider.push(
          InternationalBeneficiaryDetailsNavigationConfig(
            beneficiary: MockData.germanyEurBeneficiary,
            beneficiaryType: MockData.germanyEurBeneficiary.type,
          ),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.onSearch('a'),
    expect: () => [
      AllBeneficiariesState.loaded(
        all: beneficiaries,
        filtered: beneficiariesA,
      ),
    ],
  );
}
