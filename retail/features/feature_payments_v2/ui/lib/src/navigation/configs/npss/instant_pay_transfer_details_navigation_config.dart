import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_payments_v2_api/payments_v2_api.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/send/send_flow/npss_flow_builder.dart';

part 'instant_pay_transfer_details_navigation_config.freezed.dart';

@freezed
class InstantPayTransferDetailsNavigationConfig extends ScreenNavigationConfig
    with _$InstantPayTransferDetailsNavigationConfig {
  static const screenId = 'npss_transfer_details';

  const factory InstantPayTransferDetailsNavigationConfig({
    required NPSSDraftTransfer draftTransfer,
    required NPSSFlowData flowData,
  }) = _InstantPayTransferDetailsNavigationConfig;

  const InstantPayTransferDetailsNavigationConfig._()
      : super(
          feature: PaymentsV2FeatureNavigationConfig.name,
          id: screenId,
        );

  @override
  String toString() => 'InstantPayTransferDetailsNavigationConfig';
}
