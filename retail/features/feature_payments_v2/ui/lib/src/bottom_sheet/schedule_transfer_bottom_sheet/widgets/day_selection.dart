part of 'monthly_repeat_selection.dart';

const double _kDayPickerRowHeight = 42.0;

class _DayPickerGridDelegate extends SliverGridDelegate {
  const _DayPickerGridDelegate();

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    const columnCount = DateTime.daysPerWeek;
    final tileWidth = constraints.crossAxisExtent / columnCount;

    return SliverGridRegularTileLayout(
      crossAxisCount: columnCount,
      mainAxisStride: _kDayPickerRowHeight,
      crossAxisStride: tileWidth,
      childMainAxisExtent: _kDayPickerRowHeight,
      childCrossAxisExtent: tileWidth,
      reverseCrossAxis: axisDirectionIsReversed(constraints.crossAxisDirection),
    );
  }

  @override
  bool shouldRelayout(_DayPickerGridDelegate oldDelegate) => false;
}

const _DayPickerGridDelegate _kDayPickerGridDelegate = _DayPickerGridDelegate();

class _DayPicker extends StatelessWidget {
  final int? selectedDay;
  final ValueChanged<int>? onChanged;

  const _DayPicker({this.selectedDay, this.onChanged});

  @override
  Widget build(BuildContext context) {
    const days = 28;
    final labels = <Widget>[];

    for (var index = 0; index < days; index++) {
      final dayToBuild = index + 1;

      final selected = selectedDay == dayToBuild;

      labels.add(
        _DayWidget(
          day: dayToBuild,
          onTap: onChanged,
          selected: selected,
        ),
      );
    }

    return GridView.custom(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: _kDayPickerGridDelegate,
      childrenDelegate:
          SliverChildListDelegate(labels, addRepaintBoundaries: false),
      padding: EdgeInsets.zero,
    );
  }
}

class _DayWidget extends StatelessWidget {
  final int day;
  final ValueChanged<int>? onTap;
  final bool selected;

  const _DayWidget({
    required this.day,
    this.onTap,
    this.selected = false,
  });

  @override
  Widget build(BuildContext context) {
    final formattedDay = day;

    final Widget child = Container(
      margin: const EdgeInsets.all(5),
      decoration: selected
          ? BoxDecoration(
              shape: BoxShape.circle,
              color: context.colorStyling.surface1,
            )
          : null,
      padding: const EdgeInsets.all(2),
      child: Center(
        child: Semantics(
          label: '$formattedDay',
          selected: selected,
          sortKey: OrdinalSortKey(day.toDouble()),
          child: ExcludeSemantics(
            child: Text(
              '$day',
              style: context.textStyling.b2.copyWith(
                color: selected ? context.colorStyling.primary2 : null,
              ),
              softWrap: false,
              overflow: TextOverflow.visible,
            ),
          ),
        ),
      ),
    );

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => onTap?.call(day),
      child: child,
    );
  }
}
