import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/authentication/cubit/authentication_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/common/aani_tnc_list_box.dart';

class AuthenticationLoadedState extends StatelessWidget {
  final NPSSCustomer customer;
  final bool isTermsAndConditionsAccepted;

  const AuthenticationLoadedState({
    required this.customer,
    required this.isTermsAndConditionsAccepted,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);
    final selectedAccount = customer.accounts.firstOrNull;
    final bloc = context.read<AuthenticationCubit>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 49),
          child: Label(
            model: LabelModel(
              text: l10n.npssAuthConfirmTitle,
              textStyle: CompanyTextStylePointer.h2medium,
              color: CompanyColorPointer.primary3,
              maxLines: 2,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: Label(
            model: LabelModel(
              text: l10n.npssAuthLetsReconfirmDetailsLabel,
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.primary3,
              maxLines: 2,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: ListDetailsContainer(
            model: ListDetailsContainerModel(
              items: [
                if (customer.mobile.isNotEmpty)
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.npssEnrollConfirmPhoneLabel,
                    ),
                    valueModel: ListDetailsValueModel.text(
                      textModel: ListDetailValueTextModel.label(
                        content: customer.mobile.applyHiddenMask(),
                      ),
                    ),
                  ),
                if (customer.email.isNotEmpty)
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.npssEnrollConfirmEmailIdLabel,
                    ),
                    valueModel: ListDetailsValueModel.text(
                      textModel: ListDetailValueTextModel.label(
                        content: customer.email,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 18),
          child: CompanyRichText(
            CompanyRichTextModel(
              text: '${l10n.npssEnrollConfirmAccountPart1Label} '
                  '${l10n.npssEnrollConfirmAccountPart2Label}',
              highlightedTextModels: [
                HighlightedTextModel(
                  l10n.npssEnrollConfirmAccountPart2Label,
                ),
              ],
              normalStyle: CompanyTextStylePointer.h3,
              textAlign: TextAlign.start,
            ),
          ),
        ),
        if (selectedAccount != null)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: ListBox(
              listBoxModel: ListBoxModel(
                leftPartModel: const ListBoxPartModel.flag(
                  flag: FlagPointer.AE,
                ),
                textModel: ListBoxTextModel(
                  title: '${Currency.aed.code.toUpperCase()} '
                      '${l10n.npssEnrollConfirmAccountPart2Label}',
                  subtitle: selectedAccount.iban.applyHiddenMask(),
                  // subtitle: selectedAccount.iban,
                ),
                rightPartModel: ListBoxPartModel.icon(
                  icon: CompanyIconPointer.success.toGraphicAsset(),
                ),
                backgroundColor: CompanyColorPointer.surface2,
                isBoxed: true,
              ),
            ),
          ),
        Padding(
          padding: const EdgeInsets.only(top: 16),
          child: ListDetailsContainer(
            model: ListDetailsContainerModel(
              items: [
                ListDetailsModel(
                  textLabelModel: ListDetailsTextLabelModel(
                    text: l10n.npssEnrollConfirmBannerText,
                    textColor: CompanyColorPointer.secondary3,
                  ),
                  backgroundColor: CompanyColorPointer.surface6,
                ),
              ],
            ),
          ),
        ),
        AaniTncListBox(
          isTermsAndConditionsAccepted: isTermsAndConditionsAccepted,
          onPressed: bloc.onTermsAndConditionsPressed,
          onCheckboxPressed: bloc.onTermsAndConditionsCheckboxPressed,
        ),
      ],
    );
  }
}
