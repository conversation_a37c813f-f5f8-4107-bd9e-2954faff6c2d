import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/cubit/my_code_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/cubit/my_code_details_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/widgets/my_code_failure_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/widgets/my_code_loaded_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/qr_code/my_code_details/widgets/my_code_loading_state.dart';

class MyCodeDetailsScreen
    extends BasePage<MyCodeDetailsState, MyCodeDetailsCubit> {
  const MyCodeDetailsScreen({
    super.key,
  });

  @override
  MyCodeDetailsCubit createBloc() =>
      DependencyProvider.get<MyCodeDetailsCubit>();

  @override
  void initBloc(MyCodeDetailsCubit bloc) => bloc.init();

  @override
  Widget buildPage(
    BuildContext context,
    MyCodeDetailsCubit bloc,
    MyCodeDetailsState state,
  ) {
    return state.map(
      loading: (_) => const MyCodeLoadingState(),
      loaded: (loaded) => MyCodeLoadedState(loaded),
      failure: (failure) => MyCodeFailureState(failure),
    );
  }
}
