part of 'requests_to_pay_list_cubit.dart';

@freezed
class RequestsToPayListState with _$RequestsToPayListState {
  const factory RequestsToPayListState.loading() =
      RequestsToPayListStateLoading;

  const factory RequestsToPayListState.requestFailed() =
      RequestsToPayListStateFailed;

  const factory RequestsToPayListState.loaded({
    required List<NPSSRequestToPay> requests,
    @Default(false) bool isNextPageLoading,
  }) = RequestsToPayListStateLoaded;
}
