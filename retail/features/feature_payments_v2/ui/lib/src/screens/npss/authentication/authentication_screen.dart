import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart'
    hide FloatingActionButton;
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/authentication/cubit/authentication_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/authentication/cubit/authentication_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/authentication/widgets/loaded_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/npss/authentication/widgets/loading_state.dart';
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import 'package:wio_feature_status_view_api/navigation/status_view_feature_navigation_config.dart';

class AuthenticationScreen extends StatelessWidget {
  final OidcAuth oidcAuth;

  const AuthenticationScreen(this.oidcAuth, {super.key});

  @override
  Widget build(BuildContext context) {
    return PageBlocProvider<AuthenticationCubit, AuthenticationState>(
      createBloc: () => DependencyProvider.get<AuthenticationCubit>(),
      initBloc: (bloc) => bloc.init(),
      child: _Content(oidcAuth),
    );
  }
}

class _Content extends StatelessWidget {
  final OidcAuth oidcAuth;

  const _Content(this.oidcAuth);

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);
    final bloc = context.read<AuthenticationCubit>();

    return BlocBuilder<AuthenticationCubit, AuthenticationState>(
      builder: (context, state) {
        final isTermsAndConditionsAccepted = state.mapOrNull(
              loaded: (state) => state.isTermsAndConditionsAccepted,
            ) ??
            false;

        return Scaffold(
          appBar: TopNavigation(
            TopNavigationModel(
              state: TopNavigationState.positive,
              title: l10n.aaniAuthorizationTitle,
              leftAccessory: TopNavigationEmptyLeftAccessoryModel(),
              rightAccessory: TopNavigationIconRightAccessoryModel(
                icon: const GraphicAssetPointer.icon(CompanyIconPointer.close),
              ),
            ),
            onRightIconPressed: () => bloc.onCancelPressed(oidcAuth),
            onLeftIconPressed: Navigator.maybeOf(context)?.maybePop,
          ),
          body: Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(24, 0, 24, 100),
            child: SingleChildScrollView(
              child: state.map(
                loading: (_) => const AuthenticationLoadingState(),
                loaded: (state) => AuthenticationLoadedState(
                  customer: state.customer,
                  isTermsAndConditionsAccepted:
                      state.isTermsAndConditionsAccepted,
                ),
              ),
            ),
          ),
          floatingActionButton: Visibility(
            visible: state.maybeMap(loaded: (_) => true, orElse: () => false),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: SizedBox(
                width: double.maxFinite,
                child: Button(
                  model: ButtonModel(
                    title: l10n.npssAuthApproveCta,
                    contentAlignment: ButtonContentAlignment.spaceBetween,
                  ),
                  onPressed: isTermsAndConditionsAccepted
                      ? () => bloc.onAuth(
                            oidcAuth: oidcAuth,
                            successConfig: _successConfig(l10n),
                            errorConfig: (msg) => _errorConfig(l10n, msg),
                          )
                      : null,
                ),
              ),
            ),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        );
      },
    );
  }

  FeatureNavigationConfig _successConfig(PaymentsLocalizations l10n) {
    return StatusViewFeatureNavigationConfig(
      variant: StatusScreenVariant.alternativeSuccess,
      iconTint: CompanyColorPointer.secondary8,
      iconRatio: CompanyIconRatio.regular,
      icon: const GraphicAssetPointer.pictogram(
        CompanyPictogramPointer.validation_success,
      ),
      title: l10n.npssEnrollSuccessTitle,
      subTitleModel: CompanyRichTextModel(
        normalStyle: CompanyTextStylePointer.h3,
        text: l10n.npssAuthForAani,
      ),
      descriptionTextModel: CompanyRichTextModel(
        normalStyle: CompanyTextStylePointer.b2,
        text: l10n.npssEnrollmentSuccessText,
        maxLines: 4,
      ),
      bottomConfig: StatusPageBottomConfig.customButton(
        model: ButtonModel(
          title: l10n.npssEnrollCloseBtn,
          styleOverride: const ButtonStyleOverride(
            active: ButtonStateColorScheme(
              foreground: CompanyColorPointer.primary3,
              background: CompanyColorPointer.primary2,
            ),
            pressed: ButtonStateColorScheme(
              foreground: CompanyColorPointer.primary2,
              background: CompanyColorPointer.surface1,
            ),
            textStyle: CompanyTextStylePointer.b3,
          ),
        ),
      ),
      iconGradient: CompanyGradientPointer.midnight,
    );
  }

  FeatureNavigationConfig _errorConfig(PaymentsLocalizations l10n, String msg) {
    return StatusViewFeatureNavigationConfig(
      variant: StatusScreenVariant.error,
      iconRatio: CompanyIconRatio.regular,
      icon: CompanyPictogramPointer.validation_failure.toGraphicAsset(),
      title: l10n.npssEnrollErrorTitle,
      subTitleModel: CompanyRichTextModel(
        normalStyle: CompanyTextStylePointer.b1,
        text: msg.isEmpty ? l10n.npssAuthErrorText : msg,
      ),
      bottomConfig: StatusPageBottomConfig.customButton(
        model: ButtonModel(title: l10n.npssEnrollTryAgainBtn),
        secondaryButtonConfig: StatusPageSecondaryButtonConfig(
          title: l10n.npssEnrollCloseBtn,
        ),
        reversed: true,
      ),
    );
  }
}
