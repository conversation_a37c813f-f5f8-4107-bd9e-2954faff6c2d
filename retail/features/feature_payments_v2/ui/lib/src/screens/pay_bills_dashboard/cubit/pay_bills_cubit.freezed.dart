// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pay_bills_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PayBillsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(Object error) failed,
    required TResult Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)
        loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(Object error)? failed,
    TResult? Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)?
        loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(Object error)? failed,
    TResult Function(String cardTextureUrl, List<CardBeneficiary> beneficiaries,
            bool isPriceConfigEnabled)?
        loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PayBillsStateLoading value) loading,
    required TResult Function(PayBillsStateFailed value) failed,
    required TResult Function(PayBillsStateLoaded value) loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PayBillsStateLoading value)? loading,
    TResult? Function(PayBillsStateFailed value)? failed,
    TResult? Function(PayBillsStateLoaded value)? loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PayBillsStateLoading value)? loading,
    TResult Function(PayBillsStateFailed value)? failed,
    TResult Function(PayBillsStateLoaded value)? loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PayBillsStateCopyWith<$Res> {
  factory $PayBillsStateCopyWith(
          PayBillsState value, $Res Function(PayBillsState) then) =
      _$PayBillsStateCopyWithImpl<$Res, PayBillsState>;
}

/// @nodoc
class _$PayBillsStateCopyWithImpl<$Res, $Val extends PayBillsState>
    implements $PayBillsStateCopyWith<$Res> {
  _$PayBillsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$PayBillsStateLoadingImplCopyWith<$Res> {
  factory _$$PayBillsStateLoadingImplCopyWith(_$PayBillsStateLoadingImpl value,
          $Res Function(_$PayBillsStateLoadingImpl) then) =
      __$$PayBillsStateLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PayBillsStateLoadingImplCopyWithImpl<$Res>
    extends _$PayBillsStateCopyWithImpl<$Res, _$PayBillsStateLoadingImpl>
    implements _$$PayBillsStateLoadingImplCopyWith<$Res> {
  __$$PayBillsStateLoadingImplCopyWithImpl(_$PayBillsStateLoadingImpl _value,
      $Res Function(_$PayBillsStateLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PayBillsStateLoadingImpl implements PayBillsStateLoading {
  const _$PayBillsStateLoadingImpl();

  @override
  String toString() {
    return 'PayBillsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayBillsStateLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(Object error) failed,
    required TResult Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)
        loaded,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(Object error)? failed,
    TResult? Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)?
        loaded,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(Object error)? failed,
    TResult Function(String cardTextureUrl, List<CardBeneficiary> beneficiaries,
            bool isPriceConfigEnabled)?
        loaded,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PayBillsStateLoading value) loading,
    required TResult Function(PayBillsStateFailed value) failed,
    required TResult Function(PayBillsStateLoaded value) loaded,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PayBillsStateLoading value)? loading,
    TResult? Function(PayBillsStateFailed value)? failed,
    TResult? Function(PayBillsStateLoaded value)? loaded,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PayBillsStateLoading value)? loading,
    TResult Function(PayBillsStateFailed value)? failed,
    TResult Function(PayBillsStateLoaded value)? loaded,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class PayBillsStateLoading implements PayBillsState {
  const factory PayBillsStateLoading() = _$PayBillsStateLoadingImpl;
}

/// @nodoc
abstract class _$$PayBillsStateFailedImplCopyWith<$Res> {
  factory _$$PayBillsStateFailedImplCopyWith(_$PayBillsStateFailedImpl value,
          $Res Function(_$PayBillsStateFailedImpl) then) =
      __$$PayBillsStateFailedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Object error});
}

/// @nodoc
class __$$PayBillsStateFailedImplCopyWithImpl<$Res>
    extends _$PayBillsStateCopyWithImpl<$Res, _$PayBillsStateFailedImpl>
    implements _$$PayBillsStateFailedImplCopyWith<$Res> {
  __$$PayBillsStateFailedImplCopyWithImpl(_$PayBillsStateFailedImpl _value,
      $Res Function(_$PayBillsStateFailedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$PayBillsStateFailedImpl(
      error: null == error ? _value.error : error,
    ));
  }
}

/// @nodoc

class _$PayBillsStateFailedImpl implements PayBillsStateFailed {
  const _$PayBillsStateFailedImpl({required this.error});

  @override
  final Object error;

  @override
  String toString() {
    return 'PayBillsState.failed(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayBillsStateFailedImpl &&
            const DeepCollectionEquality().equals(other.error, error));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(error));

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PayBillsStateFailedImplCopyWith<_$PayBillsStateFailedImpl> get copyWith =>
      __$$PayBillsStateFailedImplCopyWithImpl<_$PayBillsStateFailedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(Object error) failed,
    required TResult Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)
        loaded,
  }) {
    return failed(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(Object error)? failed,
    TResult? Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)?
        loaded,
  }) {
    return failed?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(Object error)? failed,
    TResult Function(String cardTextureUrl, List<CardBeneficiary> beneficiaries,
            bool isPriceConfigEnabled)?
        loaded,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PayBillsStateLoading value) loading,
    required TResult Function(PayBillsStateFailed value) failed,
    required TResult Function(PayBillsStateLoaded value) loaded,
  }) {
    return failed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PayBillsStateLoading value)? loading,
    TResult? Function(PayBillsStateFailed value)? failed,
    TResult? Function(PayBillsStateLoaded value)? loaded,
  }) {
    return failed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PayBillsStateLoading value)? loading,
    TResult Function(PayBillsStateFailed value)? failed,
    TResult Function(PayBillsStateLoaded value)? loaded,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed(this);
    }
    return orElse();
  }
}

abstract class PayBillsStateFailed implements PayBillsState {
  const factory PayBillsStateFailed({required final Object error}) =
      _$PayBillsStateFailedImpl;

  Object get error;

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PayBillsStateFailedImplCopyWith<_$PayBillsStateFailedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PayBillsStateLoadedImplCopyWith<$Res> {
  factory _$$PayBillsStateLoadedImplCopyWith(_$PayBillsStateLoadedImpl value,
          $Res Function(_$PayBillsStateLoadedImpl) then) =
      __$$PayBillsStateLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String cardTextureUrl,
      List<CardBeneficiary> beneficiaries,
      bool isPriceConfigEnabled});
}

/// @nodoc
class __$$PayBillsStateLoadedImplCopyWithImpl<$Res>
    extends _$PayBillsStateCopyWithImpl<$Res, _$PayBillsStateLoadedImpl>
    implements _$$PayBillsStateLoadedImplCopyWith<$Res> {
  __$$PayBillsStateLoadedImplCopyWithImpl(_$PayBillsStateLoadedImpl _value,
      $Res Function(_$PayBillsStateLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardTextureUrl = null,
    Object? beneficiaries = null,
    Object? isPriceConfigEnabled = null,
  }) {
    return _then(_$PayBillsStateLoadedImpl(
      cardTextureUrl: null == cardTextureUrl
          ? _value.cardTextureUrl
          : cardTextureUrl // ignore: cast_nullable_to_non_nullable
              as String,
      beneficiaries: null == beneficiaries
          ? _value._beneficiaries
          : beneficiaries // ignore: cast_nullable_to_non_nullable
              as List<CardBeneficiary>,
      isPriceConfigEnabled: null == isPriceConfigEnabled
          ? _value.isPriceConfigEnabled
          : isPriceConfigEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$PayBillsStateLoadedImpl implements PayBillsStateLoaded {
  const _$PayBillsStateLoadedImpl(
      {required this.cardTextureUrl,
      required final List<CardBeneficiary> beneficiaries,
      required this.isPriceConfigEnabled})
      : _beneficiaries = beneficiaries;

  @override
  final String cardTextureUrl;
  final List<CardBeneficiary> _beneficiaries;
  @override
  List<CardBeneficiary> get beneficiaries {
    if (_beneficiaries is EqualUnmodifiableListView) return _beneficiaries;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_beneficiaries);
  }

  @override
  final bool isPriceConfigEnabled;

  @override
  String toString() {
    return 'PayBillsState.loaded(cardTextureUrl: $cardTextureUrl, beneficiaries: $beneficiaries, isPriceConfigEnabled: $isPriceConfigEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PayBillsStateLoadedImpl &&
            (identical(other.cardTextureUrl, cardTextureUrl) ||
                other.cardTextureUrl == cardTextureUrl) &&
            const DeepCollectionEquality()
                .equals(other._beneficiaries, _beneficiaries) &&
            (identical(other.isPriceConfigEnabled, isPriceConfigEnabled) ||
                other.isPriceConfigEnabled == isPriceConfigEnabled));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      cardTextureUrl,
      const DeepCollectionEquality().hash(_beneficiaries),
      isPriceConfigEnabled);

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PayBillsStateLoadedImplCopyWith<_$PayBillsStateLoadedImpl> get copyWith =>
      __$$PayBillsStateLoadedImplCopyWithImpl<_$PayBillsStateLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(Object error) failed,
    required TResult Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)
        loaded,
  }) {
    return loaded(cardTextureUrl, beneficiaries, isPriceConfigEnabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(Object error)? failed,
    TResult? Function(String cardTextureUrl,
            List<CardBeneficiary> beneficiaries, bool isPriceConfigEnabled)?
        loaded,
  }) {
    return loaded?.call(cardTextureUrl, beneficiaries, isPriceConfigEnabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(Object error)? failed,
    TResult Function(String cardTextureUrl, List<CardBeneficiary> beneficiaries,
            bool isPriceConfigEnabled)?
        loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(cardTextureUrl, beneficiaries, isPriceConfigEnabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PayBillsStateLoading value) loading,
    required TResult Function(PayBillsStateFailed value) failed,
    required TResult Function(PayBillsStateLoaded value) loaded,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PayBillsStateLoading value)? loading,
    TResult? Function(PayBillsStateFailed value)? failed,
    TResult? Function(PayBillsStateLoaded value)? loaded,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PayBillsStateLoading value)? loading,
    TResult Function(PayBillsStateFailed value)? failed,
    TResult Function(PayBillsStateLoaded value)? loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class PayBillsStateLoaded implements PayBillsState {
  const factory PayBillsStateLoaded(
      {required final String cardTextureUrl,
      required final List<CardBeneficiary> beneficiaries,
      required final bool isPriceConfigEnabled}) = _$PayBillsStateLoadedImpl;

  String get cardTextureUrl;
  List<CardBeneficiary> get beneficiaries;
  bool get isPriceConfigEnabled;

  /// Create a copy of PayBillsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PayBillsStateLoadedImplCopyWith<_$PayBillsStateLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
