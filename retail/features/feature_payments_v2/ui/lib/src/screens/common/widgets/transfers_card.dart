import 'package:flutter/cupertino.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class TransfersCard extends StatelessWidget {
  final String headerTitle;
  final String headerSubtitle;
  final CompanyColorPointer headerSubtitleColor;
  final String frequency;
  final CompanyIconPointer iconPointer;
  final LabelModel bottomTitleLabelModel;
  final LabelModel? bottomSubtitleLabelModel;
  final ButtonModel? trailingButtonModel;
  final VoidCallback? onTap;
  final VoidCallback? onTrailingButtonPressed;

  const TransfersCard({
    required this.headerTitle,
    required this.headerSubtitle,
    required this.headerSubtitleColor,
    required this.frequency,
    required this.iconPointer,
    required this.bottomTitleLabelModel,
    this.bottomSubtitleLabelModel,
    this.trailingButtonModel,
    this.onTap,
    this.onTrailingButtonPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          color: context.colorStyling
              .fromPointer(CompanyColorPointer.primary2)
              .withValues(alpha: 1),
          borderRadius: BorderRadius.circular(14.0),
        ),
        margin: const EdgeInsetsDirectional.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _HeaderWidget(
              headerTitle: headerTitle,
              headerSubtitle: headerSubtitle,
              headerSubtitleColor: headerSubtitleColor,
              frequency: frequency,
              iconPointer: iconPointer,
            ),
            Flexible(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxHeight: 8),
                child: const SizedBox.expand(),
              ),
            ),
            _BottomWidget(
              bottomTitleLabelModel: bottomTitleLabelModel,
              bottomSubtitleLabelModel: bottomSubtitleLabelModel,
              trailingButtonModel: trailingButtonModel,
              onTrailingButtonPressed: onTrailingButtonPressed,
            ),
            const Space.vertical(12),
          ],
        ),
      ),
    );
  }
}

class _HeaderWidget extends StatelessWidget {
  final String headerTitle;
  final String headerSubtitle;
  final CompanyColorPointer headerSubtitleColor;
  final String frequency;
  final CompanyIconPointer iconPointer;

  const _HeaderWidget({
    required this.headerTitle,
    required this.headerSubtitle,
    required this.headerSubtitleColor,
    required this.frequency,
    required this.iconPointer,
  });

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16).copyWith(top: 13),
        child: ListBox(
          listBoxModel: ListBoxModel(
            partsAlignToTop: true,
            backgroundColor: CompanyColorPointer.primary2,
            borderRadius: BorderRadius.circular(8),
            leftPartModel: ListBoxPartModel.tile(
              verticalPadding: 0,
              TileModel.iconV2(
                icon: CompanyIconModel(
                  icon: GraphicAssetPointer.icon(iconPointer),
                  size: CompanyIconSize.large,
                  color: CompanyColorPointer.primary3,
                ),
                size: TileSize.medium,
                shape: TileBoxShape.circle,
                surface: TileSurface.dark,
              ),
            ),
            textModel: ListBoxTextModel(
              title: headerTitle,
              titleMaxLines: 2,
              subtitle: headerSubtitle,
              subtitleColor: headerSubtitleColor,
              titleOverflow: LabelTextOverflow.ellipsis,
            ),
            rightPartModel: ListBoxPartModel.fromWidget(
              widget: Label(
                model: LabelModel(
                  text: frequency,
                  inheritColorFromParent: true,
                  textStyle: CompanyTextStylePointer.b3,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _BottomWidget extends StatelessWidget {
  final LabelModel bottomTitleLabelModel;
  final LabelModel? bottomSubtitleLabelModel;
  final ButtonModel? trailingButtonModel;
  final VoidCallback? onTrailingButtonPressed;

  const _BottomWidget({
    required this.bottomTitleLabelModel,
    this.bottomSubtitleLabelModel,
    this.trailingButtonModel,
    this.onTrailingButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Label(model: bottomTitleLabelModel),
              if (bottomSubtitleLabelModel != null)
                Label(model: bottomSubtitleLabelModel!),
            ],
          ),
          if (trailingButtonModel != null)
            Button(
              model: trailingButtonModel!,
              onPressed: onTrailingButtonPressed,
            ),
        ],
      ),
    );
  }
}
