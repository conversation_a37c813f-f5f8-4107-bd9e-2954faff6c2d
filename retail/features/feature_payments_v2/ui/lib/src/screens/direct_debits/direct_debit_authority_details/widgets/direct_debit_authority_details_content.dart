part of '../direct_debit_authority_details_screen.dart';

class _DirectDebitAuthorityDetailsContent extends StatelessWidget {
  final DirectDebitAuthorityDetailsLoadedState state;

  const _DirectDebitAuthorityDetailsContent(this.state);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 28),
      child: state.directDebitAuthorityDetails.hasDebitActivity
          ? _DirectDebitAuthorityTabs(state)
          : _DirectDebitAuthoritySingleList(state),
    );
  }
}

class _DirectDebitAuthoritySingleList extends StatelessWidget {
  final DirectDebitAuthorityDetailsLoadedState state;

  const _DirectDebitAuthoritySingleList(this.state);

  @override
  Widget build(BuildContext context) {
    final details = state.directDebitAuthorityDetails;

    return SingleChildScrollView(
      child: Center(
        child: Column(
          children: [
            _DirectDebitAuthorityDetailsHeader(state),
            const SizedBox(height: 32),
            _DirectDebitAuthorityDetailsList(state),
            const SizedBox(height: 18),
            DirectDebitAuthorityFileWidget(details),
            const SizedBox(height: 54),
          ],
        ),
      ),
    );
  }
}

class _DirectDebitAuthorityTabs extends StatefulWidget {
  final DirectDebitAuthorityDetailsLoadedState state;

  const _DirectDebitAuthorityTabs(this.state);

  @override
  State<_DirectDebitAuthorityTabs> createState() =>
      _DirectDebitAuthorityTabsState();
}

class _DirectDebitAuthorityTabsState extends State<_DirectDebitAuthorityTabs>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final ScrollController _scrollController;

  List<String> tabNames(PaymentsLocalizations l10n) => [
        l10n.ddaTabActivity,
        l10n.ddaTabDetails,
      ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);
    final details = widget.state.directDebitAuthorityDetails;

    return NestedScrollView(
      controller: _scrollController,
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  _DirectDebitAuthorityDetailsHeader(widget.state),
                  const SizedBox(height: 18),
                  AnnouncementBanner(
                    AnnouncementBannerModel(
                      title: l10n.ddaBannerTitle,
                      titleColor: CompanyColorPointer.secondary1,
                      subtitle: l10n.ddaBannerContent,
                      subTitleColor: CompanyColorPointer.secondary1,
                      backgroundColor: CompanyColorPointer.secondary6,
                      subtitleMaxLines: 3,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SliverAppBar(
            automaticallyImplyLeading: false,
            backgroundColor: context.colorStyling.background1,
            pinned: true,
            floating: true,
            expandedHeight: 62,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(62),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 18,
                  horizontal: 24,
                ),
                alignment: Alignment.centerLeft,
                child: Tabs(
                  TabsModel(
                    variant: TabsVariant.small,
                    inActiveBackgroundColor: CompanyColorPointer.background1,
                    tabNames: tabNames(l10n),
                    selectedIndex: _tabController.index,
                  ),
                  onTabPressed: (index) => setState(() {
                    _tabController.index = index;
                  }),
                ),
              ),
            ),
          ),
        ];
      },
      body: TabBarView(
        physics: const NeverScrollableScrollPhysics(),
        controller: _tabController,
        children: [
          _TabView(
            child: AllTransactionsContentPage(
              filterParams: const TransactionParams(
                filterModel: TransactionFilterModel(
                  selectFiltersState: [
                    SelectFilterListState.dateRange(
                      rangeState: TransactionDateRangeState.empty(),
                    ),
                  ],
                ),
              ),
              filterByItems: TransactionFilterByItems.directDebitRequests(
                directDebitAuthorityId: widget
                    .state.directDebitAuthorityDetails.mandateReferenceNumber,
              ),
              pageType: TransactionsPageType.retail,
              controller: _scrollController,
            ),
          ),
          _TabView(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _DirectDebitAuthorityDetailsList(widget.state),
                  const SizedBox(height: 18),
                  DirectDebitAuthorityFileWidget(details),
                  const SizedBox(height: 54),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TabView extends StatefulWidget {
  final Widget child;

  const _TabView({required this.child});

  @override
  State<_TabView> createState() => _TabViewState();
}

class _TabViewState extends State<_TabView>
    with AutomaticKeepAliveClientMixin<_TabView> {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return widget.child;
  }
}
