part of '../all_direct_debits_screen.dart';

class AllDirectDebitsLoading extends StatelessWidget {
  const AllDirectDebitsLoading({super.key});

  @override
  Widget build(BuildContext context) {
    final shimmerDecoration = BoxDecoration(
      borderRadius: BorderRadius.circular(14.0),
      color: Colors.white70,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Space.vertical(8),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(
          child: Container(
            width: 58,
            height: 18,
            decoration: shimmerDecoration,
            margin: const EdgeInsets.symmetric(vertical: 8),
          ),
        ),
        const Space.vertical(8),
        ...List.generate(4, (_) => const DirectDebitCardLoading()),
      ],
    );
  }
}
