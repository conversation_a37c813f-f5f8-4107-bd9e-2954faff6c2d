import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/feature_payments_v2_ui.dart';
import 'package:wio_feature_payments_v2_ui/src/extensions/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/all_transfer_limits/all_transfer_limits_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/all_transfer_limits/all_transfer_limits_state.dart';

class AllTransferLimitsPage
    extends BasePage<AllTransferLimitsState, AllTransferLimitsCubit> {
  const AllTransferLimitsPage({super.key});

  @override
  AllTransferLimitsCubit createBloc() =>
      DependencyProvider.get<AllTransferLimitsCubit>();

  @override
  void initBloc(AllTransferLimitsCubit bloc) => bloc.initialize();

  @override
  Widget buildPage(
    BuildContext context,
    AllTransferLimitsCubit bloc,
    AllTransferLimitsState state,
  ) =>
      const _AllTransferLimitsPage();
}

class _AllTransferLimitsPage extends StatelessWidget {
  const _AllTransferLimitsPage();

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return Scaffold(
      appBar: TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
          title: l10n.allTransferLimitsScreenAppBarTitle,
          leftAccessory: const TopNavigationIconLeftAccessoryModel(
            icon: GraphicAssetPointer.icon(CompanyIconPointer.close),
          ),
        ),
        onLeftIconPressed: Navigator.of(context).pop,
      ),
      body: const FixedButtonsPageLayout(
        model: FixedButtonsScrollablePageLayoutModel(),
        child: _PageContent(),
      ),
    );
  }
}

class _PageContent extends StatelessWidget {
  const _PageContent();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AllTransferLimitsCubit>();
    final state = context.watch<AllTransferLimitsCubit>().state;

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: state.maybeMap(
        idle: (it) => _LoadedContent(
          overallLimit: it.overallLimit,
          accountLimits: it.accountLimits,
          accountNames: it.accountNames,
          onEditOverallLimit: cubit.onEditOverallLimit,
          onEditAccountLimit: cubit.onEditAccountLimit,
        ),
        error: (_) => const _ErrorContent(),
        orElse: _LoadingContent.new,
      ),
    );
  }
}

class _LoadingContent extends StatelessWidget {
  const _LoadingContent();

  @override
  Widget build(BuildContext context) {
    final zeroAmount = Money.fromNumWithCurrency(0, Currency.aed);

    return CompanyShimmer(
      model: const CompanyShimmerModel(),
      child: _LoadedContent(
        overallLimit: PersonalTransferLimit(
          maxLimit: zeroAmount,
          currentLimit: zeroAmount,
          amountUsed: zeroAmount,
          amountLeft: zeroAmount,
        ),
      ),
    );
  }
}

class _ErrorContent extends StatelessWidget {
  const _ErrorContent();

  @override
  Widget build(BuildContext context) {
    final l10n = CommonLocalizations.of(context);

    return Center(
      child: GenericError(
        GenericErrorModel(
          title: l10n.genericErrorComponentTitle,
          subtitle: l10n.genericErrorComponentSubtitle,
          buttonLabel: l10n.genericErrorComponentTryAgainButton,
        ),
        onPressed: context.read<AllTransferLimitsCubit>().onRetry,
      ),
    );
  }
}

class _LoadedContent extends StatelessWidget {
  final PersonalTransferLimit overallLimit;
  final List<AccountTransferLimit> accountLimits;
  final Map<String, String> accountNames;
  final VoidCallback? onEditOverallLimit;
  final ValueSetter<AccountTransferLimit>? onEditAccountLimit;

  const _LoadedContent({
    required this.overallLimit,
    this.accountLimits = const [],
    this.accountNames = const {},
    this.onEditOverallLimit,
    this.onEditAccountLimit,
  });

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.zero,
      children: [
        const _PageTitle(),
        Space.fromSpacingVertical(Spacing.s6),
        const _FAQAction(),
        Space.fromSpacingVertical(Spacing.s4),
        _OverallLimit(
          limit: overallLimit,
          onEdit: onEditOverallLimit == null
              ? null
              : () => onEditOverallLimit?.call(),
        ),
        for (final limit in accountLimits) ...[
          Space.fromSpacingVertical(Spacing.s4),
          _AccountLimit(
            limit: limit,
            name: accountNames[limit.accountId] ?? '',
            onEdit: onEditAccountLimit == null
                ? null
                : () => onEditAccountLimit?.call(limit),
          ),
        ],
      ],
    );
  }
}

class _PageTitle extends StatelessWidget {
  const _PageTitle();

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return PageText(
      PageTextModel(
        title: l10n.allTransferLimitsScreenTitle,
        subtitle: l10n.allTransferLimitsScreenSubtitle,
        subtitleMargin: Spacing.s2.value,
      ),
    );
  }
}

class _FAQAction extends StatelessWidget {
  const _FAQAction();

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return GestureDetector(
      onTap: context.read<AllTransferLimitsCubit>().onOpenFaq,
      child: CompanyLabel(
        CompanyLabelModel.v2(
          text: l10n.allTransferLimitsFAQLabel,
          icon: const CompanyLabelIcon.leading(
            GraphicAssetPointer.icon(CompanyIconPointer.help),
          ),
          status: CompanyLabelStatus.primary,
        ),
      ),
    );
  }
}

class _OverallLimit extends StatelessWidget {
  final PersonalTransferLimit limit;
  final VoidCallback? onEdit;

  const _OverallLimit({
    required this.limit,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return _RemainingLimitCard(
      currentLimit: limit.currentLimit,
      amountLeft: limit.amountLeft,
      title: l10n.allTransferLimitsScreenRemainingLimitLabel,
      onEdit: onEdit,
    );
  }
}

class _AccountLimit extends StatelessWidget {
  final AccountTransferLimit limit;
  final String name;
  final VoidCallback? onEdit;

  const _AccountLimit({
    required this.limit,
    required this.name,
    this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return _RemainingLimitCard(
      currentLimit: limit.currentLimit,
      amountLeft: limit.amountLeft,
      title: name,
      subtitle: l10n.allTransferLimitsScreenJointAccountLabel,
      showFlag: true,
      onEdit: onEdit,
    );
  }
}

class _RemainingLimitCard extends StatelessWidget {
  final Money currentLimit;
  final Money amountLeft;
  final String title;
  final bool showFlag;
  final String? subtitle;
  final VoidCallback? onEdit;

  const _RemainingLimitCard({
    required this.currentLimit,
    required this.amountLeft,
    required this.title,
    this.showFlag = false,
    this.subtitle,
    this.onEdit,
  });

  double get _progress => (1 - amountLeft.dividedBy(currentLimit)).clamp(0, 1);

  TileModel get _flagModel => TileModel.flag(flag: currentLimit.currency.flag);

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return ProgressListWidget(
      ProgressListWidgetModel(
        icon: showFlag ? _flagModel : null,
        title: LabelModel(
          text: title,
          textStyle: CompanyTextStylePointer.b3,
          color: CompanyColorPointer.secondary1,
        ),
        subtitle: subtitle == null
            ? null
            : LabelModel(
                text: subtitle ?? '',
                textStyle: CompanyTextStylePointer.b4,
                color: CompanyColorPointer.secondary4,
              ),
        trailingTitle: LabelModel(
          text: amountLeft.toCodeOnRightFormat(),
          textStyle: CompanyTextStylePointer.b2medium,
          color: CompanyColorPointer.primary3,
        ),
        progress: ProgressModel(
          value: _progress,
          variant: ProgressVariant.var6,
        ),
        description: CompanyLabelModel.v2(
          text: l10n.allTransferLimitsScreenTotalLimitLabel(
            currentLimit.toCodeOnRightFormat(),
          ),
          status: CompanyLabelStatus.muted,
        ),
        button: ButtonModel(
          title: l10n.allTransferLimitsScreenEditLabel,
          type: ButtonType.tertiary,
          size: ButtonSize.shrink,
          applyPadding: false,
          styleOverride: const ButtonStyleOverride(
            textStyle: CompanyTextStylePointer.b3,
            active: ButtonStateColorScheme(
              background: CompanyColorPointer.transparent,
              foreground: CompanyColorPointer.primary1,
            ),
          ),
        ),
      ),
      onTap: onEdit,
      onButtonTap: onEdit,
    );
  }
}
