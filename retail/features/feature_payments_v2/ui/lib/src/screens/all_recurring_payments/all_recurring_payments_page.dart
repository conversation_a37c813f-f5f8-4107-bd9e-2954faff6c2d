import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/widgets/recurring/recurring_transfers.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/widgets/recurring/recurring_transfers_provider.dart';

class AllRecurringPaymentsPage extends StatelessWidget {
  final List<AccountDetails> accounts;

  const AllRecurringPaymentsPage({required this.accounts, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
          backgroundColor: CompanyColorPointer.background1,
        ),
      ),
      body: SafeArea(
        child: RecurringTransfersProvider(
          accounts: accounts,
          forceRefresh: false,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: SingleChildScrollView(
              child: RecurringTransfers(
                accounts: accounts,
                groupedByStatus: true,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
