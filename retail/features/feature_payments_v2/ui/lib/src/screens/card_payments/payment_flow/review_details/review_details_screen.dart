import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_feature_payments_v2_ui/l10n/payments_localization.g.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/flow/cubit/card_payment_flow_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/flow/widgets/card_payment_top_navigation.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/payment_flow/review_details/widget/review_slide_action_button.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/common/widgets/amount_and_flag.dart';

class ReviewDetailsScreen extends StatefulWidget {
  final CardBeneficiary cardBeneficiary;
  final Money amount;

  const ReviewDetailsScreen({
    required this.cardBeneficiary,
    required this.amount,
    super.key,
  });

  @override
  State<ReviewDetailsScreen> createState() => _ReviewDetailsScreenState();
}

class _ReviewDetailsScreenState extends State<ReviewDetailsScreen> {
  late final _isSubmittingNotifier = ValueNotifier(false)
    ..addListener(() => setState(() {}));

  var _slideActionKey = UniqueKey();

  @override
  void dispose() {
    _isSubmittingNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSubmitInProgress = _isSubmittingNotifier.value;
    final l10n = PaymentsLocalizations.of(context);

    return BlocListener<CardPaymentFlowCubit, CardPaymentFlowState>(
      listener: _listenFlow,
      child: IgnorePointer(
        ignoring: isSubmitInProgress,
        child: Scaffold(
          appBar: CardPaymentsTopNavigation(
            title: l10n.cardPaymentReviewScreenTitle,
            leftAccessory: TopNavigationEmptyLeftAccessoryModel(),
          ),
          body: DecoratedBox(
            decoration: BoxDecoration(
              gradient: CompanyGradientPointer.hero.toGradient(),
            ),
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: _TopSheet(
                    totalAmount: widget.amount,
                    cardBeneficiary: widget.cardBeneficiary,
                  ),
                ),
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: ReviewSlideActionButton(
                    key: _slideActionKey,
                    isSubmitInProgress: isSubmitInProgress,
                    onSubmit: () => _onSubmit(context),
                    onHorizontalDragEnd: () =>
                        _isSubmittingNotifier.value = true,
                    paymentAmount: widget.amount,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _onSubmit(BuildContext context) {
    context.read<CardPaymentFlowCubit>().submitTransfer();
  }

  void _listenFlow(BuildContext context, CardPaymentFlowState state) {
    state.maybeMap(
      confirming: (_) => _isSubmittingNotifier.value = true,
      orElse: () {
        _slideActionKey = UniqueKey();
        _isSubmittingNotifier.value = false;
      },
    );
  }
}

class _TopSheet extends StatelessWidget {
  final Money totalAmount;
  final CardBeneficiary cardBeneficiary;

  const _TopSheet({
    required this.totalAmount,
    required this.cardBeneficiary,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 36),
      decoration: BoxDecoration(
        color: context.colorStyling.background1,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AmountAndFlag(
            title: l10n.cardPaymentReviewHeaderText,
            amount: totalAmount * -1,
          ),
          const Space.vertical(24),
          ListDetailsContainer(
            model: ListDetailsContainerModel(items: _getItems(context)),
          ),
          const Space.vertical(16),
          ListBox(
            listBoxModel: ListBoxModel(
              applyPadding: true,
              isBoxed: true,
              leftPartModel: ListBoxPartModel.icon(
                icon: CompanyPictogramPointer.actions_information
                    .toGraphicAsset(),
                color: CompanyColorPointer.secondary7,
              ),
              textModel: ListBoxTextModel(
                title: l10n.cardPaymentReviewInfoText,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<ListDetailsModel> _getItems(BuildContext context) {
    final l10n = PaymentsLocalizations.of(context);

    return [
      ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          text: l10n.cardPaymentReviewCaptionCardName,
          textColor: CompanyColorPointer.secondary4,
        ),
        valueModel: ListDetailsValueModel.text(
          textModel: ListDetailValueTextModel.label(
            content: cardBeneficiary.cardName,
          ),
        ),
      ),
      ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          text: l10n.cardPaymentReviewCaptionCardNumber,
          textColor: CompanyColorPointer.secondary4,
        ),
        valueModel: ListDetailsValueModel.text(
          textModel: ListDetailValueTextModel.label(
            content: '•••• ${cardBeneficiary.cardLast4Digits}',
          ),
        ),
      ),
      ListDetailsModel(
        textLabelModel: ListDetailsTextLabelModel(
          text: l10n.cardPaymentReviewCaptionBank,
          textColor: CompanyColorPointer.secondary4,
        ),
        valueModel: ListDetailsValueModel.text(
          textModel:
              ListDetailValueTextModel.label(content: cardBeneficiary.bankName),
        ),
      ),
    ];
  }
}
