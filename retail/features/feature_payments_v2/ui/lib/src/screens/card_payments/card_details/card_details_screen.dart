import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_payments_v2_ui/src/extensions/animation_helpers.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_details/card_details_cubit.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_details/widgets/card_details_header.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_details/widgets/card_details_menu_buttons.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/card_payments/card_details/widgets/specific_card_transactions_section.dart';

class CardDetailsScreen extends StatelessWidget {
  final CardDetailsInput input;
  final bool isSpecialPlan;

  const CardDetailsScreen({
    required this.input,
    required this.isSpecialPlan,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PageBlocProvider<CardDetailsCubit, CardDetailsState>(
      createBloc: DependencyProvider.get<CardDetailsCubit>,
      child: Builder(
        builder: (context) => MaterialRefreshIndicator(
          child: Scaffold(
            backgroundColor: context.colorStyling.background3,
            appBar: const TopNavigation(
              TopNavigationModel(
                state: TopNavigationState.positive,
                backgroundColor: CompanyColorPointer.background3,
              ),
            ),
            body: FixedButtonsPageLayout(
              contentPadding: padding,
              model: const FixedButtonsScrollablePageLayoutModel(),
              child: _CardDetailsContent(
                input: input,
                isSpecialPlan: isSpecialPlan,
              ),
            ),
          ),
          onRefresh: () async => context.read<CardDetailsCubit>().refresh(),
        ),
      ),
    );
  }

  static const padding =
      EdgeInsets.symmetric(vertical: FixedButtonsPageLayout.pageContentPadding);
}

class _CardDetailsContent extends StatefulWidget {
  final CardDetailsInput input;
  final bool isSpecialPlan;

  const _CardDetailsContent({required this.input, required this.isSpecialPlan});

  @override
  State<_CardDetailsContent> createState() => _CardDetailsContentState();
}

class _CardDetailsContentState extends State<_CardDetailsContent> {
  @override
  void initState() {
    super.initState();
    afterTransition(
      () => context
          .read<CardDetailsCubit>()
          .init(input: widget.input, isSpecialPlan: widget.isSpecialPlan),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CardDetailsCubit, CardDetailsState>(
      builder: (context, state) {
        return state.map(
          idle: (_) => Center(
            child: Spinner(
              model: SpinnerModel(type: SpinnerType.primary),
            ),
          ),
          loaded: (s) => _CardDetailsLoadedContent(s),
        );
      },
    );
  }
}

class _CardDetailsLoadedContent extends StatelessWidget {
  final CardDetailsStateLoaded loadedState;

  const _CardDetailsLoadedContent(this.loadedState);

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      slivers: [
        SliverToBoxAdapter(
          child: CardDetailsHeader(
            cardBeneficiary: loadedState.beneficiary,
            cardSkinUrl: loadedState.cardTextureUrl,
          ),
        ),
        const CardDetailsMenuButtons(),
        const SliverFillRemaining(
          hasScrollBody: false,
          child: Padding(
            padding: EdgeInsets.only(top: 24.0),
            child: SpecificCardTransactionsSection(),
          ),
        ),
      ],
    );
  }
}
