import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' as mobile;
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_common_feature_context_faq_api/wio_common_feature_context_faq_api.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_payments_v2_ui/src/exception/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/beneficiary_creation/beneficiary_creation_page.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/beneficiary_creation/cubit/beneficiary_creation_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_manager.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/payment_creation_flow_state.dart';

class BeneficiaryCreationCubit extends mobile
    .StepScreenBaseCubit<PaymentCreationFlowState, BeneficiaryCreationState> {
  final BeneficiaryCreationInteractor _beneficiaryCreationInteractor;
  final BeneficiaryInteractor _beneficiaryInteractor;
  final ToastMessageProvider _toastMessageProvider;
  final RequirementsModelMapper _requirementsModelMapper;
  final Logger _logger;
  final WioPaymentsAnalytics _analytics;
  final CommonErrorHandler _commonErrorHandler;
  final FeatureToggleProvider _featureToggleProvider;
  final PaymentCreationFlowManager _flowManager;
  final NavigationProvider _navigationProvider;

  BeneficiaryCreationCubit({
    required BeneficiaryCreationInteractor beneficiaryCreationInteractor,
    required BeneficiaryInteractor beneficiaryInteractor,
    required ToastMessageProvider toastMessageProvider,
    required RequirementsModelMapper requirementsModelMapper,
    required Logger logger,
    required WioPaymentsAnalytics analytics,
    required CommonErrorHandler commonErrorHandler,
    required FeatureToggleProvider featureToggleProvider,
    required PaymentCreationFlowManager flowManager,
    required NavigationProvider navigationProvider,
  })  : _beneficiaryCreationInteractor = beneficiaryCreationInteractor,
        _beneficiaryInteractor = beneficiaryInteractor,
        _toastMessageProvider = toastMessageProvider,
        _requirementsModelMapper = requirementsModelMapper,
        _logger = logger,
        _analytics = analytics,
        _commonErrorHandler = commonErrorHandler,
        _featureToggleProvider = featureToggleProvider,
        _flowManager = flowManager,
        _navigationProvider = navigationProvider,
        super(const BeneficiaryCreationState.loading());

  bool get _isCreateBeneficiaryV2Enabled => _featureToggleProvider
      .get(PaymentsFeatureToggle.isRetailCreateBeneficiaryV2Enabled);

  bool get _isBeneficiaryAdditionLimitEnabled => _featureToggleProvider.get(
        PaymentsFeatureToggle.isBeneficiaryAdditionLimitEnabled,
      );

  Future<void> initialize() async {
    emit(const BeneficiaryCreationState.loading());

    final transferOption = stepScreenController.getGlobalState().transferOption;

    if (transferOption == null) {
      throw Exception('transferOption is null');
    }

    final optionId = transferOption.option.id;

    try {
      final transferRequirements =
          await _beneficiaryCreationInteractor.getTransferRequirements(
        optionId: optionId,
      );

      if (transferRequirements.isEmpty) {
        throw EmptyRequirementsException(
          'Transfer requirements are empty for optionId = $optionId',
        );
      }

      _logger.debug('Initial TransferRequirements: $transferRequirements');

      final beneficiaryCreationModel = _requirementsModelMapper
          .mapToBeneficiaryCreationModel(transferRequirements);

      final refreshedBeneficiaryCreationModel =
          await _refreshedBeneficiaryCreationState(
        optionId,
        beneficiaryCreationModel,
      );

      safeEmit(
        BeneficiaryCreationState.loaded(
          optionId: optionId,
          beneficiaryCreationModel: refreshedBeneficiaryCreationModel,
        ),
      );

      unawaited(
        handleCreationLimit(transferRequirements.creationLimit),
      );
    } on BeneficiaryCreationException catch (error, st) {
      showError(error.description ?? '');
      _logError(error, st);
      safeEmit(const BeneficiaryCreationState.failure());
    } on Object catch (error, st) {
      _commonErrorHandler.handleError(error);
      _logError(error, st);
      safeEmit(const BeneficiaryCreationState.failure());
    }
  }

  Future<void> handleCreationLimit(
    BeneficiaryCreationLimitData? creationLimit,
  ) async {
    if (!_isBeneficiaryAdditionLimitEnabled) {
      return;
    }

    if (creationLimit case final BeneficiaryCreationLimitData creationLimit) {
      await _navigationProvider.showBottomSheet(
        BeneficiaryAdditionLimitBottomSheetConfig(
          description: creationLimit.creationLimitMessage,
          onFaqTap: () {
            _navigationProvider.showBottomSheet<void>(
              const ContextFaqBottomSheetNavigationConfig(
                tags: [
                  ContextFaqTags.retailPaymentsContactLimit,
                ],
                fromScreen: BeneficiaryCreationPage.name,
              ),
            );
          },
        ),
      );

      if (creationLimit.isLimitReached) {
        emit(const BeneficiaryCreationState.limitReached());
      }
    }
  }

  void onValueChange(TransferRequirementInfoModel changedModel) {
    _logger.debug('Changed ${changedModel.toString()}');

    final key = changedModel.map(
      text: (model) => model.key,
      radio: (model) => model.key,
      phonenumber: (model) => model.key,
      info: (model) => model.key,
    );

    state.mapOrNull(
      loaded: (loadedState) {
        final transferRequirementsModel =
            loadedState.beneficiaryCreationModel.transferRequirementsModel;
        final valueModel = transferRequirementsModel.modelByKey(key);

        if (valueModel == changedModel) {
          // No new value. Skip
          return;
        }

        final modelUpd = transferRequirementsModel
            .map(
              (transferRequirementModel) =>
                  transferRequirementModel.copyWithUpdatedModel(
                key: key,
                updatedModel: changedModel,
              ),
            )
            .toList();

        emit(
          loadedState.copyWith(
            beneficiaryCreationModel:
                loadedState.beneficiaryCreationModel.copyWith(
              transferRequirementsModel: modelUpd,
            ),
          ),
        );
      },
    );
  }

  // ignore: avoid_positional_boolean_parameters
  Future<void> onValueSubmit(String key, bool valid) async {
    if (!valid) return;

    await state.whenOrNull(
      loaded: (optionId, beneficiaryCreationModel) async {
        final model = beneficiaryCreationModel.transferRequirementsModel;

        final refreshRequirementsOnChange =
            model.modelByKey(key)?.requirement.refreshRequirementsOnChange ??
                false;

        if (refreshRequirementsOnChange) {
          await _onRefreshRequirementsOnChange();
        }
      },
    );
  }

  Future<void> onSubmit() async {
    await state.whenOrNull(
      loaded: (optionId, beneficiaryCreationModel) async {
        final country = stepScreenController.getGlobalState().country;
        final amount = stepScreenController.getGlobalState().amount;

        if (country == null || amount == null) {
          throw Exception('country or amount is null');
        }

        final isRefreshRequired = beneficiaryCreationModel.isRefreshRequired();

        try {
          loading(true);
          if (isRefreshRequired) {
            await _processRefreshAfterSubmit(
              optionId,
              beneficiaryCreationModel,
            );
          } else {
            final creationResult =
                await _processSubmit(country, amount, beneficiaryCreationModel);

            final internationalBeneficiary = creationResult.beneficiary;

            _logger.debug(
              'CBInternationalBeneficiary result: $internationalBeneficiary',
            );

            _selectBeneAfterCreation(internationalBeneficiary);

            final result = PaymentCreationFlowSelectResult.beneficiaryCreation(
              creationResult,
            );

            await _flowManager.updateNextStep(
              stepScreenController.getGlobalState(),
              result,
            );

            if (isClosed) return;
            stepScreenController.onComplete(
              mobile.StepScreenResultType.ready,
              result: result,
            );
          }
        } on BeneficiaryCreationException catch (error, st) {
          _analytics.beneCreationError(error.toString());
          _logError(error, st);
          showError(error.description ?? '');
        } on Object catch (error, st) {
          _analytics.beneCreationError(error.toString());
          _logError(error, st);
          _commonErrorHandler.handleError(error);
        } finally {
          loading(false);
        }
      },
    );
  }

  Future<void> _onRefreshRequirementsOnChange() async {
    await state.whenOrNull(
      loaded: (optionId, beneficiaryCreationModel) async {
        final transferRequirementsModel =
            beneficiaryCreationModel.transferRequirementsModel;

        final keys = transferRequirementsModel.getRequireRefreshOnChangeKeys();

        if (keys.isNotEmpty) {
          final fields = _requirementsModelMapper
              .mapToTransferRequiremnetRefreshDataEntries(
            keys,
            transferRequirementsModel,
          );

          await _refreshRequirements(optionId, fields);
        }
      },
    );
  }

  FutureOr<TransferRequirements> _initRefreshRequirements(
    BeneficiaryCreationModel beneficiaryCreationModel,
    String optionId,
  ) {
    final transferRequirementsModel =
        beneficiaryCreationModel.transferRequirementsModel;
    final requireRefreshKeys =
        transferRequirementsModel.getRequireRefreshOnChangeKeys();

    if (requireRefreshKeys.isNotEmpty) {
      return _beneficiaryCreationInteractor
          .getRefreshedOnChangeTransferRequirements(
        optionId: optionId,
        fields:
            _requirementsModelMapper.mapToTransferRequiremnetRefreshDataEntries(
          requireRefreshKeys,
          transferRequirementsModel,
        ),
      );
    } else {
      return beneficiaryCreationModel.transferRequirements;
    }
  }

  Future<void> _refreshRequirements(
    String optionId,
    List<TransferRequiremnetRefreshDataEntry> fields,
  ) async {
    try {
      loading(true);
      final transferRequirements = await _beneficiaryCreationInteractor
          .getRefreshedOnChangeTransferRequirements(
        optionId: optionId,
        fields: fields,
      );

      _logger.debug(
        'RefreshedTransferRequirements result: $transferRequirements',
      );

      state.whenOrNull(
        loaded: (optionId, prevBeneficiaryCreationModel) {
          final beneficiaryCreationModel =
              _requirementsModelMapper.mapToBeneficiaryCreationModel(
            transferRequirements,
            prevBeneficiaryCreationModel,
          );

          safeEmit(
            BeneficiaryCreationState.loaded(
              optionId: optionId,
              beneficiaryCreationModel: beneficiaryCreationModel,
            ),
          );
        },
      );
    } on BeneficiaryCreationException catch (error, st) {
      showError(error.description ?? '');
      _logError(error, st);
    } on Object catch (error, st) {
      _commonErrorHandler.handleError(error);
      _logError(error, st);
    } finally {
      loading(false);
    }
  }

  Future<BeneficiaryCreationModel> _refreshedBeneficiaryCreationState(
    String optionId,
    BeneficiaryCreationModel prevBeneficiaryCreationModel,
  ) async {
    final transferRequirements = await _initRefreshRequirements(
      prevBeneficiaryCreationModel,
      optionId,
    );

    _logger.debug('TransferRequirements: $transferRequirements');

    return _requirementsModelMapper.mapToBeneficiaryCreationModel(
      transferRequirements,
      prevBeneficiaryCreationModel,
    );
  }

  Future<void> _processRefreshAfterSubmit(
    String optionId,
    BeneficiaryCreationModel beneficiaryCreationModel,
  ) async {
    final refreshedBeneficiaryCreationModel =
        await _refreshedBeneficiaryCreationState(
      optionId,
      beneficiaryCreationModel,
    );

    safeEmit(
      BeneficiaryCreationState.loaded(
        optionId: optionId,
        beneficiaryCreationModel: refreshedBeneficiaryCreationModel,
      ),
    );
  }

  Future<BeneficiaryCreationResult> _processSubmit(
    PaymentCountry country,
    Money amount,
    BeneficiaryCreationModel beneficiaryCreationModel,
  ) async {
    final beneficiaryData =
        _requirementsModelMapper.mapToInternationalBeneficiaryData(
      beneficiaryCreationModel.transferRequirementsModel,
    );

    return _beneficiaryInteractor.createInternationalBeneficiary(
      countryCode: country.code,
      currencyCode: amount.currency.code,
      beneficiaryData: beneficiaryData,
      transferRequirements: beneficiaryCreationModel.transferRequirements,
      isV2Enabled: _isCreateBeneficiaryV2Enabled,
    );
  }

  void _selectBeneAfterCreation(
    CrossborderInternationalBeneficiary internationalBeneficiary,
  ) {
    final accountHolderName = internationalBeneficiary.accountHolderName;

    _analytics.addBene(
      internationalBeneficiary.type.name,
      nicknameAdded: internationalBeneficiary.nickname != null,
      emailAdded: internationalBeneficiary.email != null,
    );

    Future<void>.delayed(
      const Duration(seconds: 1),
      () => _analytics.beneSelection(isNewBene: true),
    );

    _toastMessageProvider.showRetailMobileThemedToastMessage(
      mobile.NotificationToastMessageConfiguration.success(
        '$accountHolderName has been added to your list of payees.',
      ),
    );
  }

  void _logError(Object? error, StackTrace? st) => _logger.error(
        error.toString(),
        error: error,
        stackTrace: st,
      );

  @override
  String toString() => 'BeneficiaryCreationCubit';
}
