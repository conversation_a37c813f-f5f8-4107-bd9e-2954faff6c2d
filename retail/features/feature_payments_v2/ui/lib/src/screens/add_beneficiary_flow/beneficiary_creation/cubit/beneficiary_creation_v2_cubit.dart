import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:flutter/widgets.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_common_feature_context_faq_api/configs/context_faq_tags.dart';
import 'package:wio_common_feature_context_faq_api/navigation/bottom_sheets/context_faq_bottom_sheet_navigation_config.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_payments_ui/wio_common_feature_payments_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_payments_v2_ui/src/exception/index.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/add_beneficiary_flow/beneficiary_creation/cubit/beneficiary_creation_v2_state.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/beneficiary_creation/beneficiary_creation_page.dart';

abstract interface class BeneficiaryCreationDelegate {
  void onBeneficiaryCreated(BeneficiaryCreationResult result);
  void onBeneLimitReached();
}

class BeneficiaryCreationV2Cubit
    extends AddBeneBaseCubit<BeneficiaryCreationV2State> {
  final BeneficiaryCreationInteractor _beneficiaryCreationInteractor;
  final BeneficiaryInteractor _beneficiaryInteractor;
  final RequirementsModelMapper _requirementsModelMapper;
  final Logger _logger;
  final CommonErrorHandler _commonErrorHandler;
  final ToastMessageProvider _toastMessageProvider;
  final WioPaymentsAnalytics _analytics;
  final BeneficiaryCreationDelegate _delegate;
  final FeatureToggleProvider _featureToggleProvider;
  final NavigationProvider _navigationProvider;

  // This from here to easily check that form is valid
  static final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  BeneficiaryCreationV2Cubit({
    required BeneficiaryCreationDelegate delegate,
    required BeneficiaryCreationInteractor beneficiaryCreationInteractor,
    required BeneficiaryInteractor beneficiaryInteractor,
    required RequirementsModelMapper requirementsModelMapper,
    required Logger logger,
    required CommonErrorHandler commonErrorHandler,
    required ToastMessageProvider toastMessageProvider,
    required WioPaymentsAnalytics analytics,
    required FeatureToggleProvider featureToggleProvider,
    required NavigationProvider navigationProvider,
  })  : _delegate = delegate,
        _beneficiaryCreationInteractor = beneficiaryCreationInteractor,
        _beneficiaryInteractor = beneficiaryInteractor,
        _requirementsModelMapper = requirementsModelMapper,
        _logger = logger,
        _commonErrorHandler = commonErrorHandler,
        _toastMessageProvider = toastMessageProvider,
        _analytics = analytics,
        _featureToggleProvider = featureToggleProvider,
        _navigationProvider = navigationProvider,
        super(const BeneficiaryCreationV2State.loading());

  bool get _isCreateBeneficiaryV2Enabled => _featureToggleProvider
      .get(PaymentsFeatureToggle.isRetailCreateBeneficiaryV2Enabled);

  bool get _isBeneficiaryAdditionLimitEnabled => _featureToggleProvider.get(
        PaymentsFeatureToggle.isBeneficiaryAdditionLimitEnabled,
      );

  Future<void> initialize(
    PaymentCountry country,
    PaymentCurrency currency,
  ) async {
    try {
      final transferRequirements =
          await _beneficiaryCreationInteractor.getBeneficiaryRequirements(
        countryCode: country.code,
        currencyCode: currency.code,
      );

      if (transferRequirements.isEmpty) {
        throw const EmptyRequirementsException(
          'Transfer requirements are empty',
        );
      }

      _logger.debug('Initial TransferRequirements: $transferRequirements');

      final beneficiaryCreationModel = _requirementsModelMapper
          .mapToBeneficiaryCreationModel(transferRequirements);

      final refreshedBeneficiaryCreationModel =
          await _refreshedBeneficiaryCreationState(
        beneficiaryCreationModel,
        country,
        currency,
      );

      safeEmit(
        BeneficiaryCreationV2State.loaded(
          beneficiaryCreationModel: refreshedBeneficiaryCreationModel,
          currency: currency,
          country: country,
        ),
      );

      unawaited(
        handleCreationLimit(transferRequirements.creationLimit),
      );
    } on BeneficiaryCreationException catch (error, st) {
      showError(error.description ?? '');
      _logError(error, st);
      safeEmit(const BeneficiaryCreationV2State.failure());
    } on Object catch (error, st) {
      _commonErrorHandler.handleError(error);
      _logError(error, st);
      safeEmit(const BeneficiaryCreationV2State.failure());
    }
  }

  Future<void> handleCreationLimit(
    BeneficiaryCreationLimitData? creationLimit,
  ) async {
    if (!_isBeneficiaryAdditionLimitEnabled) {
      return;
    }

    if (creationLimit case final BeneficiaryCreationLimitData creationLimit) {
      await _navigationProvider.showBottomSheet(
        BeneficiaryAdditionLimitBottomSheetConfig(
          description: creationLimit.creationLimitMessage,
          onFaqTap: () {
            _navigationProvider.showBottomSheet<void>(
              const ContextFaqBottomSheetNavigationConfig(
                tags: [
                  ContextFaqTags.retailPaymentsContactLimit,
                ],
                fromScreen: BeneficiaryCreationPage.name,
              ),
            );
          },
        ),
      );

      if (creationLimit.isLimitReached) {
        _delegate.onBeneLimitReached();
      }
    }
  }

  void onValueChange(TransferRequirementInfoModel changedModel) {
    _logger.debug('Changed ${changedModel.toString()}');

    final key = changedModel.map(
      text: (model) => model.key,
      radio: (model) => model.key,
      phonenumber: (model) => model.key,
      info: (model) => model.key,
    );

    state.mapOrNull(
      loaded: (loadedState) {
        final transferRequirementsModel =
            loadedState.beneficiaryCreationModel.transferRequirementsModel;
        final valueModel = transferRequirementsModel.modelByKey(key);

        if (valueModel == changedModel) {
          // No new value. Skip
          return;
        }

        final modelUpd = transferRequirementsModel
            .map(
              (transferRequirementModel) =>
                  transferRequirementModel.copyWithUpdatedModel(
                key: key,
                updatedModel: changedModel,
              ),
            )
            .toList();

        emit(
          loadedState.copyWith(
            beneficiaryCreationModel:
                loadedState.beneficiaryCreationModel.copyWith(
              transferRequirementsModel: modelUpd,
            ),
          ),
        );
      },
    );
  }

  // ignore: avoid_positional_boolean_parameters
  Future<void> onValueSubmit(String key, bool valid) async {
    if (!valid) return;

    await state.mapOrNull(
      loaded: (model) async {
        final beneficiaryCreationModel = model.beneficiaryCreationModel;
        final transferRequirementsModel =
            beneficiaryCreationModel.transferRequirementsModel;

        final refreshRequirementsOnChange = transferRequirementsModel
                .modelByKey(key)
                ?.requirement
                .refreshRequirementsOnChange ??
            false;

        if (refreshRequirementsOnChange) {
          await _onRefreshRequirementsOnChange();
        }
      },
    );
  }

  Future<void> onSubmit() async {
    await state.mapOrNull(
      loaded: (model) async {
        final beneficiaryCreationModel = model.beneficiaryCreationModel;
        final country = model.country;
        final currency = model.currency;
        final isRefreshRequired = beneficiaryCreationModel.isRefreshRequired();

        try {
          loading(true);

          if (isRefreshRequired) {
            await _processRefreshAndMaybeSubmit(
              beneficiaryCreationModel,
              country,
              currency,
            );
          } else {
            await _submitBeneficiary(
              beneficiaryCreationModel,
              country,
              currency,
            );
          }
        } on BeneficiaryCreationException catch (error, st) {
          _logError(error, st);
          showError(error.description ?? '');
        } on Object catch (error, st) {
          _logError(error, st);
          _commonErrorHandler.handleError(error);
        } finally {
          loading(false);
        }
      },
    );
  }

  Future<void> _submitBeneficiary(
    BeneficiaryCreationModel beneficiaryCreationModel,
    PaymentCountry country,
    PaymentCurrency currency,
  ) async {
    try {
      loading(true);

      final beneficiaryContact = await _processSubmit(
        beneficiaryCreationModel,
        country,
        currency,
      );

      _logger.debug(
        'BeneficiaryContact result: $beneficiaryContact',
      );

      _selectBeneAfterCreation(
        beneficiaryContact,
      );

      _delegate.onBeneficiaryCreated(beneficiaryContact);
    } on BeneficiaryCreationException catch (error, st) {
      _logError(error, st);
      showError(error.description ?? '');
    } on Object catch (error, st) {
      _logError(error, st);
      _commonErrorHandler.handleError(error);
    } finally {
      loading(false);
    }
  }

  Future<BeneficiaryCreationResult> _processSubmit(
    BeneficiaryCreationModel beneficiaryCreationModel,
    PaymentCountry country,
    PaymentCurrency currency,
  ) async {
    final beneficiaryData =
        _requirementsModelMapper.mapToInternationalBeneficiaryData(
      beneficiaryCreationModel.transferRequirementsModel,
    );

    return _beneficiaryInteractor.createInternationalBeneficiary(
      countryCode: country.code,
      currencyCode: currency.code,
      beneficiaryData: beneficiaryData,
      transferRequirements: beneficiaryCreationModel.transferRequirements,
      isV2Enabled: _isCreateBeneficiaryV2Enabled,
    );
  }

  void _selectBeneAfterCreation(
    BeneficiaryCreationResult result,
  ) {
    final internationalBeneficiary = result.beneficiary;
    final accountHolderName = internationalBeneficiary.accountHolderName;

    _analytics.addBene(
      internationalBeneficiary.type.name,
      nicknameAdded: internationalBeneficiary.nickname != null,
      emailAdded: internationalBeneficiary.email != null,
    );

    Future<void>.delayed(
      const Duration(seconds: 1),
      () => _analytics.beneSelection(isNewBene: true),
    );

    _toastMessageProvider.showRetailMobileThemedToastMessage(
      NotificationToastMessageConfiguration.success(
        '$accountHolderName has been added to your list of payees.',
      ),
    );
  }

  Future<void> _onRefreshRequirementsOnChange() async {
    await state.mapOrNull(
      loaded: (model) async {
        final beneficiaryCreationModel = model.beneficiaryCreationModel;
        final transferRequirementsModel =
            beneficiaryCreationModel.transferRequirementsModel;

        final keys = transferRequirementsModel.getRequireRefreshOnChangeKeys();

        if (keys.isNotEmpty) {
          final fields = _requirementsModelMapper
              .mapToTransferRequiremnetRefreshDataEntries(
            keys,
            transferRequirementsModel,
          );

          await _refreshRequirements(fields);
        }
      },
    );
  }

  FutureOr<TransferRequirements> _initRefreshRequirements(
    BeneficiaryCreationModel beneficiaryCreationModel,
    PaymentCountry country,
    PaymentCurrency currency,
  ) {
    final transferRequirementsModel =
        beneficiaryCreationModel.transferRequirementsModel;
    final requireRefreshKeys =
        transferRequirementsModel.getRequireRefreshOnChangeKeys();

    if (requireRefreshKeys.isNotEmpty) {
      return _beneficiaryCreationInteractor
          .getRefreshedOnChangeBeneficiaryRequirements(
        fields:
            _requirementsModelMapper.mapToTransferRequiremnetRefreshDataEntries(
          requireRefreshKeys,
          transferRequirementsModel,
        ),
        countryCode: country.code,
        currencyCode: currency.code,
      );
    } else {
      return beneficiaryCreationModel.transferRequirements;
    }
  }

  Future<void> _refreshRequirements(
    List<TransferRequiremnetRefreshDataEntry> fields,
  ) async {
    await state.mapOrNull(
      loaded: (model) async {
        final prevBeneficiaryCreationModel = model.beneficiaryCreationModel;
        final country = model.country;
        final currency = model.currency;

        try {
          loading(true);

          final transferRequirements = await _beneficiaryCreationInteractor
              .getRefreshedOnChangeBeneficiaryRequirements(
            fields: fields,
            countryCode: country.code,
            currencyCode: currency.code,
          );

          _logger.debug(
            'RefreshedTransferRequirements result: $transferRequirements',
          );

          final beneficiaryCreationModel =
              _requirementsModelMapper.mapToBeneficiaryCreationModel(
            transferRequirements,
            prevBeneficiaryCreationModel,
          );

          safeEmit(
            BeneficiaryCreationV2State.loaded(
              beneficiaryCreationModel: beneficiaryCreationModel,
              country: country,
              currency: currency,
            ),
          );
        } on BeneficiaryCreationException catch (error, st) {
          showError(error.description ?? '');
          _logError(error, st);
        } on Object catch (error, st) {
          _commonErrorHandler.handleError(error);
          _logError(error, st);
        } finally {
          loading(false);
        }
      },
    );
  }

  Future<BeneficiaryCreationModel> _refreshedBeneficiaryCreationState(
    BeneficiaryCreationModel prevBeneficiaryCreationModel,
    PaymentCountry country,
    PaymentCurrency currency,
  ) async {
    final transferRequirements = await _initRefreshRequirements(
      prevBeneficiaryCreationModel,
      country,
      currency,
    );

    _logger.debug('TransferRequirements: $transferRequirements');

    return _requirementsModelMapper.mapToBeneficiaryCreationModel(
      transferRequirements,
      prevBeneficiaryCreationModel,
    );
  }

  Future<void> _processRefreshAndMaybeSubmit(
    BeneficiaryCreationModel beneficiaryCreationModel,
    PaymentCountry country,
    PaymentCurrency currency,
  ) async {
    final refreshedBeneficiaryCreationModel =
        await _refreshedBeneficiaryCreationState(
      beneficiaryCreationModel,
      country,
      currency,
    );

    safeEmit(
      BeneficiaryCreationV2State.loaded(
        beneficiaryCreationModel: refreshedBeneficiaryCreationModel,
        country: country,
        currency: currency,
      ),
    );

    // Delay here to let the form be validated after emitting refreshed values
    unawaited(
      Future.delayed(
        const Duration(milliseconds: 150),
        () => checkFormValidAndSubmit(),
      ),
    );
  }

  Future<void> checkFormValidAndSubmit() async {
    await state.mapOrNull(
      loaded: (value) async {
        final beneficiaryCreationModel = value.beneficiaryCreationModel;
        final country = value.country;
        final currency = value.currency;

        final isFormValid =
            BeneficiaryCreationV2Cubit.formKey.currentState?.validate() ??
                false;

        if (isFormValid && !beneficiaryCreationModel.hasAnyRequirementError()) {
          await _submitBeneficiary(
            beneficiaryCreationModel,
            country,
            currency,
          );
        }
      },
    );
  }

  void _logError(Object? error, StackTrace? st) => _logger.error(
        error.toString(),
        error: error,
        stackTrace: st,
      );

  @override
  String toString() => 'BeneficiaryCreationV2Cubit';
}
