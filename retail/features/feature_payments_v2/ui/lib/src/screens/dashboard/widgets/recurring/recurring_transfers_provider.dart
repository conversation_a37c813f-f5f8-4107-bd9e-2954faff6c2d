import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wio_feature_account_api/models/account_details.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/dashboard/widgets/recurring/cubit/recurring_transfers_cubit.dart';

class RecurringTransfersProvider extends StatefulWidget {
  final List<AccountDetails> accounts;
  final bool forceRefresh;
  final Widget child;

  const RecurringTransfersProvider({
    required this.accounts,
    required this.child,
    super.key,
    this.forceRefresh = true,
  });

  @override
  State<RecurringTransfersProvider> createState() =>
      _RecurringTransfersProviderState();
}

class _RecurringTransfersProviderState
    extends State<RecurringTransfersProvider> {
  late RecurringTransfersCubit _cubit;

  @override
  void initState() {
    _cubit = DependencyProvider.get<RecurringTransfersCubit>()
      ..init(forceRefresh: widget.forceRefresh);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RecurringTransfersCubit>.value(
      value: _cubit,
      child: widget.child,
    );
  }
}
