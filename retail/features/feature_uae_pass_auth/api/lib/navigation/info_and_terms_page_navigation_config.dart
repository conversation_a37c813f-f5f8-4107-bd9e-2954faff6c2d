import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_onboarding_domain_api/index.dart';
import 'package:wio_feature_uae_pass_auth_api/navigation/uae_pass_auth_feature_navigation_config.dart';

part 'info_and_terms_page_navigation_config.freezed.dart';

@freezed
class InfoAndTermsPageNavigationConfig extends ScreenNavigationConfig
    with _$InfoAndTermsPageNavigationConfig {
  static const screenId = 'info_and_terms_page';

  const factory InfoAndTermsPageNavigationConfig({
    required UserAttributes userAttributes,
    required String token,
  }) = _InfoAndTermsPageNavigationConfig;

  const InfoAndTermsPageNavigationConfig._()
      : super(
          id: screenId,
          feature: UaePassAuthFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'InfoAndTermsPageNavigationConfig';
}
