import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:di/di.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:flutter/cupertino.dart';
import 'package:retail_api/index.dart';
import 'package:retail_api/provider/dashboard_navigation_provider.dart';
import 'package:retail_ui/l10n/retail_ui_localizations.g.dart';
import 'package:retail_ui/navigation/retail_feature_navigation_config.dart';
import 'package:retail_ui/navigation/retail_router.dart';
import 'package:retail_ui/provider/dashboard_navigation_provider_impl.dart';
import 'package:retail_ui/screens/retail/analytics/retail_analytics.dart';
import 'package:retail_ui/screens/retail/cubit/retail_cubit.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_participant_user_api/participant_user_api.dart';
import 'package:wio_feature_user_api/index.dart';

class RetailFeatureDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton(
      () => RetailRouter(
        navigation: DependencyProvider.get<NavigationProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<RetailRouter>(),
      instanceName: RetailFeatureNavigationConfig.name,
    );

    DependencyProvider.registerLazySingleton<RetailUILocalizations>(
      () => RetailUILocalizations.of(
        DependencyProvider.get<BuildContext>(),
      ),
    );

    DependencyProvider.registerLazySingleton<DashboardNavigationProvider>(
      () => DashboardNavigationProviderImpl(
        userInteractor: DependencyProvider.get<UserInteractor>(),
        participantFlow: DependencyProvider.get<ParticipantFlow>(),
      ),
    );

    DependencyProvider.registerFactory<RetailAnalytics>(
      () => RetailAnalytics(
        analyticsFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );

    DependencyProvider.registerFactory<RetailCubit>(
      () => RetailCubit(
        featureToggleProvider: DependencyProvider.get<FeatureToggleProvider>(),
        walletInteractor: DependencyProvider.get<WalletInteractor>(),
        userInteractor: DependencyProvider.get<UserInteractor>(),
        customerAddressInteractor:
            DependencyProvider.get<CustomerAddressInteractor>(),
        navigator: DependencyProvider.get<NavigationProvider>(),
        logger: DependencyProvider.get<Logger>(
          instanceName: WioDomain.core.name,
        ),
        addressFeatureNavigationFactory:
            DependencyProvider.get<CustomerAddressFeatureNavigationFactory>(),
        analytics: DependencyProvider.get<RetailAnalytics>(),
      ),
    );
  }
}
