import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:retail_ui/navigation/retail_feature_navigation_config.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'retail_base_page_navigator_router.freezed.dart';

@freezed
class RetailPageNavigationConfig extends ScreenNavigationConfig
    with _$RetailPageNavigationConfig {
  static const screenId = 'retail_base_screen';

  const factory RetailPageNavigationConfig({
    DashboardFeatureNavigationConfig? dashboardConfig,
  }) = _RetailPageNavigationConfig;

  const RetailPageNavigationConfig._()
      : super(
          id: RetailPageNavigationConfig.screenId,
          feature: RetailFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'RetailPageNavigationConfig';

  @override
  bool get isCriticalView => true;
}
