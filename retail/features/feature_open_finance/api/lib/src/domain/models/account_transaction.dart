import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_transaction.freezed.dart';

/// Account transaction information
@freezed
class AccountTransaction with _$AccountTransaction {
  const factory AccountTransaction({
    /// Transaction ID
    required String id,

    /// Transaction amount
    required double amount,

    /// Transaction currency
    required String currency,

    /// Transaction type
    required String type,

    /// Transaction description
    required String description,

    /// Transaction date
    required DateTime date,

    /// Transaction status
    required String status,

    /// Transaction reference
    String? reference,

    /// Transaction category
    String? category,

    /// Transaction merchant
    String? merchant,
  }) = _AccountTransaction;
}
