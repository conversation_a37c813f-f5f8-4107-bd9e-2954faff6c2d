// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'consent_management.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConsentCounts {
  int get dataSharingConsents => throw _privateConstructorUsedError;
  int get serviceInitiationConsents => throw _privateConstructorUsedError;

  /// Create a copy of ConsentCounts
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConsentCountsCopyWith<ConsentCounts> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConsentCountsCopyWith<$Res> {
  factory $ConsentCountsCopyWith(
          ConsentCounts value, $Res Function(ConsentCounts) then) =
      _$ConsentCountsCopyWithImpl<$Res, ConsentCounts>;
  @useResult
  $Res call({int dataSharingConsents, int serviceInitiationConsents});
}

/// @nodoc
class _$ConsentCountsCopyWithImpl<$Res, $Val extends ConsentCounts>
    implements $ConsentCountsCopyWith<$Res> {
  _$ConsentCountsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConsentCounts
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dataSharingConsents = null,
    Object? serviceInitiationConsents = null,
  }) {
    return _then(_value.copyWith(
      dataSharingConsents: null == dataSharingConsents
          ? _value.dataSharingConsents
          : dataSharingConsents // ignore: cast_nullable_to_non_nullable
              as int,
      serviceInitiationConsents: null == serviceInitiationConsents
          ? _value.serviceInitiationConsents
          : serviceInitiationConsents // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ConsentCountsImplCopyWith<$Res>
    implements $ConsentCountsCopyWith<$Res> {
  factory _$$ConsentCountsImplCopyWith(
          _$ConsentCountsImpl value, $Res Function(_$ConsentCountsImpl) then) =
      __$$ConsentCountsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int dataSharingConsents, int serviceInitiationConsents});
}

/// @nodoc
class __$$ConsentCountsImplCopyWithImpl<$Res>
    extends _$ConsentCountsCopyWithImpl<$Res, _$ConsentCountsImpl>
    implements _$$ConsentCountsImplCopyWith<$Res> {
  __$$ConsentCountsImplCopyWithImpl(
      _$ConsentCountsImpl _value, $Res Function(_$ConsentCountsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConsentCounts
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dataSharingConsents = null,
    Object? serviceInitiationConsents = null,
  }) {
    return _then(_$ConsentCountsImpl(
      dataSharingConsents: null == dataSharingConsents
          ? _value.dataSharingConsents
          : dataSharingConsents // ignore: cast_nullable_to_non_nullable
              as int,
      serviceInitiationConsents: null == serviceInitiationConsents
          ? _value.serviceInitiationConsents
          : serviceInitiationConsents // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ConsentCountsImpl implements _ConsentCounts {
  const _$ConsentCountsImpl(
      {required this.dataSharingConsents,
      required this.serviceInitiationConsents});

  @override
  final int dataSharingConsents;
  @override
  final int serviceInitiationConsents;

  @override
  String toString() {
    return 'ConsentCounts(dataSharingConsents: $dataSharingConsents, serviceInitiationConsents: $serviceInitiationConsents)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentCountsImpl &&
            (identical(other.dataSharingConsents, dataSharingConsents) ||
                other.dataSharingConsents == dataSharingConsents) &&
            (identical(other.serviceInitiationConsents,
                    serviceInitiationConsents) ||
                other.serviceInitiationConsents == serviceInitiationConsents));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, dataSharingConsents, serviceInitiationConsents);

  /// Create a copy of ConsentCounts
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConsentCountsImplCopyWith<_$ConsentCountsImpl> get copyWith =>
      __$$ConsentCountsImplCopyWithImpl<_$ConsentCountsImpl>(this, _$identity);
}

abstract class _ConsentCounts implements ConsentCounts {
  const factory _ConsentCounts(
      {required final int dataSharingConsents,
      required final int serviceInitiationConsents}) = _$ConsentCountsImpl;

  @override
  int get dataSharingConsents;
  @override
  int get serviceInitiationConsents;

  /// Create a copy of ConsentCounts
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConsentCountsImplCopyWith<_$ConsentCountsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ConsentAudit {
  List<AuditEntry> get data => throw _privateConstructorUsedError;
  PaginationMetadata get meta => throw _privateConstructorUsedError;

  /// Create a copy of ConsentAudit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConsentAuditCopyWith<ConsentAudit> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConsentAuditCopyWith<$Res> {
  factory $ConsentAuditCopyWith(
          ConsentAudit value, $Res Function(ConsentAudit) then) =
      _$ConsentAuditCopyWithImpl<$Res, ConsentAudit>;
  @useResult
  $Res call({List<AuditEntry> data, PaginationMetadata meta});

  $PaginationMetadataCopyWith<$Res> get meta;
}

/// @nodoc
class _$ConsentAuditCopyWithImpl<$Res, $Val extends ConsentAudit>
    implements $ConsentAuditCopyWith<$Res> {
  _$ConsentAuditCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConsentAudit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<AuditEntry>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as PaginationMetadata,
    ) as $Val);
  }

  /// Create a copy of ConsentAudit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaginationMetadataCopyWith<$Res> get meta {
    return $PaginationMetadataCopyWith<$Res>(_value.meta, (value) {
      return _then(_value.copyWith(meta: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ConsentAuditImplCopyWith<$Res>
    implements $ConsentAuditCopyWith<$Res> {
  factory _$$ConsentAuditImplCopyWith(
          _$ConsentAuditImpl value, $Res Function(_$ConsentAuditImpl) then) =
      __$$ConsentAuditImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<AuditEntry> data, PaginationMetadata meta});

  @override
  $PaginationMetadataCopyWith<$Res> get meta;
}

/// @nodoc
class __$$ConsentAuditImplCopyWithImpl<$Res>
    extends _$ConsentAuditCopyWithImpl<$Res, _$ConsentAuditImpl>
    implements _$$ConsentAuditImplCopyWith<$Res> {
  __$$ConsentAuditImplCopyWithImpl(
      _$ConsentAuditImpl _value, $Res Function(_$ConsentAuditImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConsentAudit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? meta = null,
  }) {
    return _then(_$ConsentAuditImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<AuditEntry>,
      meta: null == meta
          ? _value.meta
          : meta // ignore: cast_nullable_to_non_nullable
              as PaginationMetadata,
    ));
  }
}

/// @nodoc

class _$ConsentAuditImpl implements _ConsentAudit {
  const _$ConsentAuditImpl(
      {required final List<AuditEntry> data, required this.meta})
      : _data = data;

  final List<AuditEntry> _data;
  @override
  List<AuditEntry> get data {
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_data);
  }

  @override
  final PaginationMetadata meta;

  @override
  String toString() {
    return 'ConsentAudit(data: $data, meta: $meta)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentAuditImpl &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.meta, meta) || other.meta == meta));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_data), meta);

  /// Create a copy of ConsentAudit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConsentAuditImplCopyWith<_$ConsentAuditImpl> get copyWith =>
      __$$ConsentAuditImplCopyWithImpl<_$ConsentAuditImpl>(this, _$identity);
}

abstract class _ConsentAudit implements ConsentAudit {
  const factory _ConsentAudit(
      {required final List<AuditEntry> data,
      required final PaginationMetadata meta}) = _$ConsentAuditImpl;

  @override
  List<AuditEntry> get data;
  @override
  PaginationMetadata get meta;

  /// Create a copy of ConsentAudit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConsentAuditImplCopyWith<_$ConsentAuditImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AuditEntry {
  String get providerId => throw _privateConstructorUsedError;
  String get operation => throw _privateConstructorUsedError;
  int get timestamp => throw _privateConstructorUsedError;
  String get fkId => throw _privateConstructorUsedError;
  String get id => throw _privateConstructorUsedError;
  String? get fkMongoId => throw _privateConstructorUsedError;
  String? get ozoneInteractionId => throw _privateConstructorUsedError;
  CallerDetails? get callerDetails => throw _privateConstructorUsedError;
  String? get patchFilter => throw _privateConstructorUsedError;
  String? get patch => throw _privateConstructorUsedError;

  /// Create a copy of AuditEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuditEntryCopyWith<AuditEntry> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuditEntryCopyWith<$Res> {
  factory $AuditEntryCopyWith(
          AuditEntry value, $Res Function(AuditEntry) then) =
      _$AuditEntryCopyWithImpl<$Res, AuditEntry>;
  @useResult
  $Res call(
      {String providerId,
      String operation,
      int timestamp,
      String fkId,
      String id,
      String? fkMongoId,
      String? ozoneInteractionId,
      CallerDetails? callerDetails,
      String? patchFilter,
      String? patch});

  $CallerDetailsCopyWith<$Res>? get callerDetails;
}

/// @nodoc
class _$AuditEntryCopyWithImpl<$Res, $Val extends AuditEntry>
    implements $AuditEntryCopyWith<$Res> {
  _$AuditEntryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuditEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? providerId = null,
    Object? operation = null,
    Object? timestamp = null,
    Object? fkId = null,
    Object? id = null,
    Object? fkMongoId = freezed,
    Object? ozoneInteractionId = freezed,
    Object? callerDetails = freezed,
    Object? patchFilter = freezed,
    Object? patch = freezed,
  }) {
    return _then(_value.copyWith(
      providerId: null == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String,
      operation: null == operation
          ? _value.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      fkId: null == fkId
          ? _value.fkId
          : fkId // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fkMongoId: freezed == fkMongoId
          ? _value.fkMongoId
          : fkMongoId // ignore: cast_nullable_to_non_nullable
              as String?,
      ozoneInteractionId: freezed == ozoneInteractionId
          ? _value.ozoneInteractionId
          : ozoneInteractionId // ignore: cast_nullable_to_non_nullable
              as String?,
      callerDetails: freezed == callerDetails
          ? _value.callerDetails
          : callerDetails // ignore: cast_nullable_to_non_nullable
              as CallerDetails?,
      patchFilter: freezed == patchFilter
          ? _value.patchFilter
          : patchFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      patch: freezed == patch
          ? _value.patch
          : patch // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of AuditEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CallerDetailsCopyWith<$Res>? get callerDetails {
    if (_value.callerDetails == null) {
      return null;
    }

    return $CallerDetailsCopyWith<$Res>(_value.callerDetails!, (value) {
      return _then(_value.copyWith(callerDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AuditEntryImplCopyWith<$Res>
    implements $AuditEntryCopyWith<$Res> {
  factory _$$AuditEntryImplCopyWith(
          _$AuditEntryImpl value, $Res Function(_$AuditEntryImpl) then) =
      __$$AuditEntryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String providerId,
      String operation,
      int timestamp,
      String fkId,
      String id,
      String? fkMongoId,
      String? ozoneInteractionId,
      CallerDetails? callerDetails,
      String? patchFilter,
      String? patch});

  @override
  $CallerDetailsCopyWith<$Res>? get callerDetails;
}

/// @nodoc
class __$$AuditEntryImplCopyWithImpl<$Res>
    extends _$AuditEntryCopyWithImpl<$Res, _$AuditEntryImpl>
    implements _$$AuditEntryImplCopyWith<$Res> {
  __$$AuditEntryImplCopyWithImpl(
      _$AuditEntryImpl _value, $Res Function(_$AuditEntryImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuditEntry
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? providerId = null,
    Object? operation = null,
    Object? timestamp = null,
    Object? fkId = null,
    Object? id = null,
    Object? fkMongoId = freezed,
    Object? ozoneInteractionId = freezed,
    Object? callerDetails = freezed,
    Object? patchFilter = freezed,
    Object? patch = freezed,
  }) {
    return _then(_$AuditEntryImpl(
      providerId: null == providerId
          ? _value.providerId
          : providerId // ignore: cast_nullable_to_non_nullable
              as String,
      operation: null == operation
          ? _value.operation
          : operation // ignore: cast_nullable_to_non_nullable
              as String,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
      fkId: null == fkId
          ? _value.fkId
          : fkId // ignore: cast_nullable_to_non_nullable
              as String,
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fkMongoId: freezed == fkMongoId
          ? _value.fkMongoId
          : fkMongoId // ignore: cast_nullable_to_non_nullable
              as String?,
      ozoneInteractionId: freezed == ozoneInteractionId
          ? _value.ozoneInteractionId
          : ozoneInteractionId // ignore: cast_nullable_to_non_nullable
              as String?,
      callerDetails: freezed == callerDetails
          ? _value.callerDetails
          : callerDetails // ignore: cast_nullable_to_non_nullable
              as CallerDetails?,
      patchFilter: freezed == patchFilter
          ? _value.patchFilter
          : patchFilter // ignore: cast_nullable_to_non_nullable
              as String?,
      patch: freezed == patch
          ? _value.patch
          : patch // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AuditEntryImpl implements _AuditEntry {
  const _$AuditEntryImpl(
      {required this.providerId,
      required this.operation,
      required this.timestamp,
      required this.fkId,
      required this.id,
      this.fkMongoId,
      this.ozoneInteractionId,
      this.callerDetails,
      this.patchFilter,
      this.patch});

  @override
  final String providerId;
  @override
  final String operation;
  @override
  final int timestamp;
  @override
  final String fkId;
  @override
  final String id;
  @override
  final String? fkMongoId;
  @override
  final String? ozoneInteractionId;
  @override
  final CallerDetails? callerDetails;
  @override
  final String? patchFilter;
  @override
  final String? patch;

  @override
  String toString() {
    return 'AuditEntry(providerId: $providerId, operation: $operation, timestamp: $timestamp, fkId: $fkId, id: $id, fkMongoId: $fkMongoId, ozoneInteractionId: $ozoneInteractionId, callerDetails: $callerDetails, patchFilter: $patchFilter, patch: $patch)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuditEntryImpl &&
            (identical(other.providerId, providerId) ||
                other.providerId == providerId) &&
            (identical(other.operation, operation) ||
                other.operation == operation) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.fkId, fkId) || other.fkId == fkId) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fkMongoId, fkMongoId) ||
                other.fkMongoId == fkMongoId) &&
            (identical(other.ozoneInteractionId, ozoneInteractionId) ||
                other.ozoneInteractionId == ozoneInteractionId) &&
            (identical(other.callerDetails, callerDetails) ||
                other.callerDetails == callerDetails) &&
            (identical(other.patchFilter, patchFilter) ||
                other.patchFilter == patchFilter) &&
            (identical(other.patch, patch) || other.patch == patch));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      providerId,
      operation,
      timestamp,
      fkId,
      id,
      fkMongoId,
      ozoneInteractionId,
      callerDetails,
      patchFilter,
      patch);

  /// Create a copy of AuditEntry
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuditEntryImplCopyWith<_$AuditEntryImpl> get copyWith =>
      __$$AuditEntryImplCopyWithImpl<_$AuditEntryImpl>(this, _$identity);
}

abstract class _AuditEntry implements AuditEntry {
  const factory _AuditEntry(
      {required final String providerId,
      required final String operation,
      required final int timestamp,
      required final String fkId,
      required final String id,
      final String? fkMongoId,
      final String? ozoneInteractionId,
      final CallerDetails? callerDetails,
      final String? patchFilter,
      final String? patch}) = _$AuditEntryImpl;

  @override
  String get providerId;
  @override
  String get operation;
  @override
  int get timestamp;
  @override
  String get fkId;
  @override
  String get id;
  @override
  String? get fkMongoId;
  @override
  String? get ozoneInteractionId;
  @override
  CallerDetails? get callerDetails;
  @override
  String? get patchFilter;
  @override
  String? get patch;

  /// Create a copy of AuditEntry
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuditEntryImplCopyWith<_$AuditEntryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CallerDetails {
  String? get callerOrgId => throw _privateConstructorUsedError;
  String? get callerClientId => throw _privateConstructorUsedError;
  String? get callerSoftwareStatementId => throw _privateConstructorUsedError;

  /// Create a copy of CallerDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CallerDetailsCopyWith<CallerDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CallerDetailsCopyWith<$Res> {
  factory $CallerDetailsCopyWith(
          CallerDetails value, $Res Function(CallerDetails) then) =
      _$CallerDetailsCopyWithImpl<$Res, CallerDetails>;
  @useResult
  $Res call(
      {String? callerOrgId,
      String? callerClientId,
      String? callerSoftwareStatementId});
}

/// @nodoc
class _$CallerDetailsCopyWithImpl<$Res, $Val extends CallerDetails>
    implements $CallerDetailsCopyWith<$Res> {
  _$CallerDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CallerDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callerOrgId = freezed,
    Object? callerClientId = freezed,
    Object? callerSoftwareStatementId = freezed,
  }) {
    return _then(_value.copyWith(
      callerOrgId: freezed == callerOrgId
          ? _value.callerOrgId
          : callerOrgId // ignore: cast_nullable_to_non_nullable
              as String?,
      callerClientId: freezed == callerClientId
          ? _value.callerClientId
          : callerClientId // ignore: cast_nullable_to_non_nullable
              as String?,
      callerSoftwareStatementId: freezed == callerSoftwareStatementId
          ? _value.callerSoftwareStatementId
          : callerSoftwareStatementId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CallerDetailsImplCopyWith<$Res>
    implements $CallerDetailsCopyWith<$Res> {
  factory _$$CallerDetailsImplCopyWith(
          _$CallerDetailsImpl value, $Res Function(_$CallerDetailsImpl) then) =
      __$$CallerDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? callerOrgId,
      String? callerClientId,
      String? callerSoftwareStatementId});
}

/// @nodoc
class __$$CallerDetailsImplCopyWithImpl<$Res>
    extends _$CallerDetailsCopyWithImpl<$Res, _$CallerDetailsImpl>
    implements _$$CallerDetailsImplCopyWith<$Res> {
  __$$CallerDetailsImplCopyWithImpl(
      _$CallerDetailsImpl _value, $Res Function(_$CallerDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CallerDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? callerOrgId = freezed,
    Object? callerClientId = freezed,
    Object? callerSoftwareStatementId = freezed,
  }) {
    return _then(_$CallerDetailsImpl(
      callerOrgId: freezed == callerOrgId
          ? _value.callerOrgId
          : callerOrgId // ignore: cast_nullable_to_non_nullable
              as String?,
      callerClientId: freezed == callerClientId
          ? _value.callerClientId
          : callerClientId // ignore: cast_nullable_to_non_nullable
              as String?,
      callerSoftwareStatementId: freezed == callerSoftwareStatementId
          ? _value.callerSoftwareStatementId
          : callerSoftwareStatementId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$CallerDetailsImpl implements _CallerDetails {
  const _$CallerDetailsImpl(
      {this.callerOrgId, this.callerClientId, this.callerSoftwareStatementId});

  @override
  final String? callerOrgId;
  @override
  final String? callerClientId;
  @override
  final String? callerSoftwareStatementId;

  @override
  String toString() {
    return 'CallerDetails(callerOrgId: $callerOrgId, callerClientId: $callerClientId, callerSoftwareStatementId: $callerSoftwareStatementId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CallerDetailsImpl &&
            (identical(other.callerOrgId, callerOrgId) ||
                other.callerOrgId == callerOrgId) &&
            (identical(other.callerClientId, callerClientId) ||
                other.callerClientId == callerClientId) &&
            (identical(other.callerSoftwareStatementId,
                    callerSoftwareStatementId) ||
                other.callerSoftwareStatementId == callerSoftwareStatementId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, callerOrgId, callerClientId, callerSoftwareStatementId);

  /// Create a copy of CallerDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CallerDetailsImplCopyWith<_$CallerDetailsImpl> get copyWith =>
      __$$CallerDetailsImplCopyWithImpl<_$CallerDetailsImpl>(this, _$identity);
}

abstract class _CallerDetails implements CallerDetails {
  const factory _CallerDetails(
      {final String? callerOrgId,
      final String? callerClientId,
      final String? callerSoftwareStatementId}) = _$CallerDetailsImpl;

  @override
  String? get callerOrgId;
  @override
  String? get callerClientId;
  @override
  String? get callerSoftwareStatementId;

  /// Create a copy of CallerDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CallerDetailsImplCopyWith<_$CallerDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
