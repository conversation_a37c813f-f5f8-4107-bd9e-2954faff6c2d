import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_open_finance_api/feature_open_finance_api.dart';
import 'package:wio_feature_open_finance_ui/src/bottom_sheets/all_updates/all_updates_bottom_sheet.dart';
import 'package:wio_feature_open_finance_ui/src/bottom_sheets/revocation/revocation_bottom_sheet.dart';
import 'package:wio_feature_open_finance_ui/src/navigation/configs/all_updates_bottom_sheet.dart';
import 'package:wio_feature_open_finance_ui/src/navigation/configs/consent_filters_screen_config.dart';
import 'package:wio_feature_open_finance_ui/src/navigation/configs/data_sharing_details_screen_config.dart';
import 'package:wio_feature_open_finance_ui/src/navigation/configs/index.dart';
import 'package:wio_feature_open_finance_ui/src/navigation/configs/payment_consent_details_screen_config.dart';
import 'package:wio_feature_open_finance_ui/src/screens/choose_consent_type_bottom_sheet/choose_constent_type_bottom_sheet.dart';
import 'package:wio_feature_open_finance_ui/src/screens/choose_consent_type_bottom_sheet/cubit/choose_consent_type_bottom_sheet_cubit.dart';
import 'package:wio_feature_open_finance_ui/src/screens/consent_authorization/open_finance_authorization_screen.dart';
import 'package:wio_feature_open_finance_ui/src/screens/consent_filters/consent_filters_screen.dart';
import 'package:wio_feature_open_finance_ui/src/screens/consent_management/consent_management_screen.dart';
import 'package:wio_feature_open_finance_ui/src/screens/data_sharing_details/data_sharing_details_screen.dart';
import 'package:wio_feature_open_finance_ui/src/screens/payment_consent_details/payment_consent_details_screen.dart';

class ConsentRouter extends NavigationRouter {
  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final config = settings.arguments;
    final screenConfig = config is OpenFinanceFeatureNavigationConfig
        ? config.destination
        : config;

    final currentScreenConfigSettings = RouteSettings(
      name: settings.name,
      arguments: screenConfig,
    );

    if (screenConfig is ConsentManagementScreenNavigationConfig) {
      return toRoute(
        ConsentManagementScreen(type: screenConfig.type),
        currentScreenConfigSettings,
      );
    }

    if (screenConfig is ConsentFiltersScreenNavigationConfig) {
      return toRoute(
        ConsentManagementFiltersScreen(
          initialFilters: screenConfig.initialFilters,
          accounts: screenConfig.accounts,
        ),
        currentScreenConfigSettings,
      );
    }

    if (screenConfig is DataSharingDetailsScreenNavigationConfig) {
      return toRoute(
        DataSharingDetailsScreen(consentId: screenConfig.consentId),
        currentScreenConfigSettings,
      );
    }

    if (screenConfig is OpenFinanceScreenNavigationConfig) {
      return toRoute(
        OpenFinanceAuthorizationScreen(
          clientId: screenConfig.clientId,
          requestUri: screenConfig.requestUri,
        ),
        currentScreenConfigSettings,
      );
    }

    if (screenConfig is PaymentConsentDetailsScreenNavigationConfig) {
      return toRoute(
        PaymentConsentDetailsScreen(consentId: screenConfig.consentId),
        currentScreenConfigSettings,
      );
    }

    throw Exception('Unknown $config for the $runtimeType');
  }

  @override
  Future<T?> showBottomSheet<T>(
    BuildContext context,
    BottomSheetNavigationConfig<T> config,
    RouteSettings routeSettings,
  ) {
    if (config is ChooseConsentTypeBottomSheetConfig) {
      return CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal(
        context: context,
        routeSettings: routeSettings,
        builder: (context) => BlocProvider<ChooseConsentTypeBottomSheetCubit>(
          create: (_) =>
              DependencyProvider.get<ChooseConsentTypeBottomSheetCubit>()
                ..init(),
          child: const ChooseConsentTypeBottomSheet(),
        ),
      );
    }
    if (config is RevocationBottomSheetNavigationConfig) {
      final args = config as RevocationBottomSheetNavigationConfig;
      return CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal(
        context: context,
        routeSettings: routeSettings,
        builder: (context) => RevocationBottomSheet(
          config: args,
        ),
      );
    }

    if (config is AllUpdatesBottomSheetNavigationConfig) {
      final args = config as AllUpdatesBottomSheetNavigationConfig;
      return CompanyBottomSheet.showRetailMobileThemeBasedAdaptiveModal(
        context: context,
        routeSettings: routeSettings,
        builder: (context) => AllUpdatesBottomSheet(consentId: args.consentId),
      );
    }
    return super.showBottomSheet<T>(context, config, routeSettings);
  }
}
