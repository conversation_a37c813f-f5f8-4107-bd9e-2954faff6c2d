import 'dart:async';

import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:retail_utils/index.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_open_finance_api/feature_open_finance_api.dart';
import 'package:wio_feature_open_finance_ui/feature_open_finance_ui.dart';
import 'package:wio_feature_open_finance_ui/src/screens/consent_management/cubit/consent_management_cubit.dart';
import 'package:wio_feature_open_finance_ui/src/screens/consent_management/cubit/consent_management_state.dart';
import 'package:wio_feature_open_finance_ui/src/screens/consent_management/widgets/consent_card.dart';

class ConsentManagementScreen extends StatelessWidget {
  final OpenFinanceConsentType type;

  const ConsentManagementScreen({required this.type, super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<ConsentManagementCubit>(
      create: (_) => DependencyProvider.getWithParams<ConsentManagementCubit,
          OpenFinanceConsentType, void>(param1: type)
        ..init(),
      child: const _ConsentManagementContent(),
    );
  }
}

class _ConsentManagementContent extends StatefulWidget {
  const _ConsentManagementContent();

  @override
  State<_ConsentManagementContent> createState() =>
      _ConsentManagementContentState();
}

class _ConsentManagementContentState extends State<_ConsentManagementContent> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handlePagination);
  }

  @override
  void dispose() {
    _scrollController
      ..removeListener(_handlePagination)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ConsentManagementCubit, ConsentManagementState>(
      builder: (context, state) {
        return Scaffold(
          appBar: const TopNavigation(
            TopNavigationModel(
              state: TopNavigationState.positive,
              backgroundColor: CompanyColorPointer.background1,
            ),
          ),
          body: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              children: [
                _Header(state),
                _Subtitle(state.type),
                _SearchBar(state),
                _Chips(state.filters),
                _Tabs(state.tab.index),
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 400),
                  child: state.loadState.map(
                    idle: (_) => const SizedBox.shrink(),
                    loading: (_) => const _ShimmerList(),
                    error: (_) => const _ErrorBox(),
                    loaded: (s) => s.consents.isEmpty
                        ? const _EmptyListPlaceholder()
                        : _ConsentList(s.consents),
                  ),
                ),
                _PaginationLoader(isLoadingMode: state.isLoadingMore),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handlePagination() {
    final position = _scrollController.position;
    if (position.atEdge && position.pixels != 0) {
      context.read<ConsentManagementCubit>().loadMore();
    }
  }
}

class _Header extends StatelessWidget {
  final ConsentManagementState state;

  const _Header(this.state);

  @override
  Widget build(BuildContext context) {
    final l10n = OpenFinanceLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
      child: Row(
        children: [
          Label(
            model: LabelModel(
              text: state.type == OpenFinanceConsentType.dataSharing
                  ? l10n.openFinanceDataSharing
                  : l10n.openFinancePayments,
              textStyle: CompanyTextStylePointer.h2medium,
              color: CompanyColorPointer.secondary1,
            ),
          ),
          Space.fromSpacingHorizontal(Spacing.s2),
          InkWell(
            onTap: () {
              context.read<ConsentManagementCubit>().changeConsentType();
            },
            child: CompanyIcon(
              CompanyIconModel(
                icon: GraphicAssetPointer.icon(
                  state.isTypeSheetOpen
                      ? CompanyIconPointer.chevron_up
                      : CompanyIconPointer.chevron_down,
                ),
                size: CompanyIconSize.xxLarge,
                color: CompanyColorPointer.secondary1,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Subtitle extends StatelessWidget {
  final OpenFinanceConsentType type;

  const _Subtitle(this.type);

  @override
  Widget build(BuildContext context) {
    final l10n = OpenFinanceLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Text(
        type == OpenFinanceConsentType.dataSharing
            ? l10n.serviceProviderSharing
            : l10n.servicePaymentsPermissions,
        style: context.textStyling.b2.copyWith(
          color: context.colorStyling.secondary3,
        ),
      ),
    );
  }
}

class _SearchBar extends StatefulWidget {
  final ConsentManagementState state;

  const _SearchBar(this.state);

  @override
  State<_SearchBar> createState() => _SearchBarState();
}

class _SearchBarState extends State<_SearchBar> {
  static const _delay = Duration(milliseconds: 400);
  final _textController = TextEditingController();
  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    _textController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant _SearchBar oldWidget) {
    if (oldWidget.state.type != widget.state.type) {
      _debounce?.cancel();
      _textController.clear();
      context.read<ConsentManagementCubit>().search(null);
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ConsentManagementCubit>();
    final l10n = OpenFinanceLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 40,
              child: SearchField(
                textEditingController: _textController,
                formatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^[a-zA-Z0-9 ]+$')),
                ],
                model: SearchFieldModel(labelText: l10n.openFinanceSearch),
                onChanged: (value) {
                  _debounce?.cancel();
                  _debounce = Timer(_delay, () {
                    if (!mounted) return;
                    final q = value.trim();
                    cubit.search(q.isEmpty ? null : q);
                  });
                },
                onCleared: () {
                  _debounce?.cancel();
                  _textController.clear();
                  cubit.search(null);
                },
              ),
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: cubit.openFilters,
            child: const CompanyIcon(
              CompanyIconModel(
                icon: GraphicAssetPointer.icon(CompanyIconPointer.filter),
                color: CompanyColorPointer.secondary1,
                size: CompanyIconSize.large,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Chips extends StatelessWidget {
  final ConsentFilters filters;

  const _Chips(this.filters);

  @override
  Widget build(BuildContext context) {
    if (filters.isEmpty) return const SizedBox.shrink();
    final cubit = context.read<ConsentManagementCubit>();
    final l10n = OpenFinanceLocalizations.of(context);
    final entries = _getChipEntries(filters, cubit, l10n);

    return Padding(
      padding: const EdgeInsets.only(bottom: 24.0),
      child: SizedBox(
        height: 30,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: entries.length,
          separatorBuilder: (_, __) => Space.fromSpacingHorizontal(Spacing.s2),
          itemBuilder: (_, i) {
            final entry = entries[i];
            return Padding(
              padding: EdgeInsetsDirectional.only(
                start: i == 0 ? 24 : 0,
                end: i == entries.length - 1 ? 24 : 0,
              ),
              child: CompanyChip(
                model: CompanyChipModel(
                  borderRadius: 13,
                  title: entry.label,
                  alignment: Alignment.center,
                  backgroundColor: CompanyColorPointer.primary1,
                  iconPointer: const GraphicAssetPointer.icon(
                    CompanyIconPointer.close,
                  ),
                ),
                onIconPressed: entry.onRemove,
              ),
            );
          },
        ),
      ),
    );
  }

  List<_ChipEntry> _getChipEntries(
    ConsentFilters filters,
    ConsentManagementCubit cubit,
    OpenFinanceLocalizations l10n,
  ) {
    final entries = <_ChipEntry>[];

    final accounts = filters.accounts.toList()..sort();
    for (final iban in accounts) {
      entries.add(
        _ChipEntry(
          label: iban.normalizeIban(),
          onRemove: () => cubit.removeAccount(iban),
        ),
      );
    }

    final statuses = filters.statuses.toList()
      ..sort((a, b) => a.name.compareTo(b.name));

    for (final s in statuses) {
      entries.add(
        _ChipEntry(
          label: _consentStatusTitle(s, l10n),
          onRemove: () => cubit.removeStatus(s),
        ),
      );
    }

    final cr = filters.creationDateRange;
    if (cr != null) {
      entries.add(
        _ChipEntry(
          label: l10n.consentCreationRange(
            _dateRangeFormat(
              cr.creationDateFrom,
              cr.creationDateTo,
            ),
          ),
          onRemove: cubit.clearCreationRange,
        ),
      );
    }
    final er = filters.expirationDateRange;
    if (er != null) {
      entries.add(
        _ChipEntry(
          label: l10n.consentExpirationRange(
            _dateRangeFormat(
              er.expirationDateFrom,
              er.expirationDateTo,
            ),
          ),
          onRemove: cubit.clearExpirationRange,
        ),
      );
    }

    return entries;
  }

  String _dateRangeFormat(DateTime from, DateTime to) {
    return '${from.formatTo(
      const RetailDateTimePatterns.MMMddyyyy(),
    )}–${to.formatTo(
      const RetailDateTimePatterns.MMMddyyyy(),
    )}';
  }

  String _consentStatusTitle(ConsentStatus s, OpenFinanceLocalizations l10n) {
    switch (s) {
      case ConsentStatus.authorizationPending:
        return l10n.openFinanceAuthorizationPending;
      case ConsentStatus.active:
        return l10n.openFinanceActive;
      case ConsentStatus.rejected:
        return l10n.openFinanceRejected;
      case ConsentStatus.revoked:
        return l10n.openFinanceRevoked;
      case ConsentStatus.expired:
        return l10n.openFinanceExpired;
      case ConsentStatus.consumed:
        return l10n.openFinanceConsumed;
      case ConsentStatus.suspended:
        return l10n.openFinanceSuspended;
      case ConsentStatus.cancelled:
        return l10n.openFinanceCancelled;
      case ConsentStatus.authWindowElapsed:
        return l10n.authWindowElapsed;
    }
  }
}

class _ChipEntry {
  final String label;
  final VoidCallback onRemove;

  _ChipEntry({required this.label, required this.onRemove});
}

class _Tabs extends StatelessWidget {
  final int currentIndex;

  const _Tabs(this.currentIndex);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ConsentManagementCubit>();
    final l10n = OpenFinanceLocalizations.of(context);

    return Tabs(
      padding: const EdgeInsetsDirectional.fromSTEB(24, 0, 24, 16),
      TabsModel(
        tabNames: [l10n.openFinanceCurrent, l10n.openFinanceHistory],
        variant: TabsVariant.smallConnected,
        selectedIndex: currentIndex,
        activeBackgroundColor: CompanyColorPointer.primary1,
        activeTextColor: CompanyColorPointer.primary4,
      ),
      onTabPressed: (index) => cubit.changeTab(
        ConsentManagementTab.values[index],
      ),
    );
  }
}

class _ConsentList extends StatelessWidget {
  final List<ConsentItemDetails> consents;

  const _ConsentList(this.consents);

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 24),
      itemCount: consents.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (_, __) => Space.fromSpacingVertical(Spacing.s3),
      itemBuilder: (_, i) => ConsentCard(
        item: consents[i],
        onTap: () =>
            context.read<ConsentManagementCubit>().onItemTap(consents[i]),
      ),
    );
  }
}

class _ShimmerList extends StatelessWidget {
  const _ShimmerList();

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 24),
      itemCount: 5,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (_, __) => Space.fromSpacingVertical(Spacing.s3),
      itemBuilder: (_, i) => CompanyShimmerContainer(
        child: ConsentCard(
          item: ConsentItemDetails(
            consentId: 'consentId',
            type: OpenFinanceConsentType.dataSharing,
            status: ConsentStatus.active,
            createdAt: DateTime.now(),
            expirationDate: DateTime.now(),
          ),
        ),
      ),
    );
  }
}

class _EmptyListPlaceholder extends StatelessWidget {
  const _EmptyListPlaceholder();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ConsentManagementCubit>();
    final l10n = OpenFinanceLocalizations.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 80),
            Label(
              model: LabelModel(
                text: l10n.openFinanceNothingHere,
                textStyle: CompanyTextStylePointer.h3,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 12),
            Label(
              model: LabelModel(
                text: l10n.noTransactionsCriterias,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 24),
            Button(
              model: ButtonModel(title: l10n.openFinanceClearAllFilters),
              onPressed: cubit.clearAllFilters,
            ),
          ],
        ),
      ),
    );
  }
}

class _ErrorBox extends StatelessWidget {
  const _ErrorBox();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ConsentManagementCubit>();
    final commonLocalisation = DependencyProvider.get<CommonLocalizations>();

    return Padding(
      padding: const EdgeInsets.only(top: 80.0),
      child: ErrorBox(
        model: ErrorBoxModel(
          errorText: commonLocalisation.somethingWentWrongMessage,
          retryButtonText:
              commonLocalisation.genericErrorComponentTryAgainButton,
        ),
        onRetryPressed: () => cubit.updateConsents(),
      ),
    );
  }
}

class _PaginationLoader extends StatelessWidget {
  final bool isLoadingMode;

  const _PaginationLoader({required this.isLoadingMode});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const SizedBox(height: 90),
        if (isLoadingMode)
          Padding(
            padding: const EdgeInsets.only(top: 25),
            child: Spinner(
              model: SpinnerModel(type: SpinnerType.secondary),
            ),
          ),
      ],
    );
  }
}
