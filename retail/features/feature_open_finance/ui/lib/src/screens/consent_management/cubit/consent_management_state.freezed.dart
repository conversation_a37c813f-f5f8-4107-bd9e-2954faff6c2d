// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'consent_management_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConsentManagementState {
  OpenFinanceConsentType get type => throw _privateConstructorUsedError;
  ConsentManagementTab get tab => throw _privateConstructorUsedError;
  ConsentFilters get filters => throw _privateConstructorUsedError;
  LoadState get loadState => throw _privateConstructorUsedError;
  Set<String> get accounts => throw _privateConstructorUsedError;
  bool get isLoadingMore => throw _privateConstructorUsedError;
  bool get isTypeSheetOpen => throw _privateConstructorUsedError;
  bool get hasNext => throw _privateConstructorUsedError;
  int get pageNumber => throw _privateConstructorUsedError;
  int get pageSize => throw _privateConstructorUsedError;
  String? get search => throw _privateConstructorUsedError;

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ConsentManagementStateCopyWith<ConsentManagementState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConsentManagementStateCopyWith<$Res> {
  factory $ConsentManagementStateCopyWith(ConsentManagementState value,
          $Res Function(ConsentManagementState) then) =
      _$ConsentManagementStateCopyWithImpl<$Res, ConsentManagementState>;
  @useResult
  $Res call(
      {OpenFinanceConsentType type,
      ConsentManagementTab tab,
      ConsentFilters filters,
      LoadState loadState,
      Set<String> accounts,
      bool isLoadingMore,
      bool isTypeSheetOpen,
      bool hasNext,
      int pageNumber,
      int pageSize,
      String? search});

  $ConsentFiltersCopyWith<$Res> get filters;
  $LoadStateCopyWith<$Res> get loadState;
}

/// @nodoc
class _$ConsentManagementStateCopyWithImpl<$Res,
        $Val extends ConsentManagementState>
    implements $ConsentManagementStateCopyWith<$Res> {
  _$ConsentManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? tab = null,
    Object? filters = null,
    Object? loadState = null,
    Object? accounts = null,
    Object? isLoadingMore = null,
    Object? isTypeSheetOpen = null,
    Object? hasNext = null,
    Object? pageNumber = null,
    Object? pageSize = null,
    Object? search = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OpenFinanceConsentType,
      tab: null == tab
          ? _value.tab
          : tab // ignore: cast_nullable_to_non_nullable
              as ConsentManagementTab,
      filters: null == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as ConsentFilters,
      loadState: null == loadState
          ? _value.loadState
          : loadState // ignore: cast_nullable_to_non_nullable
              as LoadState,
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isTypeSheetOpen: null == isTypeSheetOpen
          ? _value.isTypeSheetOpen
          : isTypeSheetOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      hasNext: null == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool,
      pageNumber: null == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ConsentFiltersCopyWith<$Res> get filters {
    return $ConsentFiltersCopyWith<$Res>(_value.filters, (value) {
      return _then(_value.copyWith(filters: value) as $Val);
    });
  }

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoadStateCopyWith<$Res> get loadState {
    return $LoadStateCopyWith<$Res>(_value.loadState, (value) {
      return _then(_value.copyWith(loadState: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ConsentManagementStateImplCopyWith<$Res>
    implements $ConsentManagementStateCopyWith<$Res> {
  factory _$$ConsentManagementStateImplCopyWith(
          _$ConsentManagementStateImpl value,
          $Res Function(_$ConsentManagementStateImpl) then) =
      __$$ConsentManagementStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OpenFinanceConsentType type,
      ConsentManagementTab tab,
      ConsentFilters filters,
      LoadState loadState,
      Set<String> accounts,
      bool isLoadingMore,
      bool isTypeSheetOpen,
      bool hasNext,
      int pageNumber,
      int pageSize,
      String? search});

  @override
  $ConsentFiltersCopyWith<$Res> get filters;
  @override
  $LoadStateCopyWith<$Res> get loadState;
}

/// @nodoc
class __$$ConsentManagementStateImplCopyWithImpl<$Res>
    extends _$ConsentManagementStateCopyWithImpl<$Res,
        _$ConsentManagementStateImpl>
    implements _$$ConsentManagementStateImplCopyWith<$Res> {
  __$$ConsentManagementStateImplCopyWithImpl(
      _$ConsentManagementStateImpl _value,
      $Res Function(_$ConsentManagementStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? tab = null,
    Object? filters = null,
    Object? loadState = null,
    Object? accounts = null,
    Object? isLoadingMore = null,
    Object? isTypeSheetOpen = null,
    Object? hasNext = null,
    Object? pageNumber = null,
    Object? pageSize = null,
    Object? search = freezed,
  }) {
    return _then(_$ConsentManagementStateImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as OpenFinanceConsentType,
      tab: null == tab
          ? _value.tab
          : tab // ignore: cast_nullable_to_non_nullable
              as ConsentManagementTab,
      filters: null == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as ConsentFilters,
      loadState: null == loadState
          ? _value.loadState
          : loadState // ignore: cast_nullable_to_non_nullable
              as LoadState,
      accounts: null == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      isLoadingMore: null == isLoadingMore
          ? _value.isLoadingMore
          : isLoadingMore // ignore: cast_nullable_to_non_nullable
              as bool,
      isTypeSheetOpen: null == isTypeSheetOpen
          ? _value.isTypeSheetOpen
          : isTypeSheetOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      hasNext: null == hasNext
          ? _value.hasNext
          : hasNext // ignore: cast_nullable_to_non_nullable
              as bool,
      pageNumber: null == pageNumber
          ? _value.pageNumber
          : pageNumber // ignore: cast_nullable_to_non_nullable
              as int,
      pageSize: null == pageSize
          ? _value.pageSize
          : pageSize // ignore: cast_nullable_to_non_nullable
              as int,
      search: freezed == search
          ? _value.search
          : search // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ConsentManagementStateImpl implements _ConsentManagementState {
  const _$ConsentManagementStateImpl(
      {required this.type,
      required this.tab,
      required this.filters,
      this.loadState = const LoadState.idle(),
      final Set<String> accounts = const <String>{},
      this.isLoadingMore = false,
      this.isTypeSheetOpen = false,
      this.hasNext = false,
      this.pageNumber = 1,
      this.pageSize = 5,
      this.search})
      : _accounts = accounts;

  @override
  final OpenFinanceConsentType type;
  @override
  final ConsentManagementTab tab;
  @override
  final ConsentFilters filters;
  @override
  @JsonKey()
  final LoadState loadState;
  final Set<String> _accounts;
  @override
  @JsonKey()
  Set<String> get accounts {
    if (_accounts is EqualUnmodifiableSetView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_accounts);
  }

  @override
  @JsonKey()
  final bool isLoadingMore;
  @override
  @JsonKey()
  final bool isTypeSheetOpen;
  @override
  @JsonKey()
  final bool hasNext;
  @override
  @JsonKey()
  final int pageNumber;
  @override
  @JsonKey()
  final int pageSize;
  @override
  final String? search;

  @override
  String toString() {
    return 'ConsentManagementState(type: $type, tab: $tab, filters: $filters, loadState: $loadState, accounts: $accounts, isLoadingMore: $isLoadingMore, isTypeSheetOpen: $isTypeSheetOpen, hasNext: $hasNext, pageNumber: $pageNumber, pageSize: $pageSize, search: $search)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentManagementStateImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.tab, tab) || other.tab == tab) &&
            (identical(other.filters, filters) || other.filters == filters) &&
            (identical(other.loadState, loadState) ||
                other.loadState == loadState) &&
            const DeepCollectionEquality().equals(other._accounts, _accounts) &&
            (identical(other.isLoadingMore, isLoadingMore) ||
                other.isLoadingMore == isLoadingMore) &&
            (identical(other.isTypeSheetOpen, isTypeSheetOpen) ||
                other.isTypeSheetOpen == isTypeSheetOpen) &&
            (identical(other.hasNext, hasNext) || other.hasNext == hasNext) &&
            (identical(other.pageNumber, pageNumber) ||
                other.pageNumber == pageNumber) &&
            (identical(other.pageSize, pageSize) ||
                other.pageSize == pageSize) &&
            (identical(other.search, search) || other.search == search));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      tab,
      filters,
      loadState,
      const DeepCollectionEquality().hash(_accounts),
      isLoadingMore,
      isTypeSheetOpen,
      hasNext,
      pageNumber,
      pageSize,
      search);

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConsentManagementStateImplCopyWith<_$ConsentManagementStateImpl>
      get copyWith => __$$ConsentManagementStateImplCopyWithImpl<
          _$ConsentManagementStateImpl>(this, _$identity);
}

abstract class _ConsentManagementState implements ConsentManagementState {
  const factory _ConsentManagementState(
      {required final OpenFinanceConsentType type,
      required final ConsentManagementTab tab,
      required final ConsentFilters filters,
      final LoadState loadState,
      final Set<String> accounts,
      final bool isLoadingMore,
      final bool isTypeSheetOpen,
      final bool hasNext,
      final int pageNumber,
      final int pageSize,
      final String? search}) = _$ConsentManagementStateImpl;

  @override
  OpenFinanceConsentType get type;
  @override
  ConsentManagementTab get tab;
  @override
  ConsentFilters get filters;
  @override
  LoadState get loadState;
  @override
  Set<String> get accounts;
  @override
  bool get isLoadingMore;
  @override
  bool get isTypeSheetOpen;
  @override
  bool get hasNext;
  @override
  int get pageNumber;
  @override
  int get pageSize;
  @override
  String? get search;

  /// Create a copy of ConsentManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConsentManagementStateImplCopyWith<_$ConsentManagementStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LoadState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(List<ConsentItemDetails> consents) loaded,
    required TResult Function() error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(List<ConsentItemDetails> consents)? loaded,
    TResult? Function()? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(List<ConsentItemDetails> consents)? loaded,
    TResult Function()? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConsentManagemenIdle value) idle,
    required TResult Function(_ConsentManagemenLoading value) loading,
    required TResult Function(_ConsentManagemenLoaded value) loaded,
    required TResult Function(_ConsentManagemenError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConsentManagemenIdle value)? idle,
    TResult? Function(_ConsentManagemenLoading value)? loading,
    TResult? Function(_ConsentManagemenLoaded value)? loaded,
    TResult? Function(_ConsentManagemenError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConsentManagemenIdle value)? idle,
    TResult Function(_ConsentManagemenLoading value)? loading,
    TResult Function(_ConsentManagemenLoaded value)? loaded,
    TResult Function(_ConsentManagemenError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoadStateCopyWith<$Res> {
  factory $LoadStateCopyWith(LoadState value, $Res Function(LoadState) then) =
      _$LoadStateCopyWithImpl<$Res, LoadState>;
}

/// @nodoc
class _$LoadStateCopyWithImpl<$Res, $Val extends LoadState>
    implements $LoadStateCopyWith<$Res> {
  _$LoadStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ConsentManagemenIdleImplCopyWith<$Res> {
  factory _$$ConsentManagemenIdleImplCopyWith(_$ConsentManagemenIdleImpl value,
          $Res Function(_$ConsentManagemenIdleImpl) then) =
      __$$ConsentManagemenIdleImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConsentManagemenIdleImplCopyWithImpl<$Res>
    extends _$LoadStateCopyWithImpl<$Res, _$ConsentManagemenIdleImpl>
    implements _$$ConsentManagemenIdleImplCopyWith<$Res> {
  __$$ConsentManagemenIdleImplCopyWithImpl(_$ConsentManagemenIdleImpl _value,
      $Res Function(_$ConsentManagemenIdleImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConsentManagemenIdleImpl extends _ConsentManagemenIdle {
  const _$ConsentManagemenIdleImpl() : super._();

  @override
  String toString() {
    return 'LoadState.idle()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentManagemenIdleImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(List<ConsentItemDetails> consents) loaded,
    required TResult Function() error,
  }) {
    return idle();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(List<ConsentItemDetails> consents)? loaded,
    TResult? Function()? error,
  }) {
    return idle?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(List<ConsentItemDetails> consents)? loaded,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConsentManagemenIdle value) idle,
    required TResult Function(_ConsentManagemenLoading value) loading,
    required TResult Function(_ConsentManagemenLoaded value) loaded,
    required TResult Function(_ConsentManagemenError value) error,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConsentManagemenIdle value)? idle,
    TResult? Function(_ConsentManagemenLoading value)? loading,
    TResult? Function(_ConsentManagemenLoaded value)? loaded,
    TResult? Function(_ConsentManagemenError value)? error,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConsentManagemenIdle value)? idle,
    TResult Function(_ConsentManagemenLoading value)? loading,
    TResult Function(_ConsentManagemenLoaded value)? loaded,
    TResult Function(_ConsentManagemenError value)? error,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _ConsentManagemenIdle extends LoadState {
  const factory _ConsentManagemenIdle() = _$ConsentManagemenIdleImpl;
  const _ConsentManagemenIdle._() : super._();
}

/// @nodoc
abstract class _$$ConsentManagemenLoadingImplCopyWith<$Res> {
  factory _$$ConsentManagemenLoadingImplCopyWith(
          _$ConsentManagemenLoadingImpl value,
          $Res Function(_$ConsentManagemenLoadingImpl) then) =
      __$$ConsentManagemenLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConsentManagemenLoadingImplCopyWithImpl<$Res>
    extends _$LoadStateCopyWithImpl<$Res, _$ConsentManagemenLoadingImpl>
    implements _$$ConsentManagemenLoadingImplCopyWith<$Res> {
  __$$ConsentManagemenLoadingImplCopyWithImpl(
      _$ConsentManagemenLoadingImpl _value,
      $Res Function(_$ConsentManagemenLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConsentManagemenLoadingImpl extends _ConsentManagemenLoading {
  const _$ConsentManagemenLoadingImpl() : super._();

  @override
  String toString() {
    return 'LoadState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentManagemenLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(List<ConsentItemDetails> consents) loaded,
    required TResult Function() error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(List<ConsentItemDetails> consents)? loaded,
    TResult? Function()? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(List<ConsentItemDetails> consents)? loaded,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConsentManagemenIdle value) idle,
    required TResult Function(_ConsentManagemenLoading value) loading,
    required TResult Function(_ConsentManagemenLoaded value) loaded,
    required TResult Function(_ConsentManagemenError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConsentManagemenIdle value)? idle,
    TResult? Function(_ConsentManagemenLoading value)? loading,
    TResult? Function(_ConsentManagemenLoaded value)? loaded,
    TResult? Function(_ConsentManagemenError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConsentManagemenIdle value)? idle,
    TResult Function(_ConsentManagemenLoading value)? loading,
    TResult Function(_ConsentManagemenLoaded value)? loaded,
    TResult Function(_ConsentManagemenError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _ConsentManagemenLoading extends LoadState {
  const factory _ConsentManagemenLoading() = _$ConsentManagemenLoadingImpl;
  const _ConsentManagemenLoading._() : super._();
}

/// @nodoc
abstract class _$$ConsentManagemenLoadedImplCopyWith<$Res> {
  factory _$$ConsentManagemenLoadedImplCopyWith(
          _$ConsentManagemenLoadedImpl value,
          $Res Function(_$ConsentManagemenLoadedImpl) then) =
      __$$ConsentManagemenLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<ConsentItemDetails> consents});
}

/// @nodoc
class __$$ConsentManagemenLoadedImplCopyWithImpl<$Res>
    extends _$LoadStateCopyWithImpl<$Res, _$ConsentManagemenLoadedImpl>
    implements _$$ConsentManagemenLoadedImplCopyWith<$Res> {
  __$$ConsentManagemenLoadedImplCopyWithImpl(
      _$ConsentManagemenLoadedImpl _value,
      $Res Function(_$ConsentManagemenLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? consents = null,
  }) {
    return _then(_$ConsentManagemenLoadedImpl(
      null == consents
          ? _value._consents
          : consents // ignore: cast_nullable_to_non_nullable
              as List<ConsentItemDetails>,
    ));
  }
}

/// @nodoc

class _$ConsentManagemenLoadedImpl extends _ConsentManagemenLoaded {
  const _$ConsentManagemenLoadedImpl(final List<ConsentItemDetails> consents)
      : _consents = consents,
        super._();

  final List<ConsentItemDetails> _consents;
  @override
  List<ConsentItemDetails> get consents {
    if (_consents is EqualUnmodifiableListView) return _consents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_consents);
  }

  @override
  String toString() {
    return 'LoadState.loaded(consents: $consents)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentManagemenLoadedImpl &&
            const DeepCollectionEquality().equals(other._consents, _consents));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_consents));

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConsentManagemenLoadedImplCopyWith<_$ConsentManagemenLoadedImpl>
      get copyWith => __$$ConsentManagemenLoadedImplCopyWithImpl<
          _$ConsentManagemenLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(List<ConsentItemDetails> consents) loaded,
    required TResult Function() error,
  }) {
    return loaded(consents);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(List<ConsentItemDetails> consents)? loaded,
    TResult? Function()? error,
  }) {
    return loaded?.call(consents);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(List<ConsentItemDetails> consents)? loaded,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(consents);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConsentManagemenIdle value) idle,
    required TResult Function(_ConsentManagemenLoading value) loading,
    required TResult Function(_ConsentManagemenLoaded value) loaded,
    required TResult Function(_ConsentManagemenError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConsentManagemenIdle value)? idle,
    TResult? Function(_ConsentManagemenLoading value)? loading,
    TResult? Function(_ConsentManagemenLoaded value)? loaded,
    TResult? Function(_ConsentManagemenError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConsentManagemenIdle value)? idle,
    TResult Function(_ConsentManagemenLoading value)? loading,
    TResult Function(_ConsentManagemenLoaded value)? loaded,
    TResult Function(_ConsentManagemenError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _ConsentManagemenLoaded extends LoadState {
  const factory _ConsentManagemenLoaded(
      final List<ConsentItemDetails> consents) = _$ConsentManagemenLoadedImpl;
  const _ConsentManagemenLoaded._() : super._();

  List<ConsentItemDetails> get consents;

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConsentManagemenLoadedImplCopyWith<_$ConsentManagemenLoadedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConsentManagemenErrorImplCopyWith<$Res> {
  factory _$$ConsentManagemenErrorImplCopyWith(
          _$ConsentManagemenErrorImpl value,
          $Res Function(_$ConsentManagemenErrorImpl) then) =
      __$$ConsentManagemenErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConsentManagemenErrorImplCopyWithImpl<$Res>
    extends _$LoadStateCopyWithImpl<$Res, _$ConsentManagemenErrorImpl>
    implements _$$ConsentManagemenErrorImplCopyWith<$Res> {
  __$$ConsentManagemenErrorImplCopyWithImpl(_$ConsentManagemenErrorImpl _value,
      $Res Function(_$ConsentManagemenErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoadState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConsentManagemenErrorImpl extends _ConsentManagemenError {
  const _$ConsentManagemenErrorImpl() : super._();

  @override
  String toString() {
    return 'LoadState.error()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentManagemenErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() idle,
    required TResult Function() loading,
    required TResult Function(List<ConsentItemDetails> consents) loaded,
    required TResult Function() error,
  }) {
    return error();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? idle,
    TResult? Function()? loading,
    TResult? Function(List<ConsentItemDetails> consents)? loaded,
    TResult? Function()? error,
  }) {
    return error?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? idle,
    TResult Function()? loading,
    TResult Function(List<ConsentItemDetails> consents)? loaded,
    TResult Function()? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConsentManagemenIdle value) idle,
    required TResult Function(_ConsentManagemenLoading value) loading,
    required TResult Function(_ConsentManagemenLoaded value) loaded,
    required TResult Function(_ConsentManagemenError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConsentManagemenIdle value)? idle,
    TResult? Function(_ConsentManagemenLoading value)? loading,
    TResult? Function(_ConsentManagemenLoaded value)? loaded,
    TResult? Function(_ConsentManagemenError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConsentManagemenIdle value)? idle,
    TResult Function(_ConsentManagemenLoading value)? loading,
    TResult Function(_ConsentManagemenLoaded value)? loaded,
    TResult Function(_ConsentManagemenError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _ConsentManagemenError extends LoadState {
  const factory _ConsentManagemenError() = _$ConsentManagemenErrorImpl;
  const _ConsentManagemenError._() : super._();
}
