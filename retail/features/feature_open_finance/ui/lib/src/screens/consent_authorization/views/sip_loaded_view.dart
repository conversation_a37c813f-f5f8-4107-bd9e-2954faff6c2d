part of '../open_finance_authorization_screen.dart';

class _SIPLoadedView extends StatelessWidget {
  final SingleInstantPaymentLoaded state;

  const _SIPLoadedView({
    required this.state,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = OpenFinanceLocalizations.of(context);
    final consent = state.authorization;
    final accounts = consent.accountSelection.eligibleAccounts;
    final duplicatePayment = consent.warnings?.duplicatePayment;

    final cubit = context.read<OpenFinanceAuthorizationCubit>();
    return Scaffold(
      key: OpenFinanceAuthorizationScreen.scaffoldKey,
      backgroundColor: CompanyColorPointer.surface7.colorOf(context),
      appBar: TopNavigation(
        key: OpenFinanceAuthorizationScreen.topNavigationKey,
        const TopNavigationModel(
          state: TopNavigationState.positive,
          backgroundColor: CompanyColorPointer.background1,
          centerAccessory: TopNavigationLogoCenterAccessoryModel(
            logo: CompanyImageModel(
              width: 130,
              height: 17,
              fit: BoxFit.fill,
              image: CompanyImageProvider.svg(
                name: Assets.wioPersonalAppBar,
                package: Assets.packageName,
              ),
            ),
          ),
        ),
        onLeftIconPressed: cubit.goBack,
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 30,
                  ),
                  const Center(
                    child: TareqLogo(
                      key: OpenFinanceAuthorizationScreen.tareqLogoKey,
                    ),
                  ),
                  const SizedBox(
                    height: 26,
                  ),
                  TareqTracker(
                    key: OpenFinanceAuthorizationScreen.tareqTrackerKey,
                    TareqTrackerModel(
                      steps: [
                        TareqTrackerStepModel(
                          title: l10n.open_finance_consent,
                          state: TareqTrackerStateEnum.passed,
                        ),
                        TareqTrackerStepModel(
                          title: l10n.open_finance_authorize,
                          state: TareqTrackerStateEnum.pending,
                        ),
                        TareqTrackerStepModel(
                          title: l10n.open_finance_complete,
                          state: TareqTrackerStateEnum.upComing,
                        ),
                      ],
                      progress: state.canAuthorize ? 1.0 : 0.3,
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  if (consent.warnings != null) ...[
                    AnnouncementBanner(
                      AnnouncementBannerModel(
                        subtitleMaxLines: 3,
                        icon: CompanyIconPointer.warning,
                        iconColor: CompanyColorPointer.secondary1,
                        titleTextStyle: CompanyTextStylePointer.b3medium,
                        titleColor: CompanyColorPointer.secondary1,
                        title: l10n.duplicatePaymentTitle,
                        backgroundColor: CompanyColorPointer.secondary14,
                        subTitleColor: CompanyColorPointer.secondary1,
                        subtitle: l10n.of_duplicate_payment(
                          state.authorization.payment.amount
                              .toFormattedString(),
                          duplicatePayment?.criteria.creditorName ?? '',
                          '${duplicatePayment?.lookBackHours}',
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                  ],
                  Label(
                    key: OpenFinanceAuthorizationScreen.titleTextKey,
                    model: LabelModel(
                      text: l10n.confirmPaymentDetails,
                      textStyle: CompanyTextStylePointer.h3medium,
                      color: CompanyColorPointer.secondary1,
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  CompanyRichText(
                    key: OpenFinanceAuthorizationScreen.subtitleTextKey,
                    CompanyRichTextModel(
                      textAlign: TextAlign.start,
                      text: l10n.needsPermissionsPayment(consent.displayName),
                      highlightedTextModels: [
                        HighlightedTextModel(
                          consent.displayName,
                        ),
                      ],
                      normalStyle: CompanyTextStylePointer.b3,
                      accentStyle: CompanyTextStylePointer.b3,
                      accentTextColor: CompanyColorPointer.secondary1,
                      normalTextColor: CompanyColorPointer.secondary4,
                    ),
                  ),
                  const SizedBox(
                    height: 24,
                  ),
                  ListDetailsGroup(
                    models: <ListDetailsContainerModel>[
                      ListDetailsContainerModel(
                        items: [
                          ListDetailsModel(
                            textLabelModel: ListDetailsTextLabelModel(
                              text: l10n.openFinancePaymentAmount,
                              textColor: CompanyColorPointer.secondary1,
                            ),
                            valueModel: ListDetailsValueModel.text(
                              textModel: ListDetailValueTextModel.label(
                                textStyle: CompanyTextStylePointer.b3,
                                content:
                                    consent.payment.amount.toFormattedString(),
                                textColor: CompanyColorPointer.secondary1,
                              ),
                            ),
                          ),
                          childRow(
                            name: l10n.openFinancePayeeName,
                            value: consent.payment.creditor.name,
                          ),
                          childRow(
                            name: l10n.openFinanceIBAN,
                            value:
                                consent.payment.creditor.iban.normalizeIban(),
                          ),
                          childRow(
                            name: l10n.openFinancePaymentReference,
                            value: consent.payment.reference,
                          ),
                          ListDetailsModel(
                            textLabelModel: ListDetailsTextLabelModel(
                              text: l10n.openFinancePaymentPurpose,
                              subtitle: consent.payment.purpose,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 32,
                  ),
                  Label(
                    model: LabelModel(
                      text:
                          consent.accountSelection.eligibleAccounts.length == 1
                              ? l10n.openFinancePaymentAccount
                              : l10n.selectTheAccount,
                      textStyle: CompanyTextStylePointer.h3medium,
                      color: CompanyColorPointer.secondary1,
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  AccountsListWidget(
                    key: OpenFinanceAuthorizationScreen.accountsListKey,
                    accounts: accounts,
                    selectedAccounts: state.selectedAccounts,
                    consentType: OpenFinanceConsentType.payments,
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  const SizedBox(height: 220),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.only(bottom: 24, left: 24, right: 24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    context.colorStyling.background1.withValues(alpha: 0),
                    context.colorStyling.background1,
                  ],
                ),
              ),
              child: StickyAlTareqButtons(
                isPayment: true,
                key: OpenFinanceAuthorizationScreen.stickyButtonsKey,
                onCancel: () => cubit.doFail(),
                onConfirm: () => cubit.doConfirm(),
                isAuthorizeButtonEnabled: state.canAuthorize,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

ListDetailsModel childRow({
  required String name,
  required String value,
}) {
  return ListDetailsModel(
    textLabelModel: ListDetailsTextLabelModel(
      text: name,
      textColor: CompanyColorPointer.secondary1,
    ),
    valueModel: ListDetailsValueModel.text(
      textModel: ListDetailValueTextModel.label(
        content: value,
        textColor: CompanyColorPointer.secondary1,
      ),
    ),
  );
}
