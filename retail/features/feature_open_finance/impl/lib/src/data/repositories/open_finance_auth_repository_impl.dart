import 'package:wio_feature_open_finance_api/feature_open_finance_api.dart';
import 'package:wio_feature_open_finance_impl/src/data/open_finance_data_mapper.dart';
import 'package:wio_feature_open_finance_impl/src/data/services/open_finance_auth_service.dart';

class OpenFinanceAuthRepositoryImpl extends OpenFinanceAuthRepository {
  final OpenFinanceAuthService _service;
  final OpenFinanceDataMapper _dataMapper;
  OpenFinanceAuthRepositoryImpl({
    required OpenFinanceAuthService service,
    required OpenFinanceDataMapper dataMapper,
  })  : _service = service,
        _dataMapper = dataMapper;

  @override
  Future<Authorization> getAuthorization({
    required String clientId,
    required String requestUri,
  }) async {
    final conversion = await _service.authenticate(
      clientId,
      requestUri,
    );
    return _dataMapper.mapAuthResponse(
      authResponseDto: conversion,
    );
  }

  @override
  Future<String?> doConfirm({
    required String interactionId,
    required LfiConsentUpdate confirmationRequest,
  }) async =>
      _service.doConfirm(
        interactionId,
        _dataMapper.mapLfiConsentUpdate(
          lfiConsentUpdate: confirmationRequest,
        ),
      );

  @override
  Future<String?> doFail({
    required String interactionId,
    required String consentId,
  }) async =>
      _service.doFail(interactionId, consentId);
}
