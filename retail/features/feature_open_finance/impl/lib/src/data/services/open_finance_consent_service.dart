// ignore_for_file: avoid_types_on_closure_parameters
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_open_finance_impl/src/data/dto/open_finance_consent_management_service.swagger.dart';
import 'package:wio_retail_dotenv/index.dart';

class _Constants {
  static const _service = '/openfinance/retail/consent-service';
  static const consentsPath = '/api/v1/consents';
  static const ocpApimSubscriptionKey = 'Ocp-Apim-Subscription-Key';
}

abstract class OpenFinanceConsentService {
  Future<ApiV1ConsentsGet$Response> getConsents({
    ConsentType? consentType,
    Set<ConsentStatus>? statuses,
    Set<String>? accounts,
    ApiV1ConsentsGetTab? tab,
    String? search,
    int? page,
    int? size,
    String? creationDateTimeFrom,
    String? creationDateTimeTo,
    String? expirationDateTimeFrom,
    String? expirationDateTimeTo,
  });

  Future<ConsentCountsResponse> getConsentCounts();

  Future<ApiV1ConsentsConsentIdGet$Response> getConsentById(String consentId);

  Future<ConsentAuditResponse> getConsentAudit(String consentId);

  Future<bool> revokeConsent(String consentId);
}

class OpenFinanceConsentServiceImpl extends RestApiService
    implements OpenFinanceConsentService {
  final IRestApiClient _httpClient;
  final EnvProvider _envProvider;

  OpenFinanceConsentServiceImpl({
    required IRestApiClient httpClient,
    required EnvProvider envProvider,
  })  : _httpClient = httpClient,
        _envProvider = envProvider;

  Map<String, String> get _requiredHeaders => {
        _Constants.ocpApimSubscriptionKey:
            _envProvider.get(RetailEnvKeys.secureApimKey),
      };

  String get _env => _envProvider.get(RetailEnvKeys.environmentPrefix);

  @override
  Future<ApiV1ConsentsGet$Response> getConsents({
    ConsentType? consentType,
    Set<ConsentStatus>? statuses,
    Set<String>? accounts,
    ApiV1ConsentsGetTab? tab,
    String? search,
    int? page,
    int? size,
    String? creationDateTimeFrom,
    String? creationDateTimeTo,
    String? expirationDateTimeFrom,
    String? expirationDateTimeTo,
  }) async {
    final query = <String, dynamic>{};

    if (consentType != null) query['consentType'] = consentType.value;
    if (accounts != null && accounts.isNotEmpty) {
      query['iban'] = accounts.join(',');
    }
    if (statuses != null && statuses.isNotEmpty) {
      query['state'] =
          statuses.map((status) => status.value).toList().join(',');
    }
    if (search != null) query['q'] = search;
    if (tab != null) query['tab'] = tab.value;
    if (page != null) query['pageNumber'] = page;
    if (size != null) query['pageSize'] = size;
    if (creationDateTimeFrom != null) {
      query['createdOnFrom'] = creationDateTimeFrom;
    }
    if (creationDateTimeTo != null) {
      query['createdOnTo'] = creationDateTimeTo;
    }
    if (expirationDateTimeFrom != null) {
      query['expiresOnFrom'] = expirationDateTimeFrom;
    }
    if (expirationDateTimeTo != null) {
      query['expiresOnTo'] = expirationDateTimeTo;
    }

    return execute(
      _httpClient.execute<dynamic>(
        RestApiRequest(
          _env + _Constants._service + _Constants.consentsPath,
          method: HttpRequestMethod.get,
          headers: _requiredHeaders,
          queryParameters: query,
        ),
      ),
      (Object? json) {
        return ApiV1ConsentsGet$Response.fromJson(json as Map<String, dynamic>);
      },
    );
  }

  @override
  Future<ApiV1ConsentsConsentIdGet$Response> getConsentById(
    String consentId,
  ) async {
    final url =
        '${_envProvider.get(RetailEnvKeys.environmentPrefix)}${_Constants._service}${_Constants.consentsPath}/$consentId';

    return execute(
      _httpClient.execute<dynamic>(
        RestApiRequest(
          url,
          method: HttpRequestMethod.get,
          headers: _requiredHeaders,
        ),
      ),
      (Object? json) {
        return ApiV1ConsentsConsentIdGet$Response.fromJson(
          json as Map<String, dynamic>,
        );
      },
    );
  }

  @override
  Future<ConsentAuditResponse> getConsentAudit(String consentId) async {
    final url =
        '${_envProvider.get(RetailEnvKeys.environmentPrefix)}${_Constants._service}${_Constants.consentsPath}/$consentId/audit';

    return execute(
      _httpClient.execute<dynamic>(
        RestApiRequest(
          url,
          method: HttpRequestMethod.get,
          headers: _requiredHeaders,
        ),
      ),
      (Object? json) {
        return ConsentAuditResponse.fromJson(json as Map<String, dynamic>);
      },
    );
  }

  @override
  Future<bool> revokeConsent(String consentId) async {
    final url =
        '${_envProvider.get(RetailEnvKeys.environmentPrefix)}${_Constants._service}${_Constants.consentsPath}/$consentId/action/revoke';
    try {
      await _httpClient.execute<dynamic>(
        RestApiRequest(
          url,
          method: HttpRequestMethod.post,
          headers: _requiredHeaders,
        ),
      );
      return true;
    } on Exception catch (_) {
      rethrow;
    }
  }

  @override
  Future<ConsentCountsResponse> getConsentCounts() async {
    final url =
        '${_envProvider.get(RetailEnvKeys.environmentPrefix)}${_Constants._service}${_Constants.consentsPath}/counts';

    return execute(
      _httpClient.execute<dynamic>(
        RestApiRequest(
          url,
          method: HttpRequestMethod.get,
          headers: _requiredHeaders,
        ),
      ),
      (Object? json) {
        return ConsentCountsResponse.fromJson(json as Map<String, dynamic>);
      },
    );
  }
}
