import 'package:di/di.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_open_finance_api/feature_open_finance_api.dart';
import 'package:wio_feature_open_finance_impl/src/analytics/open_finance_analytics_impl.dart';
import 'package:wio_feature_open_finance_impl/src/data/open_finance_data_mapper.dart';
import 'package:wio_feature_open_finance_impl/src/data/repositories/open_finance_account_repository_impl.dart';
import 'package:wio_feature_open_finance_impl/src/data/repositories/open_finance_auth_repository_impl.dart';
import 'package:wio_feature_open_finance_impl/src/data/repositories/open_finance_consent_repository_impl.dart';
import 'package:wio_feature_open_finance_impl/src/data/services/services.dart';
import 'package:wio_feature_open_finance_impl/src/domain/open_finance_authorization_interactor_impl.dart';

abstract final class OpenFinanceDomainDependencyModuleResolver {
  static void register() {
    _registerMapper();
    _registerServices();
    _registerRepositories();
    _registerInteractors();
    _analytics();
  }

  static void _registerMapper() {
    DependencyProvider.registerLazySingleton<OpenFinanceDataMapper>(
      () => OpenFinanceDataMapperImpl(
        errorReporter: DependencyProvider.get<ErrorReporter>(),
      ),
    );
  }

  static void _registerServices() {
    DependencyProvider.registerLazySingleton<OpenFinanceAccountService>(
      () => OpenFinanceAccountServiceImpl(
        httpClient: DependencyProvider.get<IRestApiClient>(),
        envProvider: DependencyProvider.get<EnvProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<OpenFinanceAuthService>(
      () => OpenFinanceAuthServiceImpl(
        httpClient: DependencyProvider.get<IRestApiClient>(),
        envProvider: DependencyProvider.get<EnvProvider>(),
      ),
    );

    DependencyProvider.registerLazySingleton<OpenFinanceConsentService>(
      () => OpenFinanceConsentServiceImpl(
        httpClient: DependencyProvider.get<IRestApiClient>(),
        envProvider: DependencyProvider.get<EnvProvider>(),
      ),
    );
  }

  static void _registerRepositories() {
    DependencyProvider.registerLazySingleton<OpenFinanceConsentRepository>(
      () => OpenFinanceConsentRepositoryImpl(
        service: DependencyProvider.get<OpenFinanceConsentService>(),
        dataMapper: DependencyProvider.get<OpenFinanceDataMapper>(),
      ),
    );

    DependencyProvider.registerLazySingleton<OpenFinanceAccountRepository>(
      () => OpenFinanceAccountRepositoryImpl(
        service: DependencyProvider.get<OpenFinanceAccountService>(),
        dataMapper: DependencyProvider.get<OpenFinanceDataMapper>(),
      ),
    );

    DependencyProvider.registerLazySingleton<OpenFinanceAuthRepository>(
      () => OpenFinanceAuthRepositoryImpl(
        service: DependencyProvider.get<OpenFinanceAuthService>(),
        dataMapper: DependencyProvider.get<OpenFinanceDataMapper>(),
      ),
    );
  }

  static void _registerInteractors() {
    DependencyProvider.registerLazySingleton<
        OpenFinanceAuthorizationInteractor>(
      () => OpenFinanceAuthorizationInteractorImpl(
        openFinanceConsentRepository:
            DependencyProvider.get<OpenFinanceConsentRepository>(),
        openFinanceAuthRepository:
            DependencyProvider.get<OpenFinanceAuthRepository>(),
        openFinanceAccountRepository:
            DependencyProvider.get<OpenFinanceAccountRepository>(),
      ),
    );
  }

  static void _analytics() {
    final analyticsAbstractTrackerFactory =
        DependencyProvider.get<AnalyticsAbstractTrackerFactory>();

    DependencyProvider.registerFactory<OpenFinanceAnalytics>(
      () => OpenFinanceAnalyticsImpl(
        mixpanelTracker: analyticsAbstractTrackerFactory.get(
          screenName: OpenFinanceFeatureNavigationConfig.name,
          tracker: AnalyticsTracker.mixpanel,
        ),
      ),
    );
  }
}
