// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'otp_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$OtpState {
  OtpInputModel get model => throw _privateConstructorUsedError;
  bool get suspendedModalIsOpen => throw _privateConstructorUsedError;
  bool get resendAvailable => throw _privateConstructorUsedError;
  int get resendCountdownTime => throw _privateConstructorUsedError;
  DateTime? get resendCountdownStartTime => throw _privateConstructorUsedError;
  TwoFactorAuthType get type => throw _privateConstructorUsedError;
  String get subtitle => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get transactionId => throw _privateConstructorUsedError;
  String get challengeId => throw _privateConstructorUsedError;
  TwoFaType? get twoFaType => throw _privateConstructorUsedError;
  String? get nextChallengeName => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $OtpStateCopyWith<OtpState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtpStateCopyWith<$Res> {
  factory $OtpStateCopyWith(OtpState value, $Res Function(OtpState) then) =
      _$OtpStateCopyWithImpl<$Res, OtpState>;
  @useResult
  $Res call(
      {OtpInputModel model,
      bool suspendedModalIsOpen,
      bool resendAvailable,
      int resendCountdownTime,
      DateTime? resendCountdownStartTime,
      TwoFactorAuthType type,
      String subtitle,
      String title,
      String transactionId,
      String challengeId,
      TwoFaType? twoFaType,
      String? nextChallengeName});

  $OtpInputModelCopyWith<$Res> get model;
}

/// @nodoc
class _$OtpStateCopyWithImpl<$Res, $Val extends OtpState>
    implements $OtpStateCopyWith<$Res> {
  _$OtpStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? suspendedModalIsOpen = null,
    Object? resendAvailable = null,
    Object? resendCountdownTime = null,
    Object? resendCountdownStartTime = freezed,
    Object? type = null,
    Object? subtitle = null,
    Object? title = null,
    Object? transactionId = null,
    Object? challengeId = null,
    Object? twoFaType = freezed,
    Object? nextChallengeName = freezed,
  }) {
    return _then(_value.copyWith(
      model: null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as OtpInputModel,
      suspendedModalIsOpen: null == suspendedModalIsOpen
          ? _value.suspendedModalIsOpen
          : suspendedModalIsOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      resendAvailable: null == resendAvailable
          ? _value.resendAvailable
          : resendAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      resendCountdownTime: null == resendCountdownTime
          ? _value.resendCountdownTime
          : resendCountdownTime // ignore: cast_nullable_to_non_nullable
              as int,
      resendCountdownStartTime: freezed == resendCountdownStartTime
          ? _value.resendCountdownStartTime
          : resendCountdownStartTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TwoFactorAuthType,
      subtitle: null == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      challengeId: null == challengeId
          ? _value.challengeId
          : challengeId // ignore: cast_nullable_to_non_nullable
              as String,
      twoFaType: freezed == twoFaType
          ? _value.twoFaType
          : twoFaType // ignore: cast_nullable_to_non_nullable
              as TwoFaType?,
      nextChallengeName: freezed == nextChallengeName
          ? _value.nextChallengeName
          : nextChallengeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $OtpInputModelCopyWith<$Res> get model {
    return $OtpInputModelCopyWith<$Res>(_value.model, (value) {
      return _then(_value.copyWith(model: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$OtpStateImplCopyWith<$Res>
    implements $OtpStateCopyWith<$Res> {
  factory _$$OtpStateImplCopyWith(
          _$OtpStateImpl value, $Res Function(_$OtpStateImpl) then) =
      __$$OtpStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {OtpInputModel model,
      bool suspendedModalIsOpen,
      bool resendAvailable,
      int resendCountdownTime,
      DateTime? resendCountdownStartTime,
      TwoFactorAuthType type,
      String subtitle,
      String title,
      String transactionId,
      String challengeId,
      TwoFaType? twoFaType,
      String? nextChallengeName});

  @override
  $OtpInputModelCopyWith<$Res> get model;
}

/// @nodoc
class __$$OtpStateImplCopyWithImpl<$Res>
    extends _$OtpStateCopyWithImpl<$Res, _$OtpStateImpl>
    implements _$$OtpStateImplCopyWith<$Res> {
  __$$OtpStateImplCopyWithImpl(
      _$OtpStateImpl _value, $Res Function(_$OtpStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? model = null,
    Object? suspendedModalIsOpen = null,
    Object? resendAvailable = null,
    Object? resendCountdownTime = null,
    Object? resendCountdownStartTime = freezed,
    Object? type = null,
    Object? subtitle = null,
    Object? title = null,
    Object? transactionId = null,
    Object? challengeId = null,
    Object? twoFaType = freezed,
    Object? nextChallengeName = freezed,
  }) {
    return _then(_$OtpStateImpl(
      null == model
          ? _value.model
          : model // ignore: cast_nullable_to_non_nullable
              as OtpInputModel,
      suspendedModalIsOpen: null == suspendedModalIsOpen
          ? _value.suspendedModalIsOpen
          : suspendedModalIsOpen // ignore: cast_nullable_to_non_nullable
              as bool,
      resendAvailable: null == resendAvailable
          ? _value.resendAvailable
          : resendAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      resendCountdownTime: null == resendCountdownTime
          ? _value.resendCountdownTime
          : resendCountdownTime // ignore: cast_nullable_to_non_nullable
              as int,
      resendCountdownStartTime: freezed == resendCountdownStartTime
          ? _value.resendCountdownStartTime
          : resendCountdownStartTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TwoFactorAuthType,
      subtitle: null == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      transactionId: null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
      challengeId: null == challengeId
          ? _value.challengeId
          : challengeId // ignore: cast_nullable_to_non_nullable
              as String,
      twoFaType: freezed == twoFaType
          ? _value.twoFaType
          : twoFaType // ignore: cast_nullable_to_non_nullable
              as TwoFaType?,
      nextChallengeName: freezed == nextChallengeName
          ? _value.nextChallengeName
          : nextChallengeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$OtpStateImpl implements _OtpState {
  const _$OtpStateImpl(this.model,
      {this.suspendedModalIsOpen = false,
      this.resendAvailable = false,
      this.resendCountdownTime = 0,
      this.resendCountdownStartTime,
      this.type = TwoFactorAuthType.unknown,
      this.subtitle = '',
      this.title = '',
      this.transactionId = '',
      this.challengeId = '',
      this.twoFaType,
      this.nextChallengeName});

  @override
  final OtpInputModel model;
  @override
  @JsonKey()
  final bool suspendedModalIsOpen;
  @override
  @JsonKey()
  final bool resendAvailable;
  @override
  @JsonKey()
  final int resendCountdownTime;
  @override
  final DateTime? resendCountdownStartTime;
  @override
  @JsonKey()
  final TwoFactorAuthType type;
  @override
  @JsonKey()
  final String subtitle;
  @override
  @JsonKey()
  final String title;
  @override
  @JsonKey()
  final String transactionId;
  @override
  @JsonKey()
  final String challengeId;
  @override
  final TwoFaType? twoFaType;
  @override
  final String? nextChallengeName;

  @override
  String toString() {
    return 'OtpState(model: $model, suspendedModalIsOpen: $suspendedModalIsOpen, resendAvailable: $resendAvailable, resendCountdownTime: $resendCountdownTime, resendCountdownStartTime: $resendCountdownStartTime, type: $type, subtitle: $subtitle, title: $title, transactionId: $transactionId, challengeId: $challengeId, twoFaType: $twoFaType, nextChallengeName: $nextChallengeName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtpStateImpl &&
            (identical(other.model, model) || other.model == model) &&
            (identical(other.suspendedModalIsOpen, suspendedModalIsOpen) ||
                other.suspendedModalIsOpen == suspendedModalIsOpen) &&
            (identical(other.resendAvailable, resendAvailable) ||
                other.resendAvailable == resendAvailable) &&
            (identical(other.resendCountdownTime, resendCountdownTime) ||
                other.resendCountdownTime == resendCountdownTime) &&
            (identical(
                    other.resendCountdownStartTime, resendCountdownStartTime) ||
                other.resendCountdownStartTime == resendCountdownStartTime) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.subtitle, subtitle) ||
                other.subtitle == subtitle) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId) &&
            (identical(other.challengeId, challengeId) ||
                other.challengeId == challengeId) &&
            (identical(other.twoFaType, twoFaType) ||
                other.twoFaType == twoFaType) &&
            (identical(other.nextChallengeName, nextChallengeName) ||
                other.nextChallengeName == nextChallengeName));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      model,
      suspendedModalIsOpen,
      resendAvailable,
      resendCountdownTime,
      resendCountdownStartTime,
      type,
      subtitle,
      title,
      transactionId,
      challengeId,
      twoFaType,
      nextChallengeName);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OtpStateImplCopyWith<_$OtpStateImpl> get copyWith =>
      __$$OtpStateImplCopyWithImpl<_$OtpStateImpl>(this, _$identity);
}

abstract class _OtpState implements OtpState {
  const factory _OtpState(final OtpInputModel model,
      {final bool suspendedModalIsOpen,
      final bool resendAvailable,
      final int resendCountdownTime,
      final DateTime? resendCountdownStartTime,
      final TwoFactorAuthType type,
      final String subtitle,
      final String title,
      final String transactionId,
      final String challengeId,
      final TwoFaType? twoFaType,
      final String? nextChallengeName}) = _$OtpStateImpl;

  @override
  OtpInputModel get model;
  @override
  bool get suspendedModalIsOpen;
  @override
  bool get resendAvailable;
  @override
  int get resendCountdownTime;
  @override
  DateTime? get resendCountdownStartTime;
  @override
  TwoFactorAuthType get type;
  @override
  String get subtitle;
  @override
  String get title;
  @override
  String get transactionId;
  @override
  String get challengeId;
  @override
  TwoFaType? get twoFaType;
  @override
  String? get nextChallengeName;
  @override
  @JsonKey(ignore: true)
  _$$OtpStateImplCopyWith<_$OtpStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
