import 'dart:async';

import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_otp_api/index.dart';
import 'package:wio_feature_otp_ui/l10n/otp_localization.g.dart';
import 'package:wio_feature_otp_ui/src/screens/otp_cubit.dart';
import 'package:wio_feature_otp_ui/src/screens/otp_state.dart';

class OtpPage extends BasePage<OtpState, OtpCubit> {
  final OtpNavigationRequest config;
  final OtpNavigationHandler otpNavigationHandler;

  const OtpPage({
    required this.config,
    required this.otpNavigationHandler,
    super.key,
  });

  @override
  OtpCubit createBloc() =>
      DependencyProvider.getWithParams<OtpCubit, OtpNavigationHand<PERSON>, void>(
        param1: otpNavigationHandler,
      );

  @override
  void initBloc(OtpCubit bloc) {
    bloc.initialize(
      twoFaParams: config.twoFaParams,
      challengeId: config.challengeId,
      type: config.type,
      status: config.status,
      nextChallengeName: config.nextChallengeName,
      twoFaType: config.twoFaType,
      transactionExpiredTime: config.transactionExpiredTime,
    );
    super.initBloc(bloc);
  }

  @override
  Future<bool> onWillPop(BuildContext context) {
    context.read<OtpCubit>().onBackPressed();

    return Future<bool>.value(false);
  }

  @override
  // ignore: long-method
  Widget buildPage(
    BuildContext context,
    OtpCubit bloc,
    OtpState state,
  ) {
    final localization = OtpLocalizations.of(context);
    final focusScope = FocusScope.of(context);

    final primaryButtonModel = state.suspendedModalIsOpen
        ? null
        : FixedButtonsScrollablePageLayoutButton(
            label: state.resendAvailable
                ? localization.otpInputScreenActiveButton
                : localization.otpInputScreenDisabledButton(
                    bloc.formattedResendTimer,
                  ),
          );

    final onPrimaryButtonPressed = state.resendAvailable
        ? () {
            bloc.resendCode(twoFaParams: config.twoFaParams);
          }
        : null;

    return Scaffold(
      appBar: TopNavigation(
        const TopNavigationModel(state: TopNavigationState.positive),
        onLeftIconPressed: () => bloc.onBackPressed(),
      ),
      body: FixedButtonsScrollablePageLayout(
        model: FixedButtonsScrollablePageLayoutModel(
          primaryButton: primaryButtonModel,
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: FixedButtonsScrollablePageLayout.pageContentPadding,
        ),
        onPrimaryButtonPressed: onPrimaryButtonPressed,
        onBackgroundPressed: focusScope.unfocus,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: FixedButtonsScrollablePageLayout.pageContentPadding,
            ),
            child: BlocBuilder<OtpCubit, OtpState>(
              builder: (context, state) {
                return PageText(
                  PageTextModel(
                    title: state.title,
                    subtitle: state.subtitle,
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 48.0),
          _OtpInputStateful(
            model: state.model,
            onChange: (value, isFilled) =>
                _sendOtpToBlockIfFilled(bloc, value, isFilled),
            onFocusChanged: (focus) =>
                bloc.onOtpFieldFocusChanged(hasFocus: focus),
          ),
        ],
      ),
    );
  }

  void _sendOtpToBlockIfFilled(
    OtpCubit bloc,
    String value,
    bool isFilled,
  ) {
    if (isFilled) {
      bloc.onFilled(value);
    }
  }
}

class _OtpInputStateful extends StatefulWidget {
  final OtpInputModel model;

  // ignore: avoid_positional_boolean_parameters
  final void Function(String value, bool isFilled) onChange;

  // ignore: avoid_positional_boolean_parameters
  final void Function(bool focused) onFocusChanged;

  const _OtpInputStateful({
    required this.model,
    required this.onChange,
    required this.onFocusChanged,
  });

  @override
  _OtpInputStatefulState createState() => _OtpInputStatefulState();
}

class _OtpInputStatefulState extends State<_OtpInputStateful> {
  final _focusNode = FocusNode();
  final _otpController = OtpController();

  @override
  Future<void> dispose() async {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return OtpInput(
      focusNode: _focusNode,
      model: widget.model,
      onChange: widget.onChange,
      controller: _otpController,
      horizontalPadding: FixedButtonsScrollablePageLayout.pageContentPadding,
      shouldChangeFocusOnAppState: true,
    );
  }
}
