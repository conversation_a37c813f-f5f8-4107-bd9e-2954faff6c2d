import 'package:test/test.dart';
import 'package:wio_feature_kyc_api/data/rfi_exception_mapper.dart';
import 'package:wio_feature_kyc_api/domain/exception/submit_rfi_exception.dart';
import 'package:wio_feature_kyc_impl/src/data/rfi_exception_mapper.dart';
import 'package:wio_feature_onboarding_data_api/account_maintenance_secure_data_api.dart';

void main() {
  group('RfiExceptionMapperImpl', () {
    late RfiExceptionMapper mapper;

    setUp(() {
      mapper = RfiExceptionMapperImpl();
    });

    test('should map notFound error code to ErrorCode.notFound', () {
      // Arrange
      const errorMessage = ErrorMessage(
        code: ErrorMessageCode.beRetailAccountMaintenanceRfiNotFound,
        message: 'RFI not found',
        id: '',
      );

      // Act
      final result = mapper.mapToSubmitRfiException(errorMessage);

      // Assert
      expect(result, isA<SubmitRfiException>());
      expect(result.code, ErrorCode.notFound);
    });

    test('should map alreadySubmitted error code to ErrorCode.alreadySubmitted',
        () {
      // Arrange
      const errorMessage = ErrorMessage(
        code: ErrorMessageCode.beRetailAccountMaintenanceRfiAlreadySubmitted,
        message: 'RFI already submitted',
        id: '',
      );

      // Act
      final result = mapper.mapToSubmitRfiException(errorMessage);

      // Assert
      expect(result, isA<SubmitRfiException>());
      expect(result.code, ErrorCode.alreadySubmitted);
    });

    test('should map unansweredQuestions error code to unansweredQuestions.',
        () {
      // Arrange
      const errorMessage = ErrorMessage(
        code: ErrorMessageCode
            .beRetailAccountMaintenanceRfiNotAllQuestionsAnswered,
        message: 'Not all questions answered',
        id: '',
      );

      // Act
      final result = mapper.mapToSubmitRfiException(errorMessage);

      // Assert
      expect(result, isA<SubmitRfiException>());
      expect(result.code, ErrorCode.unansweredQuestions);
    });

    test('should preserve error message content when mapping', () {
      // Arrange
      const testMessage = 'Test error message';
      const errorMessage = ErrorMessage(
        code: ErrorMessageCode.beRetailAccountMaintenanceRfiNotFound,
        message: testMessage,
        id: '',
      );

      // Act
      final result = mapper.mapToSubmitRfiException(errorMessage);

      // Assert
      expect(result, isA<SubmitRfiException>());
      expect(result.code, ErrorCode.notFound);
      // Note: If SubmitRfiException stores the message, add assertion for it
    });

    test('should handle empty error message', () {
      // Arrange
      const errorMessage = ErrorMessage(
        code: ErrorMessageCode.beRetailAccountMaintenanceRfiAlreadySubmitted,
        message: '',
        id: '',
      );

      // Act
      final result = mapper.mapToSubmitRfiException(errorMessage);

      // Assert
      expect(result, isA<SubmitRfiException>());
      expect(result.code, ErrorCode.alreadySubmitted);
    });

    test('should handle null error message', () {
      // Arrange
      const errorMessage = ErrorMessage(
        code: ErrorMessageCode
            .beRetailAccountMaintenanceRfiNotAllQuestionsAnswered,
        id: '',
      );

      // Act
      final result = mapper.mapToSubmitRfiException(errorMessage);

      // Assert
      expect(result, isA<SubmitRfiException>());
      expect(result.code, ErrorCode.unansweredQuestions);
    });

    group('all known error codes mapping', () {
      test('should map all RFI-specific error codes correctly', () {
        // Arrange & Act & Assert
        final testCases = <ErrorMessageCode, ErrorCode>{
          ErrorMessageCode.beRetailAccountMaintenanceRfiNotFound:
              ErrorCode.notFound,
          ErrorMessageCode.beRetailAccountMaintenanceRfiAlreadySubmitted:
              ErrorCode.alreadySubmitted,
          ErrorMessageCode.beRetailAccountMaintenanceRfiNotAllQuestionsAnswered:
              ErrorCode.unansweredQuestions,
        };

        for (final entry in testCases.entries) {
          final errorMessage = ErrorMessage(
            code: entry.key,
            message: 'Test message for ${entry.key}',
            id: '',
          );

          final result = mapper.mapToSubmitRfiException(errorMessage);

          expect(
            result.code,
            entry.value,
            reason: 'Failed to map ${entry.key} to ${entry.value}',
          );
        }
      });
    });

    group('edge cases', () {
      test('should handle multiple consecutive calls with same error', () {
        // Arrange
        const errorMessage = ErrorMessage(
          code: ErrorMessageCode.beRetailAccountMaintenanceRfiNotFound,
          message: 'Persistent error',
          id: '',
        );

        // Act
        final result1 = mapper.mapToSubmitRfiException(errorMessage);
        final result2 = mapper.mapToSubmitRfiException(errorMessage);

        // Assert
        expect(result1.code, ErrorCode.notFound);
        expect(result2.code, ErrorCode.notFound);
        expect(result1.runtimeType, result2.runtimeType);
      });

      test('should handle multiple consecutive calls with different errors',
          () {
        // Arrange
        const errorMessage1 = ErrorMessage(
          code: ErrorMessageCode.beRetailAccountMaintenanceRfiNotFound,
          message: 'First error',
          id: '',
        );
        const errorMessage2 = ErrorMessage(
          code: ErrorMessageCode.beRetailAccountMaintenanceRfiAlreadySubmitted,
          message: 'Second error',
          id: '',
        );

        // Act
        final result1 = mapper.mapToSubmitRfiException(errorMessage1);
        final result2 = mapper.mapToSubmitRfiException(errorMessage2);

        // Assert
        expect(result1.code, ErrorCode.notFound);
        expect(result2.code, ErrorCode.alreadySubmitted);
      });
    });

    test('should implement RfiExceptionMapper interface', () {
      // Assert
      expect(mapper, isA<RfiExceptionMapper>());
    });
  });
}
