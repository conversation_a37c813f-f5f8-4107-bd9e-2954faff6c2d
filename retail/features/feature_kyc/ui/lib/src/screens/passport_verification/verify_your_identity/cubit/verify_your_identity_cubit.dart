import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/io_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_common_file_picker_ui/file_picker_ui.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_kyc_ui/src/analytics/passport_verification_analytics.dart';
import 'package:wio_feature_kyc_ui/src/navigation/file_picker_bottom_sheet_config.dart';
import 'package:wio_feature_kyc_ui/src/navigation/passport_verification_permission_error_bottom_sheet_config.dart';
import 'package:wio_feature_kyc_ui/src/screens/passport_verification/verify_your_identity/cubit/verify_your_identity_state.dart';

class VerifyYourIdentityCubit extends BaseCubit<VerifyYourIdentityState> {
  final NavigationProvider _navigationProvider;
  final PassportVerificationAnalytics _analytics;
  final Logger _logger;
  final CommonErrorHandler _commonErrorHandler;
  bool _submissionStarted = false;

  VerifyYourIdentityCubit(
    this._navigationProvider,
    this._analytics,
    this._logger,
    this._commonErrorHandler,
  ) : super(const VerifyYourIdentityState());

  void init() => _analytics.showVerifyYourIdentity();

  Future<void> onSubmit(ValueSetter<File> onFilePicked) async {
    try {
      _submissionStarted = true;
      final result = await _navigationProvider.showBottomSheet(
        const FilePickerBottomSheetConfig(
          type: FilePickerType.passport,
        ),
      );

      result?.when(
        success: (file, option) async {
          _analytics.filePicked(
            fileName: file.path,
            source: option.name,
            fileSize: await file.getFormattedSize(),
          );
          onFilePicked(file);
        },
        failure: (errorMessage) => showError(errorMessage),
        permissionDenied: (deniedOption) => Future.delayed(
          const Duration(milliseconds: 250),
          () => _showPermissionErrorBottomSheet(deniedOption),
        ),
      );
    } on Object catch (e, st) {
      _analytics.showVerifyYourIdentityError(e.toString());
      _logger.error(
        'Passport verification file pick error',
        error: e,
        stackTrace: st,
      );

      _commonErrorHandler.handleError(e);
    }
    _submissionStarted = false;
  }

  Future<void> _showPermissionErrorBottomSheet(FilePickerOption option) async {
    final bottomSheetConfig = switch (option) {
      FilePickerOption.camera =>
        const PassportVerificationPermissionErrorBottomSheetConfig.camera(),
      FilePickerOption.gallery =>
        const PassportVerificationPermissionErrorBottomSheetConfig.gallery(),
      FilePickerOption.file =>
        const PassportVerificationPermissionErrorBottomSheetConfig.storage(),
    };

    final result = await _navigationProvider.showBottomSheet(
      bottomSheetConfig,
    );

    if (result is bool && result) {
      openAppSettings().ignore();
    }
  }

  void goBack() => _navigationProvider.goBack();

  bool get submissionStarted => _submissionStarted;

  @override
  String toString() {
    return 'VerifyYourIdentityCubit';
  }
}
