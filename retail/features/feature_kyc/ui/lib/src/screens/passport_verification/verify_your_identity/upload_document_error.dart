import 'dart:io';

import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_kyc_ui/l10n/kyc_localization.g.dart';
import 'package:wio_feature_kyc_ui/src/common/extensions.dart';
import 'package:wio_feature_kyc_ui/src/common/images.dart';
import 'package:wio_feature_kyc_ui/src/screens/passport_verification/models/verify_your_identity_error.dart';

class UploadIdentityError extends StatelessWidget {
  final VerifyYourIdentityError error;

  const UploadIdentityError({
    required this.error,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localization = KycLocalization.of(context);

    return Column(
      children: [
        _UploadedFile(
          title: localization.passportUploadTitle,
          file: error.uploadedFile,
        ),
        Padding(
          padding: const EdgeInsets.only(top: 12),
          child: Label(
            model: LabelModel(
              text: error.errorMessage(localization),
              textAlign: LabelTextAlign.left,
              textStyle: CompanyTextStylePointer.b4,
              color: CompanyColorPointer.secondary13,
            ),
          ),
        ),
      ],
    );
  }
}

class _UploadedFile extends StatelessWidget {
  final String title;
  final File file;

  const _UploadedFile({required this.title, required this.file});

  @override
  Widget build(BuildContext context) {
    Widget child = const SizedBox.shrink();

    if (file.isImage) {
      child = Image.file(
        file,
        fit: BoxFit.contain,
        errorBuilder: (_, __, ___) => _UploadFrame(
          title: title,
          child: const CompanyImage(
            CompanyImageModel(
              image: KycImages.passport,
              fit: BoxFit.fitHeight,
            ),
          ),
        ),
      );
    }

    if (file.isPdf) {
      child = SizedBox(
        height: 180,
        child: PDFView(
          model: PDFViewModel(
            documentModel: PDFViewDocumentModel.file(filePath: file.path),
          ),
        ),
      );
    }

    return _UploadFrame(
      title: title,
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: child,
      ),
    );
  }
}

class _UploadFrame extends StatelessWidget {
  final String title;
  final Widget child;

  const _UploadFrame({
    required this.title,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: context.colorStyling.fromPointer(
          CompanyColorPointer.surface2,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Label(
              model: LabelModel(
                text: title,
                textAlign: LabelTextAlign.left,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.primary3,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: child,
            ),
          ],
        ),
      ),
    );
  }
}
