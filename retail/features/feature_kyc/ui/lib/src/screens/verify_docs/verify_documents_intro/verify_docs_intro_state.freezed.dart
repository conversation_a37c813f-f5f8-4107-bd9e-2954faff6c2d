// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verify_docs_intro_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VerifyDocsIntroState {
  ConfigurationData? get config => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $VerifyDocsIntroStateCopyWith<VerifyDocsIntroState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VerifyDocsIntroStateCopyWith<$Res> {
  factory $VerifyDocsIntroStateCopyWith(VerifyDocsIntroState value,
          $Res Function(VerifyDocsIntroState) then) =
      _$VerifyDocsIntroStateCopyWithImpl<$Res, VerifyDocsIntroState>;
  @useResult
  $Res call({ConfigurationData? config});

  $ConfigurationDataCopyWith<$Res>? get config;
}

/// @nodoc
class _$VerifyDocsIntroStateCopyWithImpl<$Res,
        $Val extends VerifyDocsIntroState>
    implements $VerifyDocsIntroStateCopyWith<$Res> {
  _$VerifyDocsIntroStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = freezed,
  }) {
    return _then(_value.copyWith(
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as ConfigurationData?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ConfigurationDataCopyWith<$Res>? get config {
    if (_value.config == null) {
      return null;
    }

    return $ConfigurationDataCopyWith<$Res>(_value.config!, (value) {
      return _then(_value.copyWith(config: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VerifyDocsIntroStateImplCopyWith<$Res>
    implements $VerifyDocsIntroStateCopyWith<$Res> {
  factory _$$VerifyDocsIntroStateImplCopyWith(_$VerifyDocsIntroStateImpl value,
          $Res Function(_$VerifyDocsIntroStateImpl) then) =
      __$$VerifyDocsIntroStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ConfigurationData? config});

  @override
  $ConfigurationDataCopyWith<$Res>? get config;
}

/// @nodoc
class __$$VerifyDocsIntroStateImplCopyWithImpl<$Res>
    extends _$VerifyDocsIntroStateCopyWithImpl<$Res, _$VerifyDocsIntroStateImpl>
    implements _$$VerifyDocsIntroStateImplCopyWith<$Res> {
  __$$VerifyDocsIntroStateImplCopyWithImpl(_$VerifyDocsIntroStateImpl _value,
      $Res Function(_$VerifyDocsIntroStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? config = freezed,
  }) {
    return _then(_$VerifyDocsIntroStateImpl(
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as ConfigurationData?,
    ));
  }
}

/// @nodoc

class _$VerifyDocsIntroStateImpl implements _VerifyDocsIntroState {
  const _$VerifyDocsIntroStateImpl({this.config});

  @override
  final ConfigurationData? config;

  @override
  String toString() {
    return 'VerifyDocsIntroState(config: $config)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VerifyDocsIntroStateImpl &&
            (identical(other.config, config) || other.config == config));
  }

  @override
  int get hashCode => Object.hash(runtimeType, config);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VerifyDocsIntroStateImplCopyWith<_$VerifyDocsIntroStateImpl>
      get copyWith =>
          __$$VerifyDocsIntroStateImplCopyWithImpl<_$VerifyDocsIntroStateImpl>(
              this, _$identity);
}

abstract class _VerifyDocsIntroState implements VerifyDocsIntroState {
  const factory _VerifyDocsIntroState({final ConfigurationData? config}) =
      _$VerifyDocsIntroStateImpl;

  @override
  ConfigurationData? get config;
  @override
  @JsonKey(ignore: true)
  _$$VerifyDocsIntroStateImplCopyWith<_$VerifyDocsIntroStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
