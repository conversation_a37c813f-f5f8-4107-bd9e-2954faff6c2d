import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_kyc_api/model/rfi_model.dart';
import 'package:wio_feature_kyc_api/navigation/kyc_feature_navigation_config.dart';

class RfiListScreenNavigationConfig extends ScreenNavigationConfig {
  static const screenId = 'rfi_list_screen';
  final List<RfiModel> rfiList;
  final bool isSkippable;

  const RfiListScreenNavigationConfig({
    required this.rfiList,
    this.isSkippable = true,
  }) : super(
          id: RfiListScreenNavigationConfig.screenId,
          feature: KycFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'RfiListScreenNavigationConfig';
}
