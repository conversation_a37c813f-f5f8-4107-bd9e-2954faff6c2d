import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/feature_cards_ui.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/navigation/bottom_sheets/freeze_card_confirmation_bottom_sheet_config.dart';
import 'package:feature_cards_ui/src/navigation/card_details_bottom_sheet_navigation_config.dart';
import 'package:feature_cards_ui/src/screens/cards/card_view_model.dart';
import 'package:feature_cards_ui/src/screens/cards/cubit/card_ui_mapper.dart';
import 'package:feature_cards_ui/src/screens/cards/cubit/delegates/card_wallet_delegate.dart';
import 'package:feature_cards_ui/src/screens/participant_cards/participant_cards_analytics.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late CardWalletDelegate walletDelegate;
  late CardInteractor cardInteractor;
  late NavigationProvider navigationProvider;
  late ParticipantCardsAnalytics analytics;
  late CardUIMapper cardUIMapper;
  late CardsLocalizations l10n;

  void stubCards(Stream<Data<List<Card>>> data) {
    when(() => cardInteractor.getCards(refresh: true)).thenAnswer((_) => data);
  }

  // Test data
  final jointCard = CardFactory.getCard(
    id: '1',
    type: CardType.shared,
    ownerType: CardOwnerType.owner,
  );
  final cardViewModel = CardViewModel(
    card: jointCard,
    addWalletAvailable: true,
    isAlreadyInWallets: false,
    cardUiState: CardUiState.idle,
  );
  final idleState = IdleParticipantCardsState(
    cards: [cardViewModel],
    activeCardIndex: 0,
  );

  setUp(() {
    cardInteractor = MockCardInteractor();
    walletDelegate = MockCardWalletDelegate();
    navigationProvider = MockNavigationProvider();
    analytics = _MockParticipantCardsAnalytics();
    cardUIMapper = MockCardUiMapper();
  });

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    l10n = await CardsLocalizations.load(const Locale('en'));
  });

  ParticipantCardsCubit getCubit() => ParticipantCardsCubit(
        addToWalletStreamExecutor: MockExhaustStreamExecuter(),
        walletDelegate: walletDelegate,
        cardInteractor: cardInteractor,
        navigationProvider: navigationProvider,
        toastMessageProvider: MockToastMessageProvider(),
        cardsLocalizations: l10n,
        cardUIMapper: cardUIMapper,
        logger: MockLogger(),
        commonErrorHandler: MockCommonErrorHandler(),
        analytics: analytics,
      );

  group('Initialization >', () {
    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'initialized properly for empty cards',
      // Arrange
      build: getCubit,
      setUp: () {
        stubCards(
          Stream.fromIterable(
            [
              Data.loading(null),
              Data.success(const <Card>[]),
            ],
          ),
        );
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => const [
        ParticipantCardsState.loading(),
        ParticipantCardsState.empty(),
      ],
    );

    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'initialized properly by listening to cards stream - non-empty cards ',
      // Arrange
      build: getCubit,
      setUp: () {
        stubCards(
          Stream.fromIterable(
            [
              Data.loading(null),
              Data.success([jointCard]),
            ],
          ),
        );

        when(
          () => walletDelegate.getCardsWalletRecords([jointCard]),
        ).justAnswerAsync(
          {
            jointCard.id: const WalletCardRecord(
              canAddToWallet: true,
              isAlreadyInWallets: false,
            ),
          },
        );

        when(
          () => cardUIMapper.cardToViewModel(
            jointCard,
            addWalletAvailable: true,
          ),
        ).thenReturn(cardViewModel);
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [
        const ParticipantCardsState.loading(),
        ParticipantCardsState.idle(
          cards: [cardViewModel],
          activeCardIndex: 0,
        ),
      ],
    );

    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'initialized properly by listening to cards stream - error',
      // Arrange
      build: getCubit,
      setUp: () {
        when(
          () => cardInteractor.getCards(refresh: true),
        ).thenAnswer((_) => Stream.error(Exception()));
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [const ParticipantCardsState.failed()],
    );
  });

  group('Refresh >', () {
    final allowedStates = [
      idleState,
      const ParticipantCardsState.empty(),
      const ParticipantCardsState.failed(),
    ];
    final disallowedStates = [
      const ParticipantCardsState.initial(),
      const ParticipantCardsState.loading(),
    ];

    for (final state in allowedStates) {
      blocTest<ParticipantCardsCubit, ParticipantCardsState>(
        'can refresh cards from ${state.runtimeType} state',
        // Arrange
        build: getCubit,
        seed: () => state,
        setUp: () => stubCards(Stream.value(Data.success([jointCard]))),

        // Act
        act: (cubit) => cubit.onRefresh(),

        // Assert
        verify: (_) {
          verify(analytics.clickRefreshCards);
          verify(() => cardInteractor.getCards(refresh: true)).calledOnce;
        },
      );
    }

    for (final state in disallowedStates) {
      blocTest<ParticipantCardsCubit, ParticipantCardsState>(
        'cannot refresh cards from ${state.runtimeType} state',
        // Arrange
        build: getCubit,
        seed: () => state,

        // Act
        act: (cubit) => cubit.onRefresh(),

        // Assert
        verify: (_) {
          verifyZeroInteractions(analytics);
          verifyNever(() => cardInteractor.getCards(refresh: true));
        },
      );
    }
  });

  group('Switching cards >', () {
    final newCardViewModel = cardViewModel.copyWith(
      card: CardFactory.getCard(id: '2'),
    );

    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'changes index of selected card',
      // Arrange
      build: getCubit,
      seed: () => ParticipantCardsState.idle(
        activeCardIndex: 0,
        cards: [cardViewModel, newCardViewModel],
      ),

      // Act
      act: (cubit) => cubit.onSwitchActiveCard(1),

      // Assert
      expect: () => [
        ParticipantCardsState.idle(
          activeCardIndex: 1,
          cards: [cardViewModel, newCardViewModel],
        ),
      ],
    );

    for (final index in const [-1, 10]) {
      blocTest<ParticipantCardsCubit, ParticipantCardsState>(
        'does not accept invalid index: $index',
        // Arrange
        build: getCubit,
        seed: () => ParticipantCardsState.idle(
          activeCardIndex: 0,
          cards: [cardViewModel],
        ),

        // Act
        act: (cubit) => cubit.onSwitchActiveCard(index),

        // Assert
        expect: () => <ParticipantCardsState>[
          // no changes
        ],
      );
    }
  });

  group('Actions >', () {
    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'opens card settings',
      // Arrange
      build: getCubit,
      seed: () => idleState,

      // Act
      act: (cubit) => cubit.onOpenSettings(),

      // Assert
      verify: (_) {
        verify(analytics.clickSettings);
        verify(
          () => navigationProvider.push(
            CardSettingsScreenNavigationConfig(
              param: CardSettingsScreenParam.byCard(card: jointCard),
            ),
          ),
        );
      },
    );

    final detailsConfig = CardDetailsBottomSheetNavigationConfig(
      cardId: jointCard.id,
    );
    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'opens card details',
      // Arrange
      build: getCubit,
      seed: () => idleState,
      setUp: () {
        when(
          () => navigationProvider.showBottomSheet(detailsConfig),
        ).justCompleteAsync();
      },

      // Act
      act: (cubit) => cubit.onOpenCardDetails(),

      // Assert
      verify: (_) {
        verify(analytics.clickViewCardDetails);
        verify(() => navigationProvider.showBottomSheet(detailsConfig));
      },
    );

    blocTest<ParticipantCardsCubit, ParticipantCardsState>(
      'opens card transactions',
      // Arrange
      build: getCubit,
      seed: () => idleState,

      // Act
      act: (cubit) => cubit.onSeeAllTransactions(),

      // Assert
      verify: (_) {
        verify(analytics.clickSeeAllTransactions);
        verify(
          () => navigationProvider.navigateTo(
            any(that: isA<TransactionsFeatureNavigationConfig>()),
          ),
        );
      },
    );

    group('freezing a card >', () {
      blocTest<ParticipantCardsCubit, ParticipantCardsState>(
        'freezes a card if confirmed',
        // Arrange
        build: getCubit,
        seed: () => idleState,
        setUp: () {
          when(
            () => navigationProvider.showBottomSheet(
              const FreezeCardConfirmationBottomSheetConfig(),
            ),
          ).justAnswerAsync(FreezeCardConfirmationResult.confirmed);

          when(
            () => cardInteractor.freezeCard(jointCard.id),
          ).justCompleteAsync();
        },

        // Act
        act: (cubit) => cubit.onFreezeCard(),

        // Assert
        expect: () => [
          idleState.copyWith(
            cards: [
              cardViewModel.copyWith(cardUiState: CardUiState.freezing),
            ],
          ),
        ],
        verify: (_) {
          verify(analytics.clickFreezeCard);
          verifyInOrder([
            () => navigationProvider.showBottomSheet(
                  const FreezeCardConfirmationBottomSheetConfig(),
                ),
            () => cardInteractor.freezeCard(jointCard.id),
          ]);
        },
      );

      blocTest<ParticipantCardsCubit, ParticipantCardsState>(
        'does not freeze the card if not confirmed',
        // Arrange
        build: getCubit,
        seed: () => idleState,
        setUp: () {
          when(
            () => navigationProvider.showBottomSheet(
              const FreezeCardConfirmationBottomSheetConfig(),
            ),
          ).justAnswerAsync(FreezeCardConfirmationResult.cancelled);
        },

        // Act
        act: (cubit) => cubit.onFreezeCard(),

        // Assert
        expect: () => <ParticipantCardsState>[
          // no state changes
        ],
        verify: (_) {
          verify(analytics.clickFreezeCard);
          verify(
            () => navigationProvider.showBottomSheet(
              const FreezeCardConfirmationBottomSheetConfig(),
            ),
          );

          verifyNever(() => cardInteractor.freezeCard(jointCard.id));
        },
      );
    });
  });
}

class _MockParticipantCardsAnalytics extends Mock
    implements ParticipantCardsAnalytics {}
