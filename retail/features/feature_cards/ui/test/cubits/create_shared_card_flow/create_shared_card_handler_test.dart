import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/screens/create_shared_card/flow/create_shared_card_analytics.dart';
import 'package:feature_cards_ui/src/screens/create_shared_card/flow/create_shared_card_flow_state.dart';
import 'package:feature_cards_ui/src/screens/create_shared_card/flow/create_shared_card_handler.dart';
import 'package:flutter/rendering.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';

import '../../mocks.dart';
import '../../test_entities.dart';

void main() {
  _sharedCardCreationTests();
  _conversionToSharedCardTests();
}

void _sharedCardCreationTests() {
  late CardsLocalizations l10n;
  late CardInteractor cardInteractor;
  late NavigationProvider navigationProvider;
  late Logger logger;
  late CreateSharedCardAnalytics analytics;
  late CreateSharedCardHandler handler;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    l10n = await CardsLocalizations.load(const Locale('en'));
  });

  setUp(() {
    cardInteractor = MockCardInteractor();
    navigationProvider = MockNavigationProvider();
    logger = MockLogger();
    analytics = CreateSharedCardAnalytics(
      MockAnalyticsTrackerFactory(),
      screenName: '',
    );

    handler = CreateNewSharedCardHandler(
      cardInteractor: cardInteractor,
      navigationProvider: navigationProvider,
      l10n: l10n,
      logger: logger,
      analytics: analytics,
    );
  });

  group('Card creation >', () {
    test('successfully creates a new shared card', () async {
      // Arrange
      final cardName = randomString();
      final contactPhone = randomString();
      final contactName = randomString();
      final spendLimit = CardFactory.getSpendingLimitRecord();
      final data = SharedCardData(
        cardName: cardName,
        recipientPhoneNumber: contactPhone,
        recipientName: contactName,
        spendingLimit: spendLimit,
      );
      final input = CreateSharedCardInput(
        cardName: cardName,
        recipientName: contactName,
        recipientPhoneNumber: contactPhone,
        spendingLimitRecord: spendLimit,
      );
      final contact = CardSharedContact(
        phoneNumber: contactPhone,
        name: contactName,
      );
      final card = CardFactory.getCard(
        id: '42',
        type: CardType.shared,
        sharedContact: contact,
      );

      when(
        () => cardInteractor.createSharedCard(input: input),
      ).justAnswerAsync(card);

      // Act
      await handler.createCard(data);

      // Assert
      verifyInOrder([
        () => cardInteractor.createSharedCard(input: input),
        () => navigationProvider
            .navigateTo(any(that: isA<StatusViewFeatureNavigationConfig>())),
      ]);
    });
  });
}

void _conversionToSharedCardTests() {
  late CardInteractor cardInteractor;
  late SharedCardInteractor sharedCardInteractor;
  late NavigationProvider navigationProvider;
  late Logger logger;
  late CreateSharedCardAnalytics analytics;
  late CreateSharedCardHandler handler;
  late CardsLocalizations l10n;

  const cardId = '1337';
  final spendLimit = SpendingLimitRecord(
    limit: Money.fromNumWithCurrency(42, Currency.aed),
    frequency: CardSpendingLimitFrequency.daily,
  );

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    l10n = await CardsLocalizations.load(const Locale('en'));
  });

  setUp(() {
    cardInteractor = MockCardInteractor();
    sharedCardInteractor = MockSharedCardInteractor();
    navigationProvider = MockNavigationProvider();
    logger = MockLogger();
    analytics = CreateSharedCardAnalytics(
      MockAnalyticsTrackerFactory(),
      screenName: '',
    );

    handler = ConvertToSharedCardHandler(
      cardId: cardId,
      cardInteractor: cardInteractor,
      sharedCardInteractor: sharedCardInteractor,
      navigationProvider: navigationProvider,
      l10n: l10n,
      logger: logger,
      analytics: analytics,
    );
  });

  void stubCards(List<Card> cards) {
    when(
      () => cardInteractor.getCards(),
    ).thenAnswer((_) => Stream.value(Data.success(cards)));
  }

  group('Getting initial data >', () {
    test('prepares initial data based on given card', () async {
      // Arrange
      final cardName = randomString();
      final expiryDate = randomDate();
      final accountId = randomString();
      final card = CardFactory.getCard(
        id: cardId,
        type: CardType.virtual,
        name: cardName,
        spendingLimit: spendLimit,
        expiryDate: expiryDate,
        accountId: accountId,
      ).copyWith(image: CardFactory.getCardImage(cardType: CardType.shared));
      stubCards([CardFactory.getCard(id: 'not $cardId'), card]);

      final expected = SharedCardData(
        cardName: cardName,
        expiryDate: expiryDate,
        sourceAccountId: accountId,
      );

      // Act
      final actual = await handler.getInitialData();

      // Assert
      expect(actual, expected);
    });

    test('throws an error if no card can be found for provided ID', () {
      // Arrange
      stubCards(List.generate(5, (i) => CardFactory.getCard(id: '$i')));

      // Act && Assert
      expect(
        () => handler.getInitialData(),
        throwsA(isA<InvalidSharedCardDataException>()),
      );
    });

    test('throws an error if provided ID is not of a virtual card', () {
      // Arrange
      stubCards([
        CardFactory.getCard(id: '0'),
        CardFactory.getCard(id: cardId, type: CardType.digital),
      ]);

      // Act && Assert
      expect(
        () => handler.getInitialData(),
        throwsA(isA<InvalidSharedCardDataException>()),
      );
    });
  });

  group('Card conversion >', () {
    registerFallbackValue(FakeToastMessageConfiguration());

    test('successfully converts to a shared card', () async {
      // Arrange
      final contactPhone = randomString();
      final contactName = randomString();
      final data = SharedCardData(
        recipientPhoneNumber: contactPhone,
        recipientName: contactName,
        spendingLimit: spendLimit,
      );
      final input = ConvertToSharedCardInput(
        recipientName: contactName,
        recipientPhoneNumber: contactPhone,
        spendingLimit: spendLimit,
      );
      when(
        () => sharedCardInteractor.convertToSharedCard(
          cardId: cardId,
          input: input,
        ),
      ).justCompleteAsync();

      // Act
      await handler.createCard(data);

      // Assert
      verifyInOrder([
        () => sharedCardInteractor.convertToSharedCard(
              cardId: cardId,
              input: input,
            ),
        () => navigationProvider.navigateTo(
              any(that: isA<StatusViewFeatureNavigationConfig>()),
            ),
        () => navigationProvider.goBack(),
      ]);
    });
  });
}
