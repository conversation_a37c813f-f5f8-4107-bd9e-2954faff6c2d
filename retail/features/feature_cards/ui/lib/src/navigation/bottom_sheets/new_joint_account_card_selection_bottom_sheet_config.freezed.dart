// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'new_joint_account_card_selection_bottom_sheet_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NewJointAccountCardSelectionBottomSheetNavigationConfig {}

/// @nodoc
abstract class $NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWith<
    $Res> {
  factory $NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWith(
          NewJointAccountCardSelectionBottomSheetNavigationConfig value,
          $Res Function(NewJointAccountCardSelectionBottomSheetNavigationConfig)
              then) =
      _$NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWithImpl<
          $Res, NewJointAccountCardSelectionBottomSheetNavigationConfig>;
}

/// @nodoc
class _$NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWithImpl<
        $Res,
        $Val extends NewJointAccountCardSelectionBottomSheetNavigationConfig>
    implements
        $NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWith<$Res> {
  _$NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NewJointAccountCardSelectionBottomSheetNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$NewJointAccountCardSelectionBottomSheetNavigationConfigImplCopyWith<
    $Res> {
  factory _$$NewJointAccountCardSelectionBottomSheetNavigationConfigImplCopyWith(
          _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl value,
          $Res Function(
                  _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl)
              then) =
      __$$NewJointAccountCardSelectionBottomSheetNavigationConfigImplCopyWithImpl<
          $Res>;
}

/// @nodoc
class __$$NewJointAccountCardSelectionBottomSheetNavigationConfigImplCopyWithImpl<
        $Res>
    extends _$NewJointAccountCardSelectionBottomSheetNavigationConfigCopyWithImpl<
        $Res, _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl>
    implements
        _$$NewJointAccountCardSelectionBottomSheetNavigationConfigImplCopyWith<
            $Res> {
  __$$NewJointAccountCardSelectionBottomSheetNavigationConfigImplCopyWithImpl(
      _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl _value,
      $Res Function(
              _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl)
          _then)
      : super(_value, _then);

  /// Create a copy of NewJointAccountCardSelectionBottomSheetNavigationConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl
    extends _NewJointAccountCardSelectionBottomSheetNavigationConfig {
  const _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl()
      : super._();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _NewJointAccountCardSelectionBottomSheetNavigationConfig
    extends NewJointAccountCardSelectionBottomSheetNavigationConfig {
  const factory _NewJointAccountCardSelectionBottomSheetNavigationConfig() =
      _$NewJointAccountCardSelectionBottomSheetNavigationConfigImpl;
  const _NewJointAccountCardSelectionBottomSheetNavigationConfig._()
      : super._();
}
