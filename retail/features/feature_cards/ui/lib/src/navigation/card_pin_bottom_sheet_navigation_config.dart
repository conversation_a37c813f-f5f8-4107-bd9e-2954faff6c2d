import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:flutter/material.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

@immutable
class CardPinBottomSheetNavigationConfig
    extends BottomSheetNavigationConfig<Object?> {
  final String? cardPin;
  final bool shouldShowChangePin;

  const CardPinBottomSheetNavigationConfig({
    required this.cardPin,
    required this.shouldShowChangePin,
  });

  @override
  String get feature => CardsFeatureNavigationConfig.name;
}
