import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/models/card_account_type.dart';
import 'package:feature_cards_ui/src/navigation/replace_card_confirmation_bottom_sheet_navigation_config.dart';
import 'package:flutter/material.dart' hide Card;
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';

const _cardHeight = 150.0;

class ReplaceCardConfirmationBottomSheet extends StatelessWidget {
  final Card card;
  final CardAccountType accountType;
  final Money? cardCreationFee;

  const ReplaceCardConfirmationBottomSheet({
    required this.card,
    required this.accountType,
    required this.cardCreationFee,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = CardsLocalizations.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    final creationFee = cardCreationFee;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: ListView(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        children: [
          Space.fromSpacingVertical(Spacing.s4),
          SizedBox(
            height: _cardHeight,
            child: FittedBox(
              child: SizedBox(
                width: screenWidth,
                child: BankCard(
                  model: BankCardsModel.wio(
                    lastCardNumbers: card.lastDigits,
                    cardName: card.name,
                    cardType: const WioBankCardType.debit(),
                    accountType: _getAccountType(accountType, context),
                  ),
                ),
              ),
            ),
          ),
          Space.fromSpacingVertical(Spacing.s5),
          Label(
            model: LabelModel(
              text: localizations.replaceCardConfirmationBottomSheetTitle,
              color: CompanyColorPointer.primary3,
              textStyle: CompanyTextStylePointer.h3medium,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s2),
          Label(
            model: LabelModel(
              text: creationFee == null || creationFee.isZero
                  ? localizations
                      .replaceCardConfirmationBottomSheetDescriptionNoPrice
                  : localizations.replaceCardConfirmationBottomSheetDescription(
                      creationFee.toFormattedString(),
                    ),
              color: CompanyColorPointer.secondary4,
              textStyle: CompanyTextStylePointer.b3,
            ),
          ),
          const SizedBox(height: 20),
          Button(
            model: ButtonModel(
              title: localizations
                  .replaceCardConfirmationBottomSheetSubmitButtonTitle,
            ),
            onPressed: () => Navigator.of(context)
                .pop(const ReplaceCardConfirmationResult.replace()),
          ),
          const SizedBox(height: 10),
          Button(
            model: ButtonModel(
              type: ButtonType.secondary,
              title: localizations
                  .replaceCardConfirmationBottomSheetCancelButtonTitle,
            ),
            onPressed: () => Navigator.of(context)
                .pop(const ReplaceCardConfirmationResult.cancel()),
          ),
        ],
      ),
    );
  }

  WioBankCardAccountType _getAccountType(
    CardAccountType? type,
    BuildContext context,
  ) {
    final localizations = CardsLocalizations.of(context);

    switch (type) {
      case CardAccountType.loan:
        return WioBankCardAccountType.credit(
          title: localizations.cardsScreenCardWioCredit,
        );
      case CardAccountType.debit:
        return const WioBankCardAccountType.myMoney();
      case null:
        return const WioBankCardAccountType.myMoney();
    }
  }
}
