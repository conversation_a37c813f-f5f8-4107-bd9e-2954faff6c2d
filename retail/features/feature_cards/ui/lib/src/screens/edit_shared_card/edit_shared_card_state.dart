import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'edit_shared_card_state.freezed.dart';

enum EditSharedCardStage {
  selectPhone,
  selectName,
  verifyIdentity,
}

@freezed
sealed class EditSharedCardState with _$EditSharedCardState {
  const EditSharedCardState._();

  const factory EditSharedCardState.initial() = EditSharedCardInitialState;

  const factory EditSharedCardState.idle({
    required List<EditSharedCardStage> stages,
    required String cardId,
    String? newPhoneNumber,
    String? newName,
    CardSharedContact? contact,
  }) = EditSharedCardIdleState;

  const factory EditSharedCardState.inProgress({
    required List<EditSharedCardStage> stages,
    required String cardId,
    required String newPhoneNumber,
    required String newName,
    CardSharedContact? contact,
  }) = EditSharedCardInProgressState;

  const factory EditSharedCardState.completed({
    required String cardId,
  }) = EditSharedCardCompletedState;

  const factory EditSharedCardState.failed({
    required Object error,
    required String cardId,
  }) = EditSharedCardFailedState;

  bool get canSubmit => maybeMap(
        idle: (it) => it.newPhoneNumber != null && it.newName != null,
        orElse: () => false,
      );

  String? get suggestedName => mapOrNull(
        idle: (it) => it.newName ?? it.contact?.name,
        inProgress: (it) => it.newName,
      );

  EditSharedCardState toInProgress() => maybeMap(
        idle: (it) {
          final newPhone = it.newPhoneNumber;
          final newName = it.newName;

          if (newPhone == null || newName == null) {
            return this;
          }

          return EditSharedCardState.inProgress(
            stages: it.stages,
            cardId: it.cardId,
            contact: it.contact,
            newPhoneNumber: newPhone,
            newName: newName,
          );
        },
        orElse: () => this,
      );

  EditSharedCardState toIdle({EditSharedCardStage? nextStage}) => maybeMap(
        idle: (it) => it.copyWith(
          stages: [...it.stages, if (nextStage != null) nextStage],
        ),
        inProgress: (it) => EditSharedCardState.idle(
          stages: [...it.stages, if (nextStage != null) nextStage],
          cardId: it.cardId,
          contact: it.contact,
          newPhoneNumber: it.newPhoneNumber,
          newName: it.newName,
        ),
        orElse: () => this,
      );

  EditSharedCardState toFailed(Object error) => maybeMap(
        inProgress: (it) => EditSharedCardState.failed(
          error: error,
          cardId: it.cardId,
        ),
        orElse: () => this,
      );
}

extension EditSharedCardIdleStateExtension on EditSharedCardIdleState {
  EditSharedCardStage get currentStage => stages.last;

  EditSharedCardIdleState popStage() =>
      copyWith(stages: [...stages]..removeLast());
}
