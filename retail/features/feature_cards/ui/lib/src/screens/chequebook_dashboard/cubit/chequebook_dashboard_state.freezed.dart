// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'chequebook_dashboard_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ChequebookDashboardState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(bool areChequeFAQsEnabled) loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(bool areChequeFAQsEnabled)? loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(bool areChequeFAQsEnabled)? loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadingChequebookDashboardState value) loading,
    required TResult Function(LoadedChequebookDashboardState value) loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadingChequebookDashboardState value)? loading,
    TResult? Function(LoadedChequebookDashboardState value)? loaded,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadingChequebookDashboardState value)? loading,
    TResult Function(LoadedChequebookDashboardState value)? loaded,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChequebookDashboardStateCopyWith<$Res> {
  factory $ChequebookDashboardStateCopyWith(ChequebookDashboardState value,
          $Res Function(ChequebookDashboardState) then) =
      _$ChequebookDashboardStateCopyWithImpl<$Res, ChequebookDashboardState>;
}

/// @nodoc
class _$ChequebookDashboardStateCopyWithImpl<$Res,
        $Val extends ChequebookDashboardState>
    implements $ChequebookDashboardStateCopyWith<$Res> {
  _$ChequebookDashboardStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChequebookDashboardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadingChequebookDashboardStateImplCopyWith<$Res> {
  factory _$$LoadingChequebookDashboardStateImplCopyWith(
          _$LoadingChequebookDashboardStateImpl value,
          $Res Function(_$LoadingChequebookDashboardStateImpl) then) =
      __$$LoadingChequebookDashboardStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingChequebookDashboardStateImplCopyWithImpl<$Res>
    extends _$ChequebookDashboardStateCopyWithImpl<$Res,
        _$LoadingChequebookDashboardStateImpl>
    implements _$$LoadingChequebookDashboardStateImplCopyWith<$Res> {
  __$$LoadingChequebookDashboardStateImplCopyWithImpl(
      _$LoadingChequebookDashboardStateImpl _value,
      $Res Function(_$LoadingChequebookDashboardStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChequebookDashboardState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingChequebookDashboardStateImpl
    extends LoadingChequebookDashboardState {
  const _$LoadingChequebookDashboardStateImpl() : super._();

  @override
  String toString() {
    return 'ChequebookDashboardState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingChequebookDashboardStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(bool areChequeFAQsEnabled) loaded,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(bool areChequeFAQsEnabled)? loaded,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(bool areChequeFAQsEnabled)? loaded,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadingChequebookDashboardState value) loading,
    required TResult Function(LoadedChequebookDashboardState value) loaded,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadingChequebookDashboardState value)? loading,
    TResult? Function(LoadedChequebookDashboardState value)? loaded,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadingChequebookDashboardState value)? loading,
    TResult Function(LoadedChequebookDashboardState value)? loaded,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class LoadingChequebookDashboardState
    extends ChequebookDashboardState {
  const factory LoadingChequebookDashboardState() =
      _$LoadingChequebookDashboardStateImpl;
  const LoadingChequebookDashboardState._() : super._();
}

/// @nodoc
abstract class _$$LoadedChequebookDashboardStateImplCopyWith<$Res> {
  factory _$$LoadedChequebookDashboardStateImplCopyWith(
          _$LoadedChequebookDashboardStateImpl value,
          $Res Function(_$LoadedChequebookDashboardStateImpl) then) =
      __$$LoadedChequebookDashboardStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool areChequeFAQsEnabled});
}

/// @nodoc
class __$$LoadedChequebookDashboardStateImplCopyWithImpl<$Res>
    extends _$ChequebookDashboardStateCopyWithImpl<$Res,
        _$LoadedChequebookDashboardStateImpl>
    implements _$$LoadedChequebookDashboardStateImplCopyWith<$Res> {
  __$$LoadedChequebookDashboardStateImplCopyWithImpl(
      _$LoadedChequebookDashboardStateImpl _value,
      $Res Function(_$LoadedChequebookDashboardStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChequebookDashboardState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? areChequeFAQsEnabled = null,
  }) {
    return _then(_$LoadedChequebookDashboardStateImpl(
      areChequeFAQsEnabled: null == areChequeFAQsEnabled
          ? _value.areChequeFAQsEnabled
          : areChequeFAQsEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LoadedChequebookDashboardStateImpl
    extends LoadedChequebookDashboardState {
  const _$LoadedChequebookDashboardStateImpl(
      {required this.areChequeFAQsEnabled})
      : super._();

  @override
  final bool areChequeFAQsEnabled;

  @override
  String toString() {
    return 'ChequebookDashboardState.loaded(areChequeFAQsEnabled: $areChequeFAQsEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedChequebookDashboardStateImpl &&
            (identical(other.areChequeFAQsEnabled, areChequeFAQsEnabled) ||
                other.areChequeFAQsEnabled == areChequeFAQsEnabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, areChequeFAQsEnabled);

  /// Create a copy of ChequebookDashboardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedChequebookDashboardStateImplCopyWith<
          _$LoadedChequebookDashboardStateImpl>
      get copyWith => __$$LoadedChequebookDashboardStateImplCopyWithImpl<
          _$LoadedChequebookDashboardStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(bool areChequeFAQsEnabled) loaded,
  }) {
    return loaded(areChequeFAQsEnabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(bool areChequeFAQsEnabled)? loaded,
  }) {
    return loaded?.call(areChequeFAQsEnabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(bool areChequeFAQsEnabled)? loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(areChequeFAQsEnabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadingChequebookDashboardState value) loading,
    required TResult Function(LoadedChequebookDashboardState value) loaded,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadingChequebookDashboardState value)? loading,
    TResult? Function(LoadedChequebookDashboardState value)? loaded,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadingChequebookDashboardState value)? loading,
    TResult Function(LoadedChequebookDashboardState value)? loaded,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class LoadedChequebookDashboardState extends ChequebookDashboardState {
  const factory LoadedChequebookDashboardState(
          {required final bool areChequeFAQsEnabled}) =
      _$LoadedChequebookDashboardStateImpl;
  const LoadedChequebookDashboardState._() : super._();

  bool get areChequeFAQsEnabled;

  /// Create a copy of ChequebookDashboardState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedChequebookDashboardStateImplCopyWith<
          _$LoadedChequebookDashboardStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
