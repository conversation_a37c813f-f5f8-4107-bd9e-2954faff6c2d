// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'wallet_onboarding_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$WalletOnboardingState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() googlePay,
    required TResult Function() applePay,
    required TResult Function() unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? googlePay,
    TResult? Function()? applePay,
    TResult? Function()? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? googlePay,
    TResult Function()? applePay,
    TResult Function()? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GooglePayOnboardingState value) googlePay,
    required TResult Function(_ApplePayOnboardingState value) applePay,
    required TResult Function(_UnknownWalletOnboardingState value) unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GooglePayOnboardingState value)? googlePay,
    TResult? Function(_ApplePayOnboardingState value)? applePay,
    TResult? Function(_UnknownWalletOnboardingState value)? unknown,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GooglePayOnboardingState value)? googlePay,
    TResult Function(_ApplePayOnboardingState value)? applePay,
    TResult Function(_UnknownWalletOnboardingState value)? unknown,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WalletOnboardingStateCopyWith<$Res> {
  factory $WalletOnboardingStateCopyWith(WalletOnboardingState value,
          $Res Function(WalletOnboardingState) then) =
      _$WalletOnboardingStateCopyWithImpl<$Res, WalletOnboardingState>;
}

/// @nodoc
class _$WalletOnboardingStateCopyWithImpl<$Res,
        $Val extends WalletOnboardingState>
    implements $WalletOnboardingStateCopyWith<$Res> {
  _$WalletOnboardingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WalletOnboardingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$GooglePayOnboardingStateImplCopyWith<$Res> {
  factory _$$GooglePayOnboardingStateImplCopyWith(
          _$GooglePayOnboardingStateImpl value,
          $Res Function(_$GooglePayOnboardingStateImpl) then) =
      __$$GooglePayOnboardingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$GooglePayOnboardingStateImplCopyWithImpl<$Res>
    extends _$WalletOnboardingStateCopyWithImpl<$Res,
        _$GooglePayOnboardingStateImpl>
    implements _$$GooglePayOnboardingStateImplCopyWith<$Res> {
  __$$GooglePayOnboardingStateImplCopyWithImpl(
      _$GooglePayOnboardingStateImpl _value,
      $Res Function(_$GooglePayOnboardingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WalletOnboardingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$GooglePayOnboardingStateImpl extends _GooglePayOnboardingState {
  const _$GooglePayOnboardingStateImpl() : super._();

  @override
  String toString() {
    return 'WalletOnboardingState.googlePay()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GooglePayOnboardingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() googlePay,
    required TResult Function() applePay,
    required TResult Function() unknown,
  }) {
    return googlePay();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? googlePay,
    TResult? Function()? applePay,
    TResult? Function()? unknown,
  }) {
    return googlePay?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? googlePay,
    TResult Function()? applePay,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (googlePay != null) {
      return googlePay();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GooglePayOnboardingState value) googlePay,
    required TResult Function(_ApplePayOnboardingState value) applePay,
    required TResult Function(_UnknownWalletOnboardingState value) unknown,
  }) {
    return googlePay(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GooglePayOnboardingState value)? googlePay,
    TResult? Function(_ApplePayOnboardingState value)? applePay,
    TResult? Function(_UnknownWalletOnboardingState value)? unknown,
  }) {
    return googlePay?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GooglePayOnboardingState value)? googlePay,
    TResult Function(_ApplePayOnboardingState value)? applePay,
    TResult Function(_UnknownWalletOnboardingState value)? unknown,
    required TResult orElse(),
  }) {
    if (googlePay != null) {
      return googlePay(this);
    }
    return orElse();
  }
}

abstract class _GooglePayOnboardingState extends WalletOnboardingState {
  const factory _GooglePayOnboardingState() = _$GooglePayOnboardingStateImpl;
  const _GooglePayOnboardingState._() : super._();
}

/// @nodoc
abstract class _$$ApplePayOnboardingStateImplCopyWith<$Res> {
  factory _$$ApplePayOnboardingStateImplCopyWith(
          _$ApplePayOnboardingStateImpl value,
          $Res Function(_$ApplePayOnboardingStateImpl) then) =
      __$$ApplePayOnboardingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ApplePayOnboardingStateImplCopyWithImpl<$Res>
    extends _$WalletOnboardingStateCopyWithImpl<$Res,
        _$ApplePayOnboardingStateImpl>
    implements _$$ApplePayOnboardingStateImplCopyWith<$Res> {
  __$$ApplePayOnboardingStateImplCopyWithImpl(
      _$ApplePayOnboardingStateImpl _value,
      $Res Function(_$ApplePayOnboardingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WalletOnboardingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ApplePayOnboardingStateImpl extends _ApplePayOnboardingState {
  const _$ApplePayOnboardingStateImpl() : super._();

  @override
  String toString() {
    return 'WalletOnboardingState.applePay()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplePayOnboardingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() googlePay,
    required TResult Function() applePay,
    required TResult Function() unknown,
  }) {
    return applePay();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? googlePay,
    TResult? Function()? applePay,
    TResult? Function()? unknown,
  }) {
    return applePay?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? googlePay,
    TResult Function()? applePay,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (applePay != null) {
      return applePay();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GooglePayOnboardingState value) googlePay,
    required TResult Function(_ApplePayOnboardingState value) applePay,
    required TResult Function(_UnknownWalletOnboardingState value) unknown,
  }) {
    return applePay(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GooglePayOnboardingState value)? googlePay,
    TResult? Function(_ApplePayOnboardingState value)? applePay,
    TResult? Function(_UnknownWalletOnboardingState value)? unknown,
  }) {
    return applePay?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GooglePayOnboardingState value)? googlePay,
    TResult Function(_ApplePayOnboardingState value)? applePay,
    TResult Function(_UnknownWalletOnboardingState value)? unknown,
    required TResult orElse(),
  }) {
    if (applePay != null) {
      return applePay(this);
    }
    return orElse();
  }
}

abstract class _ApplePayOnboardingState extends WalletOnboardingState {
  const factory _ApplePayOnboardingState() = _$ApplePayOnboardingStateImpl;
  const _ApplePayOnboardingState._() : super._();
}

/// @nodoc
abstract class _$$UnknownWalletOnboardingStateImplCopyWith<$Res> {
  factory _$$UnknownWalletOnboardingStateImplCopyWith(
          _$UnknownWalletOnboardingStateImpl value,
          $Res Function(_$UnknownWalletOnboardingStateImpl) then) =
      __$$UnknownWalletOnboardingStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$UnknownWalletOnboardingStateImplCopyWithImpl<$Res>
    extends _$WalletOnboardingStateCopyWithImpl<$Res,
        _$UnknownWalletOnboardingStateImpl>
    implements _$$UnknownWalletOnboardingStateImplCopyWith<$Res> {
  __$$UnknownWalletOnboardingStateImplCopyWithImpl(
      _$UnknownWalletOnboardingStateImpl _value,
      $Res Function(_$UnknownWalletOnboardingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of WalletOnboardingState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$UnknownWalletOnboardingStateImpl extends _UnknownWalletOnboardingState {
  const _$UnknownWalletOnboardingStateImpl() : super._();

  @override
  String toString() {
    return 'WalletOnboardingState.unknown()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownWalletOnboardingStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() googlePay,
    required TResult Function() applePay,
    required TResult Function() unknown,
  }) {
    return unknown();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? googlePay,
    TResult? Function()? applePay,
    TResult? Function()? unknown,
  }) {
    return unknown?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? googlePay,
    TResult Function()? applePay,
    TResult Function()? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GooglePayOnboardingState value) googlePay,
    required TResult Function(_ApplePayOnboardingState value) applePay,
    required TResult Function(_UnknownWalletOnboardingState value) unknown,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GooglePayOnboardingState value)? googlePay,
    TResult? Function(_ApplePayOnboardingState value)? applePay,
    TResult? Function(_UnknownWalletOnboardingState value)? unknown,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GooglePayOnboardingState value)? googlePay,
    TResult Function(_ApplePayOnboardingState value)? applePay,
    TResult Function(_UnknownWalletOnboardingState value)? unknown,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class _UnknownWalletOnboardingState extends WalletOnboardingState {
  const factory _UnknownWalletOnboardingState() =
      _$UnknownWalletOnboardingStateImpl;
  const _UnknownWalletOnboardingState._() : super._();
}
