import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/screens/create_shared_card/flow/create_shared_card_flow_cubit.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';

class SharedCardAgreementPage extends StatelessWidget {
  final VoidCallback? openTermsAndConditions;

  const SharedCardAgreementPage({
    required this.openTermsAndConditions,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = CardsLocalizations.of(context);
    const buttonSize = ButtonSize.medium;

    return Scaffold(
      appBar: const TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
        ),
      ),
      body: FixedButtonsPageLayout(
        onPrimaryButtonPressed: () => _onSubmitTermsAndConditions(context),
        model: FixedButtonsScrollablePageLayoutModel(
          buttonAlignmentMode:
              FixedButtonScrollablePageLayoutButtonAlignmentMode.aboveContent,
          primaryButton: FixedButtonsScrollablePageLayoutButton(
            label: l10n.sharedCardSignupScreenButtonTitle,
            size: buttonSize,
          ),
        ),
        child: ListView(
          children: [
            Label(
              model: LabelModel(
                text: l10n.sharedCardSignupScreenTitle,
                textStyle: CompanyTextStylePointer.h2medium,
                color: CompanyColorPointer.primary3,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s3),
            Label(
              model: LabelModel(
                text: l10n.sharedCardSignupScreenSubTitle,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.secondary1,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s6),
            ListDetailsContainer(
              model: ListDetailsContainerModel(
                items: [
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.sharedCardSignupScreenListItemSecond,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.arrow_north_east,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.sharedCardSignupScreenListItemOne,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.profile,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.sharedCardSignupScreenListItemThird,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.safe,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.sharedCardSignupScreenListItemFourth,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.notification,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: l10n.sharedCardSignupScreenListItemFifth,
                      tile: const TileModel.icon(
                        icon: GraphicAssetPointer.icon(
                          CompanyIconPointer.blocked,
                        ),
                        backgroundColor: CompanyColorPointer.surface7,
                        iconSize: CompanyIconSize.medium,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Space.fromSpacingVertical(Spacing.s7),
            _AgreementButton(openTermsAndConditions: openTermsAndConditions),
            SizedBox(height: Button.getButtonHeight(buttonSize)),
          ],
        ),
      ),
    );
  }

  void _onSubmitTermsAndConditions(BuildContext context) =>
      context.read<CreateSharedCardFlowCubit>().onAgreementScreenCompleted();
}

class _AgreementButton extends StatelessWidget {
  final VoidCallback? openTermsAndConditions;

  const _AgreementButton({required this.openTermsAndConditions});

  @override
  Widget build(BuildContext context) {
    final l10n = CardsLocalizations.of(context);

    return CompanyRichText(
      CompanyRichTextModel(
        text: l10n.sharedCardSignupScreenConditions(
          l10n.sharedCardSignupScreenTermsAndCondition,
          l10n.sharedCardSignupScreenButtonTitle,
        ),
        highlightedTextModels: [
          HighlightedTextModel(
            l10n.sharedCardSignupScreenTermsAndCondition,
            onTap: openTermsAndConditions,
          ),
        ],
        normalStyle: CompanyTextStylePointer.b3,
        normalTextColor: CompanyColorPointer.secondary4,
        accentStyle: CompanyTextStylePointer.b3,
        accentTextColor: CompanyColorPointer.primary1,
      ),
    );
  }
}
