import 'package:feature_cards_ui/src/screens/cards/card_view_model.dart';
import 'package:rxdart/rxdart.dart';

class CardsGuideTrigger {
  final String cardId;
  final CardViewModel? card;

  const CardsGuideTrigger.forCard(this.cardId, {this.card});
}

class CardsGuideMediator {
  // This will live as long as the app is running, no need in closing it
  // ignore: close_sinks
  final _eventStream = PublishSubject<CardsGuideTrigger>();

  Stream<CardsGuideTrigger> get onEvent => _eventStream;

  void trigger(CardsGuideTrigger trigger) => _eventStream.add(trigger);
}
