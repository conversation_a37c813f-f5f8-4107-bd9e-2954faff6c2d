import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';

part 'order_review_state.freezed.dart';

@freezed
class OrderReviewState with _$OrderReviewState {
  const factory OrderReviewState({
    String? cardId,
    String? accountId,
    CustomerAddress? address,
    String? firstLineName,
    String? secondLineName,
    String? nameOnChequebook,
    CreationFees? creationFees,
    @Default(<int>[]) List<int> chequebookLeafsAmount,
    @Default(true) bool actionEnabled,
    @Default(true) bool showChequebookName,
  }) = _OrderReviewState;
}
