import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/common/extensions.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

final class SpendingLimitSettingItem extends StatelessWidget {
  final SpendingLimitRecord? currentLimit;
  final VoidCallback? onPressed;

  const SpendingLimitSettingItem({
    this.currentLimit,
    this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListBox(
      onPressed: onPressed,
      listBoxModel: _getListBoxModel(context),
    );
  }

  ListBoxModel _getListBoxModel(BuildContext context) {
    final l10n = CardsLocalizations.of(context);
    final arrowIcon = Directionality.of(context).chevronIcon;
    final limit = currentLimit;

    return ListBoxModel(
      isBoxed: true,
      highlightInteraction: true,
      textModel: ListBoxTextModel(
        title: l10n.newVirtualCardScreenBoxSpendingLimitTitle,
        subtitle: limit == null
            ? l10n.newVirtualCardScreenUnfocusedHint
            : l10n.getFormattedSpendingLimit(limit),
        subtitleColor: limit == null
            ? CompanyColorPointer.secondary5
            : CompanyColorPointer.primary1,
      ),
      rightPartModel: ListBoxPartModel.icon(icon: arrowIcon.toGraphicAsset()),
    );
  }
}
