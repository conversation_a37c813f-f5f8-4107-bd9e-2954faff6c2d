import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:feature_cards_ui/l10n/cards_localization.g.dart';
import 'package:feature_cards_ui/src/assets.dart';
import 'package:feature_cards_ui/src/constants.dart';
import 'package:feature_cards_ui/src/screens/cards/card_view_model.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_fraud_ui/feature_fraud_mobile_ui.dart';

class BlockedCardBanner extends StatelessWidget {
  final CardViewModel cardViewModel;
  final VoidCallback? onButtonPressed;

  const BlockedCardBanner({
    required this.cardViewModel,
    this.onButtonPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = CardsLocalizations.of(context);
    if (FraudScope.of(context).isFraudReportEnabled) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
        child: CriticalNotificationCard(
          CriticalNotificationCardModel(
            title: localizations.cardsBlockedCardBannerTitle,
            actionName: _getCtaTitle(localizations),
            body: _getSubtitle(localizations),
            emojiModel: const CriticalNotificationCardEmojiModel(
              imagePath: Assets.secureImage,
              imagePackage: Constants.featureCardUiPackageName,
              imageSource: ImageSource.asset,
            ),
          ),
          onTap: onButtonPressed,
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
      child: ProductsInsightCard(
        ProductsInsightCardModel(
          tileModel: const TileModel.image(
            backgroundColor: CompanyColorPointer.secondary6,
            paddingToImage: 10.0,
            image: ImageInTileModel(
              path: Assets.pinImage,
              package: Constants.featureCardUiPackageName,
              source: ImageSource.asset,
            ),
          ),
          title: LabelModel(
            text: localizations.cardsBlockedCardBannerTitle,
            textStyle: CompanyTextStylePointer.b2,
            color: CompanyColorPointer.primary3,
          ),
          subtitle: LabelModel(
            text: _getSubtitle(localizations),
            textStyle: CompanyTextStylePointer.b3,
            color: CompanyColorPointer.secondary4,
          ),
          cta: ButtonModel(
            title: _getCtaTitle(localizations),
            loading: cardViewModel.cardUiState == CardUiState.unlocking,
          ),
        ),
        onCtaPressed: onButtonPressed,
      ),
    );
  }

  String _getSubtitle(CardsLocalizations localizations) {
    switch (cardViewModel.cardStatus) {
      case CardStatus.blockedBySupport:
        return localizations.cardsBlockedCardBannerBlockedBySupportSubtitle(
          cardViewModel.cardLastDigits,
        );
      case CardStatus.pinBlocked:
        return localizations.cardsBlockedCardBannerBlockedByPinSubtitle(
          cardViewModel.cardLastDigits,
        );
      default:
        throw UnsupportedError('${cardViewModel.cardStatus} is wrong');
    }
  }

  String _getCtaTitle(CardsLocalizations localizations) {
    switch (cardViewModel.cardStatus) {
      case CardStatus.blockedBySupport:
        return localizations.cardsBlockedCardBannerBlockedBySupportCta;
      case CardStatus.pinBlocked:
        return localizations.cardsBlockedCardBannerBlockedByPinCta;
      default:
        throw UnsupportedError('${cardViewModel.cardStatus} is wrong');
    }
  }
}
