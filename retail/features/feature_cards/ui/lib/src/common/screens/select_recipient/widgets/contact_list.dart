part of '../select_recipient_page.dart';

class _ContactList extends StatelessWidget {
  final List<RecipientContact> contacts;
  final RecipientContact? selectedContact;
  final ValueSetter<RecipientContact>? onSelect;

  const _ContactList({
    required this.contacts,
    this.selectedContact,
    this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
      itemCount: contacts.length,
      itemBuilder: _buildContact,
      separatorBuilder: (_, __) => Space.fromSpacingVertical(Spacing.s2),
    );
  }

  Widget _buildContact(BuildContext context, int index) {
    final contact = contacts[index];

    return _ContactItem(
      onPressed: onSelect == null ? null : () => onSelect?.call(contact),
      phoneNumber: contact.phoneNumber.toFormatPhoneNumber(),
      name: contact.name?.trim() ?? '',
      selected: contact == selectedContact,
    );
  }
}

class _LoadingContactList extends StatelessWidget {
  final int itemCount;

  const _LoadingContactList({this.itemCount = 4});

  @override
  Widget build(BuildContext context) {
    return SliverList.separated(
      itemCount: itemCount,
      itemBuilder: (_, __) => const CompanyShimmer(
        model: CompanyShimmerModel(),
        child: _ContactItem(phoneNumber: '_', name: '_'),
      ),
      separatorBuilder: (_, __) => Space.fromSpacingVertical(Spacing.s2),
    );
  }
}

class _ContactItem extends StatelessWidget {
  static const _avatarSize = 44.0;

  final String phoneNumber;
  final String name;
  final bool selected;
  final VoidCallback? onPressed;

  const _ContactItem({
    required this.phoneNumber,
    required this.name,
    this.selected = false,
    this.onPressed,
  });

  bool get _hasName => name.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final title = _hasName ? name : phoneNumber;
    final subtitle = _hasName ? phoneNumber : null;

    return ListBox(
      onPressed: onPressed,
      listBoxModel: ListBoxModel(
        textModel: ListBoxTextModel(
          title: title,
          subtitle: subtitle,
          subtitleColor: CompanyColorPointer.secondary5,
        ),
        leftPartModel: ListBoxPartModel.tile(_iconModel, size: _avatarSize),
        rightPartModel: selected
            ? const ListBoxPartModel.icon(
                icon: GraphicAssetPointer.icon(CompanyIconPointer.success),
                iconSize: CompanyIconSize.medium,
              )
            : null,
        applyPadding: true,
        backgroundColor: CompanyColorPointer.surface2,
        borderRadius: const BorderRadius.all(Radius.circular(8)),
      ),
    );
  }

  TileModel get _iconModel {
    if (_hasName) {
      return TileModel.label(
        labelModel: LabelModel(
          text: name.acronym(),
          color: CompanyColorPointer.primary3,
          textStyle: CompanyTextStylePointer.b2,
          maxLines: 1,
          textAlign: LabelTextAlign.center,
          disableScale: true,
        ),
        backgroundColor: CompanyColorPointer.surface13,
        borderRadius: _avatarSize,
      );
    }

    return const TileModel.icon(
      icon: GraphicAssetPointer.icon(CompanyIconPointer.profile),
      backgroundColor: CompanyColorPointer.surface13,
      borderRadius: _avatarSize,
    );
  }
}
