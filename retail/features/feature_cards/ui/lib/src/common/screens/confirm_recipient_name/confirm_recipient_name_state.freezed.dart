// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'confirm_recipient_name_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConfirmRecipientNameState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String name) idle,
    required TResult Function(String name) submitting,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String name)? idle,
    TResult? Function(String name)? submitting,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String name)? idle,
    TResult Function(String name)? submitting,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConfirmRecipientNameInitialState value) initial,
    required TResult Function(_ConfirmRecipientNameIdleState value) idle,
    required TResult Function(_ConfirmRecipientNameSubmittingState value)
        submitting,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult? Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult? Function(_ConfirmRecipientNameSubmittingState value)? submitting,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult Function(_ConfirmRecipientNameSubmittingState value)? submitting,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConfirmRecipientNameStateCopyWith<$Res> {
  factory $ConfirmRecipientNameStateCopyWith(ConfirmRecipientNameState value,
          $Res Function(ConfirmRecipientNameState) then) =
      _$ConfirmRecipientNameStateCopyWithImpl<$Res, ConfirmRecipientNameState>;
}

/// @nodoc
class _$ConfirmRecipientNameStateCopyWithImpl<$Res,
        $Val extends ConfirmRecipientNameState>
    implements $ConfirmRecipientNameStateCopyWith<$Res> {
  _$ConfirmRecipientNameStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ConfirmRecipientNameInitialStateImplCopyWith<$Res> {
  factory _$$ConfirmRecipientNameInitialStateImplCopyWith(
          _$ConfirmRecipientNameInitialStateImpl value,
          $Res Function(_$ConfirmRecipientNameInitialStateImpl) then) =
      __$$ConfirmRecipientNameInitialStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConfirmRecipientNameInitialStateImplCopyWithImpl<$Res>
    extends _$ConfirmRecipientNameStateCopyWithImpl<$Res,
        _$ConfirmRecipientNameInitialStateImpl>
    implements _$$ConfirmRecipientNameInitialStateImplCopyWith<$Res> {
  __$$ConfirmRecipientNameInitialStateImplCopyWithImpl(
      _$ConfirmRecipientNameInitialStateImpl _value,
      $Res Function(_$ConfirmRecipientNameInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConfirmRecipientNameInitialStateImpl
    implements _ConfirmRecipientNameInitialState {
  const _$ConfirmRecipientNameInitialStateImpl();

  @override
  String toString() {
    return 'ConfirmRecipientNameState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConfirmRecipientNameInitialStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String name) idle,
    required TResult Function(String name) submitting,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String name)? idle,
    TResult? Function(String name)? submitting,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String name)? idle,
    TResult Function(String name)? submitting,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConfirmRecipientNameInitialState value) initial,
    required TResult Function(_ConfirmRecipientNameIdleState value) idle,
    required TResult Function(_ConfirmRecipientNameSubmittingState value)
        submitting,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult? Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult? Function(_ConfirmRecipientNameSubmittingState value)? submitting,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult Function(_ConfirmRecipientNameSubmittingState value)? submitting,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _ConfirmRecipientNameInitialState
    implements ConfirmRecipientNameState {
  const factory _ConfirmRecipientNameInitialState() =
      _$ConfirmRecipientNameInitialStateImpl;
}

/// @nodoc
abstract class _$$ConfirmRecipientNameIdleStateImplCopyWith<$Res> {
  factory _$$ConfirmRecipientNameIdleStateImplCopyWith(
          _$ConfirmRecipientNameIdleStateImpl value,
          $Res Function(_$ConfirmRecipientNameIdleStateImpl) then) =
      __$$ConfirmRecipientNameIdleStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String name});
}

/// @nodoc
class __$$ConfirmRecipientNameIdleStateImplCopyWithImpl<$Res>
    extends _$ConfirmRecipientNameStateCopyWithImpl<$Res,
        _$ConfirmRecipientNameIdleStateImpl>
    implements _$$ConfirmRecipientNameIdleStateImplCopyWith<$Res> {
  __$$ConfirmRecipientNameIdleStateImplCopyWithImpl(
      _$ConfirmRecipientNameIdleStateImpl _value,
      $Res Function(_$ConfirmRecipientNameIdleStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
  }) {
    return _then(_$ConfirmRecipientNameIdleStateImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ConfirmRecipientNameIdleStateImpl
    implements _ConfirmRecipientNameIdleState {
  const _$ConfirmRecipientNameIdleStateImpl({required this.name});

  @override
  final String name;

  @override
  String toString() {
    return 'ConfirmRecipientNameState.idle(name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConfirmRecipientNameIdleStateImpl &&
            (identical(other.name, name) || other.name == name));
  }

  @override
  int get hashCode => Object.hash(runtimeType, name);

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConfirmRecipientNameIdleStateImplCopyWith<
          _$ConfirmRecipientNameIdleStateImpl>
      get copyWith => __$$ConfirmRecipientNameIdleStateImplCopyWithImpl<
          _$ConfirmRecipientNameIdleStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String name) idle,
    required TResult Function(String name) submitting,
  }) {
    return idle(name);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String name)? idle,
    TResult? Function(String name)? submitting,
  }) {
    return idle?.call(name);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String name)? idle,
    TResult Function(String name)? submitting,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(name);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConfirmRecipientNameInitialState value) initial,
    required TResult Function(_ConfirmRecipientNameIdleState value) idle,
    required TResult Function(_ConfirmRecipientNameSubmittingState value)
        submitting,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult? Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult? Function(_ConfirmRecipientNameSubmittingState value)? submitting,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult Function(_ConfirmRecipientNameSubmittingState value)? submitting,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _ConfirmRecipientNameIdleState
    implements ConfirmRecipientNameState {
  const factory _ConfirmRecipientNameIdleState({required final String name}) =
      _$ConfirmRecipientNameIdleStateImpl;

  String get name;

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConfirmRecipientNameIdleStateImplCopyWith<
          _$ConfirmRecipientNameIdleStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConfirmRecipientNameSubmittingStateImplCopyWith<$Res> {
  factory _$$ConfirmRecipientNameSubmittingStateImplCopyWith(
          _$ConfirmRecipientNameSubmittingStateImpl value,
          $Res Function(_$ConfirmRecipientNameSubmittingStateImpl) then) =
      __$$ConfirmRecipientNameSubmittingStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String name});
}

/// @nodoc
class __$$ConfirmRecipientNameSubmittingStateImplCopyWithImpl<$Res>
    extends _$ConfirmRecipientNameStateCopyWithImpl<$Res,
        _$ConfirmRecipientNameSubmittingStateImpl>
    implements _$$ConfirmRecipientNameSubmittingStateImplCopyWith<$Res> {
  __$$ConfirmRecipientNameSubmittingStateImplCopyWithImpl(
      _$ConfirmRecipientNameSubmittingStateImpl _value,
      $Res Function(_$ConfirmRecipientNameSubmittingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = null,
  }) {
    return _then(_$ConfirmRecipientNameSubmittingStateImpl(
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ConfirmRecipientNameSubmittingStateImpl
    implements _ConfirmRecipientNameSubmittingState {
  const _$ConfirmRecipientNameSubmittingStateImpl({required this.name});

  @override
  final String name;

  @override
  String toString() {
    return 'ConfirmRecipientNameState.submitting(name: $name)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConfirmRecipientNameSubmittingStateImpl &&
            (identical(other.name, name) || other.name == name));
  }

  @override
  int get hashCode => Object.hash(runtimeType, name);

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConfirmRecipientNameSubmittingStateImplCopyWith<
          _$ConfirmRecipientNameSubmittingStateImpl>
      get copyWith => __$$ConfirmRecipientNameSubmittingStateImplCopyWithImpl<
          _$ConfirmRecipientNameSubmittingStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(String name) idle,
    required TResult Function(String name) submitting,
  }) {
    return submitting(name);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(String name)? idle,
    TResult? Function(String name)? submitting,
  }) {
    return submitting?.call(name);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(String name)? idle,
    TResult Function(String name)? submitting,
    required TResult orElse(),
  }) {
    if (submitting != null) {
      return submitting(name);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ConfirmRecipientNameInitialState value) initial,
    required TResult Function(_ConfirmRecipientNameIdleState value) idle,
    required TResult Function(_ConfirmRecipientNameSubmittingState value)
        submitting,
  }) {
    return submitting(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult? Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult? Function(_ConfirmRecipientNameSubmittingState value)? submitting,
  }) {
    return submitting?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ConfirmRecipientNameInitialState value)? initial,
    TResult Function(_ConfirmRecipientNameIdleState value)? idle,
    TResult Function(_ConfirmRecipientNameSubmittingState value)? submitting,
    required TResult orElse(),
  }) {
    if (submitting != null) {
      return submitting(this);
    }
    return orElse();
  }
}

abstract class _ConfirmRecipientNameSubmittingState
    implements ConfirmRecipientNameState {
  const factory _ConfirmRecipientNameSubmittingState(
      {required final String name}) = _$ConfirmRecipientNameSubmittingStateImpl;

  String get name;

  /// Create a copy of ConfirmRecipientNameState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConfirmRecipientNameSubmittingStateImplCopyWith<
          _$ConfirmRecipientNameSubmittingStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
