import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'card_details.freezed.dart';

@freezed
class CardDetails with _$CardDetails {
  const factory CardDetails({
    required String id,
    required String fullPan,
    required String cvv,
    required DateTime expiryDate,
    required String name,
    required CardType cardType,
  }) = _CardDetails;
}
