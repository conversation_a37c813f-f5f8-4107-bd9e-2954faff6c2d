import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'convert_to_shared_card_input.freezed.dart';

/// Input class that holds all the data needed for converting
/// a virtual card to a shared one.
@freezed
class ConvertToSharedCardInput with _$ConvertToSharedCardInput {
  const factory ConvertToSharedCardInput({
    required String recipientName,
    required String recipientPhoneNumber,
    required SpendingLimitRecord spendingLimit,
    String? imageId,
    String? accountId,
  }) = _ConvertToSharedCardInput;
}
