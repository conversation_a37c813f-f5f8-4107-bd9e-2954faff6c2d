import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_cards_impl/data/mappers/card_mapper.dart';
import 'package:wio_feature_cards_impl/data/models/cards_swagger.swagger.dart'
    as dtos;
import 'package:wio_feature_cards_impl/data/models/cards_swagger.swagger.dart';
import 'package:wio_feature_cards_impl/data/repositories/card_storage.dart';
import 'package:wio_feature_cards_impl/data/repositories/shared_card_repository.dart';
import 'package:wio_feature_cards_impl/data/services/card_service.dart';

import '../mocks.dart';

void main() {
  final mapper = CardMapperImpl(
    cardOwnerResolver: MockCardOwnerResolver(),
    reporter: MockErrorReporter(),
  );
  late CardService service;
  late CardStorageRefresher refresher;
  late SharedCardRepository repository;

  setUp(() {
    service = MockCardService();
    refresher = MockCardStorageRefresher();
    repository = SharedCardRepositoryImpl(
      service: service,
      mapper: mapper,
      cardsRefresher: refresher,
    );
  });

  group('To shared card conversion >', () {
    final cardId = randomString();
    final name = randomString();
    final phone = randomString();
    final input = ConvertToSharedCardInput(
      recipientName: name,
      recipientPhoneNumber: phone,
      spendingLimit: SpendingLimitRecord(
        limit: Money.fromNumWithCurrency(42, Currency.aed),
        frequency: CardSpendingLimitFrequency.weekly,
      ),
    );
    final request = dtos.ConvertToSharedCardRequest(
      beneficiaryPhoneNumber: phone,
      beneficiaryName: name,
      spendingLimit: const dtos.SpendingLimit(
        amount: 42,
        type: dtos.SpendingLimitType.weekly,
      ),
    );

    test('converts a card to a shared one', () async {
      // Arrange
      when(
        () => service.convertToSharedCard(cardId: cardId, request: request),
      ).justCompleteAsync();

      // Act
      await repository.convertToSharedCard(cardId: cardId, input: input);

      // Assert
      verifyInOrder([
        () => service.convertToSharedCard(cardId: cardId, request: request),
        () => refresher.refreshCards(),
      ]);
    });

    test('verify OTP flow', () async {
      const number = '+971586866907';
      const name = 'name';
      const request = VerifySharedCardOtpRequest(
        beneficiaryName: name,
        beneficiaryPhoneNumber: number,
      );
      // Arrange
      when(
        () => service.verifyOtp(request: request),
      ).justCompleteAsync();

      // Act
      await repository.verifyOtp(contactNumber: number, contactName: name);

      // Assert
      verifyInOrder([
        () => service.verifyOtp(request: request),
      ]);
    });
  });
}
