import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_wallet_api/wallet_api.dart' as wallet;

abstract class CardRepository implements wallet.WalletRepository, Clearable {
  Future<bool> hasUserAlreadySeenAppleWalletSplash();

  Future<void> markUserAsSeenAppleWalletSplash();

  Future<bool> getHasUserSeenCardCustomizationCoachMark();

  Future<void> markUserAsSeenCardCustomizationCoachMark();

  Future<bool> getHasUserSeenCardDetailsSecurityCoachMark();

  Future<void> markUserAsSeenCardDetailsSecurityCoachMark();

  Stream<Data<List<Card>>> getCards({
    required bool refresh,
  });

  Future<Card> getCard(String cardId);

  Future<Card> createNewVirtualCard({
    required CreateVirtualCardInput input,
  });

  Future<Card> createNewSharedCard({
    required CreateSharedCardInput input,
  });

  Future<void> freezeCard(String cardId);

  Future<void> unfreezeCard(String cardId);

  Future<CardDetails> getEncryptedCardDetails(String cardId);

  Future<void> deleteCard(String cardId);

  Future<void> activateCard(String cardId);

  Future<List<CardImage>> getCardImages({CardType? cardType});

  Future<CardConfig> getCardConfig({
    required CardType cardType,
  });

  Future<CardDetails> getSharedCardDetails({required String cardId});

  Future<bool> verifyEid({required EidInputDetails eidInputDetails});

  Future<List<Card>> getAllCards();

  Future<Card> createParticipantCard({
    required CreateParticipantCardInput input,
  });
}
