import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:quiver/collection.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_feature_cards_impl/data/mappers/card_mapper.dart';
import 'package:wio_feature_cards_impl/data/repositories/order_card_repository.dart';
import 'package:wio_feature_cards_impl/data/services/card_service.dart';

class OrderCardRepositoryImpl implements OrderCardRepository {
  final CardService _service;
  final CardMapper _cardMapper;

  final _orderCardRecordsCache =
      LruMap<String, OrderCardRecord>(maximumSize: 15);

  OrderCardRepositoryImpl({
    required CardService cardService,
    required CardMapper cardMapper,
  })  : _service = cardService,
        _cardMapper = cardMapper;

  @override
  Future<void> convertDigitalCardToPhysical({
    required String cardId,
    required CustomerAddress address,
    String? firstName,
    String? lastName,
  }) async {
    final request = _cardMapper.mapToConvertToPhysicalCardRequest(
      address,
      firstName,
      lastName,
    );

    await _service.convertToPhysicalCard(
      cardId,
      request,
    );
  }

  @override
  Future<OrderCardRecord?> getOrderCardRecord(String cardID) {
    return Future.sync(() {
      return _orderCardRecordsCache[cardID];
    });
  }

  @override
  Future<void> putOrderCardRecord(String cardId, OrderCardRecord? record) {
    return Future.sync(() {
      if (record == null) {
        _orderCardRecordsCache.remove(cardId);
      } else {
        _orderCardRecordsCache[cardId] = record;
      }
    });
  }

  @override
  Future<void> clear() {
    return Future.sync(_orderCardRecordsCache.clear);
  }
}
