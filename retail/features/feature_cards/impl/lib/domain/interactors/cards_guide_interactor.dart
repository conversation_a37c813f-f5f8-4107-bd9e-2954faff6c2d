import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:wio_feature_cards_impl/data/repositories/cards_guide_repository.dart';

class CardsGuideInteractorImpl implements CardsGuideInteractor {
  final CardsGuideRepository _repository;

  const CardsGuideInteractorImpl({
    required CardsGuideRepository repository,
  }) : _repository = repository;

  @override
  Future<bool> hasGuideForManageCardSharing() =>
      _repository.hasGuideForManageCardSharing();

  @override
  void markGuideForManageCardSharingCompleted() =>
      _repository.markGuideForManageCardSharingCompleted();

  @override
  Future<void> clear() => _repository.clear();
}
