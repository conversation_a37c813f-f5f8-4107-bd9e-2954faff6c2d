import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/feature_consent_authorization_ui_desktop.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/helpers.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/singular_consent_details/common_widgets.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/singular_consent_details/singluar_consent_details_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/widgets.dart';
import 'package:wio_feature_core_ui_desktop/baas_locale/baas_locale_widget.dart';

class SingularConsentDetailsMobileLayout extends StatelessWidget {
  final ConsentModel consent;
  final String clientId;

  const SingularConsentDetailsMobileLayout({
    required this.consent,
    required this.clientId,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final companyTheme = CompanyThemeProvider.of(context);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 48),
            const BaasLocaleWidget(),
            const SizedBox(height: 48),
            SingularConsentDetailsTitle(
              companyName: getCompanyName(consent.client?.id ?? ''),
              titleTextStyle: companyTheme.textStyles.h4medium,
            ),
            const SizedBox(height: 16),
            ConsentAuthorizationAccount(consent: consent),
            const SizedBox(height: 16),
            const SingularConsentDetailsRevokeButton(),
            const SizedBox(height: 16),
            const SingularConsentDetailsCancelButton(),
            const SizedBox(height: 24),
            const SingularConsentDetailsAcknowledgmentsInfo(),
            const SizedBox(height: 24),
            ConsentDetailsContactSupportInfo(
              onNavigateToContactSupport: () =>
                  _onNavigateToContactSupport(context),
            ),
            const SizedBox(height: 48),
          ],
        ),
      ),
    );
  }

  void _onNavigateToContactSupport(BuildContext context) {
    context.read<SingularConsentDetailsCubit>().onClickSupport();

    return const ContactSupportDrawerNotification().dispatch(context);
  }
}
