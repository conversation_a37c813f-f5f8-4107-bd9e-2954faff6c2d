// ignore_for_file: constant_identifier_names
enum ConsentEvent {
  open_link,
  regenerate_otp,
  on_consent,
  already_given_consent,
  consent_management,
  navigate_to_redirect_url,
  open_link_again,
  on_support,
  toggle_account_payout,
  failed_to_pass_otp,
  skip_two_factor_authentication,
  expand_account_details,
  expand_payout_consent,
  on_consent_detail,
  view_consent,
  click_support,
  view,
  revoke_all_consent,
  revoke_all_company_consent,
  revoke_consent,
  login_without_otp_successful,
  allow_consent,
  deny_consent,
  click_authorize_button,
  deny_payment,
  scroll_account,
  scroll_payout,
  click_back_otp,
  toggle_payout,
  toggle_account,
  cancel_revoke_consent,
  cancel_company_revoke_consent,
  cancel_revoke_all_consent,
  update_client_id,
  logout,
  go_back_from_consent_details,
}
