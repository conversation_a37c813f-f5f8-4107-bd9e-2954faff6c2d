import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/helpers.dart';

part 'consent_authorization_state.freezed.dart';

@freezed
class ConsentAuthorizationState with _$ConsentAuthorizationState {
  const factory ConsentAuthorizationState.loaded({
    required String companyName,
    required String consentId,
    required ConsentModel consent,
    required String requestParam,
    required String id,
    required List<BaasAccount> accountInfoItems,
    required List<BaasAccount> paymentItems,
    required String clientId,
    required AuthorizationPayload redirectAuthorization,
    @Default(false) bool companyConsentAlreadyGiven,
    @Default(<String>[]) List<String> selectedConsent,
    @Default(<String>[]) List<String> selectedPayout,
    @Default(ButtonVisibleFor.logout) ButtonVisibleFor buttonVisibleFor,
  }) = ConsentLoadedState;

  const factory ConsentAuthorizationState.empty() = _Empty;

  const factory ConsentAuthorizationState.showError({
    Object? error,
    @Default(ErrorBy.exception) ErrorBy errorBy,
  }) = _ConsentAuthorizationStateShowErrorState;
}
