import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_common_feature_consent_authorization_api/consent_authorization_api.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/company_consent_details/company_consent_details_cubit.dart';
import 'package:wio_feature_consent_authorization_ui_desktop/src/screens/company_consent_details/company_consent_details_state.dart';

import '../mocks.dart';
import '../stubs.dart';

void main() {
  late MockNavigationProvider mockNavigationProvider;
  late CompanyConsentDetailsCubit companyConsentDetailsBloc;
  late MockConsentInteractor mockConsentInteractor;
  late MockFeatureToggleProvider mockFeatureToggleProvider;
  late MockConsentsAnalytics mockAnalytics;
  late MockAccountInteractor mockAccountInteractor;

  setUp(() {
    mockNavigationProvider = MockNavigationProvider();
    mockConsentInteractor = MockConsentInteractor();
    mockFeatureToggleProvider = MockFeatureToggleProvider();
    mockAnalytics = MockConsentsAnalytics();
    mockAccountInteractor = MockAccountInteractor();

    companyConsentDetailsBloc = CompanyConsentDetailsCubit(
      consentInteractor: mockConsentInteractor,
      navigator: mockNavigationProvider,
      analytics: mockAnalytics,
    );

    registerFallbackValue(AuthorizeConsentRequestModelFake());

    when(() => mockFeatureToggleProvider.fetchConfigurations())
        .justCompleteAsync();
  });

  blocTest<CompanyConsentDetailsCubit, CompanyConsentDetailsState>(
    'initialize',
    build: () => companyConsentDetailsBloc,
    act: (cubit) => cubit.initialize(ConsentDetailStubs.clientId),
    expect: () => <CompanyConsentDetailsState>[
      CompanyConsentDetailsState.loaded(
        clientId: ConsentDetailStubs.clientId,
      ),
    ],
    verify: (bloc) {
      verifyNoMoreInteractions(mockConsentInteractor);
      verifyZeroInteractions(mockNavigationProvider);
    },
  );

  blocTest<CompanyConsentDetailsCubit, CompanyConsentDetailsState>(
    'bulk revokes',
    build: () => companyConsentDetailsBloc,
    setUp: () {
      when(() => mockNavigationProvider.navigateTo(captureAny(), replace: true))
          .thenAnswer((_) async => Object());

      when(
        () => mockConsentInteractor.revokeAll(
          revokeModel: BulkRevokeModel.byClientId(
            clientId: ConsentDetailStubs.clientId,
          ),
        ),
      ).thenAnswer((_) async => {});
    },
    act: (cubit) => cubit.revokeAll(),
    expect: () => <CompanyConsentDetailsState>[],
    verify: (bloc) {
      verifyNoMoreInteractions(mockAccountInteractor);
    },
  );

  blocTest<CompanyConsentDetailsCubit, CompanyConsentDetailsState>(
    'go to consent works',
    build: () => companyConsentDetailsBloc,
    act: (cubit) => cubit.goToConsent(
      consentId: ConsentDetailStubs.consentId,
      clientId: ConsentDetailStubs.clientId,
    ),
    expect: () => <CompanyConsentDetailsState>[],
    verify: (bloc) {
      verifyNoMoreInteractions(mockConsentInteractor);
      verifyNoMoreInteractions(mockNavigationProvider);
    },
  );
}
