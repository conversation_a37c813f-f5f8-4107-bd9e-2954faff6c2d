import 'package:di/di.dart';
import 'package:feature_auth_api/domain/auth_interactor.dart';
import 'package:feature_device_identity_api/index.dart';
import 'package:flutter/widgets.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_encryption_api/index.dart';
import 'package:wio_feature_two_factor_auth_api/index.dart';
import 'package:wio_feature_two_factor_auth_ui/l10n/two_factor_auth_localization.g.dart';
import 'package:wio_feature_two_factor_auth_ui/src/delegates/two_fa_solve_delegate_impl.dart';
import 'package:wio_feature_two_factor_auth_ui/src/delegates/two_factor_challenge_solver_delegate_analytics.dart';
import 'package:wio_feature_two_factor_auth_ui/src/navigation/two_factor_auth_router.dart';
import 'package:wio_feature_two_factor_auth_ui/src/navigation_handler/liveness_navigation_handler_impl.dart';
import 'package:wio_feature_two_factor_auth_ui/src/navigation_handler/otp_navigation_handler_impl.dart';
import 'package:wio_feature_two_factor_auth_ui/src/navigation_handler/passcode_navigation_handler_impl.dart';

class TwoFactorAuthFeatureDependencyModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton(TwoFactorAuthRouter.new);

    DependencyProvider.registerLazySingleton(
      () =>
          TwoFactorAuthLocalization.of(DependencyProvider.get<BuildContext>()),
    );

    DependencyProvider.registerLazySingleton<NavigationRouter>(
      () => DependencyProvider.get<TwoFactorAuthRouter>(),
      instanceName: TwoFactorAuthFeatureNavigationConfig.name,
    );

    _challengeSolverDelegate();
  }

  static void _challengeSolverDelegate() {
    DependencyProvider.registerFactory<
        TwoFactorChallengeSolverDelegateAnalytics>(
      () => TwoFactorChallengeSolverDelegateAnalytics(
        analyticsAbstractTrackerFactory:
            DependencyProvider.get<AnalyticsAbstractTrackerFactory>(),
      ),
    );

    DependencyProvider.registerFactory<LivenessNavigationTwoFaHandlerBase>(
      () => LivenessNavigationHandlerImpl(),
    );

    DependencyProvider.registerFactory<PasscodeNavigationTwoFaHandlerBase>(
      () => PasscodeNavigationHandlerImpl(),
    );

    DependencyProvider.registerFactory<OtpNavigationTwoFaHandlerBase>(
      () => OtpNavigationHandlerImpl(),
    );

    DependencyProvider.registerLazySingleton<TwoFaSolveDelegate>(
      () => TwoFaSolveDelegateImpl(
        bindDeviceInteractor: DependencyProvider.get<BindDeviceInteractor>(),
        keyPairRsaInteractor: DependencyProvider.get<KeyPairRsaInteractor>(),
        navigationProvider: DependencyProvider.get<NavigationProvider>(),
        logger: _identityLogger,
        twoFactorAuthInteractor:
            DependencyProvider.get<TwoFactorAuthInteractor>(),
        authInteractor: DependencyProvider.get<AuthInteractor>(),
        analytics:
            DependencyProvider.get<TwoFactorChallengeSolverDelegateAnalytics>(),
        livenessNavigationTwoFaHandlerBase:
            DependencyProvider.get<LivenessNavigationTwoFaHandlerBase>(),
        otpNavigationTwoFaHandlerBase:
            DependencyProvider.get<OtpNavigationTwoFaHandlerBase>(),
        passcodeNavigationTwoFaHandlerBase:
            DependencyProvider.get<PasscodeNavigationTwoFaHandlerBase>(),
        localization: DependencyProvider.get<TwoFactorAuthLocalization>(),
      ),
    );
  }

  static Logger get _identityLogger => DependencyProvider.get<Logger>(
        instanceName: WioDomain.identity.name,
      );
}
