import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_deposits_api/feature_deposits_api.dart';
import 'package:wio_feature_deposits_ui/bottom_sheet/search_locations/widgets/locations_list.dart';
import 'package:wio_feature_deposits_ui/l10n/deposits_ui_localization.g.dart';

class SearchLocationsBottomSheet extends StatefulWidget {
  final List<Location> fabLocations;

  const SearchLocationsBottomSheet({
    required this.fabLocations,
    super.key,
  });

  @override
  State<SearchLocationsBottomSheet> createState() =>
      _SearchLocationsBottomSheetState();
}

class _SearchLocationsBottomSheetState
    extends State<SearchLocationsBottomSheet> {
  final _controller = TextEditingController();

  String get searchString => _controller.text;

  List<Location> get locations => searchString.isEmpty
      ? widget.fabLocations
      : widget.fabLocations.where(
          (location) {
            final address = location.address?.toString() ?? '';

            return [location.branchName, address]
                .any((e) => e.containsIgnoreCase(_controller.text));
          },
        ).toList();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = DepositsUiLocalizations.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Label(
            model: LabelModel(
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
              text: l10n.searchLocationsTitle,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(24, 16, 24, 13),
          child: SearchField(
            model: const SearchFieldModel(),
            textEditingController: _controller,
            onChanged: (_) => setState(() {}),
          ),
        ),
        Expanded(
          child: LocationsList(locations: locations),
        ),
      ],
    );
  }
}
