// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$Location {
  String get id => throw _privateConstructorUsedError;
  double get latitude => throw _privateConstructorUsedError;
  double get longitude => throw _privateConstructorUsedError;
  String get branchName => throw _privateConstructorUsedError;
  FabAddress? get address => throw _privateConstructorUsedError;
  Distance? get distance => throw _privateConstructorUsedError;
  String? get openingTime => throw _privateConstructorUsedError;
  dynamic get hasATM => throw _privateConstructorUsedError;
  dynamic get hasCashDeposits => throw _privateConstructorUsedError;
  dynamic get isBranch => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $LocationCopyWith<Location> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationCopyWith<$Res> {
  factory $LocationCopyWith(Location value, $Res Function(Location) then) =
      _$LocationCopyWithImpl<$Res, Location>;
  @useResult
  $Res call(
      {String id,
      double latitude,
      double longitude,
      String branchName,
      FabAddress? address,
      Distance? distance,
      String? openingTime,
      dynamic hasATM,
      dynamic hasCashDeposits,
      dynamic isBranch});

  $FabAddressCopyWith<$Res>? get address;
  $DistanceCopyWith<$Res>? get distance;
}

/// @nodoc
class _$LocationCopyWithImpl<$Res, $Val extends Location>
    implements $LocationCopyWith<$Res> {
  _$LocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? branchName = null,
    Object? address = freezed,
    Object? distance = freezed,
    Object? openingTime = freezed,
    Object? hasATM = freezed,
    Object? hasCashDeposits = freezed,
    Object? isBranch = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      branchName: null == branchName
          ? _value.branchName
          : branchName // ignore: cast_nullable_to_non_nullable
              as String,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as FabAddress?,
      distance: freezed == distance
          ? _value.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as Distance?,
      openingTime: freezed == openingTime
          ? _value.openingTime
          : openingTime // ignore: cast_nullable_to_non_nullable
              as String?,
      hasATM: freezed == hasATM
          ? _value.hasATM
          : hasATM // ignore: cast_nullable_to_non_nullable
              as dynamic,
      hasCashDeposits: freezed == hasCashDeposits
          ? _value.hasCashDeposits
          : hasCashDeposits // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isBranch: freezed == isBranch
          ? _value.isBranch
          : isBranch // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $FabAddressCopyWith<$Res>? get address {
    if (_value.address == null) {
      return null;
    }

    return $FabAddressCopyWith<$Res>(_value.address!, (value) {
      return _then(_value.copyWith(address: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $DistanceCopyWith<$Res>? get distance {
    if (_value.distance == null) {
      return null;
    }

    return $DistanceCopyWith<$Res>(_value.distance!, (value) {
      return _then(_value.copyWith(distance: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_LocationCopyWith<$Res> implements $LocationCopyWith<$Res> {
  factory _$$_LocationCopyWith(
          _$_Location value, $Res Function(_$_Location) then) =
      __$$_LocationCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      double latitude,
      double longitude,
      String branchName,
      FabAddress? address,
      Distance? distance,
      String? openingTime,
      dynamic hasATM,
      dynamic hasCashDeposits,
      dynamic isBranch});

  @override
  $FabAddressCopyWith<$Res>? get address;
  @override
  $DistanceCopyWith<$Res>? get distance;
}

/// @nodoc
class __$$_LocationCopyWithImpl<$Res>
    extends _$LocationCopyWithImpl<$Res, _$_Location>
    implements _$$_LocationCopyWith<$Res> {
  __$$_LocationCopyWithImpl(
      _$_Location _value, $Res Function(_$_Location) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? branchName = null,
    Object? address = freezed,
    Object? distance = freezed,
    Object? openingTime = freezed,
    Object? hasATM = freezed,
    Object? hasCashDeposits = freezed,
    Object? isBranch = freezed,
  }) {
    return _then(_$_Location(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      branchName: null == branchName
          ? _value.branchName
          : branchName // ignore: cast_nullable_to_non_nullable
              as String,
      address: freezed == address
          ? _value.address
          : address // ignore: cast_nullable_to_non_nullable
              as FabAddress?,
      distance: freezed == distance
          ? _value.distance
          : distance // ignore: cast_nullable_to_non_nullable
              as Distance?,
      openingTime: freezed == openingTime
          ? _value.openingTime
          : openingTime // ignore: cast_nullable_to_non_nullable
              as String?,
      hasATM: freezed == hasATM ? _value.hasATM! : hasATM,
      hasCashDeposits: freezed == hasCashDeposits
          ? _value.hasCashDeposits!
          : hasCashDeposits,
      isBranch: freezed == isBranch ? _value.isBranch! : isBranch,
    ));
  }
}

/// @nodoc

class _$_Location implements _Location {
  _$_Location(
      {required this.id,
      required this.latitude,
      required this.longitude,
      required this.branchName,
      this.address,
      this.distance,
      this.openingTime,
      this.hasATM = false,
      this.hasCashDeposits = false,
      this.isBranch = false});

  @override
  final String id;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String branchName;
  @override
  final FabAddress? address;
  @override
  final Distance? distance;
  @override
  final String? openingTime;
  @override
  @JsonKey()
  final dynamic hasATM;
  @override
  @JsonKey()
  final dynamic hasCashDeposits;
  @override
  @JsonKey()
  final dynamic isBranch;

  @override
  String toString() {
    return 'Location(id: $id, latitude: $latitude, longitude: $longitude, branchName: $branchName, address: $address, distance: $distance, openingTime: $openingTime, hasATM: $hasATM, hasCashDeposits: $hasCashDeposits, isBranch: $isBranch)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_Location &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.branchName, branchName) ||
                other.branchName == branchName) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.distance, distance) ||
                other.distance == distance) &&
            (identical(other.openingTime, openingTime) ||
                other.openingTime == openingTime) &&
            const DeepCollectionEquality().equals(other.hasATM, hasATM) &&
            const DeepCollectionEquality()
                .equals(other.hasCashDeposits, hasCashDeposits) &&
            const DeepCollectionEquality().equals(other.isBranch, isBranch));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      latitude,
      longitude,
      branchName,
      address,
      distance,
      openingTime,
      const DeepCollectionEquality().hash(hasATM),
      const DeepCollectionEquality().hash(hasCashDeposits),
      const DeepCollectionEquality().hash(isBranch));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_LocationCopyWith<_$_Location> get copyWith =>
      __$$_LocationCopyWithImpl<_$_Location>(this, _$identity);
}

abstract class _Location implements Location {
  factory _Location(
      {required final String id,
      required final double latitude,
      required final double longitude,
      required final String branchName,
      final FabAddress? address,
      final Distance? distance,
      final String? openingTime,
      final dynamic hasATM,
      final dynamic hasCashDeposits,
      final dynamic isBranch}) = _$_Location;

  @override
  String get id;
  @override
  double get latitude;
  @override
  double get longitude;
  @override
  String get branchName;
  @override
  FabAddress? get address;
  @override
  Distance? get distance;
  @override
  String? get openingTime;
  @override
  dynamic get hasATM;
  @override
  dynamic get hasCashDeposits;
  @override
  dynamic get isBranch;
  @override
  @JsonKey(ignore: true)
  _$$_LocationCopyWith<_$_Location> get copyWith =>
      throw _privateConstructorUsedError;
}
