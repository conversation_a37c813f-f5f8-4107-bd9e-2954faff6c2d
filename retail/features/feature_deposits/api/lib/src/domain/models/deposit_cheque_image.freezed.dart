// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_cheque_image.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$DepositChequeImage {
  String? get fileType => throw _privateConstructorUsedError;
  String? get fileContentBase64 => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $DepositChequeImageCopyWith<DepositChequeImage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DepositChequeImageCopyWith<$Res> {
  factory $DepositChequeImageCopyWith(
          DepositChequeImage value, $Res Function(DepositChequeImage) then) =
      _$DepositChequeImageCopyWithImpl<$Res, DepositChequeImage>;
  @useResult
  $Res call({String? fileType, String? fileContentBase64});
}

/// @nodoc
class _$DepositChequeImageCopyWithImpl<$Res, $Val extends DepositChequeImage>
    implements $DepositChequeImageCopyWith<$Res> {
  _$DepositChequeImageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = freezed,
    Object? fileContentBase64 = freezed,
  }) {
    return _then(_value.copyWith(
      fileType: freezed == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String?,
      fileContentBase64: freezed == fileContentBase64
          ? _value.fileContentBase64
          : fileContentBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_DepositChequeImageCopyWith<$Res>
    implements $DepositChequeImageCopyWith<$Res> {
  factory _$$_DepositChequeImageCopyWith(_$_DepositChequeImage value,
          $Res Function(_$_DepositChequeImage) then) =
      __$$_DepositChequeImageCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? fileType, String? fileContentBase64});
}

/// @nodoc
class __$$_DepositChequeImageCopyWithImpl<$Res>
    extends _$DepositChequeImageCopyWithImpl<$Res, _$_DepositChequeImage>
    implements _$$_DepositChequeImageCopyWith<$Res> {
  __$$_DepositChequeImageCopyWithImpl(
      _$_DepositChequeImage _value, $Res Function(_$_DepositChequeImage) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileType = freezed,
    Object? fileContentBase64 = freezed,
  }) {
    return _then(_$_DepositChequeImage(
      fileType: freezed == fileType
          ? _value.fileType
          : fileType // ignore: cast_nullable_to_non_nullable
              as String?,
      fileContentBase64: freezed == fileContentBase64
          ? _value.fileContentBase64
          : fileContentBase64 // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$_DepositChequeImage implements _DepositChequeImage {
  _$_DepositChequeImage({this.fileType, this.fileContentBase64});

  @override
  final String? fileType;
  @override
  final String? fileContentBase64;

  @override
  String toString() {
    return 'DepositChequeImage(fileType: $fileType, fileContentBase64: $fileContentBase64)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_DepositChequeImage &&
            (identical(other.fileType, fileType) ||
                other.fileType == fileType) &&
            (identical(other.fileContentBase64, fileContentBase64) ||
                other.fileContentBase64 == fileContentBase64));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fileType, fileContentBase64);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_DepositChequeImageCopyWith<_$_DepositChequeImage> get copyWith =>
      __$$_DepositChequeImageCopyWithImpl<_$_DepositChequeImage>(
          this, _$identity);
}

abstract class _DepositChequeImage implements DepositChequeImage {
  factory _DepositChequeImage(
      {final String? fileType,
      final String? fileContentBase64}) = _$_DepositChequeImage;

  @override
  String? get fileType;
  @override
  String? get fileContentBase64;
  @override
  @JsonKey(ignore: true)
  _$$_DepositChequeImageCopyWith<_$_DepositChequeImage> get copyWith =>
      throw _privateConstructorUsedError;
}
