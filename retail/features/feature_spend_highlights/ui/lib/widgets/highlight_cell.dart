import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_spend_highlights_api/index.dart';
import 'package:wio_feature_spend_highlights_ui/l10n/spend_highlights_localization.g.dart';

part 'highlight_cell.freezed.dart';

@freezed
class HighlightPictureModel with _$HighlightPictureModel {
  const factory HighlightPictureModel.icon({
    required GraphicAssetPointer icon,
  }) = _HighlightPictureIconModel;

  const factory HighlightPictureModel.flag({
    required FlagPointer flag,
  }) = _HighlightFlagIconModel;
}

class HighlightCellModel {
  final SpendHighlight spendHighlight;
  final Money totalSpent;
  final int transactionsCount;
  final String name;
  final HighlightPictureModel pictureModel;
  final CompanyColorPointer iconBackgroundColor;

  const HighlightCellModel({
    required this.name,
    required this.spendHighlight,
    required this.totalSpent,
    required this.transactionsCount,
    required this.pictureModel,
    this.iconBackgroundColor = CompanyColorPointer.background1,
  });
}

class HighlightCell extends StatelessWidget {
  final HighlightCellModel model;
  final void Function(SpendHighlight) onTap;

  const HighlightCell({
    required this.model,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localization = SpendHighlightsLocalizations.of(context);

    return Cell(
      CellModel(
        leading: model.pictureModel.map(
          icon: (icon) => CellLeadingModel.icon(
            size: CompanyIconSize.large,
            icon: icon.icon,
            backgroundColor: model.iconBackgroundColor,
          ),
          flag: (flag) => CellLeadingModel.flag(
            flag: flag.flag,
            size: CompanyIconSize.large,
            backgroundColor: model.iconBackgroundColor,
          ),
        ),
        body: CellBodyModel.text(
          title: model.name,
          subtitle: localization.spendHighlightsTransactionsLabel(
            model.transactionsCount.toString(),
          ),
        ),
        trailing: CellTrailingModel.text(
          title: '-${model.totalSpent.toFormattedString()}',
          titleColor: CompanyColorPointer.secondary1,
          titleMaxLines: 1,
        ),
      ),
      onPressed: () => onTap(model.spendHighlight),
    );
  }
}
