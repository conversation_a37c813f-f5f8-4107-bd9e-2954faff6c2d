import 'package:di/di.dart';
import 'package:wio_feature_spend_highlights_api/index.dart';
import 'package:wio_feature_spend_highlights_impl/data/highlights_repository.dart';
import 'package:wio_feature_spend_highlights_impl/data/highlights_repository_impl.dart';
import 'package:wio_feature_spend_highlights_impl/domain/highlights_interactor_impl.dart';
import 'package:wio_retail_db/index.dart';

class SpendHighlightsDomainDependencyModuleResolver {
  const SpendHighlightsDomainDependencyModuleResolver();

  static void register() {
    _repositories();
    _interactors();
  }

  static void _repositories() {
    DependencyProvider.registerLazySingleton<HighlightsRepository>(
      () => HighlightsRepositoryImpl(
        database: DependencyProvider.get<RetailDatabase>(),
        dataBaseMapper: DependencyProvider.get<TransactionsDataBaseMapper>(),
      ),
    );
  }

  static void _interactors() {
    DependencyProvider.registerLazySingleton<HighlightsInteractor>(
      () => HighlightsInteractorImpl(
        repository: DependencyProvider.get<HighlightsRepository>(),
      ),
    );
  }
}
