import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:flutter/cupertino.dart';
import 'package:rxdart/rxdart.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_common_feature_tutorial_api/feature_tutorial_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_critical_notification_api/feature_critical_notification_api.dart';
import 'package:wio_feature_error_domain_api/index.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_participant_user_api/participant_user_api.dart';
import 'package:wio_feature_participant_user_ui/src/navigation/configs/pocket_shared_bottom_sheet_config.dart';

class ParticipantFlowImpl implements ParticipantFlow {
  @visibleForTesting
  static String getTutorialKey(ParticipantType participantType) =>
      switch (participantType) {
        ParticipantType.minor => '_familyParticipantTutorialKey',
        ParticipantType.adult => '_familyAdultParticipantTutorialKey',
      };

  final FamilyBankingInteractor _familyBankingInteractor;
  final FamilyBankingFlow _familyBankingFlow;
  final TutorialInteractor _tutorialInteractor;
  final NavigationProvider _navigationProvider;
  final FeatureToggleProvider _featureToggles;
  final Logger _logger;
  final LoadingProvider _loadingProvider;
  final CommonErrorHandler _errorHandler;

  const ParticipantFlowImpl({
    required FamilyBankingInteractor familyBankingInteractor,
    required FamilyBankingFlow familyBankingFlow,
    required TutorialInteractor tutorialInteractor,
    required NavigationProvider navigationProvider,
    required FeatureToggleProvider featureToggles,
    required Logger logger,
    required LoadingProvider loadingProvider,
    required CommonErrorHandler errorHandler,
  })  : _familyBankingInteractor = familyBankingInteractor,
        _familyBankingFlow = familyBankingFlow,
        _tutorialInteractor = tutorialInteractor,
        _navigationProvider = navigationProvider,
        _featureToggles = featureToggles,
        _logger = logger,
        _loadingProvider = loadingProvider,
        _errorHandler = errorHandler;

  bool get _isFamilyTutorialEnabled =>
      _featureToggles.get(FamilyBankingFeatureToggles.isTutorialEnabled);

  bool get _areAvatarsEnabled =>
      _featureToggles.get(FamilyBankingFeatureToggles.areAvatarsEnabled);

  @override
  Future<void> handleTutorialFlow({
    ParticipantType participantType = ParticipantType.minor,
  }) async {
    final shouldShowTutorials = _isFamilyTutorialEnabled;

    _logger
      ..debug('Handling tutorial flow for participant type $participantType')
      ..debug('handleTutorialFlow > shouldShowTutorials: $shouldShowTutorials');

    if (!shouldShowTutorials) {
      return;
    }

    final tutorialKey = getTutorialKey(participantType);

    _logger.debug('handleTutorialFlow > tutorialKey: $tutorialKey');

    try {
      final isTutorialRequired = await _tutorialInteractor.isTutorialRequired(
        tutorialKey,
      );

      _logger.debug(
        'handleTutorialFlow > isTutorialRequired: $isTutorialRequired',
      );

      if (!isTutorialRequired) {
        return;
      }

      final result = await _showTutorial(participantType: participantType);

      _logger.debug('Result from show tutorial: $result');

      if (result == FamilyTutorialResult.completed) {
        _logger.debug('Setting tutorial as completed for key: $tutorialKey');

        await _tutorialInteractor.setTutorialAsCompleted(tutorialKey);
      }
    } on Object catch (error, stackTrace) {
      _logger.error(
        'An error occurred handling the tutorial for participant with key '
        '$tutorialKey',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<void> handleAvatarSelectionFlow() async {
    try {
      final needShowAvatarFlow = await _needShowAvatarSelectionFlow();
      _logger.debug('handleAvatarFlow. needShowAvatarFlow=$needShowAvatarFlow');

      if (needShowAvatarFlow) {
        await _familyBankingFlow.openAvatarSelection(
          options: AvatarFlowOption.onlySkip,
        );
      }
    } on Object catch (error, stackTrace) {
      _logger.error(
        'An error occurred while handling avatar flow',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  @override
  Future<void> runDeepLink(ParticipantDeepLinkType type) {
    return switch (type) {
      PocketSharedParticipantDeepLinkType() => _handlePocketSharedBottomSheet(),
      NotificationInboxParticipantDeepLinkType() => _handleOpenInbox(),
      InAppOtpParticipantDeepLinkType() => _handleOpenInAppOtp(),
    };
  }

  // Private
  Future<FamilyTutorialResult> _showTutorial({
    ParticipantType participantType = ParticipantType.minor,
  }) {
    return _maybeGetFamilyTutorial(participantType: participantType)
        .asyncMap(
          (tutorial) async {
            if (tutorial == null) {
              return FamilyTutorialResult.unavailable;
            }

            final isCompleted = await _navigationProvider.navigateTo<Object?>(
              TutorialFeatureNavigationConfig(
                config: _getTutorialConfig(tutorial),
              ),
            );

            if (isCompleted == true) {
              return FamilyTutorialResult.completed;
            }

            return FamilyTutorialResult.cancelled;
          },
        )
        .logError(_logger)
        .firstOrDefault(FamilyTutorialResult.unavailable);
  }

  TutorialConfig _getTutorialConfig(FamilyTutorial familyTutorial) {
    return TutorialConfig.cardStack(
      cards: familyTutorial.cards
          .map(
            (it) => CardStackTutorialCard(
              pageTitle: it.pageTitle,
              image: CardStackTutorialImage.network(url: it.imageUrl),
              title: it.title,
              description: it.description,
            ),
          )
          .toList(),
      fromFeature: FamilyBankingFeatureNavigationConfig.name,
      canDismiss: false, // cannot dismiss the tutorial as per design and UX
    );
  }

  Stream<FamilyTutorial?> _maybeGetFamilyTutorial({
    ParticipantType participantType = ParticipantType.minor,
  }) {
    final tutorialType = switch (participantType) {
      ParticipantType.minor => FamilyTutorialType.participant,
      ParticipantType.adult => FamilyTutorialType.adultParticipant,
    };

    return _familyBankingInteractor
        .getFamilyTutorial(type: tutorialType)
        .toStream()
        .logError(_logger)
        .map<FamilyTutorial?>((tutorial) => tutorial)
        .onErrorReturn(null);
  }

  Future<bool> _needShowAvatarSelectionFlow() async {
    if (!_areAvatarsEnabled) {
      return false;
    }

    final currentUser = await _familyBankingInteractor.getCurrentUserConfig();
    return !currentUser.hasImageAvatar;
  }

  Future<void> _handlePocketSharedBottomSheet() {
    return _familyBankingInteractor
        .getMyFamily()
        .asStream()
        .withLoading(_loadingProvider)
        .map((it) => it.acceptedMembers)
        .where((owners) => owners.isNotEmpty)
        .withError(_errorHandler.handleError)
        .doOnData(
          (owners) => _navigationProvider.showBottomSheet(
            PocketSharedBottomSheetConfig(owners: owners),
          ),
        )
        .drain<void>();
  }

  Future<void> _handleOpenInbox() {
    _navigateToDashboard();

    return _navigationProvider.push(
      const CriticalNotificationInboxScreenNavigationConfig(),
    );
  }

  Future<void> _handleOpenInAppOtp() {
    _navigateToDashboard();

    return _navigationProvider.push(
      const InAppNotificationScreenNavigationConfig(),
    );
  }

  void _navigateToDashboard() {
    _navigationProvider.popUntilFirstRoute();
  }
}
