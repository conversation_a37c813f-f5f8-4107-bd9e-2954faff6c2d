import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_critical_notification_api/domain/enums/notification_status.dart';
import 'package:wio_critical_notification_api/feature_critical_notification_api.dart';
import 'package:wio_feature_participant_user_ui/feature_participant_user_mobile_ui.dart';
import 'package:wio_feature_participant_user_ui/src/common/constants.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_notifications/participant_notifications_cubit.dart';
import 'package:wio_feature_participant_user_ui/src/screens/dashboard/participant_notifications/participant_notifications_state.dart';

const _sectionHeight = 128.0;

class ParticipantNotificationsSection extends StatelessWidget {
  final EdgeInsetsGeometry padding;

  const ParticipantNotificationsSection({
    this.padding = EdgeInsets.zero,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final state = context.watch<ParticipantNotificationsCubit>().state;

    return switch (state) {
      // NOTE: initially we don't show anything for this loading because
      // the user doesn't always have notifications and it results in layout
      // jumps, but we show it only when refreshing data where we know
      // that the customer has notifications and can potentially still have
      // them after reloading.
      LoadingParticipantNotificationsState(hasNotifications: true) =>
        CompanyShimmer(
          model: const CompanyShimmerModel(),
          child: Container(
            height: _sectionHeight,
            margin: const EdgeInsets.symmetric(horizontal: 24).add(padding),
            decoration: const BoxDecoration(
              color: Colors.white70,
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
          ),
        ),
      IdleParticipantNotificationsState(:final notifications)
          when notifications.isNotEmpty =>
        Padding(
          padding: padding,
          child: _NotificationList(notifications: notifications),
        ),
      _ => const Space.shrink(),
    };
  }
}

class _NotificationList extends StatelessWidget {
  final List<CriticalNotification> notifications;

  const _NotificationList({required this.notifications});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ParticipantNotificationsCubit>();

    return SizedBox(
      height: _sectionHeight,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        scrollDirection: Axis.horizontal,
        itemCount: notifications.length,
        separatorBuilder: (_, __) => Space.fromSpacingHorizontal(Spacing.s5),
        itemBuilder: (context, index) {
          final notification = notifications[index];

          return _NotificationItem(
            notification: notification,
            padded: notifications.length > 1,
            onClose: () => cubit.onCloseNotification(notification),
            onPressed: () => cubit.onOpenNotification(notification),
          );
        },
      ),
    );
  }
}

class _NotificationItem extends StatelessWidget {
  final CriticalNotification notification;
  final bool padded;
  final VoidCallback? onPressed;
  final VoidCallback? onClose;

  const _NotificationItem({
    required this.notification,
    this.padded = true,
    this.onPressed,
    this.onClose,
  });

  double _calculateWidth(BuildContext context) {
    const padding = 24;
    const totalPadding = padding * 2;
    final availableWidth = MediaQuery.of(context).size.width;
    final itemWidth = availableWidth - totalPadding;

    return padded ? itemWidth - padding : itemWidth;
  }

  @override
  Widget build(BuildContext context) {
    final image = notification.image ?? '';
    final status = notification.status;
    final l10n = ParticipantUserLocalizations.of(context);

    return SizedBox(
      width: _calculateWidth(context),
      child: CriticalNotificationCard(
        CriticalNotificationCardModel.v2(
          theme: CriticalNotificationCardTheme.tertiary,
          title: notification.heading,
          description: notification.preparedContent,
          label: notification.linkText,
          hasCloseIcon: notification.closable,
          isRead: notification.read,
          emojiModel: _getIconModel(notification.icon),
          imageModel: image.isNotEmpty
              ? CriticalNotificationCardImageModel(imagePath: image)
              : null,
          notificationStatus: status == null
              ? null
              : NotificationStatusModel(
                  text: l10n.participantNotificationStatusTitle(status.name),
                  status: status.uiStatus,
                ),
        ),
        onTap: onPressed,
        onCloseButtonPressed: onClose,
      ),
    );
  }

  CriticalNotificationCardEmojiModel _getIconModel(String? icon) {
    if (icon == null || icon.isEmpty) {
      return const CriticalNotificationCardEmojiModel(
        imagePath: 'assets/images/traffic_cone.webp',
        imageSource: ImageSource.asset,
        imagePackage: ParticipantUserConstants.packageName,
      );
    }

    return CriticalNotificationCardEmojiModel(imagePath: icon);
  }
}

extension on CriticalNotificationStatus {
  NotificationStatus get uiStatus => switch (this) {
        CriticalNotificationStatus.urgent => NotificationStatus.urgent,
        CriticalNotificationStatus.actionRequired =>
          NotificationStatus.actionRequired,
        CriticalNotificationStatus.fraud => NotificationStatus.fraud,
        CriticalNotificationStatus.important => NotificationStatus.important,
      };
}
