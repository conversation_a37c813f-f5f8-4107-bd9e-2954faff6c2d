import 'package:feature_device_identity_api/domain/models/trusted_device_data.dart';

/// Interactor for binding device for specific user
abstract class BindDeviceInteractor {
  /// Create new keyPair, send public key to BE and save keys to local storage
  Future<bool> bindDevice();

  /// Check is device bound and return bool
  Future<bool> deviceBound();

  /// Get unique device id
  Future<String> getDeviceId();

  /// Get all data for device binding check
  Future<TrustedDeviceData?> getTrustedDeviceData();

  /// Unbound device
  Future<void> unbindDevice();

  /// Get Trusted device private key from secure storage
  Future<String?> getTrustedDevicePrivateKey();

  /// Check if device bound and bind it
  Future<bool> bindDeviceIfItIsNotBound();

  /// Check if current local device bind data same as on the server side
  Future<bool> checkIfDeviceBindDataTheSame(
    String keyParId,
    String deviceId,
  );
}
