import 'package:feature_todo_actions_api/feature_todo_actions_api.dart';

class TodoActionsInteractorImpl implements TodoActionsInteractor {
  final TodoActionsRepository _todoActionsRepository;

  TodoActionsInteractorImpl({
    required TodoActionsRepository todoActionsRepository,
  }) : _todoActionsRepository = todoActionsRepository;

  @override
  Future<bool> isPasscodeForgotten() {
    return _todoActionsRepository.isPasscodeForgotten();
  }

  @override
  Future<void> setPasscodeForgotStatus({required bool status}) {
    return _todoActionsRepository.setPasscodeForgotStatus(status: status);
  }
}
