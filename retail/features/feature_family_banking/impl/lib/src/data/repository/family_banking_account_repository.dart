import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_family_banking_impl/src/data/mappers/family_banking_mapper.dart';
import 'package:wio_feature_family_banking_impl/src/data/service/family_banking_service.dart';

class FamilyBankingAccountRepository {
  final FamilyBankingService _service;
  final FamilyBankingMapper _mapper;

  const FamilyBankingAccountRepository({
    required FamilyBankingService service,
    required FamilyBankingMapper mapper,
  })  : _service = service,
        _mapper = mapper;

  Future<AccountClosureRequest> getAccountClosureRequest(
    String requestId,
  ) async {
    final response = await _service.getAccountClosureRequest(requestId);

    return _mapper.mapToAccountClosureDetails(response);
  }

  Future<AccountClosureRequest> updateAccountClosureRequest({
    required String requestId,
    required AccountClosureRequestAction action,
  }) async {
    final updateRequest = _mapper.mapToAccountClosureUpdateRequest(action);
    final response = await _service.updateAccountClosureRequest(
      requestId: requestId,
      updateRequest: updateRequest,
    );

    return _mapper.mapToAccountClosureDetails(response);
  }

  Future<SharedAccountClosureValidation> validateAccountClosure(
    String accountId,
  ) async {
    final dto = await _service.validateAccountClosure(accountId);

    return _mapper.mapValidateAccountClosure(dto);
  }

  Future<SharedAccountClosureResult> initiateAccountClosure({
    required String accountId,
    required String accountNickname,
    required String initiatorName,
  }) async {
    final requestDto = _mapper.mapToAccountClosureRequest(
      accountId: accountId,
      accountNickname: accountNickname,
      initiatorName: initiatorName,
    );
    final response = await _service.initiateAccountClosure(requestDto);

    return _mapper.mapToAccountClosureResult(response);
  }
}
