import 'package:bloc_test/bloc_test.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_content_domain_api/index.dart';
import 'package:wio_feature_family_banking_ui/src/screens/terms_and_conditions/tnc_cubit.dart';
import 'package:wio_feature_family_banking_ui/src/screens/terms_and_conditions/tnc_state.dart';

import '../mocks.dart';

void main() {
  setUpAll(() {
    registerFallbackValue(
      LegalDocumentType.retailJointAccountTermsAndConditions,
    );
  });

  group('TncCubit', () {
    late ContentInteractor mockContentInteractor;
    late TncCubit tncCubit;

    setUp(() {
      mockContentInteractor = MockContentInteractor();
      tncCubit = TncCubit(interactor: mockContentInteractor);
    });

    blocTest<TncCubit, TncState>(
      'emits idle state when initialize is called with successful data',
      build: () => tncCubit,
      setUp: () {
        when(() => mockContentInteractor.getDocumentByType(any())).thenAnswer(
          (_) => Future.value(
            LegalDocument(
              version: '1',
              type: LegalDocumentType.retailJointAccountTermsAndConditions,
              urls: LocalizedUrls(
                englishUrl: 'englishUrl',
                arabicUrl: 'arabicUrl',
              ),
            ),
          ),
        );
      },
      act: (cubit) => cubit.initialize(
        documentType: LegalDocumentType.retailJointAccountTermsAndConditions,
      ),
      expect: () => [
        TncState.idle(
          urls: LocalizedUrls(
            englishUrl: 'englishUrl',
            arabicUrl: 'arabicUrl',
          ),
        ),
      ],
    );

    blocTest<TncCubit, TncState>(
      'emits failed state when initialize encounters an error',
      build: () => tncCubit,
      setUp: () {
        when(() => mockContentInteractor.getDocumentByType(any()))
            .thenAnswer((_) => Future.error(Exception('Error')));
      },
      act: (cubit) => cubit.initialize(
        documentType: LegalDocumentType.retailJointAccountTermsAndConditions,
      ),
      expect: () => [
        const TncState.failed(),
      ],
    );

    blocTest<TncCubit, TncState>(
      'emits initial and then idle state when onRetry is called',
      build: () => tncCubit,
      seed: () => const TncState.failed(),
      setUp: () {
        when(() => mockContentInteractor.getDocumentByType(any())).thenAnswer(
          (_) => Future.value(
            LegalDocument(
              version: '1',
              type: LegalDocumentType.retailJointAccountTermsAndConditions,
              urls: LocalizedUrls(
                englishUrl: 'englishUrl',
                arabicUrl: 'arabicUrl',
              ),
            ),
          ),
        );
      },
      act: (cubit) => cubit.onRetry(
        documentType: LegalDocumentType.retailJointAccountTermsAndConditions,
      ),
      expect: () => [
        const TncState.initial(),
        TncState.idle(
          urls: LocalizedUrls(
            englishUrl: 'englishUrl',
            arabicUrl: 'arabicUrl',
          ),
        ),
      ],
    );

    blocTest<TncCubit, TncState>(
      'emits new language state when onToggleLanguage is called',
      build: () => tncCubit,
      seed: () => TncState.idle(
        urls: LocalizedUrls(
          englishUrl: 'englishUrl',
          arabicUrl: 'arabicUrl',
        ),
      ),
      act: (cubit) => cubit.onToggleLanguage(),
      expect: () => [
        TncState.idle(
          urls: LocalizedUrls(
            englishUrl: 'englishUrl',
            arabicUrl: 'arabicUrl',
          ),
          language: DocumentLanguage.arabic,
        ),
      ],
    );
  });
}
