import 'dart:ui';

import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_faq_api/feature_faq_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_family_banking_api/domain/family_banking_interactor.dart';
import 'package:wio_feature_family_banking_api/domain/family_banking_invitation_interactor.dart';
import 'package:wio_feature_family_banking_api/domain/models/member_invitation_eligibility.dart';
import 'package:wio_feature_family_banking_api/domain/models/member_invitation_exception.dart';
import 'package:wio_feature_family_banking_api/domain/models/member_role.dart';
import 'package:wio_feature_family_banking_ui/feature_family_banking_mobile_ui.dart';
import 'package:wio_feature_family_banking_ui/src/common/family_banking_feedback_provider.dart';
import 'package:wio_feature_family_banking_ui/src/common/models/contact.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/bottom_sheets/cannot_invite_member_bottom_sheet_config.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/bottom_sheets/invite_co_owner_confirmation_bottom_sheet_config.dart';
import 'package:wio_feature_family_banking_ui/src/screens/invite_co_owner/flow/analytics/invite_co_owner_analytics.dart';
import 'package:wio_feature_family_banking_ui/src/screens/invite_co_owner/flow/cubit/invite_co_owner_cubit.dart';
import 'package:wio_feature_family_banking_ui/src/screens/invite_co_owner/flow/cubit/invite_co_owner_state.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late InviteCoOwnerCubit cubit;
  late NavigationProvider navigationProvider;
  late FamilyBankingInvitationInteractor invitationInteractor;
  late InviteCoOwnerAnalytics analytics;
  late FamilyBankingFeedbackProvider feedbackProvider;
  late FamilyBankingInteractor familyBankingInteractor;
  late FaqNavigationFlow faqNavigationFlow;
  late FeatureToggleProvider featureToggleProvider;
  late FamilyBankingLocalizations l10n;

  setUpAll(() async {
    registerFallbackValue(
      FakeJointAccountInvitationConfirmationBottomSheetConfig(),
    );
    await OtaLocalizationImpl().initMock();
    l10n = await FamilyBankingLocalizations.load(const Locale('en'));
  });

  setUp(() {
    navigationProvider = MockNavigationProvider();
    analytics = MockInviteCoOwnerAnalytics();
    feedbackProvider = MockFeedbackProvider();
    invitationInteractor = MockFamilyBankingInvitationInteractor();
    familyBankingInteractor = MockFamilyBankingInteractor();
    faqNavigationFlow = MockFaqNavigationFlow();
    featureToggleProvider = MockFeatureToggleProvider();
    cubit = InviteCoOwnerCubit(
      navigationProvider: navigationProvider,
      analytics: analytics,
      feedbackProvider: feedbackProvider,
      invitationInteractor: invitationInteractor,
      familyInteractor: familyBankingInteractor,
      logger: MockLogger(),
      faqNavigationFlow: faqNavigationFlow,
      featureToggleProvider: featureToggleProvider,
      l10n: l10n,
    );
  });

  void stubTncAgreed({
    InvitationConfirmationResult result = InvitationConfirmationResult.agree,
  }) {
    when(
      () => navigationProvider
          .showBottomSheet<InvitationConfirmationResult?>(any()),
    ).justAnswerAsync(result);
  }

  group('Stages >', () {
    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'init emits stages with intro',
      build: () => cubit,
      verify: (cubit) {
        expect(cubit.state, InviteCoOwnerState.initial());
      },
    );

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'When RulesScreen is Completed adds selectRecipient stage',
      build: () => cubit,
      seed: () => const InviteCoOwnerState.idle(
        stages: [InviteCoOwnerStage.intro()],
      ),
      act: (cubit) => cubit.onRulesScreenCompleted(),
      verify: (_) {
        verify(() => analytics.onCompleteRulesScreen()).calledOnce;
      },
      expect: () => [
        const InviteCoOwnerState.idle(
          stages: [
            InviteCoOwnerStage.intro(),
            InviteCoOwnerStage.selectMemberPhone(),
          ],
        ),
      ],
    );

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'onPopScreen emits stages when more than one stage',
      build: () => cubit,
      seed: () => const InviteCoOwnerState.idle(
        stages: [
          InviteCoOwnerStage.intro(),
          InviteCoOwnerStage.selectMemberPhone(),
        ],
      ),
      act: (cubit) => cubit.onPopScreen(),
      verify: (_) {
        verify(
          () => analytics.onPopScreen(
            const InviteCoOwnerStage.selectMemberPhone(),
          ),
        ).calledOnce;
      },
      expect: () => [
        const InviteCoOwnerState.idle(
          stages: [InviteCoOwnerStage.intro()],
        ),
      ],
    );

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'onPopScreen calls goBack when only one stage',
      build: () => cubit,
      seed: () => const InviteCoOwnerState.idle(
        stages: [InviteCoOwnerStage.intro()],
      ),
      act: (cubit) => cubit.onPopScreen(),
      verify: (_) {
        verify(
          () => analytics.onPopScreen(const InviteCoOwnerStage.intro()),
        ).calledOnce;
        verify(() => navigationProvider.goBack()).calledOnce;
      },
    );

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'onRelationshipConfirmed does not trigger invite creation '
      'when result is not agree',
      build: () => cubit,
      setUp: () {
        stubTncAgreed(result: InvitationConfirmationResult.dismiss);
      },
      act: (cubit) => cubit.onConfirmRelationship('wife'),
      verify: (_) {
        verify(
          () =>
              navigationProvider.showBottomSheet<InvitationConfirmationResult?>(
            any(),
          ),
        ).calledOnce;
      },
    );

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'confirming phone number switches to name input stage '
      'if validation returns ${MemberInvitationEligibility.allowed.name}',
      // Arrange
      build: () => cubit,
      seed: () => const InviteCoOwnerState.idle(
        stages: [InviteCoOwnerStage.selectMemberPhone()],
      ),
      setUp: () {
        when(
          () => invitationInteractor.getInvitationEligibility(
            '42',
            inviteeRole: MemberRole.coOwner,
          ),
        ).justAnswerAsync(MemberInvitationEligibility.allowed);
      },

      // Act
      act: (cubit) => cubit.onConfirmContact(
        const Contact(phoneNumber: '42', name: 'L'),
      ),

      // Assert
      expect: () => [
        const InviteCoOwnerState.idle(
          stages: [
            InviteCoOwnerStage.selectMemberPhone(),
            InviteCoOwnerStage.selectMemberName(phoneNumber: '42', name: 'L'),
          ],
        ),
      ],
      verify: (_) {
        verify(
          () => invitationInteractor.getInvitationEligibility(
            '42',
            inviteeRole: MemberRole.coOwner,
          ),
        );
      },
    );

    const disallowedStatuses = [
      MemberInvitationEligibility.alreadyOwner,
      MemberInvitationEligibility.alreadyParticipant,
    ];
    for (final status in disallowedStatuses) {
      final config = CannotInviteMemberBottomSheetConfig(
        eligibility: status,
        canAddCoOwner: false,
      );

      blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
        'confirming phone number does not switch to name input stage '
        'if validation returns ${status.name} error',
        // Arrange
        build: () => cubit,
        seed: () => const InviteCoOwnerState.idle(
          stages: [InviteCoOwnerStage.selectMemberPhone()],
        ),
        setUp: () {
          when(
            () => invitationInteractor.getInvitationEligibility(
              '42',
              inviteeRole: MemberRole.coOwner,
            ),
          ).justAnswerAsync(status);

          when(
            () => navigationProvider.showBottomSheet(config),
          ).justAnswerAsync(CannotInviteMemberResult.goBack);
        },

        // Act
        act: (cubit) => cubit.onConfirmContact(
          const Contact(phoneNumber: '42', name: 'L'),
        ),

        // Assert
        expect: () => <InviteCoOwnerState>[
          // no state changes
        ],
        verify: (_) {
          verifyInOrder(
            [
              () => invitationInteractor.getInvitationEligibility(
                    '42',
                    inviteeRole: MemberRole.coOwner,
                  ),
              () => navigationProvider.showBottomSheet(config),
            ],
          );
        },
      );
    }

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'confirming member name switches to relationship input stage',
      // Arrange
      build: () => cubit,
      seed: () => const InviteCoOwnerState.idle(
        stages: [
          InviteCoOwnerStage.selectMemberPhone(),
          InviteCoOwnerStage.selectMemberName(phoneNumber: '42'),
        ],
      ),

      // Act
      act: (cubit) => cubit.onConfirmName('L'),

      // Assert
      expect: () => [
        const InviteCoOwnerState.idle(
          stages: [
            InviteCoOwnerStage.selectMemberPhone(),
            InviteCoOwnerStage.selectMemberName(phoneNumber: '42'),
            InviteCoOwnerStage.selectRelationship(
              phoneNumber: '42',
              name: 'L',
            ),
          ],
        ),
      ],
    );
  });

  group('Invite creation >', () {
    final invite = TestEntities.randInvitation();
    const stages = [
      InviteCoOwnerStage.selectRelationship(phoneNumber: '', name: ''),
    ];

    blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
      'onRelationshipConfirmed triggers invite creation when result is agree',
      // Arrange
      build: () => cubit,
      seed: () => const InviteCoOwnerStateIdle(stages: stages),
      setUp: () {
        stubTncAgreed();

        when(
          () => invitationInteractor.inviteCoOwner(
            phoneNumber: anyNamed('phoneNumber'),
            name: anyNamed('name'),
            relationship: anyNamed('relationship'),
          ),
        ).justAnswerAsync(invite);
      },

      // Act
      act: (cubit) => cubit.onConfirmRelationship('wife'),

      // Assert
      expect: () => [
        const InviteCoOwnerState.creating(stages: stages),
        InviteCoOwnerState.completed(invitation: invite),
      ],
      verify: (_) {
        verify(() => analytics.onAcceptTermsAndConditions()).calledOnce;
        verify(() => analytics.onViewStatusScreen()).calledOnce;
        verify(familyBankingInteractor.reloadMyFamily);
        verify(
          () => navigationProvider
              .showBottomSheet<InvitationConfirmationResult?>(any()),
        );
      },
    );

    final testCases = [
      (
        description: 'goes back to idle state if invitation creation fails',
        error: Exception("A man's money should be in his pocket"),
        verify: () => verify(() => feedbackProvider.showFromError(any())),
      ),
      (
        description: 'closes the flow when invitation error is received',
        error: const MemberInvitationException(
          message: 'Ooops',
          code: MemberInvitationErrorCode.cannotCreate,
        ),
        verify: () {
          verify(navigationProvider.popUntilFirstRoute);
          verify(() => feedbackProvider.showError(any()));
        },
      ),
    ];

    for (final test in testCases) {
      blocTest<InviteCoOwnerCubit, InviteCoOwnerState>(
        test.description,
        // Arrange
        build: () => cubit,
        seed: () => const InviteCoOwnerStateIdle(stages: stages),
        setUp: () {
          stubTncAgreed();

          when(
            () => invitationInteractor.inviteCoOwner(
              phoneNumber: anyNamed('phoneNumber'),
              name: anyNamed('name'),
              relationship: anyNamed('relationship'),
            ),
          ).justThrowAsync(test.error);
        },

        // Act
        act: (cubit) => cubit.onConfirmRelationship('wife'),

        // Assert
        expect: () => const [
          InviteCoOwnerState.creating(stages: stages),
          InviteCoOwnerState.idle(stages: stages),
        ],
        verify: (_) => test.verify(),
      );
    }
  });
}
