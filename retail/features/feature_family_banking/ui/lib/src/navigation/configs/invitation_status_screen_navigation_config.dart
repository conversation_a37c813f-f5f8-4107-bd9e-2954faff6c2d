import 'package:flutter/cupertino.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

part 'invitation_status_screen_navigation_config.freezed.dart';

@Freezed(when: FreezedWhenOptions.none)
sealed class InvitationStatusScreenNavigationConfig
    extends ScreenNavigationConfig
    with _$InvitationStatusScreenNavigationConfig {
  static const screenName = 'invitation_status_screen';

  /// The config for the status screen displayed after sending an invitation.
  const factory InvitationStatusScreenNavigationConfig.sent({
    required MemberInvitation invitation,
    VoidCallback? onPrimaryButtonPressed,
  }) = InvitationSentScreenNavigationConfig;

  /// The config for the status screen displayed when inviter clicks on
  /// pending member card whose Invitation is in CREATED status
  const factory InvitationStatusScreenNavigationConfig.sentStatusCheck({
    required PendingMember member,
    VoidCallback? onPrimaryButtonPressed,
  }) = InvitationSentStatusCheckScreenNavigationConfig;

  /// The config for the status screen displayed when an invitee opens
  /// a notification with invitation.
  const factory InvitationStatusScreenNavigationConfig.pending({
    required String inviterName,
    VoidCallback? onPrimaryButtonPressed,
  }) = InvitationPendingScreenNavigationConfig;

  /// The config for the status screen displayed when inviter clicks on
  /// pending member card whose Invitation is in ACCEPTED_BY_INVITEE status
  const factory InvitationStatusScreenNavigationConfig.inviterPendingApproval({
    required String inviteeName,
    VoidCallback? onPrimaryButtonPressed,
  }) = InvitationPendingInviterApprovalNavigationConfig;

  /// The config for the status screen displayed when inviter clicks on
  /// pending member card whose Invitation is in CREATED status and
  /// Type is addCoOwnerToPocket
  const factory InvitationStatusScreenNavigationConfig.sentSharedPocketStatus({
    required String inviteeName,
    VoidCallback? onPrimaryButtonPressed,
  }) = InvitationSentSharedPocketStatusScreenNavigationConfig;

  const InvitationStatusScreenNavigationConfig._()
      : super(
          id: screenName,
          feature: FamilyBankingFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'InvitationStatusScreenNavigationConfig';
}
