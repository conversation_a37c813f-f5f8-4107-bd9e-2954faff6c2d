import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

part 'avatar_image_selection_state.freezed.dart';

@freezed
class AvatarImageSelectionState with _$AvatarImageSelectionState {
  const AvatarImageSelectionState._();

  const factory AvatarImageSelectionState.initial() =
      _AvatarImageSelectionInitialState;

  const factory AvatarImageSelectionState.loading() =
      _AvatarImageSelectionLoadingState;

  @With<_AvatarImageGetter>()
  const factory AvatarImageSelectionState.idle({
    required List<AvatarImage> avatars,
    String? selectedCategory,
    AvatarImage? selectedAvatar,
  }) = AvatarImageSelectionIdleState;

  const factory AvatarImageSelectionState.failed() =
      _AvatarImageSelectionFailedState;

  bool get isLoading => this is _AvatarImageSelectionLoadingState;

  bool get canSubmit => maybeMap(
        idle: (it) => it.selectedAvatar != null,
        orElse: () => false,
      );
}

mixin _AvatarImageGetter {
  List<AvatarImage> get avatars;
  String? get selectedCategory;
  AvatarImage? get selectedAvatar;

  List<String> get categories =>
      avatars.map((it) => it.category).toSet().toList();

  List<AvatarImage> get selectedCategoryImages => selectedCategory == null
      ? avatars
      : avatars.where((it) => it.category == selectedCategory).toList();
}
