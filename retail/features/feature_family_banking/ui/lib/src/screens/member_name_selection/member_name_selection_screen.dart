import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_family_banking_ui/feature_family_banking_mobile_ui.dart';
import 'package:wio_feature_family_banking_ui/src/screens/member_name_selection/member_name_selection_cubit.dart';
import 'package:wio_feature_family_banking_ui/src/screens/member_name_selection/member_name_selection_state.dart';

class MemberNameSelectionScreen
    extends BasePage<MemberNameSelectionState, MemberNameSelectionCubit> {
  final MemberNameSelectionDelegate delegate;
  final String? contactName;

  const MemberNameSelectionScreen({
    required this.delegate,
    this.contactName,
    super.key,
  });

  @override
  Widget buildPage(
    BuildContext context,
    MemberNameSelectionCubit bloc,
    MemberNameSelectionState state,
  ) {
    final l10n = FamilyBankingLocalizations.of(context);

    return Scaffold(
      appBar: TopNavigation(
        TopNavigationModel(
          state: TopNavigationState.positive,
          title: l10n.createJointAccountSelectMemberNameAppBarTitle,
        ),
      ),
      body: const _BodyContent(),
    );
  }

  @override
  MemberNameSelectionCubit createBloc() => DependencyProvider.getWithParams<
      MemberNameSelectionCubit,
      MemberNameSelectionDelegate,
      void>(param1: delegate);

  @override
  void initBloc(MemberNameSelectionCubit bloc) =>
      bloc.init(contactName: contactName);
}

class _BodyContent extends StatelessWidget {
  const _BodyContent();

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);
    final state = context.watch<MemberNameSelectionCubit>().state;
    final isValidName = state.isValidName;

    return FixedButtonsPageLayout(
      onBackgroundPressed: FocusScope.of(context).unfocus,
      onPrimaryButtonPressed:
          state.canSubmit ? () => _handleSubmit(context) : null,
      model: _getButtonLayout(
        l10n.createJointAccountSelectMemberNameConfirmButtonTitle,
      ),
      child: ListView(
        children: [
          Label(
            model: LabelModel(
              text: l10n.createJointAccountSelectMemberNameTitle,
              textStyle: CompanyTextStylePointer.h2medium,
              color: CompanyColorPointer.primary3,
              maxLines: 2,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s2),
          Label(
            model: LabelModel(
              text: l10n.createJointAccountSelectMemberNameSubtitle,
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.secondary4,
              maxLines: 2,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s5),
          _NameInputField(
            isError: !isValidName,
            name: state.name,
          ),
        ],
      ),
    );
  }

  FixedButtonsScrollablePageLayoutModel _getButtonLayout(String title) {
    return FixedButtonsScrollablePageLayoutModel(
      primaryButton: FixedButtonsScrollablePageLayoutButton(
        label: title,
        size: ButtonSize.medium,
        type: ButtonType.primary,
      ),
    );
  }

  void _handleSubmit(BuildContext context) {
    FocusScope.of(context).unfocus();
    context.read<MemberNameSelectionCubit>().onSubmit();
  }
}

class _NameInputField extends StatelessWidget {
  static final _allowAlphaNumericFormatter = FilteringTextInputFormatter.allow(
    DomainRegExps.charactersAndEmojiNameInput,
  );

  final bool isError;
  final String? name;

  const _NameInputField({
    this.isError = false,
    this.name,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<MemberNameSelectionCubit>();
    final l10n = FamilyBankingLocalizations.of(context);

    return InputField(
      autoFocus: true,
      textCapitalization: TextCapitalization.words,
      keyboardType: TextInputType.name,
      formatters: [
        _allowAlphaNumericFormatter,
      ],
      model: InputFieldModel(
        initialValue: name,
        hint: '',
        label: l10n.createJointAccountSelectMemberNameInputLabel,
        size: InputFieldSize.medium,
        theme: InputFieldTheme.light,
        // Why not use LengthLimitingTextInputFormatter for this?
        error: isError
            ? l10n.createJointAccountSelectMemberNameInputErrorMessage(
                ValidationConstants.nameAllowedLength.toString(),
              )
            : null,
      ),
      onInputChanged: cubit.onEnterContactName,
      onFieldSubmitted: (_) {},
    );
  }
}
