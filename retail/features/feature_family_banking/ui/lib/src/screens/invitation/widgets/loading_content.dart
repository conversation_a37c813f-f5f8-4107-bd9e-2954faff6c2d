part of '../invitation_screen.dart';

final class _LoadingContent extends StatelessWidget {
  const _LoadingContent();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: CompanyShimmer(
            model: const CompanyShimmerModel(),
            child: _TopPartLayout(
              avatars: AvatarSection(
                inviterName: 'A',
                inviteeName: 'Z',
                currency: Currency.aed,
              ),
              title: const _LoadingTextContainer(
                lineCount: 2,
                textStyle: CompanyTextStylePointer.h2medium,
                widthFactor: 0.7,
              ),
              subtitle: const _LoadingTextContainer(
                lineCount: 2,
                textStyle: CompanyTextStylePointer.h2medium,
                widthFactor: 0.9,
              ),
              helpAction: const _LoadingTextContainer(
                textStyle: CompanyTextStylePointer.b3,
                widthFactor: 0.7,
              ),
            ),
          ),
        ),
        const CompanyShimmer(
          model: CompanyShimmerModel(),
          child: _BottomPart(),
        ),
      ],
    );
  }
}

final class _LoadingTextContainer extends StatelessWidget {
  final CompanyTextStylePointer textStyle;
  final int lineCount;
  final double widthFactor;

  const _LoadingTextContainer({
    required this.textStyle,
    this.lineCount = 1,
    this.widthFactor = 1,
  });

  @override
  Widget build(BuildContext context) {
    final textSize = Label(
      model: LabelModel(text: '\n' * (lineCount - 1), textStyle: textStyle),
    ).getTextPainter(context);

    return FractionallySizedBox(
      alignment: AlignmentDirectional.centerStart,
      widthFactor: widthFactor,
      child: Container(
        height: textSize.height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
        ),
      ),
    );
  }
}
