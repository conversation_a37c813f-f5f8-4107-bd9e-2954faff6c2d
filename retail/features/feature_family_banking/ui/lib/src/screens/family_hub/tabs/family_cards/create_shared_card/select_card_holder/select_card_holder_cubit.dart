import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_family_banking_api/domain/models/member.dart';
import 'package:wio_feature_family_banking_api/domain/models/member_role.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/bottom_sheets/link_shared_card_bottom_sheet_config.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/configs/invite_member_flow_navigation_config.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/configs/select_card_holder_screen_navigation_config.dart';
import 'package:wio_feature_family_banking_ui/src/screens/family_hub/tabs/family_cards/create_shared_card/select_card_holder/select_card_holder_state.dart';

class SelectCardHolderCubit extends BaseCubit<SelectCardHolderState> {
  final Logger _logger;
  final NavigationProvider _navigationProvider;

  SelectCardHolderCubit({
    required SelectCardHolderParams params,
    required Logger logger,
    required NavigationProvider navigationProvider,
  })  : _logger = logger,
        _navigationProvider = navigationProvider,
        super(
          SelectCardHolderState(
            currentUser: params.currentUser,
            members: params.members,
            createSharedCardUIConfig: params.config,
          ),
        );

  @override
  String toString() => 'SelectCardHolderCubit';

  Future<void> onSelectCurrentUser() async {
    _logger.debug('$this. onSelectCurrentUser');

    final showPersonalAccountChoiceBottomSheet =
        state.createSharedCardUIConfig.showPersonalAccountChoiceBottomSheet;

    if (showPersonalAccountChoiceBottomSheet) {
      await _navigationProvider.showBottomSheet(
        const LinkSharedCardBottomSheetConfig.currentUser(),
      );
    } else {
      // TODO(implementation): call shared card creation flow for current user
    }
  }

  Future<void> onSelectFamilyMember(AcceptedMember member) async {
    _logger.debug('$this. onSelectFamilyMember');

    switch (member.role) {
      case MemberRole.coOwner:
        return _handleCoOwnerSelection(member);
      case MemberRole.participant:
        return _handleParticipantSelection(member);
      default:
        throw ArgumentError.value(
          member.role,
          'member.role',
          'Member role ${member.role} is not supported',
        );
    }
  }

  Future<void> onInviteAdultParticipant() async {
    _logger.debug('$this. onInviteAdultParticipant');

    await _navigationProvider.push<void>(
      const InviteMemberFlowNavigationConfig.adultParticipant(),
    );
  }

  // Private
  Future<void> _handleCoOwnerSelection(AcceptedMember member) async {
    _logger.debug('$this. _handleCoOwnerSelection for member: ${member.name}');

    // TODO(implementation): call co-owner card creation flow
  }

  Future<void> _handleParticipantSelection(AcceptedMember member) async {
    _logger.debug(
      '$this. _handleParticipantSelection for member: ${member.name}',
    );

    final hasActiveCard =
        member.products.whereType<SharedCardProduct>().isNotEmpty;

    if (hasActiveCard) {
      await _navigationProvider.showBottomSheet(
        const LinkSharedCardBottomSheetConfig.participantWithActiveCard(),
      );
    } else {
      await _navigationProvider.showBottomSheet(
        LinkSharedCardBottomSheetConfig.participantWithTerminatedCard(
          member: member,
        ),
      );
    }
  }
}
