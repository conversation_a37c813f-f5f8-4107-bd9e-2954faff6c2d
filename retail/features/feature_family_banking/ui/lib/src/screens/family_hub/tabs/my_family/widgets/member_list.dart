part of '../my_family_tab.dart';

final class _OwnersSectionTitle extends StatelessWidget {
  final VoidCallback? onAddPressed;
  final bool showAddButton;

  const _OwnersSectionTitle({
    this.onAddPressed,
    this.showAddButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    return SectionTitle(
      title: l10n.myFamilyOwnersSectionTitle,
      buttonTitle: l10n.myFamilyAddOwnerButtonTitle,
      buttonIcon: const GraphicAssetPointer.icon(CompanyIconPointer.plus),
      onButtonPressed: onAddPressed,
      showButton: showAddButton,
    );
  }
}

final class _ParticipantsSectionTitle extends StatelessWidget {
  final VoidCallback? onAddPressed;
  final bool showAddButton;

  const _ParticipantsSectionTitle({
    this.onAddPressed,
    this.showAddButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    return SectionTitle(
      title: l10n.myFamilyParticipantsSectionTitle,
      buttonTitle: l10n.myFamilyAddParticipantButtonTitle,
      buttonIcon: const GraphicAssetPointer.icon(CompanyIconPointer.plus),
      onButtonPressed: onAddPressed,
      showButton: showAddButton,
    );
  }
}

class _MemberList extends StatelessWidget {
  final List<AcceptedMember> members;
  final ValueSetter<AcceptedMember>? onPressed;

  const _MemberList({
    required this.members,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    return SliverGrid.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: members.length,
      itemBuilder: (context, index) {
        final member = members[index];
        final cardCount = member.cardCount;
        final accountCount = member.accountCount;
        final pocketCount = member.pocketCount;
        final fixedSavingSpaceCount = member.fixedSavingSpaceCount;

        return _MemberCard(
          onPressed: onPressed?.maybeCall(member),
          title: member.name,
          label: member.relationshipToInviter ?? '',
          avatar: member.avatar,
          items: switch (member.role) {
            MemberRole.coOwner => [
                if (cardCount > 0)
                  (
                    title: l10n.myFamilyOwnerCardCountLabel(cardCount),
                    color: CompanyColorPointer.secondary8,
                  ),
                if (fixedSavingSpaceCount > 0)
                  (
                    title:
                        l10n.myFamilyOwnerFSSCountLabel(fixedSavingSpaceCount),
                    color: CompanyColorPointer.secondary12,
                  ),
                if (pocketCount > 0)
                  (
                    title: l10n.myFamilySharedPocketCountLabel(pocketCount),
                    color: CompanyColorPointer.surface13,
                  ),
                if (accountCount > 0)
                  (
                    title: l10n.myFamilyOwnerAccountCountLabel(accountCount),
                    color: CompanyColorPointer.primary4,
                  ),
              ],

            /// Note: adultParticipant will only have card not the pocket.
            MemberRole.participant || MemberRole.adultParticipant => [
                if (cardCount > 0)
                  (
                    title: l10n.myFamilyParticipantCardCountLabel(cardCount),
                    color: CompanyColorPointer.secondary8,
                  ),
                if (pocketCount > 0)
                  (
                    title:
                        l10n.myFamilyParticipantAccountCountLabel(pocketCount),
                    color: CompanyColorPointer.secondary12,
                  ),
              ],
            MemberRole.sharedOwner => const [],
          },
        );
      },
    );
  }
}

extension on AcceptedMember {
  int get accountCount => products.whereType<SharedAccountProduct>().length;

  int get pocketCount => products.whereType<SharedPocketProduct>().length;

  int get cardCount => products.whereType<SharedCardProduct>().length;

  int get fixedSavingSpaceCount =>
      products.whereType<SharedFixedSavingSpaceProduct>().length;
}
