part of 'analytics_section.dart';

class _MoneyMovementContent extends SafeStatelessWidget {
  final MoneyMovementDetails moneyMovementDetails;

  const _MoneyMovementContent({
    required this.moneyMovementDetails,
  });

  @override
  Widget buildSafe(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final l10n = FamilyBankingLocalizations.of(context);
    final savedMoney = moneyMovementDetails.savedMoney;

    return SizedBox(
      width: size.width - _AnalyticsContent._basePadding.start * 3,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: context.colorStyling.surface2,
          borderRadius: BorderRadius.circular(14),
        ),
        child: Padding(
          padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Label(
                model: LabelModel(
                  text: l10n.myFamilyAnalyticsMoneyMovementTitle,
                  textStyle: CompanyTextStylePointer.b4,
                  color: CompanyColorPointer.secondary4,
                ),
              ),
              const Space.vertical(SpacingX.s2),
              Figure(
                model: FigureModel.h2OneLineTwoSizes(
                  moneyMovementDetails.movedInMoney,
                ),
              ),
              Label(
                model: LabelModel(
                  text: l10n.myFamilyAnalyticsMoneyMovementChartTitle,
                  textStyle: CompanyTextStylePointer.b3,
                  color: CompanyColorPointer.secondary3,
                ),
              ),
              const Space.vertical(SpacingX.s4),
              _MoneyMovementComparisonChart(
                movedInMoney: moneyMovementDetails.movedInMoney,
                movedOutMoney: moneyMovementDetails.movedOutMoney,
              ),
              const Space.vertical(SpacingX.s5),
              const Spacer(),
              _MovementLabel(savedMoney: savedMoney),
            ],
          ),
        ),
      ),
    );
  }
}

class _MoneyMovementComparisonChart extends StatelessWidget {
  final Money movedInMoney;
  final Money movedOutMoney;

  const _MoneyMovementComparisonChart({
    required this.movedInMoney,
    required this.movedOutMoney,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    final moneyInTitle = l10n.myFamilyAnalyticsMoneyMovementChartItemIn;
    final moneyOutTitle = l10n.myFamilyAnalyticsMoneyMovementChartItemOut;

    // If both money equal 0, don't show titles.
    // If one of the money equals 0, show 0 and
    // only the other money with the sign.
    // If both money are not zero, show them with the sign.
    final (moneyInMoneyText, moneyOutMoneyText) = (
      movedInMoney.isZero
          ? movedOutMoney.isZero
              ? null
              : movedInMoney.withoutCodeFormat()
          : '+${movedInMoney.withoutCodeFormat()}',
      movedOutMoney.isZero
          ? movedInMoney.isZero
              ? null
              : movedOutMoney.withoutCodeFormat()
          : '-${movedOutMoney.withoutCodeFormat()}',
    );

    final isMoneyInBigger = movedInMoney >= movedOutMoney;

    final (biggerMoney, smallerMoney) = isMoneyInBigger
        ? (movedInMoney, movedOutMoney)
        : (movedOutMoney, movedInMoney);

    // coefficient calculated according to the design
    final bigItemHeight = MediaQuery.sizeOf(context).height * 0.17;
    final smallItemHeight = smallerMoney.isZero
        ? bigItemHeight * 0.45
        : bigItemHeight *
            (smallerMoney.minorUnits / biggerMoney.minorUnits).clamp(0.3, 1);

    final (inMoneyHeight, outMoneyHeight) = isMoneyInBigger
        ? (bigItemHeight, smallItemHeight)
        : (smallItemHeight, bigItemHeight);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        _MoneyMovementComparisonChartItem(
          model: (
            title: moneyInTitle,
            isLeftItem: true,
            isActive: !movedInMoney.isZero,
            height: inMoneyHeight,
            moneyTitle: moneyInMoneyText,
          ),
        ),
        _MoneyMovementComparisonChartItem(
          model: (
            title: moneyOutTitle,
            isLeftItem: false,
            isActive: !movedOutMoney.isZero,
            height: outMoneyHeight,
            moneyTitle: moneyOutMoneyText,
          ),
        ),
      ],
    );
  }
}

typedef _MoneyMovementComparisonChartModel = ({
  String title,
  bool isLeftItem,
  bool isActive,
  double height,
  String? moneyTitle,
});

class _MoneyMovementComparisonChartItem extends StatelessWidget {
  static const activeBigBlockColor = CompanyColorPointer.chart1;
  static const activeSmallBlockColor = CompanyColorPointer.chart2;
  static const inactiveBigBlockColor = CompanyColorPointer.secondary6;
  static const inactiveSmallBlockColor = CompanyColorPointer.background1;

  final _MoneyMovementComparisonChartModel model;

  const _MoneyMovementComparisonChartItem({
    required this.model,
  });

  @override
  Widget build(BuildContext context) {
    final color = model.isActive
        ? model.isLeftItem
            ? activeBigBlockColor
            : activeSmallBlockColor
        : model.isLeftItem
            ? inactiveBigBlockColor
            : inactiveSmallBlockColor;

    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        spacing: SpacingX.s2,
        children: [
          if (model.moneyTitle case final moneyTitle?)
            Label(
              model: LabelModel(
                text: moneyTitle,
              ),
            ),
          SizedBox(
            height: model.height,
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: color.colorOf(context),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: model.isLeftItem
                      ? const Radius.circular(16)
                      : Radius.zero,
                  bottomRight: !model.isLeftItem
                      ? const Radius.circular(16)
                      : Radius.zero,
                ),
              ),
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: SpacingX.s4),
                  child: Label(
                    model: LabelModel(
                      text: model.title,
                      textStyle: CompanyTextStylePointer.b4,
                      color: model.isActive
                          ? CompanyColorPointer.secondary14
                          : CompanyColorPointer.secondary3,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _MovementLabel extends StatelessWidget {
  final Money savedMoney;

  const _MovementLabel({
    required this.savedMoney,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);
    final savedMoneyString = savedMoney.toDisplayFormat();

    if (savedMoney.isPositive) {
      return CompanyRichText(
        CompanyRichTextModel(
          text: l10n.myFamilyAnalyticsMoneyMovementBottomSubtitle(
            savedMoneyString,
          ),
          textAlign: TextAlign.start,
          normalStyle: CompanyTextStylePointer.b4,
          normalTextColor: CompanyColorPointer.primary3,
          accentStyle: CompanyTextStylePointer.b4,
          accentTextColor: CompanyColorPointer.primary1,
          highlightedTextModels: [
            HighlightedTextModel(savedMoneyString),
          ],
        ),
      );
    }

    return Label(
      model: LabelModel(
        text: l10n.myFamilyAnalyticsMoneyMovementBottomSubtitleEmptyData,
        textStyle: CompanyTextStylePointer.b4,
        color: CompanyColorPointer.primary3,
      ),
    );
  }
}
