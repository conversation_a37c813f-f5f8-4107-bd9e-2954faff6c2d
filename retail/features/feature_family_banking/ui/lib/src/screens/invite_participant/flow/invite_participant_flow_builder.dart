import 'dart:async';

import 'package:flutter/material.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_family_banking_ui/src/common/flow_page.dart';
import 'package:wio_feature_family_banking_ui/src/screens/invite_participant/flow/cubit/invite_participant_cubit.dart';
import 'package:wio_feature_family_banking_ui/src/screens/invite_participant/flow/cubit/invite_participant_state.dart';

typedef InviteParticipantFlowStageBuilder = Widget? Function(
  BuildContext context,
  InviteParticipantStage stage,
  InviteParticipantState state,
);

class InviteParticipantFlowBuilder extends StatelessWidget {
  final InviteParticipantFlowStageBuilder stageBuilder;

  const InviteParticipantFlowBuilder({
    required this.stageBuilder,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<InviteParticipantCubit, InviteParticipantState>(
      buildWhen: (previous, current) => !current.isProcessing,
      builder: (context, state) => Navigator(
        pages: state.stages
            .map((it) => stageBuilder(context, it, state))
            .nonNulls
            .map((it) => it.toPage())
            .toList(),
        // ignore: deprecated_member_use, avoid_types_on_closure_parameters
        onPopPage: (route, Object? result) =>
            _onPopPage(context, state, route, result),
      ),
    );
  }

  bool _onPopPage(
    BuildContext context,
    InviteParticipantState state,
    Route<Object?> route,
    Object? result,
  ) {
    final cubit = context.read<InviteParticipantCubit>();
    if (route.settings is Page) {
      scheduleMicrotask(cubit.onPopPage);

      return false;
    }

    return route.didPop(result);
  }
}
