import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_family_banking_ui/src/common/models/contact.dart';

part 'member_phone_selection_state.freezed.dart';

@freezed
sealed class MemberPhoneSelectionState with _$MemberPhoneSelectionState {
  const MemberPhoneSelectionState._();

  /// Starting state.
  const factory MemberPhoneSelectionState.initial() =
      InitialMemberPhoneSelectionState;

  /// The state while getting required data and permissions.
  const factory MemberPhoneSelectionState.initializing() =
      InitializingMemberPhoneSelectionState;

  /// The state when contacts are available.
  const factory MemberPhoneSelectionState.fromContacts({
    @Default(ContactSearchState.initial()) ContactSearchState searchResult,
    Contact? selectedContact,
  }) = FromContactsMemberPhoneSelectionState;

  /// The state when contacts are not available.
  const factory MemberPhoneSelectionState.fromInput({
    @Default('') String value,
    Contact? selectedContact,
  }) = FromInputMemberPhoneSelectionState;

  const factory MemberPhoneSelectionState.failed({Object? error}) =
      FailedMemberPhoneSelectionState;

  bool get canConfirm => selectedContact != null;

  Contact? get selectedContact => mapOrNull(
        fromContacts: (it) => it.selectedContact,
        fromInput: (it) => it.selectedContact,
      );

  String? get inputValue => mapOrNull(
        fromContacts: (it) =>
            it.searchResult.contactsLength == 0 ? it.searchResult.query : null,
        fromInput: (it) => it.value,
      );
}

@freezed
sealed class ContactSearchState with _$ContactSearchState {
  const ContactSearchState._();

  const factory ContactSearchState.initial() = InitialContactSearchState;

  const factory ContactSearchState.inProgress({
    required String query,
  }) = InProgressContactSearchState;

  const factory ContactSearchState.idle({
    required String query,
    @Default(<Contact>[]) List<Contact> contacts,
  }) = IdleContactSearchState;

  String? get query => mapOrNull(
        inProgress: (it) => it.query,
        idle: (it) => it.query,
      );

  int? get contactsLength => mapOrNull(idle: (it) => it.contacts.length);
}
