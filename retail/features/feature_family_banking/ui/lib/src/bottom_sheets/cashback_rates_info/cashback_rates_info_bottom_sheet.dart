import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_family_banking_ui/feature_family_banking_mobile_ui.dart';
import 'package:wio_feature_family_banking_ui/src/common/views/bottom_sheet_title.dart';
import 'package:wio_feature_family_banking_ui/src/navigation/bottom_sheets/cashback_rates_info_bottom_sheet_config.dart';

class CashbackRatesInfoBottomSheet extends StatelessWidget {
  const CashbackRatesInfoBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(24, 8, 24, 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          BottomSheetTitle(title: l10n.cashbackRatesBottomSheetTitle),
          Space.fromSpacingVertical(Spacing.s4),
          Label(
            model: LabelModel(
              text: l10n.cashbackRatesBottomSheetDescription(
                Currency.aedSymbol,
              ),
              color: CompanyColorPointer.secondary1,
              textStyle: CompanyTextStylePointer.b4,
            ),
          ),
          Space.fromSpacingVertical(Spacing.s3),
          const _FamilyAccountCashbackRules(),
        ],
      ),
    );
  }
}

class _FamilyAccountCashbackRules extends StatelessWidget {
  const _FamilyAccountCashbackRules();

  @override
  Widget build(BuildContext context) {
    final l10n = FamilyBankingLocalizations.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      decoration: BoxDecoration(
        color: context.colorStyling.surface2,
        borderRadius: const BorderRadius.all(Radius.circular(14)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: SpacingX.s2,
        children: [
          Label(
            model: LabelModel(
              text: l10n.cashbackRatesBottomSheetFamilyCashbackRules,
              color: CompanyColorPointer.secondary1,
              textStyle: CompanyTextStylePointer.b3,
            ),
          ),
          GestureDetector(
            onTap: () => Navigator.of(context).pop(
              CashbackRatesResult.openPlans,
            ),
            child: Label(
              model: LabelModel(
                text: l10n.cashbackRatesBottomSheetLearnPlanRatesButtonTitle,
                textStyle: CompanyTextStylePointer.b4underline,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
