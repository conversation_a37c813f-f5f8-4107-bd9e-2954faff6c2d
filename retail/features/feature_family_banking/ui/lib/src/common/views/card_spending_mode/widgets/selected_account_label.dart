part of '../card_spending_mode_block.dart';

class _SelectedAccountLabel extends StatelessWidget {
  final String title;

  const _SelectedAccountLabel({
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: Spacing.s1.value,
      mainAxisSize: MainAxisSize.min,
      children: [
        const CompanyIcon(
          CompanyIconModel(
            icon: GraphicAssetPointer.icon(
              CompanyIconPointer.information,
            ),
            size: CompanyIconSize.small,
            color: CompanyColorPointer.secondary3,
          ),
        ),
        Label(
          model: LabelModel(
            text: title,
            textStyle: CompanyTextStylePointer.b4,
            color: CompanyColorPointer.secondary3,
          ),
        ),
      ],
    );
  }
}
