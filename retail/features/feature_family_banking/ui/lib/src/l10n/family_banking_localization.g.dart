// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class FamilyBankingLocalizations {
  FamilyBankingLocalizations._internal();

  static const LocalizationsDelegate<FamilyBankingLocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'activateAccountBottomSheetConfirmButtonTitle': [],
    'activateAccountBottomSheetDismissButtonTitle': [],
    'activateAccountBottomSheetPhoneNumberLabel': [],
    'activateAccountBottomSheetSubtitle': ['role', 'name'],
    'activateAccountBottomSheetTitle': ['invitationType'],
    'activateSharedPocketBottomsheetCoOwnerLabel': [],
    'activateSharedPocketBottomsheetConfirmButtonTitle': [],
    'activateSharedPocketBottomsheetDismissButtonTitle': [],
    'activateSharedPocketBottomsheetParticipantLabel': [],
    'activateSharedPocketBottomsheetSubTitle': ['name'],
    'activateSharedPocketBottomsheetTitle': [],
    'addAvatarScreenAddButtonTitle': [],
    'addAvatarScreenButtonTitle': [],
    'addAvatarScreenFooterText': [],
    'addAvatarScreenSkipButtonTitle': [],
    'addAvatarScreenSubtitle': [],
    'addAvatarScreenTitle': [],
    'addCoOwnerToPocketBannerHeader': [],
    'addCoOwnerToPocketBannerShareNowButtonTitle': [],
    'addCoOwnerToPocketBannerTitle': [],
    'adultParticipantInviteRelationshipScreenButtonTitle': [],
    'adultParticipantInviteRelationshipScreenPageTitle': [],
    'adultParticipantInviteRelationshipScreenSubTitle': [],
    'adultParticipantInviteRelationshipScreenTitle': ['name'],
    'adultParticipantRuleCardSubtitle1': [],
    'adultParticipantRuleCardSubtitle2': [],
    'adultParticipantRuleCardSubtitle3': [],
    'adultParticipantRuleCardTitle1': [],
    'adultParticipantRuleCardTitle2': [],
    'adultParticipantRuleCardTitle3': [],
    'adultParticipantRuleScreenButtonTitle': [],
    'adultParticipantRuleScreenComingSoonText': [],
    'adultParticipantRuleScreenHeader': [],
    'adultParticipantRuleScreenSubheader': [],
    'approveJointAccountClosureApproveButtonTitle': [],
    'approveJointAccountClosureConfirmationBottomSheetSubtitle': [],
    'approveJointAccountClosureConfirmationBottomSheetTitle': [],
    'approveJointAccountClosureConfirmationConfirmButtonTitle': [],
    'approveJointAccountClosureConfirmationGoBackButtonTitle': [],
    'approveJointAccountClosureFirstStepSubtitle': ['coOwner'],
    'approveJointAccountClosureFirstStepTitle': ['coOwner'],
    'approveJointAccountClosureHelpLabel': [],
    'approveJointAccountClosureMoneyMovedMessage': ['currency'],
    'approveJointAccountClosurePageTitle': [],
    'approveJointAccountClosureRejectButtonTitle': [],
    'approveJointAccountClosureSecondStepActionButtonTitle': [],
    'approveJointAccountClosureSecondStepSubtitle': [],
    'approveJointAccountClosureSecondStepTitle': [],
    'approveJointAccountClosureSubmitButtonTitle': [],
    'approveJointAccountClosureSuccessScreenDescriptionItem1': [],
    'approveJointAccountClosureSuccessScreenDescriptionItem2': [],
    'approveJointAccountClosureSuccessScreenDownloadStatementLabel': [],
    'approveJointAccountClosureSuccessScreenHeader': ['coOwner'],
    'approveJointAccountClosureSuccessScreenSubmitButtonTitle': [],
    'approveJointAccountClosureThirdStepSubtitle': [],
    'approveJointAccountClosureThirdStepTitle': [],
    'backgroundSelectionFooterButtonChooseDifferent': [],
    'backgroundSelectionFooterButtonConfirm': [],
    'backgroundSelectionHeaderTitle': [],
    'cancelInvitationSuccessButtonTitle': [],
    'cancelInvitationSuccessTitle': [],
    'cancelInviteConfirmationBottomsheetAgreeButtonTitle': [],
    'cancelInviteConfirmationBottomsheetBackButtonTitle': [],
    'cancelInviteConfirmationBottomsheetSubTitle': ['invitationType'],
    'cancelInviteConfirmationBottomsheetTitle': ['invitationType'],
    'cancelInviteScreenHeader': [],
    'cancelnvitationSuccessSubTitle': ['name'],
    'cannotInviteCoOwnerBottomSheetDescription': [],
    'cannotInviteCoOwnerBottomSheetTitle': [],
    'cannotInviteMemberBottomAddAsCoOwnerButtonTitle': [],
    'cannotInviteParticipantBottomSheetDescription': [],
    'cannotInviteParticipantBottomSheetGoBackButtonTitle': [],
    'cannotInviteParticipantBottomSheetTitle': [],
    'cardsBankCardLimitOverallLabel': ['amount'],
    'cardsBankCardLimitTitle': [],
    'cashbackRatesBottomSheetDescription': ['currencySymbol'],
    'cashbackRatesBottomSheetFamilyCashbackRules': [],
    'cashbackRatesBottomSheetLearnPlanRatesButtonTitle': [],
    'cashbackRatesBottomSheetTitle': [],
    'closeJointAccountConfirmationBottomSheetConfirmButtonTitle': [],
    'closeJointAccountConfirmationBottomSheetGoBackButtonTitle': [],
    'closeJointAccountConfirmationBottomSheetSubtitle': ['coOwner'],
    'closeJointAccountConfirmationBottomSheetTitle': [],
    'closeJointAccountDownloadStatementAccountStatementLabel': [],
    'closeJointAccountDownloadStatementPageSubtitle': [],
    'closeJointAccountDownloadStatementPageTitle': [],
    'closeJointAccountDownloadStatementSubmitButtonTitle': [],
    'closeJointAccountFollowStepsFirstStepActionButtonTitle': [],
    'closeJointAccountFollowStepsFirstStepSubtitle': [],
    'closeJointAccountFollowStepsFirstStepTitle': [],
    'closeJointAccountFollowStepsHelpLabel': [],
    'closeJointAccountFollowStepsMoneyMovedMessage': ['currency'],
    'closeJointAccountFollowStepsPageTitle': [],
    'closeJointAccountFollowStepsSecondStepSubtitle': ['coOwner'],
    'closeJointAccountFollowStepsSecondStepTitle': ['coOwner'],
    'closeJointAccountFollowStepsSubmitButtonTitle': [],
    'closeJointAccountFollowStepsThirdStepSubtitle': ['coOwner'],
    'closeJointAccountFollowStepsThirdStepTitle': [],
    'closeJointAccountIntroBenefitFour': [],
    'closeJointAccountIntroBenefitOne': ['coOwner'],
    'closeJointAccountIntroBenefitThree': [],
    'closeJointAccountIntroBenefitTwo': [],
    'closeJointAccountIntroHelpLabel': [],
    'closeJointAccountIntroPageSubtitle': [],
    'closeJointAccountIntroPageTitle': [],
    'closeJointAccountIntroPrimaryButtonTitle': [],
    'closeJointAccountIntroSecondaryButtonTitle': [],
    'closeJointAccountSelectReasonOtherReasonInputLabel': [],
    'closeJointAccountSelectReasonPageSubtitle': [],
    'closeJointAccountSelectReasonPageTitle': [],
    'closeJointAccountSelectReasonScreenReasonLabelOf': ['reason'],
    'closeJointAccountSelectReasonSubmitButtonTitle': [],
    'closeJointAccountSuccessScreenDescription': [],
    'closeJointAccountSuccessScreenSubmitButtonTitle': [],
    'closeJointAccountSuccessScreenSubtitle': ['coOwner'],
    'closeJointAccountSuccessScreenTitle': [],
    'closureRequestStatusApprovedText': [],
    'closureRequestStatusCancelledText': [],
    'closureRequestStatusRejectedText': [],
    'confirmedInvitationBottomSheetButtonTitle': [],
    'confirmedInvitationBottomSheetSubtitle': [],
    'confirmedInvitationBottomSheetTitle': ['inviterName'],
    'createJointAccountSelectMemberNameAppBarTitle': [],
    'createJointAccountSelectMemberNameConfirmButtonTitle': [],
    'createJointAccountSelectMemberNameInputErrorMessage': ['count'],
    'createJointAccountSelectMemberNameInputLabel': [],
    'createJointAccountSelectMemberNameSubtitle': [],
    'createJointAccountSelectMemberNameTitle': [],
    'createJointAccountSelectMemberPhoneAppBarTitle': [],
    'createJointAccountSelectMemberPhoneConfirmButtonTitle': [],
    'createJointAccountSelectMemberPhoneInfoMessage': [],
    'createJointAccountSelectMemberPhoneInputLabel': [],
    'createJointAccountSelectMemberPhoneInvalidPhoneMessage': [],
    'createJointAccountSelectMemberPhoneOnlyNumberInputLabel': [],
    'createJointAccountSelectMemberPhonePermissionBannerText': [],
    'createJointAccountSelectMemberPhoneSubtitle': [],
    'createJointAccountSelectMemberPhoneTitle': [],
    'createSharedCardCustomerFeedbackHighlightedTitle': [],
    'createSharedCardCustomerFeedbackTitle': [],
    'currentUserProfileScreenAvatarUpdateResultCompleted': [],
    'currentUserProfileScreenEditHubNameTitle': [],
    'currentUserProfileScreenHeaderTitle': [],
    'currentUserProfileScreenHeaderUpdateAvatarButton': [],
    'currentUserProfileScreenSharedProdcutsListTitle': [],
    'editFamilyHubNameBottomsheetButtonTitle': [],
    'editFamilyHubNameBottomsheetInputLabel': [],
    'editFamilyHubNameBottomsheetSuccessMessage': [],
    'editFamilyHubNameBottomsheetTitle': [],
    'existingUserInvitationCancelledErrorButtonTitle': [],
    'existingUserInvitationCancelledErrorSubtitle': ['inviterName'],
    'existingUserInvitationCancelledErrorTitle': [],
    'existingUserInvitationErrorButtonTitle': [],
    'existingUserInvitationErrorSubtitle': [],
    'existingUserInvitationErrorTitle': [],
    'existingUserInvitationExpiredErrorButtonTitle': [],
    'existingUserInvitationExpiredErrorSubtitle': ['inviterName'],
    'existingUserInvitationExpiredErrorTitle': [],
    'existingUserInvitationRejectedByInviterErrorButtonTitle': [],
    'existingUserInvitationRejectedByInviterErrorSubtitle': ['inviterName'],
    'existingUserInvitationRejectedByInviterErrorTitle': [],
    'existingUserInvitationRejectedErrorButtonTitle': [],
    'existingUserInvitationRejectedErrorInviteeViewTitle': [],
    'existingUserInvitationRejectedErrorSubtitle': ['name'],
    'existingUserInvitationRejectedErrorTitle': [],
    'familyAccountsPocketsSectionTitle': [],
    'familyAccountsTabAddMoneyButtonTitle': [],
    'familyAccountsTabRecurringRulesButtonTitle': [],
    'familyBanking': [],
    'familyHubHeader': ['name'],
    'familyHubTabName': ['tab'],
    'familyJointAccountInfoBottomSheetBody': [],
    'familyJointAccountInfoBottomSheetTitle': [],
    'familyParticipantAccountBottomSheetBody': [],
    'familyParticipantAccountBottomSheetTitle': [],
    'familyRewardsAllTimeTabName': [],
    'familyRewardsCashbackRatesButtonTitle': [],
    'familyRewardsEmptyTransactionsSubtitle': [],
    'familyRewardsEmptyTransactionsTitle': [],
    'familyRewardsTransactionsSectionTitle': [],
    'familySavingsAddButtonTitle': [],
    'familySavingsSectionTitle': [],
    'familySavingsTabEmptyStateButtonTitle': [],
    'familySavingsTabEmptyStateSubtitle': [],
    'familySavingsTabEmptyStateTitle': [],
    'familySavingsTabNoFamilyEmptyStateSubtitle': [],
    'familyTabAddMoneyButtonTitle': [],
    'familyTabParticipantAccountZeroBalancePromoForPocketTitle': [
      'cashback',
      'name'
    ],
    'familyTabParticipantAccountZeroBalanceTitle': ['name'],
    'familyTermDepositDeadlineText': ['date', 'months'],
    'invitationParticipantScreenAcceptInviteButtonTitle': [],
    'invitationParticipantScreenAgeWarningText': [],
    'invitationParticipantScreenLearnMoreLabel': [],
    'invitationParticipantScreenRejectButtonTitle': [],
    'invitationParticipantScreenSubtitle': ['inviter'],
    'invitationParticipantScreenTitle': [],
    'invitationRejectionReasonsScreenOtherReason': [],
    'invitationRejectionReasonsScreenReason1': ['inviter'],
    'invitationRejectionReasonsScreenReason2': [],
    'invitationRejectionReasonsScreenReason3': [],
    'invitationRejectionReasonsScreenSubmitButtonTitle': [],
    'invitationRejectionReasonsScreenSubtitle': [],
    'invitationRejectionReasonsScreenTitle': [],
    'invitationScreenAcceptInviteButtonTitle': [],
    'invitationScreenAppBarTitle': ['type'],
    'invitationScreenLearnMoreLabel': [],
    'invitationScreenRejectButtonTitle': [],
    'invitationScreenSharedPocketAcceptCta': [],
    'invitationScreenSharedPocketDoablesHeader': [],
    'invitationScreenSharedPocketDoablesPt1Subtitle': [],
    'invitationScreenSharedPocketDoablesPt1Title': [],
    'invitationScreenSharedPocketDoablesPt2Subtitle': [],
    'invitationScreenSharedPocketDoablesPt2Title': [],
    'invitationScreenSharedPocketDoablesPt3Subtitle': [],
    'invitationScreenSharedPocketDoablesPt3Title': [],
    'invitationScreenSharedPocketRejectCta': [],
    'invitationScreenSharedPocketSubtitle': ['CoOwner'],
    'invitationScreenSharedPocketTitle': [],
    'invitationScreenSkipForNowButtonTitle': [],
    'invitationScreenSubtitle': ['currency', 'inviter'],
    'invitationScreenTermAndConditionsInfoHighlightedLabel': [],
    'invitationScreenTermAndConditionsInfoLabel': [],
    'invitationScreenTitle': [],
    'invitationsScreenActionAlreadyTakenErrorMessage': ['takenAction'],
    'invitationsScreenAppBarTitle': [],
    'invitationsScreenInvitationAcceptedMessage': ['inviter'],
    'invitationsScreenInvitationCardAcceptedLabel': [],
    'invitationsScreenInvitationCardRejectedLabel': [],
    'invitationsScreenInvitationCardSkippedLabel': [],
    'invitationsScreenInvitationCardSubtitle': ['duration'],
    'invitationsScreenInvitationCardTimeAgoLabel': ['type', 'amount'],
    'invitationsScreenInvitationRejectedMessage': [],
    'invitationsScreenInvitationSkippedMessage': [],
    'invitationsScreenSubmitButtonTitle': [],
    'invitationsScreenSubtitle': [],
    'invitationsScreenTitle': [],
    'inviteAdultParticipantAccountSelectionFACardSubtitle': [],
    'inviteAdultParticipantAccountSelectionFACardTitle': [],
    'inviteAdultParticipantAccountSelectionHeader': [],
    'inviteAdultParticipantAccountSelectionNoFACardCta': [],
    'inviteAdultParticipantAccountSelectionNoFACardDesc': [],
    'inviteAdultParticipantAccountSelectionTitle': [],
    'inviteAdultParticipantCustomerFeedbackHighlightedTitle': [],
    'inviteAdultParticipantCustomerFeedbackTitle': [],
    'inviteAdultParticipantFASelectionHeader': [],
    'inviteAdultParticipantFASelectionListBoxTitle': ['coOwnerName'],
    'inviteAdultParticipantFASelectionTitle': [],
    'inviteAdultParticipantPersonalAccountSelectionCardSubtitle': [],
    'inviteAdultParticipantPersonalAccountSelectionCardTitle': [],
    'inviteAdultParticipantReviewScreenCardPreviewCredit': [],
    'inviteAdultParticipantReviewScreenCardPreviewSubtitleCredit': [],
    'inviteAdultParticipantReviewScreenCardPreviewSubtitleFamily': [],
    'inviteAdultParticipantReviewScreenCardPreviewSubtitleMyMoney': [],
    'inviteAdultParticipantReviewScreenExpiryCardSubTitle': [],
    'inviteAdultParticipantReviewScreenExpiryCardTitle': [],
    'inviteAdultParticipantReviewScreenSwitchCredit': [],
    'inviteAdultParticipantReviewScreenSwitchMyMoney': [],
    'inviteAdultParticipantSetUpCardAppBarTitle': [],
    'inviteAdultParticipantSpendingLimitScreenSubTitle': ['name'],
    'inviteAdultParticipantSpendingLimitScreenTitle': [],
    'inviteAdultParticipantSuccessScreenButtonTitle': [],
    'inviteAdultParticipantSuccessScreenSubTitle': ['inviteeName'],
    'inviteAdultParticipantSuccessScreenTitle': [],
    'inviteAdultParticpantReviewScreenButtonTitle': [],
    'inviteAdultParticpantReviewScreenPageTitle': [],
    'inviteAdultParticpantReviewScreenRecipientCardTitle': [],
    'inviteAdultParticpantReviewScreenTitle': [],
    'inviteCoOwnerCustomerFeedbackHighlightedTitle': [],
    'inviteCoOwnerCustomerFeedbackTitle': [],
    'inviteCoOwnerToPocketAppBarTitle': [],
    'inviteCoOwnerToPocketConfirmationBottomSheetDescription': [],
    'inviteCoOwnerToPocketConfirmationBottomSheetTitle': [
      'coOwnerName',
      'participantName'
    ],
    'inviteCoOwnerToPocketConfirmationGoBackButtonTitle': [],
    'inviteCoOwnerToPocketConfirmationSubmitButtonTitle': [],
    'inviteCoOwnerToPocketFamilyAccountListBoxTitle': ['coOwnerName'],
    'inviteCoOwnerToPocketPageSubtitle': [],
    'inviteCoOwnerToPocketPageTitle': [],
    'inviteCoOwnerToPocketSuccessGoToHubButtonTitle': [],
    'inviteCoOwnerToPocketSuccessSubtitle': [],
    'inviteCoOwnerToPocketSuccessTitle': [],
    'inviteParticipantAppBarTitle': [],
    'inviteParticipantCardComponentShortTitle': ['name'],
    'inviteParticipantCustomerFeedbackHighlightedTitle': [],
    'inviteParticipantCustomerFeedbackTitle': [],
    'inviteParticipantSetUpCardNameErrorNameAlreadyExists': [],
    'inviteParticipantSetUpCardNameErrorNameTooLong': [],
    'inviteParticipantSetUpCardNameInputHint': [],
    'inviteParticipantSetUpCardNamePageTitle': ['name'],
    'inviteParticipantSetUpCardNextButtonTitle': [],
    'inviteParticipantSetUpCardSkinPageTitle': [],
    'inviteParticipantSetUpCardSkinUngroupedSectionTitle': [],
    'inviteParticipantSpendingLimitScreenSubTitle': ['name'],
    'inviteParticipantSpendingLimitScreenTitle': [],
    'inviteParticipantSuccessScreenButtonTitle': [],
    'inviteParticipantSuccessScreenSubTitle': ['name'],
    'inviteParticipantSuccessScreenTitle': [],
    'inviteParticpantReviewScreenButtonTitle': [],
    'inviteParticpantReviewScreenExpiryCardSubTitle': [],
    'inviteParticpantReviewScreenExpiryCardTitle': [],
    'inviteParticpantReviewScreenPageTitle': [],
    'inviteParticpantReviewScreenPermissionTitle': [],
    'inviteParticpantReviewScreenSubTitle': ['name'],
    'inviteParticpantReviewScreenTitle': [],
    'inviteParticpantReviewScreenTncHighlightedTitle': [],
    'inviteParticpantReviewScreenTncTitle': [],
    'jointAccountCoOwnerRulesCardSubTitle1': [],
    'jointAccountCoOwnerRulesCardSubTitle2': [],
    'jointAccountCoOwnerRulesCardSubTitle3': [],
    'jointAccountCoOwnerRulesCardTitle1': [],
    'jointAccountCoOwnerRulesCardTitle2': [],
    'jointAccountCoOwnerRulesCardTitle3': [],
    'jointAccountInvitationAcceptedScreenButtonTitle': [],
    'jointAccountInvitationAcceptedScreenSubtitle': ['inviterName'],
    'jointAccountInvitationAcceptedScreenTitle': [],
    'jointAccountInvitationConfirmationButtonTitle': [],
    'jointAccountInvitationConfirmationSubTitle': [],
    'jointAccountInvitationConfirmationTitle': [],
    'jointAccountInvitationRejectedScreenButtonTitle': [],
    'jointAccountInvitationRejectedScreenSubtitle': [],
    'jointAccountInvitationRejectedScreenTitle': [],
    'jointAccountInviteSentNextStepsSectionTitle': [],
    'jointAccountInviteSentPageButtonTitle': [],
    'jointAccountInviteSentPageSubtitleExistingUser': ['name'],
    'jointAccountInviteSentPageSubtitleNewUser': ['name', 'phone'],
    'jointAccountInviteSentPageTitle': [],
    'jointAccountInviteSentStep1SubtitleExistingUser': ['name'],
    'jointAccountInviteSentStep1SubtitleNewUser': ['name'],
    'jointAccountInviteSentStep1TitleExistingUser': [],
    'jointAccountInviteSentStep1TitleNewUser': [],
    'jointAccountInviteSentStep2SubtitleExistingUser': ['name'],
    'jointAccountInviteSentStep2SubtitleNewUser': [],
    'jointAccountInviteSentStep2TitleExistingUser': ['name'],
    'jointAccountInviteSentStep2TitleNewUser': ['name'],
    'jointAccountInviteSentStep3SubtitleExistingUser': ['name'],
    'jointAccountInviteSentStep3SubtitleNewUser': ['name'],
    'jointAccountInviteSentStep3TitleExistingUser': ['name'],
    'jointAccountInviteSentStep3TitleNewUser': ['name'],
    'jointAccountsTabAccountSubTitle': ['currency'],
    'jointAccountsTabDetailsButtonTitle': [],
    'jointAccountsTabEmptyStateSubtitle': [],
    'jointAccountsTabEmptyStateTitle': [],
    'jointAccountsTabManageButtonTitle': [],
    'jointAccountsTabMoveMoneyButtonTitle': [],
    'jointAccountsTabSectionTitle': [],
    'jointAccountsTabSendButtonTitle': [],
    'manageInviteApproveInvitationSuccessButtonTitle': [],
    'manageInviteApproveInvitationSuccessSubTitle': [
      'inviteType',
      'name',
      'participantName'
    ],
    'manageInviteApproveInvitationSuccessTitle': [],
    'manageScreenCoOwnerTitle': [],
    'memberProfileAddRelationshipCoachmarkBody': ['name'],
    'memberProfileAddRelationshipCoachmarkButtonTitle': [],
    'memberProfileAddRelationshipCoachmarkTitle': [],
    'memberProfileAddedCoOwnerCardSubTitle': [],
    'memberProfileAddedCoOwnerSectionTitle': [],
    'memberProfileRecentTransactionsEmptySubTitle': [],
    'memberProfileRecentTransactionsEmptyTitle': [],
    'memberProfileScreenAddRelationshipButtonTitle': [],
    'memberProfileScreenAllTransactionActionTitle': [],
    'memberProfileScreenSharedProcutsSectionTitle': ['name'],
    'memberProfileScreenSubTitle': ['role'],
    'memberProfileScreenTitle': [],
    'memberProfileScreenTransactionSectionTitle': [],
    'memberRelationshipSelectionScreenButtonTitle': [],
    'memberRelationshipSelectionScreenPageTitle': [],
    'memberRelationshipSelectionScreenTitle': ['name'],
    'myFamilyAddOwnerButtonTitle': [],
    'myFamilyAddParticipantButtonTitle': [],
    'myFamilyAnalyticsMoneyMovementBottomSubtitle': ['amountWithCurrency'],
    'myFamilyAnalyticsMoneyMovementBottomSubtitleEmptyData': [],
    'myFamilyAnalyticsMoneyMovementChartItemIn': [],
    'myFamilyAnalyticsMoneyMovementChartItemOut': [],
    'myFamilyAnalyticsMoneyMovementChartTitle': [],
    'myFamilyAnalyticsMoneyMovementTitle': [],
    'myFamilyAnalyticsSpentMembersBottomSubtitle': ['biggerPercent', 'member'],
    'myFamilyAnalyticsSpentMembersBottomSubtitleEmptyData': [],
    'myFamilyAnalyticsSpentMembersListItemTransactions': ['count'],
    'myFamilyAnalyticsSpentMembersListTitle': [],
    'myFamilyAnalyticsSpentMembersTitle': [],
    'myFamilyAnalyticsTotalWealthBottomSheetSubtitle': [],
    'myFamilyAnalyticsTotalWealthBottomSheetTitle': [],
    'myFamilyAnalyticsTotalWealthTitle': [],
    'myFamilyEmptyOwnerStateSubtitle': [],
    'myFamilyEmptyOwnerStateTitle': [],
    'myFamilyEmptyParticipantStateSubtitle': [],
    'myFamilyEmptyParticipantStateTitle': [],
    'myFamilyEmptyStateAddMemberButtonTitle': [],
    'myFamilyEmptyStateSubtitle': [],
    'myFamilyEmptyStateTitle': [],
    'myFamilyOwnerAccountCountLabel': ['count'],
    'myFamilyOwnerCardCountLabel': ['count'],
    'myFamilyOwnerFSSCountLabel': ['count'],
    'myFamilyOwnersSectionTitle': [],
    'myFamilyParticipantAccountCountLabel': ['count'],
    'myFamilyParticipantCardCountLabel': ['count'],
    'myFamilyParticipantsSectionTitle': [],
    'myFamilySharedPocketCountLabel': ['count'],
    'myFamilyTabActivateInvitationButtonTitle': [],
    'myFamilyTabAdultParticipantInvitationAcceptedTitle': ['name'],
    'myFamilyTabAdultParticipantInvitationCoOwnerAcceptedTitle': [
      'inviteeName',
      'inviterName'
    ],
    'myFamilyTabAdultParticipantInvitationCoOwnerCreatedTitle': [
      'inviteeName',
      'inviterName'
    ],
    'myFamilyTabAdultParticipantInvitationCoOwnerExpiredTitle': [
      'inviteeName',
      'inviterName'
    ],
    'myFamilyTabAdultParticipantInvitationCoOwnerNotOnboardedTitle': [
      'inviteeName',
      'inviterName'
    ],
    'myFamilyTabAdultParticipantInvitationCoOwnerRejectedTitle': [
      'inviteeName',
      'inviterName'
    ],
    'myFamilyTabAdultParticipantInvitationCreatedTitle': ['name'],
    'myFamilyTabAdultParticipantInvitationExpiredTitle': ['name'],
    'myFamilyTabAdultParticipantInvitationNotOnboardedTitle': ['name'],
    'myFamilyTabAdultParticipantInvitationRejectedTitle': ['name'],
    'myFamilyTabCancelInvitationButtonTitle': [],
    'myFamilyTabOwnerInvitationAcceptedTitle': ['name'],
    'myFamilyTabOwnerInvitationCreatedTitle': ['name'],
    'myFamilyTabOwnerInvitationExpiredTitle': ['name'],
    'myFamilyTabOwnerInvitationNotOnboardedTitle': ['name'],
    'myFamilyTabOwnerInvitationRejectedTitle': ['name'],
    'myFamilyTabParticipantInvitationAcceptedTitle': ['name'],
    'myFamilyTabParticipantInvitationCreatedTitle': ['name'],
    'myFamilyTabParticipantInvitationExpiredTitle': ['name'],
    'myFamilyTabParticipantInvitationNotOnboardedTitle': ['name'],
    'myFamilyTabParticipantInvitationRejectedTitle': ['name'],
    'myFamilyTabRemoveInvitationButtonTitle': [],
    'myFamilyTabResendInvitationButtonTitle': [],
    'myFamilyTabSharedPocketInvitationAcceptedTitle': ['name'],
    'myFamilyTabSharedPocketInvitationCreatedTitle': ['name'],
    'myFamilyTabSharedPocketInvitationExpiredTitle': ['name'],
    'myFamilyTabSharedPocketInvitationRejectedTitle': ['name'],
    'participantInviteRelationshipScreenButtonTitle': [],
    'participantInviteRelationshipScreenPageTitle': [],
    'participantInviteRelationshipScreenSubTitle': [],
    'participantInviteRelationshipScreenTitle': ['name'],
    'participantInviteRulesCardSubTitle1': [],
    'participantInviteRulesCardSubTitle2': [],
    'participantInviteRulesCardSubTitle3': [],
    'participantInviteRulesCardSubTitle4': [],
    'participantInviteRulesCardTitle1': [],
    'participantInviteRulesCardTitle2': [],
    'participantInviteRulesCardTitle3': [],
    'participantInviteRulesCardTitle4': [],
    'participantInviteRulesScreenPrimaryButtonTitle': [],
    'participantInviteRulesScreenSecondaryButtonTitle': [],
    'participantInviteRulesScreenSubTitle': [],
    'participantInviteRulesScreenTitle': [],
    'pendingInvitationBottomSheetButtonTitle': [],
    'pendingInvitationBottomSheetSubtitle': [],
    'pendingInvitationBottomSheetTitle': ['inviterName'],
    'pendingInvitationStatusScreenButtonTitle': [],
    'pendingInvitationStatusScreenSubtitle': ['inviterName'],
    'pendingInvitationStatusScreenTitle': [],
    'pendingInvitationStatusStep1Subtitle': ['inviterName'],
    'pendingInvitationStatusStep1Title': [],
    'pendingInvitationStatusStep2Subtitle': ['inviterName'],
    'pendingInvitationStatusStep2Title': [],
    'pendingInvitationStatusStep3Subtitle': ['inviterName'],
    'pendingInvitationStatusStep3Title': ['inviterName'],
    'pendingInviterApprovalInvitationStatusScreenButtonTitle': [],
    'pendingInviterApprovalInvitationStatusScreenStep1SubTitle': ['name'],
    'pendingInviterApprovalInvitationStatusScreenStep1Title': [],
    'pendingInviterApprovalInvitationStatusScreenStep2SubTitle': ['name'],
    'pendingInviterApprovalInvitationStatusScreenStep2Title': ['name'],
    'pendingInviterApprovalInvitationStatusScreenStep3SubTitle': ['name'],
    'pendingInviterApprovalInvitationStatusScreenStep3Title': ['name'],
    'pendingInviterApprovalInvitationStatusScreenSubTitle': ['name'],
    'pendingInviterApprovalInvitationStatusScreenTitle': [],
    'pendingPocketInviteCoOwnerCardTitle': ['name'],
    'pendingSharedPocketInvitationBottomSheetButtonTitle': [],
    'pendingSharedPocketInvitationBottomSheetSubtitle': ['participantName'],
    'pendingSharedPocketInvitationBottomSheetTitle': [
      'inviterName',
      'participantName'
    ],
    'pocketAccountsTabAccountSubTitle': ['currency'],
    'promoForPocketsBannerBody': ['cashback'],
    'promoForPocketsBannerTitle': [],
    'promoForPocketsBottomSheetButtonTitle': [],
    'promoForPocketsBottomSheetDescription': ['cashback', 'minDeposit'],
    'promoForPocketsBottomSheetTitle': ['cashback'],
    'rejectJointAccountClosureConfirmationBottomSheetSubtitle': [],
    'rejectJointAccountClosureConfirmationBottomSheetTitle': [],
    'rejectJointAccountClosureConfirmationConfirmButtonTitle': [],
    'rejectJointAccountClosureConfirmationGoBackButtonTitle': [],
    'rejectedInviteSendAgainButtonTitle': [],
    'relationshipSelectionScreenAdultParticipantSuggestionChip1': [],
    'relationshipSelectionScreenAdultParticipantSuggestionChip2': [],
    'relationshipSelectionScreenAdultParticipantSuggestionChip3': [],
    'relationshipSelectionScreenAdultParticipantSuggestionChip4': [],
    'relationshipSelectionScreenButtonTitle': [],
    'relationshipSelectionScreenHeaderTitle': [],
    'relationshipSelectionScreenNameTitle': [],
    'relationshipSelectionScreenParticipantSuggestionChip1': [],
    'relationshipSelectionScreenParticipantSuggestionChip2': [],
    'relationshipSelectionScreenSubTitle': [],
    'relationshipSelectionScreenSuggestionChip1': [],
    'relationshipSelectionScreenSuggestionChip2': [],
    'relationshipSelectionScreenSuggestionChip3': [],
    'relationshipSelectionScreenSuggestionChip4': [],
    'relationshipSelectionScreenSuggestionChip5': [],
    'relationshipSelectionScreenTitle': ['name'],
    'removeInviteFailureToastMessage': [],
    'removeInviteSuccessfulToastMessage': ['name'],
    'resendAdultParticipantInviteBottomsheetConfirmButtonTitle': [],
    'resendAdultParticipantInviteBottomsheetDismissButtonTitle': [],
    'resendAdultParticipantInviteBottomsheetSubTitle': [],
    'resendAdultParticipantInviteBottomsheetTitle': ['name'],
    'resendInvitationSuccessButtonTitle': [],
    'resendInvitationSuccessSubtitle': ['name'],
    'resendInvitationSuccessTitle': [],
    'resendInviteBottomSheetConfirmButtonTitle': [],
    'resendInviteBottomSheetConfirmationDescription': ['role'],
    'resendInviteBottomSheetConfirmationHighlightedText': ['role'],
    'resendInviteBottomSheetDismissButtonTitle': [],
    'resendInviteBottomSheetSubtitle': ['role'],
    'resendInviteBottomSheetTitle': [],
    'resendSharedPocketInviteBottomsheetConfirmButtonTitle': [],
    'resendSharedPocketInviteBottomsheetDismissButtonTitle': [],
    'resendSharedPocketInviteBottomsheetSubtitle': [],
    'resendSharedPocketInviteBottomsheetTitle': [
      'coOwnerName',
      'participantName'
    ],
    'reviewFamilyAccountClosureRejectText': [],
    'reviewFamilyAccountClosureScreenCtaApprove': [],
    'reviewFamilyAccountClosureScreenCtaDeny': [],
    'reviewFamilyAccountClosureScreenDesc': [],
    'reviewFamilyAccountClosureScreenHelpText': [],
    'reviewFamilyAccountClosureScreenPoint1': [],
    'reviewFamilyAccountClosureScreenPoint2': [],
    'reviewFamilyAccountClosureScreenSubtitle': ['accountNickname'],
    'reviewFamilyAccountClosureScreenTitle': [],
    'reviewFamilyAccountClosureSuccessButtonTitle': [],
    'reviewFamilyAccountClosureSuccessDesc': [],
    'reviewFamilyAccountClosureSuccessSubtitle': [],
    'reviewFamilyAccountClosureSuccessTitle': [],
    'reviewSharedPocketClosureRejectText': [],
    'reviewSharedPocketClosureScreenCtaConfirm': [],
    'reviewSharedPocketClosureScreenCtaReject': [],
    'reviewSharedPocketClosureScreenFallbackTitle': [],
    'reviewSharedPocketClosureScreenPoint1': [],
    'reviewSharedPocketClosureScreenPoint2': [],
    'reviewSharedPocketClosureScreenTitle': ['accountNickname', 'coOwner'],
    'reviewSharedPocketClosureSuccessButtonTitle': [],
    'reviewSharedPocketClosureSuccessDesc': [],
    'reviewSharedPocketClosureSuccessSubtitle': ['pocketName'],
    'reviewSharedPocketClosureSuccessTitle': [],
    'roleSelectionScreenCoOwnerCardExtraLine': [],
    'roleSelectionScreenCoOwnerCardSubTitle': [],
    'roleSelectionScreenCoOwnerCardTitle': [],
    'roleSelectionScreenHeaderTitle': [],
    'roleSelectionScreenParticipantCardExtraLine': [],
    'roleSelectionScreenParticipantCardSubTitle': [],
    'roleSelectionScreenParticipantCardTitle': [],
    'roleSelectionScreenSubTitle': [],
    'roleSelectionScreenTitle': [],
    'roleSelectionWalktroughCta': [],
    'ruleScreenJointAccountSectionTitle': [],
    'rulesScreenFAQButtonTitle': [],
    'rulesScreenJointAccountAlternativeButtonTitle': [],
    'rulesScreenJointAccountButtonTitle': [],
    'rulesScreenJointAccountLearnButtonTitle': [],
    'rulesScreenJointAccountSubTitle': [],
    'rulesScreenJointAccountTitle': [],
    'rulesScreenWalkthroughBannerCta': [],
    'rulesScreenWalkthroughBannerDurationTitle': [],
    'rulesScreenWalkthroughBannerTitle': [],
    'selectAvatarImagePageCategoryAll': [],
    'selectAvatarImagePageNextButtonTitle': [],
    'selectAvatarImagePageTitle': [],
    'selectCardHolderScreenAppBarTitle': [],
    'selectCardHolderScreenInviteAdultParticipantTitle': [],
    'selectCardHolderScreenMemberBoxTitleCurrentAccount': [],
    'selectCardHolderScreenMemberBoxTrailing': ['role'],
    'selectCardHolderScreenTitle': [],
    'selectParticipantTypeBottomSheetAbove18ButtonSubtitle': [],
    'selectParticipantTypeBottomSheetAbove18ButtonTitle': [],
    'selectParticipantTypeBottomSheetBelow18ButtonSubtitle': [],
    'selectParticipantTypeBottomSheetBelow18ButtonTitle': [],
    'selectParticipantTypeBottomSheetSubTitle': [],
    'selectParticipantTypeBottomSheetTitle': [],
    'sharedAccountsTabFamilyAccountCardTitle': ['name'],
    'sharedAccountsTabPocketCardSubTitle': [],
    'sharedCardHolderBottomSheetActiveCardParticipantButton': [],
    'sharedCardHolderBottomSheetActiveCardParticipantSubtitle': [],
    'sharedCardHolderBottomSheetActiveCardParticipantTitle': [],
    'sharedCardHolderBottomSheetCurrentAccountPrimaryButton': [],
    'sharedCardHolderBottomSheetCurrentAccountSecondaryButton': [],
    'sharedCardHolderBottomSheetCurrentAccountSubtitle': [],
    'sharedCardHolderBottomSheetCurrentAccountTitle': [],
    'sharedCardHolderBottomSheetTerminatedCardParticipantButton': [],
    'sharedCardHolderBottomSheetTerminatedCardParticipantSubtitle': [],
    'sharedCardHolderBottomSheetTerminatedCardParticipantTitle': [],
    'sharedCardTabNoFamilyEmptyStateSubtitle': [],
    'sharedCardTabNoFamilyEmptyStateTitle': [],
    'sharedCardsTabAddButtonTitle': [],
    'sharedCardsTabAdultParticipantCardsSectionTitle': [],
    'sharedCardsTabButtonTitle': [],
    'sharedCardsTabEmptyStateButtonTitle': [],
    'sharedCardsTabEmptyStateSubtitle': [],
    'sharedCardsTabEmptyStateTitle': [],
    'sharedCardsTabInfoBottomSheetHowCreatedDesc': ['cardOwnerType'],
    'sharedCardsTabInfoBottomSheetHowCreatedTitle': [],
    'sharedCardsTabInfoBottomSheetHowWorksDesc': ['cardOwnerType'],
    'sharedCardsTabInfoBottomSheetHowWorksTitle': [],
    'sharedCardsTabInfoBottomSheetTitle': ['cardOwnerType'],
    'sharedCardsTabMyCardsSectionTitle': [],
    'sharedCardsTabOwnerCardsSectionTitle': [],
    'sharedCardsTabParticipantCardsSectionTitle': [],
    'sharedCardsTabSharedLabel': [],
    'sharedMyCardsEmptyStateText': [],
    'sharedOwnerCardsEmptytStateText': [],
    'sharedParticipantsCardsEmptyStateText': [],
    'sharedPocketInvitationAcceptedScreenButtonTitle': [],
    'sharedPocketInvitationAcceptedScreenSubtitle': [],
    'sharedPocketInvitationAcceptedScreenTitle': [],
    'sharedPocketInvitationRejectedText': [],
    'sharedPocketInviteSentStatusPageButtonTitle': [],
    'sharedPocketInviteSentStatusPageSubTitle': ['name'],
    'sharedPocketInviteSentStatusPageTitle': [],
    'sharedPocketInviteSentStatusStep1SubTitle': ['name'],
    'sharedPocketInviteSentStatusStep1Title': [],
    'sharedPocketInviteSentStatusStep2SubTitle': ['name'],
    'sharedPocketInviteSentStatusStep2Title': ['name'],
    'sharedPocketInviteSentStatusStep3SubTitle': ['name'],
    'sharedPocketInviteSentStatusStep3Title': [],
    'sharedProductsListAccountsSectionTitle': [],
    'sharedProductsListCardsSectionTitle': [],
    'sharedProductsListFixedSavingSpacesSectionTitle': [],
    'sharedProductsListPocketsSectionTitle': [],
    'spendingLimitFrequencyInfoDaily': [],
    'spendingLimitFrequencyInfoMonthly': ['month'],
    'spendingLimitFrequencyInfoOverall': [],
    'spendingLimitFrequencyInfoWeekly': [],
    'spendingLimitItemSubTitle': ['frequency', 'limit'],
    'spendingLimitItemTitle': [],
    'updateAdultParticipantInvitationErrorSubTitle': ['status'],
    'updateInvitationErrorButtonTitle': [],
    'updateInvitationErrorSubTitle': ['status'],
    'updateInvitationErrorTitle': [],
    'updateRelationshipSuccessMsg': [],
    'wioFamilyExplainedWalkthroughBannerCta': [],
    'wioFamilyExplainedWalkthroughBannerTitle': [],
    'wioPocketExplainedWalkthroughBannerCta': [],
    'wioPocketExplainedWalkthroughBannerTitle': []
  };

  static Future<FamilyBankingLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = FamilyBankingLocalizations._internal();
      return instance;
    });
  }

  static FamilyBankingLocalizations of(BuildContext context) {
    final instance = Localizations.of<FamilyBankingLocalizations>(
        context, FamilyBankingLocalizations);
    assert(instance != null,
        'No instance of FamilyBankingLocalizations present in the widget tree. Did you add FamilyBankingLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `Yes, I’m sure`
  String get activateAccountBottomSheetConfirmButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'activateAccountBottomSheetConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get activateAccountBottomSheetDismissButtonTitle {
    return Intl.message(
      'Go back',
      name: 'activateAccountBottomSheetDismissButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Phone number`
  String get activateAccountBottomSheetPhoneNumberLabel {
    return Intl.message(
      'Phone number',
      name: 'activateAccountBottomSheetPhoneNumberLabel',
      desc: '',
      args: [],
    );
  }

  /// `{role, select, coOwner{Activating account means you and {name} will be able to create shared cards and manage money together.} participant{Activating account means you can share card, set limit and add money for {name}’s pocket.} adultParticipant{Once this is activated, you’ll be able totrack, set limits and add money to {name}’s card.} other{ }}`
  String activateAccountBottomSheetSubtitle(String role, String name) {
    return Intl.select(
      role,
      {
        'coOwner':
            'Activating account means you and $name will be able to create shared cards and manage money together.',
        'participant':
            'Activating account means you can share card, set limit and add money for $name’s pocket.',
        'adultParticipant':
            'Once this is activated, you’ll be able totrack, set limits and add money to $name’s card.',
        'other': ' ',
      },
      name: 'activateAccountBottomSheetSubtitle',
      desc: '',
      args: [role, name],
    );
  }

  /// `{invitationType, select, inviteAdultParticipant{Are you sure you want to activate this card?} other{Are you sure you want to activate account?}}`
  String activateAccountBottomSheetTitle(String invitationType) {
    return Intl.select(
      invitationType,
      {
        'inviteAdultParticipant':
            'Are you sure you want to activate this card?',
        'other': 'Are you sure you want to activate account?',
      },
      name: 'activateAccountBottomSheetTitle',
      desc: '',
      args: [invitationType],
    );
  }

  /// `Family lead`
  String get activateSharedPocketBottomsheetCoOwnerLabel {
    return Intl.message(
      'Family lead',
      name: 'activateSharedPocketBottomsheetCoOwnerLabel',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I’m sure`
  String get activateSharedPocketBottomsheetConfirmButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'activateSharedPocketBottomsheetConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get activateSharedPocketBottomsheetDismissButtonTitle {
    return Intl.message(
      'Go back',
      name: 'activateSharedPocketBottomsheetDismissButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family member`
  String get activateSharedPocketBottomsheetParticipantLabel {
    return Intl.message(
      'Family member',
      name: 'activateSharedPocketBottomsheetParticipantLabel',
      desc: '',
      args: [],
    );
  }

  /// `Activating account means you and {name} will have equal access to shared pocket.`
  String activateSharedPocketBottomsheetSubTitle(String name) {
    return Intl.message(
      'Activating account means you and $name will have equal access to shared pocket.',
      name: 'activateSharedPocketBottomsheetSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Are you sure you want to activate shared access to pocket?`
  String get activateSharedPocketBottomsheetTitle {
    return Intl.message(
      'Are you sure you want to activate shared access to pocket?',
      name: 'activateSharedPocketBottomsheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Tap to create avatar`
  String get addAvatarScreenAddButtonTitle {
    return Intl.message(
      'Tap to create avatar',
      name: 'addAvatarScreenAddButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Surprise me instead`
  String get addAvatarScreenButtonTitle {
    return Intl.message(
      'Surprise me instead',
      name: 'addAvatarScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Don’t want to create one?`
  String get addAvatarScreenFooterText {
    return Intl.message(
      'Don’t want to create one?',
      name: 'addAvatarScreenFooterText',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get addAvatarScreenSkipButtonTitle {
    return Intl.message(
      'Skip',
      name: 'addAvatarScreenSkipButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `This is how other people will see you on Wio Family. You can change it anytime.`
  String get addAvatarScreenSubtitle {
    return Intl.message(
      'This is how other people will see you on Wio Family. You can change it anytime.',
      name: 'addAvatarScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Start by adding an avatar`
  String get addAvatarScreenTitle {
    return Intl.message(
      'Start by adding an avatar',
      name: 'addAvatarScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Shared pocket access`
  String get addCoOwnerToPocketBannerHeader {
    return Intl.message(
      'Shared pocket access',
      name: 'addCoOwnerToPocketBannerHeader',
      desc: '',
      args: [],
    );
  }

  /// `Share now`
  String get addCoOwnerToPocketBannerShareNowButtonTitle {
    return Intl.message(
      'Share now',
      name: 'addCoOwnerToPocketBannerShareNowButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Let your family leads manage this pocket with you – so they can help with spending, top-ups and limits.`
  String get addCoOwnerToPocketBannerTitle {
    return Intl.message(
      'Let your family leads manage this pocket with you – so they can help with spending, top-ups and limits.',
      name: 'addCoOwnerToPocketBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get adultParticipantInviteRelationshipScreenButtonTitle {
    return Intl.message(
      'Continue',
      name: 'adultParticipantInviteRelationshipScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite member`
  String get adultParticipantInviteRelationshipScreenPageTitle {
    return Intl.message(
      'Invite member',
      name: 'adultParticipantInviteRelationshipScreenPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Describe your relationship to this added person.`
  String get adultParticipantInviteRelationshipScreenSubTitle {
    return Intl.message(
      'Describe your relationship to this added person.',
      name: 'adultParticipantInviteRelationshipScreenSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `What is {name}'s relationship to you?`
  String adultParticipantInviteRelationshipScreenTitle(String name) {
    return Intl.message(
      'What is $name\'s relationship to you?',
      name: 'adultParticipantInviteRelationshipScreenTitle',
      desc: '',
      args: [name],
    );
  }

  /// `They’ll be able to spend using the card,  just like other family members.`
  String get adultParticipantRuleCardSubtitle1 {
    return Intl.message(
      'They’ll be able to spend using the card,  just like other family members.',
      name: 'adultParticipantRuleCardSubtitle1',
      desc: '',
      args: [],
    );
  }

  /// `They won’t have a pocket and can’t contribute to shared savings goals for now. They only have access to a virtual card.`
  String get adultParticipantRuleCardSubtitle2 {
    return Intl.message(
      'They won’t have a pocket and can’t contribute to shared savings goals for now. They only have access to a virtual card.',
      name: 'adultParticipantRuleCardSubtitle2',
      desc: '',
      args: [],
    );
  }

  /// `We’re working on unlocking full Wio Family features for family members over 18, including saving together, tracking goals, and more.`
  String get adultParticipantRuleCardSubtitle3 {
    return Intl.message(
      'We’re working on unlocking full Wio Family features for family members over 18, including saving together, tracking goals, and more.',
      name: 'adultParticipantRuleCardSubtitle3',
      desc: '',
      args: [],
    );
  }

  /// `Use a virtual card linked to your personal or family account`
  String get adultParticipantRuleCardTitle1 {
    return Intl.message(
      'Use a virtual card linked to your personal or family account',
      name: 'adultParticipantRuleCardTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Spending only.\nNo pockets or saving spaces (yet)`
  String get adultParticipantRuleCardTitle2 {
    return Intl.message(
      'Spending only.\nNo pockets or saving spaces (yet)',
      name: 'adultParticipantRuleCardTitle2',
      desc: '',
      args: [],
    );
  }

  /// `New features unlocked...`
  String get adultParticipantRuleCardTitle3 {
    return Intl.message(
      'New features unlocked...',
      name: 'adultParticipantRuleCardTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get adultParticipantRuleScreenButtonTitle {
    return Intl.message(
      'Continue',
      name: 'adultParticipantRuleScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon:`
  String get adultParticipantRuleScreenComingSoonText {
    return Intl.message(
      'Coming soon:',
      name: 'adultParticipantRuleScreenComingSoonText',
      desc: '',
      args: [],
    );
  }

  /// `Adding someone over 18 years old`
  String get adultParticipantRuleScreenHeader {
    return Intl.message(
      'Adding someone over 18 years old',
      name: 'adultParticipantRuleScreenHeader',
      desc: '',
      args: [],
    );
  }

  /// `Here’s what they can do for now:`
  String get adultParticipantRuleScreenSubheader {
    return Intl.message(
      'Here’s what they can do for now:',
      name: 'adultParticipantRuleScreenSubheader',
      desc: '',
      args: [],
    );
  }

  /// `Confirm account closure`
  String get approveJointAccountClosureApproveButtonTitle {
    return Intl.message(
      'Confirm account closure',
      name: 'approveJointAccountClosureApproveButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to close your family account?`
  String get approveJointAccountClosureConfirmationBottomSheetSubtitle {
    return Intl.message(
      'Are you sure you want to close your family account?',
      name: 'approveJointAccountClosureConfirmationBottomSheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Closure confirmation`
  String get approveJointAccountClosureConfirmationBottomSheetTitle {
    return Intl.message(
      'Closure confirmation',
      name: 'approveJointAccountClosureConfirmationBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, Close this account`
  String get approveJointAccountClosureConfirmationConfirmButtonTitle {
    return Intl.message(
      'Yes, Close this account',
      name: 'approveJointAccountClosureConfirmationConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get approveJointAccountClosureConfirmationGoBackButtonTitle {
    return Intl.message(
      'Go back',
      name: 'approveJointAccountClosureConfirmationGoBackButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{coOwner} has confirmed to close the family account from their Wio app.`
  String approveJointAccountClosureFirstStepSubtitle(String coOwner) {
    return Intl.message(
      '$coOwner has confirmed to close the family account from their Wio app.',
      name: 'approveJointAccountClosureFirstStepSubtitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `{coOwner} requested for account closure`
  String approveJointAccountClosureFirstStepTitle(String coOwner) {
    return Intl.message(
      '$coOwner requested for account closure',
      name: 'approveJointAccountClosureFirstStepTitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `Help with family account closure`
  String get approveJointAccountClosureHelpLabel {
    return Intl.message(
      'Help with family account closure',
      name: 'approveJointAccountClosureHelpLabel',
      desc: '',
      args: [],
    );
  }

  /// `Money has been moved to your {currency} account.`
  String approveJointAccountClosureMoneyMovedMessage(String currency) {
    return Intl.message(
      'Money has been moved to your $currency account.',
      name: 'approveJointAccountClosureMoneyMovedMessage',
      desc: '',
      args: [currency],
    );
  }

  /// `Follow these steps to close your account`
  String get approveJointAccountClosurePageTitle {
    return Intl.message(
      'Follow these steps to close your account',
      name: 'approveJointAccountClosurePageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Reject closure request`
  String get approveJointAccountClosureRejectButtonTitle {
    return Intl.message(
      'Reject closure request',
      name: 'approveJointAccountClosureRejectButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Move money`
  String get approveJointAccountClosureSecondStepActionButtonTitle {
    return Intl.message(
      'Move money',
      name: 'approveJointAccountClosureSecondStepActionButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Ensure your balance is zero across all currency accounts and savings spaces.`
  String get approveJointAccountClosureSecondStepSubtitle {
    return Intl.message(
      'Ensure your balance is zero across all currency accounts and savings spaces.',
      name: 'approveJointAccountClosureSecondStepSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `This account balance needs to be zero before you close`
  String get approveJointAccountClosureSecondStepTitle {
    return Intl.message(
      'This account balance needs to be zero before you close',
      name: 'approveJointAccountClosureSecondStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `Confirm account closure`
  String get approveJointAccountClosureSubmitButtonTitle {
    return Intl.message(
      'Confirm account closure',
      name: 'approveJointAccountClosureSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We’ve taken onboard your feedback and hope to serve you better in the future`
  String get approveJointAccountClosureSuccessScreenDescriptionItem1 {
    return Intl.message(
      'We’ve taken onboard your feedback and hope to serve you better in the future',
      name: 'approveJointAccountClosureSuccessScreenDescriptionItem1',
      desc: '',
      args: [],
    );
  }

  /// `Your cards will be terminated`
  String get approveJointAccountClosureSuccessScreenDescriptionItem2 {
    return Intl.message(
      'Your cards will be terminated',
      name: 'approveJointAccountClosureSuccessScreenDescriptionItem2',
      desc: '',
      args: [],
    );
  }

  /// `Download monthly statements`
  String get approveJointAccountClosureSuccessScreenDownloadStatementLabel {
    return Intl.message(
      'Download monthly statements',
      name: 'approveJointAccountClosureSuccessScreenDownloadStatementLabel',
      desc: '',
      args: [],
    );
  }

  /// `Your family account with {coOwner} has been closed`
  String approveJointAccountClosureSuccessScreenHeader(String coOwner) {
    return Intl.message(
      'Your family account with $coOwner has been closed',
      name: 'approveJointAccountClosureSuccessScreenHeader',
      desc: '',
      args: [coOwner],
    );
  }

  /// `Done`
  String get approveJointAccountClosureSuccessScreenSubmitButtonTitle {
    return Intl.message(
      'Done',
      name: 'approveJointAccountClosureSuccessScreenSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `The account will be closed after your confirmation.`
  String get approveJointAccountClosureThirdStepSubtitle {
    return Intl.message(
      'The account will be closed after your confirmation.',
      name: 'approveJointAccountClosureThirdStepSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Account closed`
  String get approveJointAccountClosureThirdStepTitle {
    return Intl.message(
      'Account closed',
      name: 'approveJointAccountClosureThirdStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose a different avatar`
  String get backgroundSelectionFooterButtonChooseDifferent {
    return Intl.message(
      'Choose a different avatar',
      name: 'backgroundSelectionFooterButtonChooseDifferent',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get backgroundSelectionFooterButtonConfirm {
    return Intl.message(
      'Confirm',
      name: 'backgroundSelectionFooterButtonConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Looking good!\nNow choose your background color`
  String get backgroundSelectionHeaderTitle {
    return Intl.message(
      'Looking good!\nNow choose your background color',
      name: 'backgroundSelectionHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get cancelInvitationSuccessButtonTitle {
    return Intl.message(
      'Done',
      name: 'cancelInvitationSuccessButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `DONE!`
  String get cancelInvitationSuccessTitle {
    return Intl.message(
      'DONE!',
      name: 'cancelInvitationSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I’m sure`
  String get cancelInviteConfirmationBottomsheetAgreeButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'cancelInviteConfirmationBottomsheetAgreeButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get cancelInviteConfirmationBottomsheetBackButtonTitle {
    return Intl.message(
      'Go back',
      name: 'cancelInviteConfirmationBottomsheetBackButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{invitationType, select, createFamilyAccount{Cancelling this invitation means you won’t set up a family account just yet. You can always come back and do it later.} createPocket{Cancelling this invitation means you won’t set up a pocket. You can always come back and do it later.} addCoOwnerToPocket{Cancelling this invitation you won't share pocket with your family lead. You can always come back and do it later.} other { }}`
  String cancelInviteConfirmationBottomsheetSubTitle(String invitationType) {
    return Intl.select(
      invitationType,
      {
        'createFamilyAccount':
            'Cancelling this invitation means you won’t set up a family account just yet. You can always come back and do it later.',
        'createPocket':
            'Cancelling this invitation means you won’t set up a pocket. You can always come back and do it later.',
        'addCoOwnerToPocket':
            'Cancelling this invitation you won\'t share pocket with your family lead. You can always come back and do it later.',
        'other': ' ',
      },
      name: 'cancelInviteConfirmationBottomsheetSubTitle',
      desc: '',
      args: [invitationType],
    );
  }

  /// `{invitationType, select, addCoOwnerToPocket{Are you sure you want to cancel sharing pocket invitation?}  other {Are you sure you want to cancel invitation?}}`
  String cancelInviteConfirmationBottomsheetTitle(String invitationType) {
    return Intl.select(
      invitationType,
      {
        'addCoOwnerToPocket':
            'Are you sure you want to cancel sharing pocket invitation?',
        'other': 'Are you sure you want to cancel invitation?',
      },
      name: 'cancelInviteConfirmationBottomsheetTitle',
      desc: '',
      args: [invitationType],
    );
  }

  /// `Invitation`
  String get cancelInviteScreenHeader {
    return Intl.message(
      'Invitation',
      name: 'cancelInviteScreenHeader',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to {name} has been canceled successfully.`
  String cancelnvitationSuccessSubTitle(String name) {
    return Intl.message(
      'Your invite to $name has been canceled successfully.',
      name: 'cancelnvitationSuccessSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Looks like there is already active Wio account under this number.`
  String get cannotInviteCoOwnerBottomSheetDescription {
    return Intl.message(
      'Looks like there is already active Wio account under this number.',
      name: 'cannotInviteCoOwnerBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `This number is already \na Wio customer`
  String get cannotInviteCoOwnerBottomSheetTitle {
    return Intl.message(
      'This number is already \na Wio customer',
      name: 'cannotInviteCoOwnerBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add as a family lead`
  String get cannotInviteMemberBottomAddAsCoOwnerButtonTitle {
    return Intl.message(
      'Add as a family lead',
      name: 'cannotInviteMemberBottomAddAsCoOwnerButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Seems like there is already family member account registered under this number.`
  String get cannotInviteParticipantBottomSheetDescription {
    return Intl.message(
      'Seems like there is already family member account registered under this number.',
      name: 'cannotInviteParticipantBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get cannotInviteParticipantBottomSheetGoBackButtonTitle {
    return Intl.message(
      'Go back',
      name: 'cannotInviteParticipantBottomSheetGoBackButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We can not process with invitation`
  String get cannotInviteParticipantBottomSheetTitle {
    return Intl.message(
      'We can not process with invitation',
      name: 'cannotInviteParticipantBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `of {amount}`
  String cardsBankCardLimitOverallLabel(String amount) {
    return Intl.message(
      'of $amount',
      name: 'cardsBankCardLimitOverallLabel',
      desc: '',
      args: [amount],
    );
  }

  /// `Spending limit`
  String get cardsBankCardLimitTitle {
    return Intl.message(
      'Spending limit',
      name: 'cardsBankCardLimitTitle',
      desc: '',
      args: [],
    );
  }

  /// `Earn up to {currencySymbol}2,500 on your Personal Account and up to {currencySymbol}2,500 on each Family Account.\n\nEarn cashback on spends when you pay with Wio. (Doesn’t apply to transfers, withdrawals, or investments).`
  String cashbackRatesBottomSheetDescription(String currencySymbol) {
    return Intl.message(
      'Earn up to ${currencySymbol}2,500 on your Personal Account and up to ${currencySymbol}2,500 on each Family Account.\n\nEarn cashback on spends when you pay with Wio. (Doesn’t apply to transfers, withdrawals, or investments).',
      name: 'cashbackRatesBottomSheetDescription',
      desc: '',
      args: [currencySymbol],
    );
  }

  /// `For Family Accounts, cashback is based on the highest plan among family leads—Standard, Plus, or Salary.`
  String get cashbackRatesBottomSheetFamilyCashbackRules {
    return Intl.message(
      'For Family Accounts, cashback is based on the highest plan among family leads—Standard, Plus, or Salary.',
      name: 'cashbackRatesBottomSheetFamilyCashbackRules',
      desc: '',
      args: [],
    );
  }

  /// `Learn more about plans rates`
  String get cashbackRatesBottomSheetLearnPlanRatesButtonTitle {
    return Intl.message(
      'Learn more about plans rates',
      name: 'cashbackRatesBottomSheetLearnPlanRatesButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Cashback rates`
  String get cashbackRatesBottomSheetTitle {
    return Intl.message(
      'Cashback rates',
      name: 'cashbackRatesBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Create closure request`
  String get closeJointAccountConfirmationBottomSheetConfirmButtonTitle {
    return Intl.message(
      'Create closure request',
      name: 'closeJointAccountConfirmationBottomSheetConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get closeJointAccountConfirmationBottomSheetGoBackButtonTitle {
    return Intl.message(
      'Go back',
      name: 'closeJointAccountConfirmationBottomSheetGoBackButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to close your family account?\nAfter confirmation, we will send another request to your family lead {coOwner} to confirm the closure.`
  String closeJointAccountConfirmationBottomSheetSubtitle(String coOwner) {
    return Intl.message(
      'Are you sure you want to close your family account?\nAfter confirmation, we will send another request to your family lead $coOwner to confirm the closure.',
      name: 'closeJointAccountConfirmationBottomSheetSubtitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `Closure confirmation`
  String get closeJointAccountConfirmationBottomSheetTitle {
    return Intl.message(
      'Closure confirmation',
      name: 'closeJointAccountConfirmationBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Account statement`
  String get closeJointAccountDownloadStatementAccountStatementLabel {
    return Intl.message(
      'Account statement',
      name: 'closeJointAccountDownloadStatementAccountStatementLabel',
      desc: '',
      args: [],
    );
  }

  /// `Make sure you download your statements for your records, after you close you won’t have access to this.`
  String get closeJointAccountDownloadStatementPageSubtitle {
    return Intl.message(
      'Make sure you download your statements for your records, after you close you won’t have access to this.',
      name: 'closeJointAccountDownloadStatementPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Download your statements before you close`
  String get closeJointAccountDownloadStatementPageTitle {
    return Intl.message(
      'Download your statements before you close',
      name: 'closeJointAccountDownloadStatementPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `No, Thanks`
  String get closeJointAccountDownloadStatementSubmitButtonTitle {
    return Intl.message(
      'No, Thanks',
      name: 'closeJointAccountDownloadStatementSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Move money`
  String get closeJointAccountFollowStepsFirstStepActionButtonTitle {
    return Intl.message(
      'Move money',
      name: 'closeJointAccountFollowStepsFirstStepActionButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You can move the money from family account to your current account before account closure.`
  String get closeJointAccountFollowStepsFirstStepSubtitle {
    return Intl.message(
      'You can move the money from family account to your current account before account closure.',
      name: 'closeJointAccountFollowStepsFirstStepSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Would you like to move money from this family account?`
  String get closeJointAccountFollowStepsFirstStepTitle {
    return Intl.message(
      'Would you like to move money from this family account?',
      name: 'closeJointAccountFollowStepsFirstStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `Help with family account closure`
  String get closeJointAccountFollowStepsHelpLabel {
    return Intl.message(
      'Help with family account closure',
      name: 'closeJointAccountFollowStepsHelpLabel',
      desc: '',
      args: [],
    );
  }

  /// `Money has been moved to your {currency} account.`
  String closeJointAccountFollowStepsMoneyMovedMessage(String currency) {
    return Intl.message(
      'Money has been moved to your $currency account.',
      name: 'closeJointAccountFollowStepsMoneyMovedMessage',
      desc: '',
      args: [currency],
    );
  }

  /// `Follow these steps to close your account`
  String get closeJointAccountFollowStepsPageTitle {
    return Intl.message(
      'Follow these steps to close your account',
      name: 'closeJointAccountFollowStepsPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{coOwner} needs to confirm the account closure from their side.`
  String closeJointAccountFollowStepsSecondStepSubtitle(String coOwner) {
    return Intl.message(
      '$coOwner needs to confirm the account closure from their side.',
      name: 'closeJointAccountFollowStepsSecondStepSubtitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `{coOwner} received a notification`
  String closeJointAccountFollowStepsSecondStepTitle(String coOwner) {
    return Intl.message(
      '$coOwner received a notification',
      name: 'closeJointAccountFollowStepsSecondStepTitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `Create closure request`
  String get closeJointAccountFollowStepsSubmitButtonTitle {
    return Intl.message(
      'Create closure request',
      name: 'closeJointAccountFollowStepsSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your account will be closed after confirmation from {coOwner}.`
  String closeJointAccountFollowStepsThirdStepSubtitle(String coOwner) {
    return Intl.message(
      'Your account will be closed after confirmation from $coOwner.',
      name: 'closeJointAccountFollowStepsThirdStepSubtitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `Account closed`
  String get closeJointAccountFollowStepsThirdStepTitle {
    return Intl.message(
      'Account closed',
      name: 'closeJointAccountFollowStepsThirdStepTitle',
      desc: '',
      args: [],
    );
  }

  /// `All scheduled payments will be cancelled.`
  String get closeJointAccountIntroBenefitFour {
    return Intl.message(
      'All scheduled payments will be cancelled.',
      name: 'closeJointAccountIntroBenefitFour',
      desc: '',
      args: [],
    );
  }

  /// `{coOwner} will lose access to this account.`
  String closeJointAccountIntroBenefitOne(String coOwner) {
    return Intl.message(
      '$coOwner will lose access to this account.',
      name: 'closeJointAccountIntroBenefitOne',
      desc: '',
      args: [coOwner],
    );
  }

  /// `All cards linked to this account will be removed.`
  String get closeJointAccountIntroBenefitThree {
    return Intl.message(
      'All cards linked to this account will be removed.',
      name: 'closeJointAccountIntroBenefitThree',
      desc: '',
      args: [],
    );
  }

  /// `Your transactions for this account will remain in your transaction history.`
  String get closeJointAccountIntroBenefitTwo {
    return Intl.message(
      'Your transactions for this account will remain in your transaction history.',
      name: 'closeJointAccountIntroBenefitTwo',
      desc: '',
      args: [],
    );
  }

  /// `Help with family account closure`
  String get closeJointAccountIntroHelpLabel {
    return Intl.message(
      'Help with family account closure',
      name: 'closeJointAccountIntroHelpLabel',
      desc: '',
      args: [],
    );
  }

  /// `We’re sad to hear you want to go.\nHere’s what happens after you close:`
  String get closeJointAccountIntroPageSubtitle {
    return Intl.message(
      'We’re sad to hear you want to go.\nHere’s what happens after you close:',
      name: 'closeJointAccountIntroPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to close this family account?`
  String get closeJointAccountIntroPageTitle {
    return Intl.message(
      'Are you sure you want to close this family account?',
      name: 'closeJointAccountIntroPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, close account`
  String get closeJointAccountIntroPrimaryButtonTitle {
    return Intl.message(
      'Yes, close account',
      name: 'closeJointAccountIntroPrimaryButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `No, keep it`
  String get closeJointAccountIntroSecondaryButtonTitle {
    return Intl.message(
      'No, keep it',
      name: 'closeJointAccountIntroSecondaryButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Please specify other`
  String get closeJointAccountSelectReasonOtherReasonInputLabel {
    return Intl.message(
      'Please specify other',
      name: 'closeJointAccountSelectReasonOtherReasonInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `We’re sorry to hear you want to close your family account. Let us know the reason, and we’ll strive to improve.`
  String get closeJointAccountSelectReasonPageSubtitle {
    return Intl.message(
      'We’re sorry to hear you want to close your family account. Let us know the reason, and we’ll strive to improve.',
      name: 'closeJointAccountSelectReasonPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Let us know why you want to close`
  String get closeJointAccountSelectReasonPageTitle {
    return Intl.message(
      'Let us know why you want to close',
      name: 'closeJointAccountSelectReasonPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{reason, select, financialIssues{Financial issues or fees} dissatisfaction{Dissatisfied with service} personal{Personal reasons} other{Other}}`
  String closeJointAccountSelectReasonScreenReasonLabelOf(String reason) {
    return Intl.select(
      reason,
      {
        'financialIssues': 'Financial issues or fees',
        'dissatisfaction': 'Dissatisfied with service',
        'personal': 'Personal reasons',
        'other': 'Other',
      },
      name: 'closeJointAccountSelectReasonScreenReasonLabelOf',
      desc: '',
      args: [reason],
    );
  }

  /// `Close account`
  String get closeJointAccountSelectReasonSubmitButtonTitle {
    return Intl.message(
      'Close account',
      name: 'closeJointAccountSelectReasonSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `After the confirmation your family account will be closed.`
  String get closeJointAccountSuccessScreenDescription {
    return Intl.message(
      'After the confirmation your family account will be closed.',
      name: 'closeJointAccountSuccessScreenDescription',
      desc: '',
      args: [],
    );
  }

  /// `Alright`
  String get closeJointAccountSuccessScreenSubmitButtonTitle {
    return Intl.message(
      'Alright',
      name: 'closeJointAccountSuccessScreenSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We have sent a notification to {coOwner} to confirm account closure`
  String closeJointAccountSuccessScreenSubtitle(String coOwner) {
    return Intl.message(
      'We have sent a notification to $coOwner to confirm account closure',
      name: 'closeJointAccountSuccessScreenSubtitle',
      desc: '',
      args: [coOwner],
    );
  }

  /// `IT’S DONE`
  String get closeJointAccountSuccessScreenTitle {
    return Intl.message(
      'IT’S DONE',
      name: 'closeJointAccountSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Closure request was already approved.`
  String get closureRequestStatusApprovedText {
    return Intl.message(
      'Closure request was already approved.',
      name: 'closureRequestStatusApprovedText',
      desc: '',
      args: [],
    );
  }

  /// `Closure request has been cancelled.`
  String get closureRequestStatusCancelledText {
    return Intl.message(
      'Closure request has been cancelled.',
      name: 'closureRequestStatusCancelledText',
      desc: '',
      args: [],
    );
  }

  /// `Closure request was already rejected.`
  String get closureRequestStatusRejectedText {
    return Intl.message(
      'Closure request was already rejected.',
      name: 'closureRequestStatusRejectedText',
      desc: '',
      args: [],
    );
  }

  /// `Explore now`
  String get confirmedInvitationBottomSheetButtonTitle {
    return Intl.message(
      'Explore now',
      name: 'confirmedInvitationBottomSheetButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You are now ready to contribute, access, and manage your shared finances.`
  String get confirmedInvitationBottomSheetSubtitle {
    return Intl.message(
      'You are now ready to contribute, access, and manage your shared finances.',
      name: 'confirmedInvitationBottomSheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Your family account  with {inviterName} is ready!`
  String confirmedInvitationBottomSheetTitle(String inviterName) {
    return Intl.message(
      'Your family account  with $inviterName is ready!',
      name: 'confirmedInvitationBottomSheetTitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `Invite member`
  String get createJointAccountSelectMemberNameAppBarTitle {
    return Intl.message(
      'Invite member',
      name: 'createJointAccountSelectMemberNameAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get createJointAccountSelectMemberNameConfirmButtonTitle {
    return Intl.message(
      'Continue',
      name: 'createJointAccountSelectMemberNameConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Name cannot exceed {count} characters`
  String createJointAccountSelectMemberNameInputErrorMessage(String count) {
    return Intl.message(
      'Name cannot exceed $count characters',
      name: 'createJointAccountSelectMemberNameInputErrorMessage',
      desc: '',
      args: [count],
    );
  }

  /// `Full name`
  String get createJointAccountSelectMemberNameInputLabel {
    return Intl.message(
      'Full name',
      name: 'createJointAccountSelectMemberNameInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Add a name to your contact so that it’s easy to remember later.`
  String get createJointAccountSelectMemberNameSubtitle {
    return Intl.message(
      'Add a name to your contact so that it’s easy to remember later.',
      name: 'createJointAccountSelectMemberNameSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Add a name to your contact`
  String get createJointAccountSelectMemberNameTitle {
    return Intl.message(
      'Add a name to your contact',
      name: 'createJointAccountSelectMemberNameTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite member`
  String get createJointAccountSelectMemberPhoneAppBarTitle {
    return Intl.message(
      'Invite member',
      name: 'createJointAccountSelectMemberPhoneAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get createJointAccountSelectMemberPhoneConfirmButtonTitle {
    return Intl.message(
      'Continue',
      name: 'createJointAccountSelectMemberPhoneConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Only UAE numbers are accepted`
  String get createJointAccountSelectMemberPhoneInfoMessage {
    return Intl.message(
      'Only UAE numbers are accepted',
      name: 'createJointAccountSelectMemberPhoneInfoMessage',
      desc: '',
      args: [],
    );
  }

  /// `Name or Number`
  String get createJointAccountSelectMemberPhoneInputLabel {
    return Intl.message(
      'Name or Number',
      name: 'createJointAccountSelectMemberPhoneInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Please use a valid UAE number`
  String get createJointAccountSelectMemberPhoneInvalidPhoneMessage {
    return Intl.message(
      'Please use a valid UAE number',
      name: 'createJointAccountSelectMemberPhoneInvalidPhoneMessage',
      desc: '',
      args: [],
    );
  }

  /// `Number`
  String get createJointAccountSelectMemberPhoneOnlyNumberInputLabel {
    return Intl.message(
      'Number',
      name: 'createJointAccountSelectMemberPhoneOnlyNumberInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Please allow Wio to access your contacts to invite members.`
  String get createJointAccountSelectMemberPhonePermissionBannerText {
    return Intl.message(
      'Please allow Wio to access your contacts to invite members.',
      name: 'createJointAccountSelectMemberPhonePermissionBannerText',
      desc: '',
      args: [],
    );
  }

  /// `Search for a name in your phonebook or add someone new via mobile number.`
  String get createJointAccountSelectMemberPhoneSubtitle {
    return Intl.message(
      'Search for a name in your phonebook or add someone new via mobile number.',
      name: 'createJointAccountSelectMemberPhoneSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Who do you want to add?`
  String get createJointAccountSelectMemberPhoneTitle {
    return Intl.message(
      'Who do you want to add?',
      name: 'createJointAccountSelectMemberPhoneTitle',
      desc: '',
      args: [],
    );
  }

  /// `How did it go?`
  String get createSharedCardCustomerFeedbackHighlightedTitle {
    return Intl.message(
      'How did it go?',
      name: 'createSharedCardCustomerFeedbackHighlightedTitle',
      desc: '',
      args: [],
    );
  }

  /// `You just shared a card!\nHow did it go?`
  String get createSharedCardCustomerFeedbackTitle {
    return Intl.message(
      'You just shared a card!\nHow did it go?',
      name: 'createSharedCardCustomerFeedbackTitle',
      desc: '',
      args: [],
    );
  }

  /// `Avatar successfully updated`
  String get currentUserProfileScreenAvatarUpdateResultCompleted {
    return Intl.message(
      'Avatar successfully updated',
      name: 'currentUserProfileScreenAvatarUpdateResultCompleted',
      desc: '',
      args: [],
    );
  }

  /// `Name your family hub`
  String get currentUserProfileScreenEditHubNameTitle {
    return Intl.message(
      'Name your family hub',
      name: 'currentUserProfileScreenEditHubNameTitle',
      desc: '',
      args: [],
    );
  }

  /// `Profile`
  String get currentUserProfileScreenHeaderTitle {
    return Intl.message(
      'Profile',
      name: 'currentUserProfileScreenHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Change avatar`
  String get currentUserProfileScreenHeaderUpdateAvatarButton {
    return Intl.message(
      'Change avatar',
      name: 'currentUserProfileScreenHeaderUpdateAvatarButton',
      desc: '',
      args: [],
    );
  }

  /// `You’re managing`
  String get currentUserProfileScreenSharedProdcutsListTitle {
    return Intl.message(
      'You’re managing',
      name: 'currentUserProfileScreenSharedProdcutsListTitle',
      desc: '',
      args: [],
    );
  }

  /// `Save name`
  String get editFamilyHubNameBottomsheetButtonTitle {
    return Intl.message(
      'Save name',
      name: 'editFamilyHubNameBottomsheetButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get editFamilyHubNameBottomsheetInputLabel {
    return Intl.message(
      'Name',
      name: 'editFamilyHubNameBottomsheetInputLabel',
      desc: '',
      args: [],
    );
  }

  /// `Hub successfully renamed`
  String get editFamilyHubNameBottomsheetSuccessMessage {
    return Intl.message(
      'Hub successfully renamed',
      name: 'editFamilyHubNameBottomsheetSuccessMessage',
      desc: '',
      args: [],
    );
  }

  /// `Name your family hub`
  String get editFamilyHubNameBottomsheetTitle {
    return Intl.message(
      'Name your family hub',
      name: 'editFamilyHubNameBottomsheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Alright`
  String get existingUserInvitationCancelledErrorButtonTitle {
    return Intl.message(
      'Alright',
      name: 'existingUserInvitationCancelledErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Looks like this invite has been cancelled by {inviterName}.`
  String existingUserInvitationCancelledErrorSubtitle(String inviterName) {
    return Intl.message(
      'Looks like this invite has been cancelled by $inviterName.',
      name: 'existingUserInvitationCancelledErrorSubtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `UH OH!`
  String get existingUserInvitationCancelledErrorTitle {
    return Intl.message(
      'UH OH!',
      name: 'existingUserInvitationCancelledErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Alright`
  String get existingUserInvitationErrorButtonTitle {
    return Intl.message(
      'Alright',
      name: 'existingUserInvitationErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We were unable to get your invite details at the moment. Please try again later.`
  String get existingUserInvitationErrorSubtitle {
    return Intl.message(
      'We were unable to get your invite details at the moment. Please try again later.',
      name: 'existingUserInvitationErrorSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Uh Oh!`
  String get existingUserInvitationErrorTitle {
    return Intl.message(
      'Uh Oh!',
      name: 'existingUserInvitationErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Alright`
  String get existingUserInvitationExpiredErrorButtonTitle {
    return Intl.message(
      'Alright',
      name: 'existingUserInvitationExpiredErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Looks like this invite has expired. You can ask {inviterName} to send you a new one.`
  String existingUserInvitationExpiredErrorSubtitle(String inviterName) {
    return Intl.message(
      'Looks like this invite has expired. You can ask $inviterName to send you a new one.',
      name: 'existingUserInvitationExpiredErrorSubtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `UH OH!`
  String get existingUserInvitationExpiredErrorTitle {
    return Intl.message(
      'UH OH!',
      name: 'existingUserInvitationExpiredErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Alright`
  String get existingUserInvitationRejectedByInviterErrorButtonTitle {
    return Intl.message(
      'Alright',
      name: 'existingUserInvitationRejectedByInviterErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Looks like this Invite has already been rejected by {inviterName}.`
  String existingUserInvitationRejectedByInviterErrorSubtitle(
      String inviterName) {
    return Intl.message(
      'Looks like this Invite has already been rejected by $inviterName.',
      name: 'existingUserInvitationRejectedByInviterErrorSubtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `UH OH!`
  String get existingUserInvitationRejectedByInviterErrorTitle {
    return Intl.message(
      'UH OH!',
      name: 'existingUserInvitationRejectedByInviterErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Alright`
  String get existingUserInvitationRejectedErrorButtonTitle {
    return Intl.message(
      'Alright',
      name: 'existingUserInvitationRejectedErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Looks like this invite has already been rejected by you`
  String get existingUserInvitationRejectedErrorInviteeViewTitle {
    return Intl.message(
      'Looks like this invite has already been rejected by you',
      name: 'existingUserInvitationRejectedErrorInviteeViewTitle',
      desc: '',
      args: [],
    );
  }

  /// `Looks like this invite has already been rejected by {name}.`
  String existingUserInvitationRejectedErrorSubtitle(String name) {
    return Intl.message(
      'Looks like this invite has already been rejected by $name.',
      name: 'existingUserInvitationRejectedErrorSubtitle',
      desc: '',
      args: [name],
    );
  }

  /// `UH OH!`
  String get existingUserInvitationRejectedErrorTitle {
    return Intl.message(
      'UH OH!',
      name: 'existingUserInvitationRejectedErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pockets`
  String get familyAccountsPocketsSectionTitle {
    return Intl.message(
      'Pockets',
      name: 'familyAccountsPocketsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add money`
  String get familyAccountsTabAddMoneyButtonTitle {
    return Intl.message(
      'Add money',
      name: 'familyAccountsTabAddMoneyButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Recurring transfer`
  String get familyAccountsTabRecurringRulesButtonTitle {
    return Intl.message(
      'Recurring transfer',
      name: 'familyAccountsTabRecurringRulesButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family Banking`
  String get familyBanking {
    return Intl.message(
      'Family Banking',
      name: 'familyBanking',
      desc: '',
      args: [],
    );
  }

  /// `Welcome, {name}!`
  String familyHubHeader(String name) {
    return Intl.message(
      'Welcome, $name!',
      name: 'familyHubHeader',
      desc: '',
      args: [name],
    );
  }

  /// `{tab, select, members{My family} accounts{Shared accounts} cards{Shared cards} savingSpaces{Family saving spaces} rewards{Family rewards} other{Other}}`
  String familyHubTabName(String tab) {
    return Intl.select(
      tab,
      {
        'members': 'My family',
        'accounts': 'Shared accounts',
        'cards': 'Shared cards',
        'savingSpaces': 'Family saving spaces',
        'rewards': 'Family rewards',
        'other': 'Other',
      },
      name: 'familyHubTabName',
      desc: '',
      args: [tab],
    );
  }

  /// `Family account is created automatically the moment you activate Family Account with your family lead`
  String get familyJointAccountInfoBottomSheetBody {
    return Intl.message(
      'Family account is created automatically the moment you activate Family Account with your family lead',
      name: 'familyJointAccountInfoBottomSheetBody',
      desc: '',
      args: [],
    );
  }

  /// `Family accounts are auto-created`
  String get familyJointAccountInfoBottomSheetTitle {
    return Intl.message(
      'Family accounts are auto-created',
      name: 'familyJointAccountInfoBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `A pocket is instantly created when a family member joins your family.`
  String get familyParticipantAccountBottomSheetBody {
    return Intl.message(
      'A pocket is instantly created when a family member joins your family.',
      name: 'familyParticipantAccountBottomSheetBody',
      desc: '',
      args: [],
    );
  }

  /// `Family member pockets are created automatically`
  String get familyParticipantAccountBottomSheetTitle {
    return Intl.message(
      'Family member pockets are created automatically',
      name: 'familyParticipantAccountBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `All time`
  String get familyRewardsAllTimeTabName {
    return Intl.message(
      'All time',
      name: 'familyRewardsAllTimeTabName',
      desc: '',
      args: [],
    );
  }

  /// `Cashback rates`
  String get familyRewardsCashbackRatesButtonTitle {
    return Intl.message(
      'Cashback rates',
      name: 'familyRewardsCashbackRatesButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Once you start spending from your family accounts, you'll see a list of your cashback transactions here.`
  String get familyRewardsEmptyTransactionsSubtitle {
    return Intl.message(
      'Once you start spending from your family accounts, you\'ll see a list of your cashback transactions here.',
      name: 'familyRewardsEmptyTransactionsSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Start spending today!`
  String get familyRewardsEmptyTransactionsTitle {
    return Intl.message(
      'Start spending today!',
      name: 'familyRewardsEmptyTransactionsTitle',
      desc: '',
      args: [],
    );
  }

  /// `Cashback transactions`
  String get familyRewardsTransactionsSectionTitle {
    return Intl.message(
      'Cashback transactions',
      name: 'familyRewardsTransactionsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get familySavingsAddButtonTitle {
    return Intl.message(
      'Add',
      name: 'familySavingsAddButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Saving spaces`
  String get familySavingsSectionTitle {
    return Intl.message(
      'Saving spaces',
      name: 'familySavingsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add new`
  String get familySavingsTabEmptyStateButtonTitle {
    return Intl.message(
      'Add new',
      name: 'familySavingsTabEmptyStateButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Get started by creating a space\nfor your next shared goal.`
  String get familySavingsTabEmptyStateSubtitle {
    return Intl.message(
      'Get started by creating a space\nfor your next shared goal.',
      name: 'familySavingsTabEmptyStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Your saving spaces\nwill show up here`
  String get familySavingsTabEmptyStateTitle {
    return Intl.message(
      'Your saving spaces\nwill show up here',
      name: 'familySavingsTabEmptyStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add a family lead from the\n‘My family’ tab to get started.`
  String get familySavingsTabNoFamilyEmptyStateSubtitle {
    return Intl.message(
      'Add a family lead from the\n‘My family’ tab to get started.',
      name: 'familySavingsTabNoFamilyEmptyStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Add money`
  String get familyTabAddMoneyButtonTitle {
    return Intl.message(
      'Add money',
      name: 'familyTabAddMoneyButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Top up {name}'s pocket today and get {cashback} cashback from Wio`
  String familyTabParticipantAccountZeroBalancePromoForPocketTitle(
      String cashback, String name) {
    return Intl.message(
      'Top up $name\'s pocket today and get $cashback cashback from Wio',
      name: 'familyTabParticipantAccountZeroBalancePromoForPocketTitle',
      desc: '',
      args: [cashback, name],
    );
  }

  /// `Get started by adding money to {name}’s pocket`
  String familyTabParticipantAccountZeroBalanceTitle(String name) {
    return Intl.message(
      'Get started by adding money to $name’s pocket',
      name: 'familyTabParticipantAccountZeroBalanceTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{months, plural, =1{For 1 month, till {date}} other{For {months} months, till {date}}}`
  String familyTermDepositDeadlineText(String date, num months) {
    return Intl.plural(
      months,
      one: 'For 1 month, till $date',
      other: 'For $months months, till $date',
      name: 'familyTermDepositDeadlineText',
      desc: '',
      args: [date, months],
    );
  }

  /// `Join Family`
  String get invitationParticipantScreenAcceptInviteButtonTitle {
    return Intl.message(
      'Join Family',
      name: 'invitationParticipantScreenAcceptInviteButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We can not onboard you if you turn 18 in less than 6 months`
  String get invitationParticipantScreenAgeWarningText {
    return Intl.message(
      'We can not onboard you if you turn 18 in less than 6 months',
      name: 'invitationParticipantScreenAgeWarningText',
      desc: '',
      args: [],
    );
  }

  /// `What’s a family member?`
  String get invitationParticipantScreenLearnMoreLabel {
    return Intl.message(
      'What’s a family member?',
      name: 'invitationParticipantScreenLearnMoreLabel',
      desc: '',
      args: [],
    );
  }

  /// `No Thanks`
  String get invitationParticipantScreenRejectButtonTitle {
    return Intl.message(
      'No Thanks',
      name: 'invitationParticipantScreenRejectButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ve been invited by {inviter} to join Wio Family as a family member.`
  String invitationParticipantScreenSubtitle(String inviter) {
    return Intl.message(
      'You’ve been invited by $inviter to join Wio Family as a family member.',
      name: 'invitationParticipantScreenSubtitle',
      desc: '',
      args: [inviter],
    );
  }

  /// `You’ve got an invite`
  String get invitationParticipantScreenTitle {
    return Intl.message(
      'You’ve got an invite',
      name: 'invitationParticipantScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Other`
  String get invitationRejectionReasonsScreenOtherReason {
    return Intl.message(
      'Other',
      name: 'invitationRejectionReasonsScreenOtherReason',
      desc: '',
      args: [],
    );
  }

  /// `I don’t know {inviter}`
  String invitationRejectionReasonsScreenReason1(String inviter) {
    return Intl.message(
      'I don’t know $inviter',
      name: 'invitationRejectionReasonsScreenReason1',
      desc: '',
      args: [inviter],
    );
  }

  /// `I have changed my mind`
  String get invitationRejectionReasonsScreenReason2 {
    return Intl.message(
      'I have changed my mind',
      name: 'invitationRejectionReasonsScreenReason2',
      desc: '',
      args: [],
    );
  }

  /// `I was just trying this feature`
  String get invitationRejectionReasonsScreenReason3 {
    return Intl.message(
      'I was just trying this feature',
      name: 'invitationRejectionReasonsScreenReason3',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get invitationRejectionReasonsScreenSubmitButtonTitle {
    return Intl.message(
      'Done',
      name: 'invitationRejectionReasonsScreenSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `This will help us improve your experience. We appreciate your feedback.`
  String get invitationRejectionReasonsScreenSubtitle {
    return Intl.message(
      'This will help us improve your experience. We appreciate your feedback.',
      name: 'invitationRejectionReasonsScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `What is the reason for rejection?`
  String get invitationRejectionReasonsScreenTitle {
    return Intl.message(
      'What is the reason for rejection?',
      name: 'invitationRejectionReasonsScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Accept Invite`
  String get invitationScreenAcceptInviteButtonTitle {
    return Intl.message(
      'Accept Invite',
      name: 'invitationScreenAcceptInviteButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{type, select, createFamilyAccount{Family account invite} addCoOwnerToPocket{Shared pocket invite} other{ }}`
  String invitationScreenAppBarTitle(String type) {
    return Intl.select(
      type,
      {
        'createFamilyAccount': 'Family account invite',
        'addCoOwnerToPocket': 'Shared pocket invite',
        'other': ' ',
      },
      name: 'invitationScreenAppBarTitle',
      desc: '',
      args: [type],
    );
  }

  /// `What’s a Family account?`
  String get invitationScreenLearnMoreLabel {
    return Intl.message(
      'What’s a Family account?',
      name: 'invitationScreenLearnMoreLabel',
      desc: '',
      args: [],
    );
  }

  /// `Reject`
  String get invitationScreenRejectButtonTitle {
    return Intl.message(
      'Reject',
      name: 'invitationScreenRejectButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Accept Invite`
  String get invitationScreenSharedPocketAcceptCta {
    return Intl.message(
      'Accept Invite',
      name: 'invitationScreenSharedPocketAcceptCta',
      desc: '',
      args: [],
    );
  }

  /// `What you can do`
  String get invitationScreenSharedPocketDoablesHeader {
    return Intl.message(
      'What you can do',
      name: 'invitationScreenSharedPocketDoablesHeader',
      desc: '',
      args: [],
    );
  }

  /// `Stay on top of their spending and empower smart money habits.`
  String get invitationScreenSharedPocketDoablesPt1Subtitle {
    return Intl.message(
      'Stay on top of their spending and empower smart money habits.',
      name: 'invitationScreenSharedPocketDoablesPt1Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Track spending`
  String get invitationScreenSharedPocketDoablesPt1Title {
    return Intl.message(
      'Track spending',
      name: 'invitationScreenSharedPocketDoablesPt1Title',
      desc: '',
      args: [],
    );
  }

  /// `Add funds from your family or personal account whenever they need it.`
  String get invitationScreenSharedPocketDoablesPt2Subtitle {
    return Intl.message(
      'Add funds from your family or personal account whenever they need it.',
      name: 'invitationScreenSharedPocketDoablesPt2Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Top-up allowance`
  String get invitationScreenSharedPocketDoablesPt2Title {
    return Intl.message(
      'Top-up allowance',
      name: 'invitationScreenSharedPocketDoablesPt2Title',
      desc: '',
      args: [],
    );
  }

  /// `Set spending limits, add expiry dates, and create or freeze cards for extra security.`
  String get invitationScreenSharedPocketDoablesPt3Subtitle {
    return Intl.message(
      'Set spending limits, add expiry dates, and create or freeze cards for extra security.',
      name: 'invitationScreenSharedPocketDoablesPt3Subtitle',
      desc: '',
      args: [],
    );
  }

  /// `Control limits`
  String get invitationScreenSharedPocketDoablesPt3Title {
    return Intl.message(
      'Control limits',
      name: 'invitationScreenSharedPocketDoablesPt3Title',
      desc: '',
      args: [],
    );
  }

  /// `Reject`
  String get invitationScreenSharedPocketRejectCta {
    return Intl.message(
      'Reject',
      name: 'invitationScreenSharedPocketRejectCta',
      desc: '',
      args: [],
    );
  }

  /// `{CoOwner} has invited you to share access to a pocket for`
  String invitationScreenSharedPocketSubtitle(Object CoOwner) {
    return Intl.message(
      '$CoOwner has invited you to share access to a pocket for',
      name: 'invitationScreenSharedPocketSubtitle',
      desc: '',
      args: [CoOwner],
    );
  }

  /// `You've received a shared pocket access invite`
  String get invitationScreenSharedPocketTitle {
    return Intl.message(
      'You\'ve received a shared pocket access invite',
      name: 'invitationScreenSharedPocketTitle',
      desc: '',
      args: [],
    );
  }

  /// `Skip for now`
  String get invitationScreenSkipForNowButtonTitle {
    return Intl.message(
      'Skip for now',
      name: 'invitationScreenSkipForNowButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `An invite was sent by {inviter} to open a family {currency} account.`
  String invitationScreenSubtitle(String currency, String inviter) {
    return Intl.message(
      'An invite was sent by $inviter to open a family $currency account.',
      name: 'invitationScreenSubtitle',
      desc: '',
      args: [currency, inviter],
    );
  }

  /// `Terms & Conditions`
  String get invitationScreenTermAndConditionsInfoHighlightedLabel {
    return Intl.message(
      'Terms & Conditions',
      name: 'invitationScreenTermAndConditionsInfoHighlightedLabel',
      desc: '',
      args: [],
    );
  }

  /// `By tapping “Accept invite” you agree with\nTerms & Conditions`
  String get invitationScreenTermAndConditionsInfoLabel {
    return Intl.message(
      'By tapping “Accept invite” you agree with\nTerms & Conditions',
      name: 'invitationScreenTermAndConditionsInfoLabel',
      desc: '',
      args: [],
    );
  }

  /// `We found a family account invite!`
  String get invitationScreenTitle {
    return Intl.message(
      'We found a family account invite!',
      name: 'invitationScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `{takenAction, select, accept{Invite already accepted. You can make changes after onboarding.} reject{You have rejected the invite. Get another invite to setup family account.} skip{Invite already skipped. You can make changes after onboarding.} other{Cannot take action for the invite. Try another invite.}}`
  String invitationsScreenActionAlreadyTakenErrorMessage(String takenAction) {
    return Intl.select(
      takenAction,
      {
        'accept':
            'Invite already accepted. You can make changes after onboarding.',
        'reject':
            'You have rejected the invite. Get another invite to setup family account.',
        'skip':
            'Invite already skipped. You can make changes after onboarding.',
        'other': 'Cannot take action for the invite. Try another invite.',
      },
      name: 'invitationsScreenActionAlreadyTakenErrorMessage',
      desc: '',
      args: [takenAction],
    );
  }

  /// `Family account invite`
  String get invitationsScreenAppBarTitle {
    return Intl.message(
      'Family account invite',
      name: 'invitationsScreenAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite accepted. Your family account will be ready after approval from {inviter}.`
  String invitationsScreenInvitationAcceptedMessage(String inviter) {
    return Intl.message(
      'Invite accepted. Your family account will be ready after approval from $inviter.',
      name: 'invitationsScreenInvitationAcceptedMessage',
      desc: '',
      args: [inviter],
    );
  }

  /// `Accepted`
  String get invitationsScreenInvitationCardAcceptedLabel {
    return Intl.message(
      'Accepted',
      name: 'invitationsScreenInvitationCardAcceptedLabel',
      desc: '',
      args: [],
    );
  }

  /// `Rejected`
  String get invitationsScreenInvitationCardRejectedLabel {
    return Intl.message(
      'Rejected',
      name: 'invitationsScreenInvitationCardRejectedLabel',
      desc: '',
      args: [],
    );
  }

  /// `Skipped`
  String get invitationsScreenInvitationCardSkippedLabel {
    return Intl.message(
      'Skipped',
      name: 'invitationsScreenInvitationCardSkippedLabel',
      desc: '',
      args: [],
    );
  }

  /// `Invited {duration} days ago`
  String invitationsScreenInvitationCardSubtitle(String duration) {
    return Intl.message(
      'Invited $duration days ago',
      name: 'invitationsScreenInvitationCardSubtitle',
      desc: '',
      args: [duration],
    );
  }

  /// `{type, select, second{{amount, plural, =1{Just now} other{Invited {amount} seconds ago}}} minute{{amount, plural, =1{Invited a minute ago} other{Invited {amount} minutes ago}}} hour{{amount, plural, =1{Invited an hour ago} other{Invited {amount} hours ago}}} day{{amount, plural, =1{Invited a day ago} other{Invited {amount} days ago}}} week{{amount, plural, =1{Invited a week ago} other{Invited {amount} weeks ago}}} month{{amount, plural, =1{Invited a month ago} other{Invited {amount} months ago}}} year{{amount, plural, =1{Invited a year ago} other{Invited {amount} years ago}}} other{Invited some time ago}}`
  String invitationsScreenInvitationCardTimeAgoLabel(String type, num amount) {
    return Intl.select(
      type,
      {
        'second':
            '{amount, plural, =1{Just now} other{Invited {amount} seconds ago}}',
        'minute':
            '{amount, plural, =1{Invited a minute ago} other{Invited {amount} minutes ago}}',
        'hour':
            '{amount, plural, =1{Invited an hour ago} other{Invited {amount} hours ago}}',
        'day':
            '{amount, plural, =1{Invited a day ago} other{Invited {amount} days ago}}',
        'week':
            '{amount, plural, =1{Invited a week ago} other{Invited {amount} weeks ago}}',
        'month':
            '{amount, plural, =1{Invited a month ago} other{Invited {amount} months ago}}',
        'year':
            '{amount, plural, =1{Invited a year ago} other{Invited {amount} years ago}}',
        'other': 'Invited some time ago',
      },
      name: 'invitationsScreenInvitationCardTimeAgoLabel',
      desc: '',
      args: [type, amount],
    );
  }

  /// `Invite rejected. You can set up your own family account later anytime.`
  String get invitationsScreenInvitationRejectedMessage {
    return Intl.message(
      'Invite rejected. You can set up your own family account later anytime.',
      name: 'invitationsScreenInvitationRejectedMessage',
      desc: '',
      args: [],
    );
  }

  /// `Invite skipped. You can accept or reject invite after finishing onboarding.`
  String get invitationsScreenInvitationSkippedMessage {
    return Intl.message(
      'Invite skipped. You can accept or reject invite after finishing onboarding.',
      name: 'invitationsScreenInvitationSkippedMessage',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get invitationsScreenSubmitButtonTitle {
    return Intl.message(
      'Next',
      name: 'invitationsScreenSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You got some invites for family accounts.`
  String get invitationsScreenSubtitle {
    return Intl.message(
      'You got some invites for family accounts.',
      name: 'invitationsScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `We found some invites!`
  String get invitationsScreenTitle {
    return Intl.message(
      'We found some invites!',
      name: 'invitationsScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `This virtual card for will be linked to your family account.`
  String get inviteAdultParticipantAccountSelectionFACardSubtitle {
    return Intl.message(
      'This virtual card for will be linked to your family account.',
      name: 'inviteAdultParticipantAccountSelectionFACardSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Family account`
  String get inviteAdultParticipantAccountSelectionFACardTitle {
    return Intl.message(
      'Family account',
      name: 'inviteAdultParticipantAccountSelectionFACardTitle',
      desc: '',
      args: [],
    );
  }

  /// `New shared card`
  String get inviteAdultParticipantAccountSelectionHeader {
    return Intl.message(
      'New shared card',
      name: 'inviteAdultParticipantAccountSelectionHeader',
      desc: '',
      args: [],
    );
  }

  /// `Open now`
  String get inviteAdultParticipantAccountSelectionNoFACardCta {
    return Intl.message(
      'Open now',
      name: 'inviteAdultParticipantAccountSelectionNoFACardCta',
      desc: '',
      args: [],
    );
  }

  /// `Manage member with your other family lead - set limits, top up card and more`
  String get inviteAdultParticipantAccountSelectionNoFACardDesc {
    return Intl.message(
      'Manage member with your other family lead - set limits, top up card and more',
      name: 'inviteAdultParticipantAccountSelectionNoFACardDesc',
      desc: '',
      args: [],
    );
  }

  /// `Where will this card spend from?`
  String get inviteAdultParticipantAccountSelectionTitle {
    return Intl.message(
      'Where will this card spend from?',
      name: 'inviteAdultParticipantAccountSelectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `How do you feel about this experience?`
  String get inviteAdultParticipantCustomerFeedbackHighlightedTitle {
    return Intl.message(
      'How do you feel about this experience?',
      name: 'inviteAdultParticipantCustomerFeedbackHighlightedTitle',
      desc: '',
      args: [],
    );
  }

  /// `You just invited an adult participant! How do you feel about this experience?`
  String get inviteAdultParticipantCustomerFeedbackTitle {
    return Intl.message(
      'You just invited an adult participant! How do you feel about this experience?',
      name: 'inviteAdultParticipantCustomerFeedbackTitle',
      desc: '',
      args: [],
    );
  }

  /// `New shared card`
  String get inviteAdultParticipantFASelectionHeader {
    return Intl.message(
      'New shared card',
      name: 'inviteAdultParticipantFASelectionHeader',
      desc: '',
      args: [],
    );
  }

  /// `Your and {coOwnerName}`
  String inviteAdultParticipantFASelectionListBoxTitle(Object coOwnerName) {
    return Intl.message(
      'Your and $coOwnerName',
      name: 'inviteAdultParticipantFASelectionListBoxTitle',
      desc: '',
      args: [coOwnerName],
    );
  }

  /// `Which family account do you want to link the card to?`
  String get inviteAdultParticipantFASelectionTitle {
    return Intl.message(
      'Which family account do you want to link the card to?',
      name: 'inviteAdultParticipantFASelectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `This virtual card will be linked to your personal account. Use this for my money or my credit modes.`
  String get inviteAdultParticipantPersonalAccountSelectionCardSubtitle {
    return Intl.message(
      'This virtual card will be linked to your personal account. Use this for my money or my credit modes.',
      name: 'inviteAdultParticipantPersonalAccountSelectionCardSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Your personal account`
  String get inviteAdultParticipantPersonalAccountSelectionCardTitle {
    return Intl.message(
      'Your personal account',
      name: 'inviteAdultParticipantPersonalAccountSelectionCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `WIO CREDIT`
  String get inviteAdultParticipantReviewScreenCardPreviewCredit {
    return Intl.message(
      'WIO CREDIT',
      name: 'inviteAdultParticipantReviewScreenCardPreviewCredit',
      desc: '',
      args: [],
    );
  }

  /// `This card is using Wio Credit`
  String get inviteAdultParticipantReviewScreenCardPreviewSubtitleCredit {
    return Intl.message(
      'This card is using Wio Credit',
      name: 'inviteAdultParticipantReviewScreenCardPreviewSubtitleCredit',
      desc: '',
      args: [],
    );
  }

  /// `This card is using Family Account`
  String get inviteAdultParticipantReviewScreenCardPreviewSubtitleFamily {
    return Intl.message(
      'This card is using Family Account',
      name: 'inviteAdultParticipantReviewScreenCardPreviewSubtitleFamily',
      desc: '',
      args: [],
    );
  }

  /// `You're spending with your money`
  String get inviteAdultParticipantReviewScreenCardPreviewSubtitleMyMoney {
    return Intl.message(
      'You\'re spending with your money',
      name: 'inviteAdultParticipantReviewScreenCardPreviewSubtitleMyMoney',
      desc: '',
      args: [],
    );
  }

  /// `Set a custom expiry date`
  String get inviteAdultParticipantReviewScreenExpiryCardSubTitle {
    return Intl.message(
      'Set a custom expiry date',
      name: 'inviteAdultParticipantReviewScreenExpiryCardSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Expiry date`
  String get inviteAdultParticipantReviewScreenExpiryCardTitle {
    return Intl.message(
      'Expiry date',
      name: 'inviteAdultParticipantReviewScreenExpiryCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wio Credit`
  String get inviteAdultParticipantReviewScreenSwitchCredit {
    return Intl.message(
      'Wio Credit',
      name: 'inviteAdultParticipantReviewScreenSwitchCredit',
      desc: '',
      args: [],
    );
  }

  /// `My Money`
  String get inviteAdultParticipantReviewScreenSwitchMyMoney {
    return Intl.message(
      'My Money',
      name: 'inviteAdultParticipantReviewScreenSwitchMyMoney',
      desc: '',
      args: [],
    );
  }

  /// `New shared card`
  String get inviteAdultParticipantSetUpCardAppBarTitle {
    return Intl.message(
      'New shared card',
      name: 'inviteAdultParticipantSetUpCardAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose how much {name} can spend using this shared card.`
  String inviteAdultParticipantSpendingLimitScreenSubTitle(String name) {
    return Intl.message(
      'Choose how much $name can spend using this shared card.',
      name: 'inviteAdultParticipantSpendingLimitScreenSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Set up a spending limit for this card`
  String get inviteAdultParticipantSpendingLimitScreenTitle {
    return Intl.message(
      'Set up a spending limit for this card',
      name: 'inviteAdultParticipantSpendingLimitScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Back to hub`
  String get inviteAdultParticipantSuccessScreenButtonTitle {
    return Intl.message(
      'Back to hub',
      name: 'inviteAdultParticipantSuccessScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to {inviteeName} has been sent.\n\nWe’ll notify you once they have accepted.`
  String inviteAdultParticipantSuccessScreenSubTitle(String inviteeName) {
    return Intl.message(
      'Your invite to $inviteeName has been sent.\n\nWe’ll notify you once they have accepted.',
      name: 'inviteAdultParticipantSuccessScreenSubTitle',
      desc: '',
      args: [inviteeName],
    );
  }

  /// `MABROOK!`
  String get inviteAdultParticipantSuccessScreenTitle {
    return Intl.message(
      'MABROOK!',
      name: 'inviteAdultParticipantSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Send an invite`
  String get inviteAdultParticpantReviewScreenButtonTitle {
    return Intl.message(
      'Send an invite',
      name: 'inviteAdultParticpantReviewScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `New shared card`
  String get inviteAdultParticpantReviewScreenPageTitle {
    return Intl.message(
      'New shared card',
      name: 'inviteAdultParticpantReviewScreenPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Card shared with`
  String get inviteAdultParticpantReviewScreenRecipientCardTitle {
    return Intl.message(
      'Card shared with',
      name: 'inviteAdultParticpantReviewScreenRecipientCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Review your card sharing details`
  String get inviteAdultParticpantReviewScreenTitle {
    return Intl.message(
      'Review your card sharing details',
      name: 'inviteAdultParticpantReviewScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `How do you feel about this experience?`
  String get inviteCoOwnerCustomerFeedbackHighlightedTitle {
    return Intl.message(
      'How do you feel about this experience?',
      name: 'inviteCoOwnerCustomerFeedbackHighlightedTitle',
      desc: '',
      args: [],
    );
  }

  /// `You just invited a family lead! How do you feel about this experience?`
  String get inviteCoOwnerCustomerFeedbackTitle {
    return Intl.message(
      'You just invited a family lead! How do you feel about this experience?',
      name: 'inviteCoOwnerCustomerFeedbackTitle',
      desc: '',
      args: [],
    );
  }

  /// `Shared access`
  String get inviteCoOwnerToPocketAppBarTitle {
    return Intl.message(
      'Shared access',
      name: 'inviteCoOwnerToPocketAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `We'll send an invite to your family lead. \nOnce accepted you need to approve family member for them to see themselves and the pocket on their dashboard.`
  String get inviteCoOwnerToPocketConfirmationBottomSheetDescription {
    return Intl.message(
      'We\'ll send an invite to your family lead. \nOnce accepted you need to approve family member for them to see themselves and the pocket on their dashboard.',
      name: 'inviteCoOwnerToPocketConfirmationBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to share {participantName}'s pocket with {coOwnerName}?`
  String inviteCoOwnerToPocketConfirmationBottomSheetTitle(
      String coOwnerName, String participantName) {
    return Intl.message(
      'Are you sure you want to share $participantName\'s pocket with $coOwnerName?',
      name: 'inviteCoOwnerToPocketConfirmationBottomSheetTitle',
      desc: '',
      args: [coOwnerName, participantName],
    );
  }

  /// `Go back`
  String get inviteCoOwnerToPocketConfirmationGoBackButtonTitle {
    return Intl.message(
      'Go back',
      name: 'inviteCoOwnerToPocketConfirmationGoBackButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I’m sure`
  String get inviteCoOwnerToPocketConfirmationSubmitButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'inviteCoOwnerToPocketConfirmationSubmitButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You & {coOwnerName}`
  String inviteCoOwnerToPocketFamilyAccountListBoxTitle(String coOwnerName) {
    return Intl.message(
      'You & $coOwnerName',
      name: 'inviteCoOwnerToPocketFamilyAccountListBoxTitle',
      desc: '',
      args: [coOwnerName],
    );
  }

  /// `Link pocket to a family account — your family lead will get equal access to it.`
  String get inviteCoOwnerToPocketPageSubtitle {
    return Intl.message(
      'Link pocket to a family account — your family lead will get equal access to it.',
      name: 'inviteCoOwnerToPocketPageSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Who do you want to share this pocket with?`
  String get inviteCoOwnerToPocketPageTitle {
    return Intl.message(
      'Who do you want to share this pocket with?',
      name: 'inviteCoOwnerToPocketPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go to hub`
  String get inviteCoOwnerToPocketSuccessGoToHubButtonTitle {
    return Intl.message(
      'Go to hub',
      name: 'inviteCoOwnerToPocketSuccessGoToHubButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to share pocket access has been sent.`
  String get inviteCoOwnerToPocketSuccessSubtitle {
    return Intl.message(
      'Your invite to share pocket access has been sent.',
      name: 'inviteCoOwnerToPocketSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `DONE!`
  String get inviteCoOwnerToPocketSuccessTitle {
    return Intl.message(
      'DONE!',
      name: 'inviteCoOwnerToPocketSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `New shared card`
  String get inviteParticipantAppBarTitle {
    return Intl.message(
      'New shared card',
      name: 'inviteParticipantAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `SHARED WITH {name}`
  String inviteParticipantCardComponentShortTitle(String name) {
    return Intl.message(
      'SHARED WITH $name',
      name: 'inviteParticipantCardComponentShortTitle',
      desc: '',
      args: [name],
    );
  }

  /// `How do you feel about this experience?`
  String get inviteParticipantCustomerFeedbackHighlightedTitle {
    return Intl.message(
      'How do you feel about this experience?',
      name: 'inviteParticipantCustomerFeedbackHighlightedTitle',
      desc: '',
      args: [],
    );
  }

  /// `You just invited a family member! How do you feel about this experience?`
  String get inviteParticipantCustomerFeedbackTitle {
    return Intl.message(
      'You just invited a family member! How do you feel about this experience?',
      name: 'inviteParticipantCustomerFeedbackTitle',
      desc: '',
      args: [],
    );
  }

  /// `This name already exists.`
  String get inviteParticipantSetUpCardNameErrorNameAlreadyExists {
    return Intl.message(
      'This name already exists.',
      name: 'inviteParticipantSetUpCardNameErrorNameAlreadyExists',
      desc: '',
      args: [],
    );
  }

  /// `Try a shorter name`
  String get inviteParticipantSetUpCardNameErrorNameTooLong {
    return Intl.message(
      'Try a shorter name',
      name: 'inviteParticipantSetUpCardNameErrorNameTooLong',
      desc: '',
      args: [],
    );
  }

  /// `Card's name`
  String get inviteParticipantSetUpCardNameInputHint {
    return Intl.message(
      'Card\'s name',
      name: 'inviteParticipantSetUpCardNameInputHint',
      desc: '',
      args: [],
    );
  }

  /// `Give a name to {name}’s card`
  String inviteParticipantSetUpCardNamePageTitle(String name) {
    return Intl.message(
      'Give a name to $name’s card',
      name: 'inviteParticipantSetUpCardNamePageTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Next`
  String get inviteParticipantSetUpCardNextButtonTitle {
    return Intl.message(
      'Next',
      name: 'inviteParticipantSetUpCardNextButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Select your preferred card design`
  String get inviteParticipantSetUpCardSkinPageTitle {
    return Intl.message(
      'Select your preferred card design',
      name: 'inviteParticipantSetUpCardSkinPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose your color`
  String get inviteParticipantSetUpCardSkinUngroupedSectionTitle {
    return Intl.message(
      'Choose your color',
      name: 'inviteParticipantSetUpCardSkinUngroupedSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose how much {name} can spend using this shared card.`
  String inviteParticipantSpendingLimitScreenSubTitle(String name) {
    return Intl.message(
      'Choose how much $name can spend using this shared card.',
      name: 'inviteParticipantSpendingLimitScreenSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Set up a spending limit for this card`
  String get inviteParticipantSpendingLimitScreenTitle {
    return Intl.message(
      'Set up a spending limit for this card',
      name: 'inviteParticipantSpendingLimitScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Back to hub`
  String get inviteParticipantSuccessScreenButtonTitle {
    return Intl.message(
      'Back to hub',
      name: 'inviteParticipantSuccessScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to {name} has been sent.\n\nWe’ll notify you once they have accepted.`
  String inviteParticipantSuccessScreenSubTitle(String name) {
    return Intl.message(
      'Your invite to $name has been sent.\n\nWe’ll notify you once they have accepted.',
      name: 'inviteParticipantSuccessScreenSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `MABROOK!`
  String get inviteParticipantSuccessScreenTitle {
    return Intl.message(
      'MABROOK!',
      name: 'inviteParticipantSuccessScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Send an invite`
  String get inviteParticpantReviewScreenButtonTitle {
    return Intl.message(
      'Send an invite',
      name: 'inviteParticpantReviewScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Set a custom expiry date`
  String get inviteParticpantReviewScreenExpiryCardSubTitle {
    return Intl.message(
      'Set a custom expiry date',
      name: 'inviteParticpantReviewScreenExpiryCardSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Card expiry date`
  String get inviteParticpantReviewScreenExpiryCardTitle {
    return Intl.message(
      'Card expiry date',
      name: 'inviteParticpantReviewScreenExpiryCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Review your card sharing details`
  String get inviteParticpantReviewScreenPageTitle {
    return Intl.message(
      'Review your card sharing details',
      name: 'inviteParticpantReviewScreenPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Permissions`
  String get inviteParticpantReviewScreenPermissionTitle {
    return Intl.message(
      'Permissions',
      name: 'inviteParticpantReviewScreenPermissionTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} will receive an SMS with an invite link, we’ll let you know once they accept.`
  String inviteParticpantReviewScreenSubTitle(String name) {
    return Intl.message(
      '$name will receive an SMS with an invite link, we’ll let you know once they accept.',
      name: 'inviteParticpantReviewScreenSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Review and invite`
  String get inviteParticpantReviewScreenTitle {
    return Intl.message(
      'Review and invite',
      name: 'inviteParticpantReviewScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Terms & Conditions`
  String get inviteParticpantReviewScreenTncHighlightedTitle {
    return Intl.message(
      'Terms & Conditions',
      name: 'inviteParticpantReviewScreenTncHighlightedTitle',
      desc: '',
      args: [],
    );
  }

  /// `By tapping “Send an invite” you agree with Terms & Conditions`
  String get inviteParticpantReviewScreenTncTitle {
    return Intl.message(
      'By tapping “Send an invite” you agree with Terms & Conditions',
      name: 'inviteParticpantReviewScreenTncTitle',
      desc: '',
      args: [],
    );
  }

  /// `Both family leads can manage the account - view activity, set controls, and make changes anytime.`
  String get jointAccountCoOwnerRulesCardSubTitle1 {
    return Intl.message(
      'Both family leads can manage the account - view activity, set controls, and make changes anytime.',
      name: 'jointAccountCoOwnerRulesCardSubTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Combine your efforts to save smarter, spend better, and reach shared goals faster.`
  String get jointAccountCoOwnerRulesCardSubTitle2 {
    return Intl.message(
      'Combine your efforts to save smarter, spend better, and reach shared goals faster.',
      name: 'jointAccountCoOwnerRulesCardSubTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Earn interest on family accounts and get even more if your family lead is on a higher plan.`
  String get jointAccountCoOwnerRulesCardSubTitle3 {
    return Intl.message(
      'Earn interest on family accounts and get even more if your family lead is on a higher plan.',
      name: 'jointAccountCoOwnerRulesCardSubTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Share control`
  String get jointAccountCoOwnerRulesCardTitle1 {
    return Intl.message(
      'Share control',
      name: 'jointAccountCoOwnerRulesCardTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Do more together`
  String get jointAccountCoOwnerRulesCardTitle2 {
    return Intl.message(
      'Do more together',
      name: 'jointAccountCoOwnerRulesCardTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Better interest for both`
  String get jointAccountCoOwnerRulesCardTitle3 {
    return Intl.message(
      'Better interest for both',
      name: 'jointAccountCoOwnerRulesCardTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get jointAccountInvitationAcceptedScreenButtonTitle {
    return Intl.message(
      'Done',
      name: 'jointAccountInvitationAcceptedScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You will be able to access the family account once {inviterName} approves your request.`
  String jointAccountInvitationAcceptedScreenSubtitle(String inviterName) {
    return Intl.message(
      'You will be able to access the family account once $inviterName approves your request.',
      name: 'jointAccountInvitationAcceptedScreenSubtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `MABROOK!`
  String get jointAccountInvitationAcceptedScreenTitle {
    return Intl.message(
      'MABROOK!',
      name: 'jointAccountInvitationAcceptedScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Accept`
  String get jointAccountInvitationConfirmationButtonTitle {
    return Intl.message(
      'Accept',
      name: 'jointAccountInvitationConfirmationButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Review and agree with the terms & conditions for family accounts.`
  String get jointAccountInvitationConfirmationSubTitle {
    return Intl.message(
      'Review and agree with the terms & conditions for family accounts.',
      name: 'jointAccountInvitationConfirmationSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Before proceeding...`
  String get jointAccountInvitationConfirmationTitle {
    return Intl.message(
      'Before proceeding...',
      name: 'jointAccountInvitationConfirmationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go to Dashboard`
  String get jointAccountInvitationRejectedScreenButtonTitle {
    return Intl.message(
      'Go to Dashboard',
      name: 'jointAccountInvitationRejectedScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `The invite has been rejected successfully.`
  String get jointAccountInvitationRejectedScreenSubtitle {
    return Intl.message(
      'The invite has been rejected successfully.',
      name: 'jointAccountInvitationRejectedScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `DONE!`
  String get jointAccountInvitationRejectedScreenTitle {
    return Intl.message(
      'DONE!',
      name: 'jointAccountInvitationRejectedScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Here’s what’s next`
  String get jointAccountInviteSentNextStepsSectionTitle {
    return Intl.message(
      'Here’s what’s next',
      name: 'jointAccountInviteSentNextStepsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go to hub`
  String get jointAccountInviteSentPageButtonTitle {
    return Intl.message(
      'Go to hub',
      name: 'jointAccountInviteSentPageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to {name} has been sent.\nHere’s what happens next:`
  String jointAccountInviteSentPageSubtitleExistingUser(String name) {
    return Intl.message(
      'Your invite to $name has been sent.\nHere’s what happens next:',
      name: 'jointAccountInviteSentPageSubtitleExistingUser',
      desc: '',
      args: [name],
    );
  }

  /// `An SMS has been sent to {name}  {phone} with the invite link`
  String jointAccountInviteSentPageSubtitleNewUser(String name, String phone) {
    return Intl.message(
      'An SMS has been sent to $name  $phone with the invite link',
      name: 'jointAccountInviteSentPageSubtitleNewUser',
      desc: '',
      args: [name, phone],
    );
  }

  /// `Invite sent!`
  String get jointAccountInviteSentPageTitle {
    return Intl.message(
      'Invite sent!',
      name: 'jointAccountInviteSentPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} will receive a notification in app`
  String jointAccountInviteSentStep1SubtitleExistingUser(String name) {
    return Intl.message(
      '$name will receive a notification in app',
      name: 'jointAccountInviteSentStep1SubtitleExistingUser',
      desc: '',
      args: [name],
    );
  }

  /// `{name} will receive an SMS link.`
  String jointAccountInviteSentStep1SubtitleNewUser(String name) {
    return Intl.message(
      '$name will receive an SMS link.',
      name: 'jointAccountInviteSentStep1SubtitleNewUser',
      desc: '',
      args: [name],
    );
  }

  /// `Invite sent`
  String get jointAccountInviteSentStep1TitleExistingUser {
    return Intl.message(
      'Invite sent',
      name: 'jointAccountInviteSentStep1TitleExistingUser',
      desc: '',
      args: [],
    );
  }

  /// `Invite sent`
  String get jointAccountInviteSentStep1TitleNewUser {
    return Intl.message(
      'Invite sent',
      name: 'jointAccountInviteSentStep1TitleNewUser',
      desc: '',
      args: [],
    );
  }

  /// `Once {name} accepts, we’ll send you  a notification`
  String jointAccountInviteSentStep2SubtitleExistingUser(String name) {
    return Intl.message(
      'Once $name accepts, we’ll send you  a notification',
      name: 'jointAccountInviteSentStep2SubtitleExistingUser',
      desc: '',
      args: [name],
    );
  }

  /// `Accepts invite and completes the sign up steps. We’ll notify you once that’s done.`
  String get jointAccountInviteSentStep2SubtitleNewUser {
    return Intl.message(
      'Accepts invite and completes the sign up steps. We’ll notify you once that’s done.',
      name: 'jointAccountInviteSentStep2SubtitleNewUser',
      desc: '',
      args: [],
    );
  }

  /// `{name} accepts the invite`
  String jointAccountInviteSentStep2TitleExistingUser(String name) {
    return Intl.message(
      '$name accepts the invite',
      name: 'jointAccountInviteSentStep2TitleExistingUser',
      desc: '',
      args: [name],
    );
  }

  /// `{name} signs up`
  String jointAccountInviteSentStep2TitleNewUser(String name) {
    return Intl.message(
      '$name signs up',
      name: 'jointAccountInviteSentStep2TitleNewUser',
      desc: '',
      args: [name],
    );
  }

  /// `Make sure {name}’s details are correct, hit approve, and you’re done!`
  String jointAccountInviteSentStep3SubtitleExistingUser(String name) {
    return Intl.message(
      'Make sure $name’s details are correct, hit approve, and you’re done!',
      name: 'jointAccountInviteSentStep3SubtitleExistingUser',
      desc: '',
      args: [name],
    );
  }

  /// `Make sure {name}’s details are correct, hit approve, and you’re done!`
  String jointAccountInviteSentStep3SubtitleNewUser(String name) {
    return Intl.message(
      'Make sure $name’s details are correct, hit approve, and you’re done!',
      name: 'jointAccountInviteSentStep3SubtitleNewUser',
      desc: '',
      args: [name],
    );
  }

  /// `You approve {name}`
  String jointAccountInviteSentStep3TitleExistingUser(String name) {
    return Intl.message(
      'You approve $name',
      name: 'jointAccountInviteSentStep3TitleExistingUser',
      desc: '',
      args: [name],
    );
  }

  /// `You approve {name}`
  String jointAccountInviteSentStep3TitleNewUser(String name) {
    return Intl.message(
      'You approve $name',
      name: 'jointAccountInviteSentStep3TitleNewUser',
      desc: '',
      args: [name],
    );
  }

  /// `{currency} account`
  String jointAccountsTabAccountSubTitle(String currency) {
    return Intl.message(
      '$currency account',
      name: 'jointAccountsTabAccountSubTitle',
      desc: '',
      args: [currency],
    );
  }

  /// `Details`
  String get jointAccountsTabDetailsButtonTitle {
    return Intl.message(
      'Details',
      name: 'jointAccountsTabDetailsButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add a family member from the\n‘My family’ tab to get started.`
  String get jointAccountsTabEmptyStateSubtitle {
    return Intl.message(
      'Add a family member from the\n‘My family’ tab to get started.',
      name: 'jointAccountsTabEmptyStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Your family accounts\nwill show up here`
  String get jointAccountsTabEmptyStateTitle {
    return Intl.message(
      'Your family accounts\nwill show up here',
      name: 'jointAccountsTabEmptyStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Manage`
  String get jointAccountsTabManageButtonTitle {
    return Intl.message(
      'Manage',
      name: 'jointAccountsTabManageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Move money`
  String get jointAccountsTabMoveMoneyButtonTitle {
    return Intl.message(
      'Move money',
      name: 'jointAccountsTabMoveMoneyButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family accounts`
  String get jointAccountsTabSectionTitle {
    return Intl.message(
      'Family accounts',
      name: 'jointAccountsTabSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Send`
  String get jointAccountsTabSendButtonTitle {
    return Intl.message(
      'Send',
      name: 'jointAccountsTabSendButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go to hub`
  String get manageInviteApproveInvitationSuccessButtonTitle {
    return Intl.message(
      'Go to hub',
      name: 'manageInviteApproveInvitationSuccessButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{inviteType, select, createFamilyAccount{We are activating your account with {name} and will notify when you can start using it.} createPocket{We are activating pocket for {name} and will notify when you can start using it.} addCoOwnerToPocket{You now share access to {participantName}’s pocket with {name}.} inviteAdultParticipant{You successfully activated card for {name} they now can start using it.} other{ }}`
  String manageInviteApproveInvitationSuccessSubTitle(
      String inviteType, String name, String participantName) {
    return Intl.select(
      inviteType,
      {
        'createFamilyAccount':
            'We are activating your account with $name and will notify when you can start using it.',
        'createPocket':
            'We are activating pocket for $name and will notify when you can start using it.',
        'addCoOwnerToPocket':
            'You now share access to $participantName’s pocket with $name.',
        'inviteAdultParticipant':
            'You successfully activated card for $name they now can start using it.',
        'other': ' ',
      },
      name: 'manageInviteApproveInvitationSuccessSubTitle',
      desc: '',
      args: [inviteType, name, participantName],
    );
  }

  /// `MABROOK!`
  String get manageInviteApproveInvitationSuccessTitle {
    return Intl.message(
      'MABROOK!',
      name: 'manageInviteApproveInvitationSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family lead`
  String get manageScreenCoOwnerTitle {
    return Intl.message(
      'Family lead',
      name: 'manageScreenCoOwnerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Describe your relationship with {name} to make the experience more personal.`
  String memberProfileAddRelationshipCoachmarkBody(String name) {
    return Intl.message(
      'Describe your relationship with $name to make the experience more personal.',
      name: 'memberProfileAddRelationshipCoachmarkBody',
      desc: '',
      args: [name],
    );
  }

  /// `Try now`
  String get memberProfileAddRelationshipCoachmarkButtonTitle {
    return Intl.message(
      'Try now',
      name: 'memberProfileAddRelationshipCoachmarkButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `+ Add relationship`
  String get memberProfileAddRelationshipCoachmarkTitle {
    return Intl.message(
      '+ Add relationship',
      name: 'memberProfileAddRelationshipCoachmarkTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family lead`
  String get memberProfileAddedCoOwnerCardSubTitle {
    return Intl.message(
      'Family lead',
      name: 'memberProfileAddedCoOwnerCardSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Shared pocket access`
  String get memberProfileAddedCoOwnerSectionTitle {
    return Intl.message(
      'Shared pocket access',
      name: 'memberProfileAddedCoOwnerSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `As soon as spending begins, you'll see the full history here.`
  String get memberProfileRecentTransactionsEmptySubTitle {
    return Intl.message(
      'As soon as spending begins, you\'ll see the full history here.',
      name: 'memberProfileRecentTransactionsEmptySubTitle',
      desc: '',
      args: [],
    );
  }

  /// `No transactions yet`
  String get memberProfileRecentTransactionsEmptyTitle {
    return Intl.message(
      'No transactions yet',
      name: 'memberProfileRecentTransactionsEmptyTitle',
      desc: '',
      args: [],
    );
  }

  /// `+ Add Relationship`
  String get memberProfileScreenAddRelationshipButtonTitle {
    return Intl.message(
      '+ Add Relationship',
      name: 'memberProfileScreenAddRelationshipButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `See all`
  String get memberProfileScreenAllTransactionActionTitle {
    return Intl.message(
      'See all',
      name: 'memberProfileScreenAllTransactionActionTitle',
      desc: '',
      args: [],
    );
  }

  /// `You share with {name}`
  String memberProfileScreenSharedProcutsSectionTitle(String name) {
    return Intl.message(
      'You share with $name',
      name: 'memberProfileScreenSharedProcutsSectionTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{role, select, coOwner{Family lead} participant{Family member} adultParticipant{Family member} other{ }}`
  String memberProfileScreenSubTitle(String role) {
    return Intl.select(
      role,
      {
        'coOwner': 'Family lead',
        'participant': 'Family member',
        'adultParticipant': 'Family member',
        'other': ' ',
      },
      name: 'memberProfileScreenSubTitle',
      desc: '',
      args: [role],
    );
  }

  /// `Profile`
  String get memberProfileScreenTitle {
    return Intl.message(
      'Profile',
      name: 'memberProfileScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Recent transactions`
  String get memberProfileScreenTransactionSectionTitle {
    return Intl.message(
      'Recent transactions',
      name: 'memberProfileScreenTransactionSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get memberRelationshipSelectionScreenButtonTitle {
    return Intl.message(
      'Save',
      name: 'memberRelationshipSelectionScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Manage`
  String get memberRelationshipSelectionScreenPageTitle {
    return Intl.message(
      'Manage',
      name: 'memberRelationshipSelectionScreenPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `What’s {name}’s relationship to you?`
  String memberRelationshipSelectionScreenTitle(String name) {
    return Intl.message(
      'What’s $name’s relationship to you?',
      name: 'memberRelationshipSelectionScreenTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Add`
  String get myFamilyAddOwnerButtonTitle {
    return Intl.message(
      'Add',
      name: 'myFamilyAddOwnerButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get myFamilyAddParticipantButtonTitle {
    return Intl.message(
      'Add',
      name: 'myFamilyAddParticipantButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your family saved {amountWithCurrency} this month`
  String myFamilyAnalyticsMoneyMovementBottomSubtitle(
      String amountWithCurrency) {
    return Intl.message(
      'Your family saved $amountWithCurrency this month',
      name: 'myFamilyAnalyticsMoneyMovementBottomSubtitle',
      desc: '',
      args: [amountWithCurrency],
    );
  }

  /// `Here’s how money moved in and out of your family accounts and pockets.`
  String get myFamilyAnalyticsMoneyMovementBottomSubtitleEmptyData {
    return Intl.message(
      'Here’s how money moved in and out of your family accounts and pockets.',
      name: 'myFamilyAnalyticsMoneyMovementBottomSubtitleEmptyData',
      desc: '',
      args: [],
    );
  }

  /// `Money in`
  String get myFamilyAnalyticsMoneyMovementChartItemIn {
    return Intl.message(
      'Money in',
      name: 'myFamilyAnalyticsMoneyMovementChartItemIn',
      desc: '',
      args: [],
    );
  }

  /// `Money out`
  String get myFamilyAnalyticsMoneyMovementChartItemOut {
    return Intl.message(
      'Money out',
      name: 'myFamilyAnalyticsMoneyMovementChartItemOut',
      desc: '',
      args: [],
    );
  }

  /// `Total moved in this month`
  String get myFamilyAnalyticsMoneyMovementChartTitle {
    return Intl.message(
      'Total moved in this month',
      name: 'myFamilyAnalyticsMoneyMovementChartTitle',
      desc: '',
      args: [],
    );
  }

  /// `Money in vs money out`
  String get myFamilyAnalyticsMoneyMovementTitle {
    return Intl.message(
      'Money in vs money out',
      name: 'myFamilyAnalyticsMoneyMovementTitle',
      desc: '',
      args: [],
    );
  }

  /// `{member} is the top spender this month with {biggerPercent} of spends from his card.`
  String myFamilyAnalyticsSpentMembersBottomSubtitle(
      String biggerPercent, String member) {
    return Intl.message(
      '$member is the top spender this month with $biggerPercent of spends from his card.',
      name: 'myFamilyAnalyticsSpentMembersBottomSubtitle',
      desc: '',
      args: [biggerPercent, member],
    );
  }

  /// `Start using your shared accounts and cards to see insights here. We'll show each family member’s trends.`
  String get myFamilyAnalyticsSpentMembersBottomSubtitleEmptyData {
    return Intl.message(
      'Start using your shared accounts and cards to see insights here. We\'ll show each family member’s trends.',
      name: 'myFamilyAnalyticsSpentMembersBottomSubtitleEmptyData',
      desc: '',
      args: [],
    );
  }

  /// `{count, plural, =1{1 transaction} other{{count} transactions}}`
  String myFamilyAnalyticsSpentMembersListItemTransactions(int count) {
    return Intl.plural(
      count,
      one: '1 transaction',
      other: '$count transactions',
      name: 'myFamilyAnalyticsSpentMembersListItemTransactions',
      desc: '',
      args: [count],
    );
  }

  /// `Total spent this month`
  String get myFamilyAnalyticsSpentMembersListTitle {
    return Intl.message(
      'Total spent this month',
      name: 'myFamilyAnalyticsSpentMembersListTitle',
      desc: '',
      args: [],
    );
  }

  /// `Biggest spenders`
  String get myFamilyAnalyticsSpentMembersTitle {
    return Intl.message(
      'Biggest spenders',
      name: 'myFamilyAnalyticsSpentMembersTitle',
      desc: '',
      args: [],
    );
  }

  /// `The Family Wealth section shows the total wealth accumulated by your family, calculated as the combined balance of all family accounts, Pockets, and fixed savings spaces.`
  String get myFamilyAnalyticsTotalWealthBottomSheetSubtitle {
    return Intl.message(
      'The Family Wealth section shows the total wealth accumulated by your family, calculated as the combined balance of all family accounts, Pockets, and fixed savings spaces.',
      name: 'myFamilyAnalyticsTotalWealthBottomSheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Family wealth`
  String get myFamilyAnalyticsTotalWealthBottomSheetTitle {
    return Intl.message(
      'Family wealth',
      name: 'myFamilyAnalyticsTotalWealthBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Total family wealth`
  String get myFamilyAnalyticsTotalWealthTitle {
    return Intl.message(
      'Total family wealth',
      name: 'myFamilyAnalyticsTotalWealthTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add Family leads and rule \nthe budget together`
  String get myFamilyEmptyOwnerStateSubtitle {
    return Intl.message(
      'Add Family leads and rule \nthe budget together',
      name: 'myFamilyEmptyOwnerStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `It’s more fun together`
  String get myFamilyEmptyOwnerStateTitle {
    return Intl.message(
      'It’s more fun together',
      name: 'myFamilyEmptyOwnerStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Set their limits, control spendings and savings.`
  String get myFamilyEmptyParticipantStateSubtitle {
    return Intl.message(
      'Set their limits, control spendings and savings.',
      name: 'myFamilyEmptyParticipantStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `It’s more fun together`
  String get myFamilyEmptyParticipantStateTitle {
    return Intl.message(
      'It’s more fun together',
      name: 'myFamilyEmptyParticipantStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add a family member`
  String get myFamilyEmptyStateAddMemberButtonTitle {
    return Intl.message(
      'Add a family member',
      name: 'myFamilyEmptyStateAddMemberButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `It’s more fun together`
  String get myFamilyEmptyStateSubtitle {
    return Intl.message(
      'It’s more fun together',
      name: 'myFamilyEmptyStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Start building your family`
  String get myFamilyEmptyStateTitle {
    return Intl.message(
      'Start building your family',
      name: 'myFamilyEmptyStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `{count, plural, =1{1 Family account} other{{count} Family accounts}}`
  String myFamilyOwnerAccountCountLabel(num count) {
    return Intl.plural(
      count,
      one: '1 Family account',
      other: '$count Family accounts',
      name: 'myFamilyOwnerAccountCountLabel',
      desc: '',
      args: [count],
    );
  }

  /// `{count, plural, =1{1 Virtual card} other{{count} Virtual cards}}`
  String myFamilyOwnerCardCountLabel(num count) {
    return Intl.plural(
      count,
      one: '1 Virtual card',
      other: '$count Virtual cards',
      name: 'myFamilyOwnerCardCountLabel',
      desc: '',
      args: [count],
    );
  }

  /// `{count, plural, =1{1 Fixed saving space} other{{count} Fixed saving spaces}}`
  String myFamilyOwnerFSSCountLabel(num count) {
    return Intl.plural(
      count,
      one: '1 Fixed saving space',
      other: '$count Fixed saving spaces',
      name: 'myFamilyOwnerFSSCountLabel',
      desc: '',
      args: [count],
    );
  }

  /// `Family leads`
  String get myFamilyOwnersSectionTitle {
    return Intl.message(
      'Family leads',
      name: 'myFamilyOwnersSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `{count, plural, =1{1 Shared pocket} other{{count} Shared pockets}}`
  String myFamilyParticipantAccountCountLabel(num count) {
    return Intl.plural(
      count,
      one: '1 Shared pocket',
      other: '$count Shared pockets',
      name: 'myFamilyParticipantAccountCountLabel',
      desc: '',
      args: [count],
    );
  }

  /// `{count, plural, =1{1 Virtual card} other{{count} Virtual cards}}`
  String myFamilyParticipantCardCountLabel(num count) {
    return Intl.plural(
      count,
      one: '1 Virtual card',
      other: '$count Virtual cards',
      name: 'myFamilyParticipantCardCountLabel',
      desc: '',
      args: [count],
    );
  }

  /// `Family members`
  String get myFamilyParticipantsSectionTitle {
    return Intl.message(
      'Family members',
      name: 'myFamilyParticipantsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `{count, plural, =1{1 Pocket} other{{count} Pockets}}`
  String myFamilySharedPocketCountLabel(num count) {
    return Intl.plural(
      count,
      one: '1 Pocket',
      other: '$count Pockets',
      name: 'myFamilySharedPocketCountLabel',
      desc: '',
      args: [count],
    );
  }

  /// `Activate`
  String get myFamilyTabActivateInvitationButtonTitle {
    return Intl.message(
      'Activate',
      name: 'myFamilyTabActivateInvitationButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} accepted your shared card invite.\nActivate it for her to start using the card.`
  String myFamilyTabAdultParticipantInvitationAcceptedTitle(String name) {
    return Intl.message(
      '$name accepted your shared card invite.\nActivate it for her to start using the card.',
      name: 'myFamilyTabAdultParticipantInvitationAcceptedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{inviteeName} accepted {inviterName} shared card invite.\nActivate it for her to start using the card.`
  String myFamilyTabAdultParticipantInvitationCoOwnerAcceptedTitle(
      String inviteeName, String inviterName) {
    return Intl.message(
      '$inviteeName accepted $inviterName shared card invite.\nActivate it for her to start using the card.',
      name: 'myFamilyTabAdultParticipantInvitationCoOwnerAcceptedTitle',
      desc: '',
      args: [inviteeName, inviterName],
    );
  }

  /// `{inviterName} sent an invite for shared card to {inviteeName}, they’ll need to accept it`
  String myFamilyTabAdultParticipantInvitationCoOwnerCreatedTitle(
      String inviteeName, String inviterName) {
    return Intl.message(
      '$inviterName sent an invite for shared card to $inviteeName, they’ll need to accept it',
      name: 'myFamilyTabAdultParticipantInvitationCoOwnerCreatedTitle',
      desc: '',
      args: [inviteeName, inviterName],
    );
  }

  /// `{inviterName} invite for shared card to {inviteeName} has expired.`
  String myFamilyTabAdultParticipantInvitationCoOwnerExpiredTitle(
      String inviteeName, String inviterName) {
    return Intl.message(
      '$inviterName invite for shared card to $inviteeName has expired.',
      name: 'myFamilyTabAdultParticipantInvitationCoOwnerExpiredTitle',
      desc: '',
      args: [inviteeName, inviterName],
    );
  }

  /// `{inviteeName} accepted {inviterName} invite and now onboarding. You can activate their card once they're ready.`
  String myFamilyTabAdultParticipantInvitationCoOwnerNotOnboardedTitle(
      String inviteeName, String inviterName) {
    return Intl.message(
      '$inviteeName accepted $inviterName invite and now onboarding. You can activate their card once they\'re ready.',
      name: 'myFamilyTabAdultParticipantInvitationCoOwnerNotOnboardedTitle',
      desc: '',
      args: [inviteeName, inviterName],
    );
  }

  /// `{inviteeName} rejected {inviterName} shared card invite.`
  String myFamilyTabAdultParticipantInvitationCoOwnerRejectedTitle(
      String inviteeName, String inviterName) {
    return Intl.message(
      '$inviteeName rejected $inviterName shared card invite.',
      name: 'myFamilyTabAdultParticipantInvitationCoOwnerRejectedTitle',
      desc: '',
      args: [inviteeName, inviterName],
    );
  }

  /// `You sent an invite for shared card to {name}, they’ll need to accept it`
  String myFamilyTabAdultParticipantInvitationCreatedTitle(String name) {
    return Intl.message(
      'You sent an invite for shared card to $name, they’ll need to accept it',
      name: 'myFamilyTabAdultParticipantInvitationCreatedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite for shared card to {name} has expired.`
  String myFamilyTabAdultParticipantInvitationExpiredTitle(String name) {
    return Intl.message(
      'Your invite for shared card to $name has expired.',
      name: 'myFamilyTabAdultParticipantInvitationExpiredTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} accepted your invite and now onboarding. You can activate their card once they ready. `
  String myFamilyTabAdultParticipantInvitationNotOnboardedTitle(String name) {
    return Intl.message(
      '$name accepted your invite and now onboarding. You can activate their card once they ready. ',
      name: 'myFamilyTabAdultParticipantInvitationNotOnboardedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} rejected your shared card invite.`
  String myFamilyTabAdultParticipantInvitationRejectedTitle(String name) {
    return Intl.message(
      '$name rejected your shared card invite.',
      name: 'myFamilyTabAdultParticipantInvitationRejectedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Cancel invite`
  String get myFamilyTabCancelInvitationButtonTitle {
    return Intl.message(
      'Cancel invite',
      name: 'myFamilyTabCancelInvitationButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} accepted your invite! Activate your account now.`
  String myFamilyTabOwnerInvitationAcceptedTitle(String name) {
    return Intl.message(
      '$name accepted your invite! Activate your account now.',
      name: 'myFamilyTabOwnerInvitationAcceptedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `You sent an invite to {name}. \nIt’s pending from their end.`
  String myFamilyTabOwnerInvitationCreatedTitle(String name) {
    return Intl.message(
      'You sent an invite to $name. \nIt’s pending from their end.',
      name: 'myFamilyTabOwnerInvitationCreatedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite to {name} is expired.`
  String myFamilyTabOwnerInvitationExpiredTitle(String name) {
    return Intl.message(
      'Your invite to $name is expired.',
      name: 'myFamilyTabOwnerInvitationExpiredTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} accepted your invite and is now onboarding. You can activate your family account once they are ready.`
  String myFamilyTabOwnerInvitationNotOnboardedTitle(Object name) {
    return Intl.message(
      '$name accepted your invite and is now onboarding. You can activate your family account once they are ready.',
      name: 'myFamilyTabOwnerInvitationNotOnboardedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite was rejected by {name}.`
  String myFamilyTabOwnerInvitationRejectedTitle(String name) {
    return Intl.message(
      'Your invite was rejected by $name.',
      name: 'myFamilyTabOwnerInvitationRejectedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} accepted your invite, you can now activate their pocket`
  String myFamilyTabParticipantInvitationAcceptedTitle(String name) {
    return Intl.message(
      '$name accepted your invite, you can now activate their pocket',
      name: 'myFamilyTabParticipantInvitationAcceptedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `You sent an invite to {name}. \nIt’s pending from their end.`
  String myFamilyTabParticipantInvitationCreatedTitle(String name) {
    return Intl.message(
      'You sent an invite to $name. \nIt’s pending from their end.',
      name: 'myFamilyTabParticipantInvitationCreatedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite to {name} is expired.`
  String myFamilyTabParticipantInvitationExpiredTitle(String name) {
    return Intl.message(
      'Your invite to $name is expired.',
      name: 'myFamilyTabParticipantInvitationExpiredTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} accepted your invite and now onboarding. You can activate pocket once they ready. `
  String myFamilyTabParticipantInvitationNotOnboardedTitle(Object name) {
    return Intl.message(
      '$name accepted your invite and now onboarding. You can activate pocket once they ready. ',
      name: 'myFamilyTabParticipantInvitationNotOnboardedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite was rejected by {name}.`
  String myFamilyTabParticipantInvitationRejectedTitle(String name) {
    return Intl.message(
      'Your invite was rejected by $name.',
      name: 'myFamilyTabParticipantInvitationRejectedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Close`
  String get myFamilyTabRemoveInvitationButtonTitle {
    return Intl.message(
      'Close',
      name: 'myFamilyTabRemoveInvitationButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Resend`
  String get myFamilyTabResendInvitationButtonTitle {
    return Intl.message(
      'Resend',
      name: 'myFamilyTabResendInvitationButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} accepted your shared pocket invite. Activate pocket access to get started.`
  String myFamilyTabSharedPocketInvitationAcceptedTitle(String name) {
    return Intl.message(
      '$name accepted your shared pocket invite. Activate pocket access to get started.',
      name: 'myFamilyTabSharedPocketInvitationAcceptedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Pocket sharing invite sent to {name}.\nJust waiting for them to accept.`
  String myFamilyTabSharedPocketInvitationCreatedTitle(String name) {
    return Intl.message(
      'Pocket sharing invite sent to $name.\nJust waiting for them to accept.',
      name: 'myFamilyTabSharedPocketInvitationCreatedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite for shared pocket to {name} has expired.`
  String myFamilyTabSharedPocketInvitationExpiredTitle(String name) {
    return Intl.message(
      'Your invite for shared pocket to $name has expired.',
      name: 'myFamilyTabSharedPocketInvitationExpiredTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} rejected your shared pocket invite. `
  String myFamilyTabSharedPocketInvitationRejectedTitle(String name) {
    return Intl.message(
      '$name rejected your shared pocket invite. ',
      name: 'myFamilyTabSharedPocketInvitationRejectedTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Continue`
  String get participantInviteRelationshipScreenButtonTitle {
    return Intl.message(
      'Continue',
      name: 'participantInviteRelationshipScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite member`
  String get participantInviteRelationshipScreenPageTitle {
    return Intl.message(
      'Invite member',
      name: 'participantInviteRelationshipScreenPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `Describe your relationship to this added person.`
  String get participantInviteRelationshipScreenSubTitle {
    return Intl.message(
      'Describe your relationship to this added person.',
      name: 'participantInviteRelationshipScreenSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `What is {name}'s relationship to you?`
  String participantInviteRelationshipScreenTitle(String name) {
    return Intl.message(
      'What is $name\'s relationship to you?',
      name: 'participantInviteRelationshipScreenTitle',
      desc: '',
      args: [name],
    );
  }

  /// `From kids and teens to cousins or friends - Family members only see what’s relevant to them, like their own pockets or cards - not your personal account.`
  String get participantInviteRulesCardSubTitle1 {
    return Intl.message(
      'From kids and teens to cousins or friends - Family members only see what’s relevant to them, like their own pockets or cards - not your personal account.',
      name: 'participantInviteRulesCardSubTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Add money from your main Wio AED account to your family member’s pocket`
  String get participantInviteRulesCardSubTitle2 {
    return Intl.message(
      'Add money from your main Wio AED account to your family member’s pocket',
      name: 'participantInviteRulesCardSubTitle2',
      desc: '',
      args: [],
    );
  }

  /// `You stay in control by setting limits, approving access, and tracking activity.\nNote: cash withdrawals are not available on pockets.`
  String get participantInviteRulesCardSubTitle3 {
    return Intl.message(
      'You stay in control by setting limits, approving access, and tracking activity.\nNote: cash withdrawals are not available on pockets.',
      name: 'participantInviteRulesCardSubTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Create Pockets for your family members, which are like mini bank accounts, but not.  You have full control over what they can spend and save from it.`
  String get participantInviteRulesCardSubTitle4 {
    return Intl.message(
      'Create Pockets for your family members, which are like mini bank accounts, but not.  You have full control over what they can spend and save from it.',
      name: 'participantInviteRulesCardSubTitle4',
      desc: '',
      args: [],
    );
  }

  /// `Invite your family circle`
  String get participantInviteRulesCardTitle1 {
    return Intl.message(
      'Invite your family circle',
      name: 'participantInviteRulesCardTitle1',
      desc: '',
      args: [],
    );
  }

  /// `Empower them with a pocket`
  String get participantInviteRulesCardTitle2 {
    return Intl.message(
      'Empower them with a pocket',
      name: 'participantInviteRulesCardTitle2',
      desc: '',
      args: [],
    );
  }

  /// `Track and manage spends from pockets`
  String get participantInviteRulesCardTitle3 {
    return Intl.message(
      'Track and manage spends from pockets',
      name: 'participantInviteRulesCardTitle3',
      desc: '',
      args: [],
    );
  }

  /// `Give allowances and manage their account`
  String get participantInviteRulesCardTitle4 {
    return Intl.message(
      'Give allowances and manage their account',
      name: 'participantInviteRulesCardTitle4',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get participantInviteRulesScreenPrimaryButtonTitle {
    return Intl.message(
      'Continue',
      name: 'participantInviteRulesScreenPrimaryButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Create card later`
  String get participantInviteRulesScreenSecondaryButtonTitle {
    return Intl.message(
      'Create card later',
      name: 'participantInviteRulesScreenSecondaryButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Everything you need to know to get your family members set up`
  String get participantInviteRulesScreenSubTitle {
    return Intl.message(
      'Everything you need to know to get your family members set up',
      name: 'participantInviteRulesScreenSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Adding family members`
  String get participantInviteRulesScreenTitle {
    return Intl.message(
      'Adding family members',
      name: 'participantInviteRulesScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `View Invite`
  String get pendingInvitationBottomSheetButtonTitle {
    return Intl.message(
      'View Invite',
      name: 'pendingInvitationBottomSheetButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ll be able to share control of the family finances, and add other members.`
  String get pendingInvitationBottomSheetSubtitle {
    return Intl.message(
      'You’ll be able to share control of the family finances, and add other members.',
      name: 'pendingInvitationBottomSheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `{inviterName} has invited you to\nopen a family account`
  String pendingInvitationBottomSheetTitle(String inviterName) {
    return Intl.message(
      '$inviterName has invited you to\nopen a family account',
      name: 'pendingInvitationBottomSheetTitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `Alright`
  String get pendingInvitationStatusScreenButtonTitle {
    return Intl.message(
      'Alright',
      name: 'pendingInvitationStatusScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `An invite was sent by {inviterName} to open a family AED account.`
  String pendingInvitationStatusScreenSubtitle(String inviterName) {
    return Intl.message(
      'An invite was sent by $inviterName to open a family AED account.',
      name: 'pendingInvitationStatusScreenSubtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `Family account invite`
  String get pendingInvitationStatusScreenTitle {
    return Intl.message(
      'Family account invite',
      name: 'pendingInvitationStatusScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `{inviterName} sent you an invite for family account`
  String pendingInvitationStatusStep1Subtitle(String inviterName) {
    return Intl.message(
      '$inviterName sent you an invite for family account',
      name: 'pendingInvitationStatusStep1Subtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `Invite received`
  String get pendingInvitationStatusStep1Title {
    return Intl.message(
      'Invite received',
      name: 'pendingInvitationStatusStep1Title',
      desc: '',
      args: [],
    );
  }

  /// `We have notified {inviterName} to review and activate the account`
  String pendingInvitationStatusStep2Subtitle(String inviterName) {
    return Intl.message(
      'We have notified $inviterName to review and activate the account',
      name: 'pendingInvitationStatusStep2Subtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `You have accepted the invite`
  String get pendingInvitationStatusStep2Title {
    return Intl.message(
      'You have accepted the invite',
      name: 'pendingInvitationStatusStep2Title',
      desc: '',
      args: [],
    );
  }

  /// `Once {inviterName} approves your family account will be ready to go!`
  String pendingInvitationStatusStep3Subtitle(Object inviterName) {
    return Intl.message(
      'Once $inviterName approves your family account will be ready to go!',
      name: 'pendingInvitationStatusStep3Subtitle',
      desc: '',
      args: [inviterName],
    );
  }

  /// `{inviterName}'s approval pending`
  String pendingInvitationStatusStep3Title(String inviterName) {
    return Intl.message(
      '$inviterName\'s approval pending',
      name: 'pendingInvitationStatusStep3Title',
      desc: '',
      args: [inviterName],
    );
  }

  /// `Go to hub`
  String get pendingInviterApprovalInvitationStatusScreenButtonTitle {
    return Intl.message(
      'Go to hub',
      name: 'pendingInviterApprovalInvitationStatusScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} will receive a notification in app`
  String pendingInviterApprovalInvitationStatusScreenStep1SubTitle(
      String name) {
    return Intl.message(
      '$name will receive a notification in app',
      name: 'pendingInviterApprovalInvitationStatusScreenStep1SubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Invite sent`
  String get pendingInviterApprovalInvitationStatusScreenStep1Title {
    return Intl.message(
      'Invite sent',
      name: 'pendingInviterApprovalInvitationStatusScreenStep1Title',
      desc: '',
      args: [],
    );
  }

  /// `Once {name} accepts, we’ll send you a notification`
  String pendingInviterApprovalInvitationStatusScreenStep2SubTitle(
      String name) {
    return Intl.message(
      'Once $name accepts, we’ll send you a notification',
      name: 'pendingInviterApprovalInvitationStatusScreenStep2SubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} accepts the invite`
  String pendingInviterApprovalInvitationStatusScreenStep2Title(String name) {
    return Intl.message(
      '$name accepts the invite',
      name: 'pendingInviterApprovalInvitationStatusScreenStep2Title',
      desc: '',
      args: [name],
    );
  }

  /// `Make sure {name}'s details are correct, hit approve, and you’re done!`
  String pendingInviterApprovalInvitationStatusScreenStep3SubTitle(
      String name) {
    return Intl.message(
      'Make sure $name\'s details are correct, hit approve, and you’re done!',
      name: 'pendingInviterApprovalInvitationStatusScreenStep3SubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `You approve {name}`
  String pendingInviterApprovalInvitationStatusScreenStep3Title(String name) {
    return Intl.message(
      'You approve $name',
      name: 'pendingInviterApprovalInvitationStatusScreenStep3Title',
      desc: '',
      args: [name],
    );
  }

  /// `Your invite to {name} has been sent. Here’s what happens next`
  String pendingInviterApprovalInvitationStatusScreenSubTitle(String name) {
    return Intl.message(
      'Your invite to $name has been sent. Here’s what happens next',
      name: 'pendingInviterApprovalInvitationStatusScreenSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Invite sent!`
  String get pendingInviterApprovalInvitationStatusScreenTitle {
    return Intl.message(
      'Invite sent!',
      name: 'pendingInviterApprovalInvitationStatusScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Pocket sharing invite sent to {name}.`
  String pendingPocketInviteCoOwnerCardTitle(String name) {
    return Intl.message(
      'Pocket sharing invite sent to $name.',
      name: 'pendingPocketInviteCoOwnerCardTitle',
      desc: '',
      args: [name],
    );
  }

  /// `View Invite`
  String get pendingSharedPocketInvitationBottomSheetButtonTitle {
    return Intl.message(
      'View Invite',
      name: 'pendingSharedPocketInvitationBottomSheetButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You’ve been invited to help manage a Pocket created for {participantName}, a shared space to oversee spending, set limits, and teach smart money habits together.`
  String pendingSharedPocketInvitationBottomSheetSubtitle(
      String participantName) {
    return Intl.message(
      'You’ve been invited to help manage a Pocket created for $participantName, a shared space to oversee spending, set limits, and teach smart money habits together.',
      name: 'pendingSharedPocketInvitationBottomSheetSubtitle',
      desc: '',
      args: [participantName],
    );
  }

  /// `{inviterName} has invited you to co-manage a Pocket for {participantName}`
  String pendingSharedPocketInvitationBottomSheetTitle(
      String inviterName, String participantName) {
    return Intl.message(
      '$inviterName has invited you to co-manage a Pocket for $participantName',
      name: 'pendingSharedPocketInvitationBottomSheetTitle',
      desc: '',
      args: [inviterName, participantName],
    );
  }

  /// `{currency} account`
  String pocketAccountsTabAccountSubTitle(String currency) {
    return Intl.message(
      '$currency account',
      name: 'pocketAccountsTabAccountSubTitle',
      desc: '',
      args: [currency],
    );
  }

  /// `Invite a family member under 18 to join Wio and get {cashback} in cash.`
  String promoForPocketsBannerBody(String cashback) {
    return Intl.message(
      'Invite a family member under 18 to join Wio and get $cashback in cash.',
      name: 'promoForPocketsBannerBody',
      desc: '',
      args: [cashback],
    );
  }

  /// `Welcome gift from Wio`
  String get promoForPocketsBannerTitle {
    return Intl.message(
      'Welcome gift from Wio',
      name: 'promoForPocketsBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite now`
  String get promoForPocketsBottomSheetButtonTitle {
    return Intl.message(
      'Invite now',
      name: 'promoForPocketsBottomSheetButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite a family member under 18, send them {minDeposit}, and they’ll get {cashback} the following month.\n\nValid for up to 2 pockets.`
  String promoForPocketsBottomSheetDescription(
      String cashback, String minDeposit) {
    return Intl.message(
      'Invite a family member under 18, send them $minDeposit, and they’ll get $cashback the following month.\n\nValid for up to 2 pockets.',
      name: 'promoForPocketsBottomSheetDescription',
      desc: '',
      args: [cashback, minDeposit],
    );
  }

  /// `Create a pocket with a {cashback} bonus`
  String promoForPocketsBottomSheetTitle(String cashback) {
    return Intl.message(
      'Create a pocket with a $cashback bonus',
      name: 'promoForPocketsBottomSheetTitle',
      desc: '',
      args: [cashback],
    );
  }

  /// `Are you sure you want to reject closure request?`
  String get rejectJointAccountClosureConfirmationBottomSheetSubtitle {
    return Intl.message(
      'Are you sure you want to reject closure request?',
      name: 'rejectJointAccountClosureConfirmationBottomSheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Confirmation`
  String get rejectJointAccountClosureConfirmationBottomSheetTitle {
    return Intl.message(
      'Confirmation',
      name: 'rejectJointAccountClosureConfirmationBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I’m sure`
  String get rejectJointAccountClosureConfirmationConfirmButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'rejectJointAccountClosureConfirmationConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get rejectJointAccountClosureConfirmationGoBackButtonTitle {
    return Intl.message(
      'Go back',
      name: 'rejectJointAccountClosureConfirmationGoBackButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Send again`
  String get rejectedInviteSendAgainButtonTitle {
    return Intl.message(
      'Send again',
      name: 'rejectedInviteSendAgainButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Son`
  String get relationshipSelectionScreenAdultParticipantSuggestionChip1 {
    return Intl.message(
      'Son',
      name: 'relationshipSelectionScreenAdultParticipantSuggestionChip1',
      desc: '',
      args: [],
    );
  }

  /// `Daughter`
  String get relationshipSelectionScreenAdultParticipantSuggestionChip2 {
    return Intl.message(
      'Daughter',
      name: 'relationshipSelectionScreenAdultParticipantSuggestionChip2',
      desc: '',
      args: [],
    );
  }

  /// `House helper`
  String get relationshipSelectionScreenAdultParticipantSuggestionChip3 {
    return Intl.message(
      'House helper',
      name: 'relationshipSelectionScreenAdultParticipantSuggestionChip3',
      desc: '',
      args: [],
    );
  }

  /// `Driver`
  String get relationshipSelectionScreenAdultParticipantSuggestionChip4 {
    return Intl.message(
      'Driver',
      name: 'relationshipSelectionScreenAdultParticipantSuggestionChip4',
      desc: '',
      args: [],
    );
  }

  /// `Send invite`
  String get relationshipSelectionScreenButtonTitle {
    return Intl.message(
      'Send invite',
      name: 'relationshipSelectionScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite member`
  String get relationshipSelectionScreenHeaderTitle {
    return Intl.message(
      'Invite member',
      name: 'relationshipSelectionScreenHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Relationship`
  String get relationshipSelectionScreenNameTitle {
    return Intl.message(
      'Relationship',
      name: 'relationshipSelectionScreenNameTitle',
      desc: '',
      args: [],
    );
  }

  /// `Son`
  String get relationshipSelectionScreenParticipantSuggestionChip1 {
    return Intl.message(
      'Son',
      name: 'relationshipSelectionScreenParticipantSuggestionChip1',
      desc: '',
      args: [],
    );
  }

  /// `Daughter`
  String get relationshipSelectionScreenParticipantSuggestionChip2 {
    return Intl.message(
      'Daughter',
      name: 'relationshipSelectionScreenParticipantSuggestionChip2',
      desc: '',
      args: [],
    );
  }

  /// `Describe your relationship to this added person.`
  String get relationshipSelectionScreenSubTitle {
    return Intl.message(
      'Describe your relationship to this added person.',
      name: 'relationshipSelectionScreenSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Wife`
  String get relationshipSelectionScreenSuggestionChip1 {
    return Intl.message(
      'Wife',
      name: 'relationshipSelectionScreenSuggestionChip1',
      desc: '',
      args: [],
    );
  }

  /// `Husband`
  String get relationshipSelectionScreenSuggestionChip2 {
    return Intl.message(
      'Husband',
      name: 'relationshipSelectionScreenSuggestionChip2',
      desc: '',
      args: [],
    );
  }

  /// `Son`
  String get relationshipSelectionScreenSuggestionChip3 {
    return Intl.message(
      'Son',
      name: 'relationshipSelectionScreenSuggestionChip3',
      desc: '',
      args: [],
    );
  }

  /// `Daughter`
  String get relationshipSelectionScreenSuggestionChip4 {
    return Intl.message(
      'Daughter',
      name: 'relationshipSelectionScreenSuggestionChip4',
      desc: '',
      args: [],
    );
  }

  /// `Helper`
  String get relationshipSelectionScreenSuggestionChip5 {
    return Intl.message(
      'Helper',
      name: 'relationshipSelectionScreenSuggestionChip5',
      desc: '',
      args: [],
    );
  }

  /// `What’s {name}’s relationship to you?`
  String relationshipSelectionScreenTitle(String name) {
    return Intl.message(
      'What’s $name’s relationship to you?',
      name: 'relationshipSelectionScreenTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Sorry, We were not able to remove this invite. Please try again later.`
  String get removeInviteFailureToastMessage {
    return Intl.message(
      'Sorry, We were not able to remove this invite. Please try again later.',
      name: 'removeInviteFailureToastMessage',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to {name} has been removed successfully`
  String removeInviteSuccessfulToastMessage(String name) {
    return Intl.message(
      'Your invite to $name has been removed successfully',
      name: 'removeInviteSuccessfulToastMessage',
      desc: '',
      args: [name],
    );
  }

  /// `Yes, I’m sure`
  String get resendAdultParticipantInviteBottomsheetConfirmButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'resendAdultParticipantInviteBottomsheetConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get resendAdultParticipantInviteBottomsheetDismissButtonTitle {
    return Intl.message(
      'Go back',
      name: 'resendAdultParticipantInviteBottomsheetDismissButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We'll send an invite to your member. Once accepted, you have to activate for them to start using card.`
  String get resendAdultParticipantInviteBottomsheetSubTitle {
    return Intl.message(
      'We\'ll send an invite to your member. Once accepted, you have to activate for them to start using card.',
      name: 'resendAdultParticipantInviteBottomsheetSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to share card with {name}?`
  String resendAdultParticipantInviteBottomsheetTitle(String name) {
    return Intl.message(
      'Are you sure you want to share card with $name?',
      name: 'resendAdultParticipantInviteBottomsheetTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Done`
  String get resendInvitationSuccessButtonTitle {
    return Intl.message(
      'Done',
      name: 'resendInvitationSuccessButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to {name} was resent successfully.`
  String resendInvitationSuccessSubtitle(String name) {
    return Intl.message(
      'Your invite to $name was resent successfully.',
      name: 'resendInvitationSuccessSubtitle',
      desc: '',
      args: [name],
    );
  }

  /// `DONE!`
  String get resendInvitationSuccessTitle {
    return Intl.message(
      'DONE!',
      name: 'resendInvitationSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I’m sure`
  String get resendInviteBottomSheetConfirmButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'resendInviteBottomSheetConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{role, select, coOwner{I agree with the Family Account terms & conditions} participant{I agree with the Pockets terms & conditions} other{ }}`
  String resendInviteBottomSheetConfirmationDescription(String role) {
    return Intl.select(
      role,
      {
        'coOwner': 'I agree with the Family Account terms & conditions',
        'participant': 'I agree with the Pockets terms & conditions',
        'other': ' ',
      },
      name: 'resendInviteBottomSheetConfirmationDescription',
      desc: '',
      args: [role],
    );
  }

  /// `{role, select, coOwner{Family Account terms & conditions} participant{Pockets terms & conditions} other{ }}`
  String resendInviteBottomSheetConfirmationHighlightedText(String role) {
    return Intl.select(
      role,
      {
        'coOwner': 'Family Account terms & conditions',
        'participant': 'Pockets terms & conditions',
        'other': ' ',
      },
      name: 'resendInviteBottomSheetConfirmationHighlightedText',
      desc: '',
      args: [role],
    );
  }

  /// `Go back`
  String get resendInviteBottomSheetDismissButtonTitle {
    return Intl.message(
      'Go back',
      name: 'resendInviteBottomSheetDismissButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{role, select, coOwner{Review and agree with the terms & conditions for family accounts.} participant{Review and agree with the terms & conditions for pockets.} other{ }}`
  String resendInviteBottomSheetSubtitle(String role) {
    return Intl.select(
      role,
      {
        'coOwner':
            'Review and agree with the terms & conditions for family accounts.',
        'participant':
            'Review and agree with the terms & conditions for pockets.',
        'other': ' ',
      },
      name: 'resendInviteBottomSheetSubtitle',
      desc: '',
      args: [role],
    );
  }

  /// `Before proceeding...`
  String get resendInviteBottomSheetTitle {
    return Intl.message(
      'Before proceeding...',
      name: 'resendInviteBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Yes, I’m sure`
  String get resendSharedPocketInviteBottomsheetConfirmButtonTitle {
    return Intl.message(
      'Yes, I’m sure',
      name: 'resendSharedPocketInviteBottomsheetConfirmButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get resendSharedPocketInviteBottomsheetDismissButtonTitle {
    return Intl.message(
      'Go back',
      name: 'resendSharedPocketInviteBottomsheetDismissButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We'll send an invite to your family lead. \nOnce accepted you need to approve family member for them to see them and the pocket in their dashboard.`
  String get resendSharedPocketInviteBottomsheetSubtitle {
    return Intl.message(
      'We\'ll send an invite to your family lead. \nOnce accepted you need to approve family member for them to see them and the pocket in their dashboard.',
      name: 'resendSharedPocketInviteBottomsheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to share {participantName}'s pocket with {coOwnerName}?`
  String resendSharedPocketInviteBottomsheetTitle(
      String coOwnerName, String participantName) {
    return Intl.message(
      'Are you sure you want to share $participantName\'s pocket with $coOwnerName?',
      name: 'resendSharedPocketInviteBottomsheetTitle',
      desc: '',
      args: [coOwnerName, participantName],
    );
  }

  /// `You successfully rejected family account closure`
  String get reviewFamilyAccountClosureRejectText {
    return Intl.message(
      'You successfully rejected family account closure',
      name: 'reviewFamilyAccountClosureRejectText',
      desc: '',
      args: [],
    );
  }

  /// `Approve`
  String get reviewFamilyAccountClosureScreenCtaApprove {
    return Intl.message(
      'Approve',
      name: 'reviewFamilyAccountClosureScreenCtaApprove',
      desc: '',
      args: [],
    );
  }

  /// `Deny`
  String get reviewFamilyAccountClosureScreenCtaDeny {
    return Intl.message(
      'Deny',
      name: 'reviewFamilyAccountClosureScreenCtaDeny',
      desc: '',
      args: [],
    );
  }

  /// `Please approve to complete closure.\nIf you don’t want this account to be closed, press deny.`
  String get reviewFamilyAccountClosureScreenDesc {
    return Intl.message(
      'Please approve to complete closure.\nIf you don’t want this account to be closed, press deny.',
      name: 'reviewFamilyAccountClosureScreenDesc',
      desc: '',
      args: [],
    );
  }

  /// `Help with family account closure`
  String get reviewFamilyAccountClosureScreenHelpText {
    return Intl.message(
      'Help with family account closure',
      name: 'reviewFamilyAccountClosureScreenHelpText',
      desc: '',
      args: [],
    );
  }

  /// `Once you confirm, the account will be closed - access and cards will be deactivated.`
  String get reviewFamilyAccountClosureScreenPoint1 {
    return Intl.message(
      'Once you confirm, the account will be closed - access and cards will be deactivated.',
      name: 'reviewFamilyAccountClosureScreenPoint1',
      desc: '',
      args: [],
    );
  }

  /// `Money was moved out by the family lead, account balance remains zero.`
  String get reviewFamilyAccountClosureScreenPoint2 {
    return Intl.message(
      'Money was moved out by the family lead, account balance remains zero.',
      name: 'reviewFamilyAccountClosureScreenPoint2',
      desc: '',
      args: [],
    );
  }

  /// `Your family lead has initiated an account closure request for your {accountNickname}.`
  String reviewFamilyAccountClosureScreenSubtitle(Object accountNickname) {
    return Intl.message(
      'Your family lead has initiated an account closure request for your $accountNickname.',
      name: 'reviewFamilyAccountClosureScreenSubtitle',
      desc: '',
      args: [accountNickname],
    );
  }

  /// `Account closure request`
  String get reviewFamilyAccountClosureScreenTitle {
    return Intl.message(
      'Account closure request',
      name: 'reviewFamilyAccountClosureScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get reviewFamilyAccountClosureSuccessButtonTitle {
    return Intl.message(
      'Done',
      name: 'reviewFamilyAccountClosureSuccessButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Closure is now complete, all cards will be terminated.`
  String get reviewFamilyAccountClosureSuccessDesc {
    return Intl.message(
      'Closure is now complete, all cards will be terminated.',
      name: 'reviewFamilyAccountClosureSuccessDesc',
      desc: '',
      args: [],
    );
  }

  /// `Your family account will now be closed. \n\nWe will notify you with updates.`
  String get reviewFamilyAccountClosureSuccessSubtitle {
    return Intl.message(
      'Your family account will now be closed. \n\nWe will notify you with updates.',
      name: 'reviewFamilyAccountClosureSuccessSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `IT'S DONE`
  String get reviewFamilyAccountClosureSuccessTitle {
    return Intl.message(
      'IT\'S DONE',
      name: 'reviewFamilyAccountClosureSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `You successfully rejected pocket closure`
  String get reviewSharedPocketClosureRejectText {
    return Intl.message(
      'You successfully rejected pocket closure',
      name: 'reviewSharedPocketClosureRejectText',
      desc: '',
      args: [],
    );
  }

  /// `Confirm pocket closure`
  String get reviewSharedPocketClosureScreenCtaConfirm {
    return Intl.message(
      'Confirm pocket closure',
      name: 'reviewSharedPocketClosureScreenCtaConfirm',
      desc: '',
      args: [],
    );
  }

  /// `Reject closure request`
  String get reviewSharedPocketClosureScreenCtaReject {
    return Intl.message(
      'Reject closure request',
      name: 'reviewSharedPocketClosureScreenCtaReject',
      desc: '',
      args: [],
    );
  }

  /// `Pocket closure request has been initiated`
  String get reviewSharedPocketClosureScreenFallbackTitle {
    return Intl.message(
      'Pocket closure request has been initiated',
      name: 'reviewSharedPocketClosureScreenFallbackTitle',
      desc: '',
      args: [],
    );
  }

  /// `Once you confirm, the pocket will be permanently closed.`
  String get reviewSharedPocketClosureScreenPoint1 {
    return Intl.message(
      'Once you confirm, the pocket will be permanently closed.',
      name: 'reviewSharedPocketClosureScreenPoint1',
      desc: '',
      args: [],
    );
  }

  /// `All access and cards will be deactivated.`
  String get reviewSharedPocketClosureScreenPoint2 {
    return Intl.message(
      'All access and cards will be deactivated.',
      name: 'reviewSharedPocketClosureScreenPoint2',
      desc: '',
      args: [],
    );
  }

  /// `{coOwner} wants to close {accountNickname}`
  String reviewSharedPocketClosureScreenTitle(
      String accountNickname, String coOwner) {
    return Intl.message(
      '$coOwner wants to close $accountNickname',
      name: 'reviewSharedPocketClosureScreenTitle',
      desc: '',
      args: [accountNickname, coOwner],
    );
  }

  /// `Thanks`
  String get reviewSharedPocketClosureSuccessButtonTitle {
    return Intl.message(
      'Thanks',
      name: 'reviewSharedPocketClosureSuccessButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `All access and cards have been deactivated.`
  String get reviewSharedPocketClosureSuccessDesc {
    return Intl.message(
      'All access and cards have been deactivated.',
      name: 'reviewSharedPocketClosureSuccessDesc',
      desc: '',
      args: [],
    );
  }

  /// `Your pocket for {pocketName} will be closed. \n\nWe will notify you with updates.`
  String reviewSharedPocketClosureSuccessSubtitle(String pocketName) {
    return Intl.message(
      'Your pocket for $pocketName will be closed. \n\nWe will notify you with updates.',
      name: 'reviewSharedPocketClosureSuccessSubtitle',
      desc: '',
      args: [pocketName],
    );
  }

  /// `IT'S DONE`
  String get reviewSharedPocketClosureSuccessTitle {
    return Intl.message(
      'IT\'S DONE',
      name: 'reviewSharedPocketClosureSuccessTitle',
      desc: '',
      args: [],
    );
  }

  /// `Great for partners, spouses, or trusted adults (18+)`
  String get roleSelectionScreenCoOwnerCardExtraLine {
    return Intl.message(
      'Great for partners, spouses, or trusted adults (18+)',
      name: 'roleSelectionScreenCoOwnerCardExtraLine',
      desc: '',
      args: [],
    );
  }

  /// `They hold equal rights to own and manage the family money with you.`
  String get roleSelectionScreenCoOwnerCardSubTitle {
    return Intl.message(
      'They hold equal rights to own and manage the family money with you.',
      name: 'roleSelectionScreenCoOwnerCardSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family lead`
  String get roleSelectionScreenCoOwnerCardTitle {
    return Intl.message(
      'Family lead',
      name: 'roleSelectionScreenCoOwnerCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Invite member`
  String get roleSelectionScreenHeaderTitle {
    return Intl.message(
      'Invite member',
      name: 'roleSelectionScreenHeaderTitle',
      desc: '',
      args: [],
    );
  }

  /// `Great for kids, teens, trusted adults`
  String get roleSelectionScreenParticipantCardExtraLine {
    return Intl.message(
      'Great for kids, teens, trusted adults',
      name: 'roleSelectionScreenParticipantCardExtraLine',
      desc: '',
      args: [],
    );
  }

  /// `Kids and teens get a money space (pocket) and card, while adults (18+) use a shared card.`
  String get roleSelectionScreenParticipantCardSubTitle {
    return Intl.message(
      'Kids and teens get a money space (pocket) and card, while adults (18+) use a shared card.',
      name: 'roleSelectionScreenParticipantCardSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family member`
  String get roleSelectionScreenParticipantCardTitle {
    return Intl.message(
      'Family member',
      name: 'roleSelectionScreenParticipantCardTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose who runs the show,\nand who gets to play a part in it.`
  String get roleSelectionScreenSubTitle {
    return Intl.message(
      'Choose who runs the show,\nand who gets to play a part in it.',
      name: 'roleSelectionScreenSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Who will you invite to your Wio Family?`
  String get roleSelectionScreenTitle {
    return Intl.message(
      'Who will you invite to your Wio Family?',
      name: 'roleSelectionScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family roles explained`
  String get roleSelectionWalktroughCta {
    return Intl.message(
      'Family roles explained',
      name: 'roleSelectionWalktroughCta',
      desc: '',
      args: [],
    );
  }

  /// `Things to know`
  String get ruleScreenJointAccountSectionTitle {
    return Intl.message(
      'Things to know',
      name: 'ruleScreenJointAccountSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Learn what each role can do in Wio Family`
  String get rulesScreenFAQButtonTitle {
    return Intl.message(
      'Learn what each role can do in Wio Family',
      name: 'rulesScreenFAQButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Got it`
  String get rulesScreenJointAccountAlternativeButtonTitle {
    return Intl.message(
      'Got it',
      name: 'rulesScreenJointAccountAlternativeButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get rulesScreenJointAccountButtonTitle {
    return Intl.message(
      'Continue',
      name: 'rulesScreenJointAccountButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Learn more about family accounts`
  String get rulesScreenJointAccountLearnButtonTitle {
    return Intl.message(
      'Learn more about family accounts',
      name: 'rulesScreenJointAccountLearnButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `As a family lead, you’ll set up a family account with someone you trust. You’ll both hold equal rights over the money in this account.`
  String get rulesScreenJointAccountSubTitle {
    return Intl.message(
      'As a family lead, you’ll set up a family account with someone you trust. You’ll both hold equal rights over the money in this account.',
      name: 'rulesScreenJointAccountSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Adding family leads`
  String get rulesScreenJointAccountTitle {
    return Intl.message(
      'Adding family leads',
      name: 'rulesScreenJointAccountTitle',
      desc: '',
      args: [],
    );
  }

  /// `Watch how it works`
  String get rulesScreenWalkthroughBannerCta {
    return Intl.message(
      'Watch how it works',
      name: 'rulesScreenWalkthroughBannerCta',
      desc: '',
      args: [],
    );
  }

  /// `2 mins`
  String get rulesScreenWalkthroughBannerDurationTitle {
    return Intl.message(
      '2 mins',
      name: 'rulesScreenWalkthroughBannerDurationTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family leads &\nfamily members\nexplained`
  String get rulesScreenWalkthroughBannerTitle {
    return Intl.message(
      'Family leads &\nfamily members\nexplained',
      name: 'rulesScreenWalkthroughBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get selectAvatarImagePageCategoryAll {
    return Intl.message(
      'All',
      name: 'selectAvatarImagePageCategoryAll',
      desc: '',
      args: [],
    );
  }

  /// `Next`
  String get selectAvatarImagePageNextButtonTitle {
    return Intl.message(
      'Next',
      name: 'selectAvatarImagePageNextButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Choose from Wioji’s`
  String get selectAvatarImagePageTitle {
    return Intl.message(
      'Choose from Wioji’s',
      name: 'selectAvatarImagePageTitle',
      desc: '',
      args: [],
    );
  }

  /// `New shared card`
  String get selectCardHolderScreenAppBarTitle {
    return Intl.message(
      'New shared card',
      name: 'selectCardHolderScreenAppBarTitle',
      desc: '',
      args: [],
    );
  }

  /// `Someone else (18+)`
  String get selectCardHolderScreenInviteAdultParticipantTitle {
    return Intl.message(
      'Someone else (18+)',
      name: 'selectCardHolderScreenInviteAdultParticipantTitle',
      desc: '',
      args: [],
    );
  }

  /// `Me`
  String get selectCardHolderScreenMemberBoxTitleCurrentAccount {
    return Intl.message(
      'Me',
      name: 'selectCardHolderScreenMemberBoxTitleCurrentAccount',
      desc: '',
      args: [],
    );
  }

  /// `{role, select, coOwner{Family lead} participant{Family member} adultParticipant{Family member} other{ }}`
  String selectCardHolderScreenMemberBoxTrailing(String role) {
    return Intl.select(
      role,
      {
        'coOwner': 'Family lead',
        'participant': 'Family member',
        'adultParticipant': 'Family member',
        'other': ' ',
      },
      name: 'selectCardHolderScreenMemberBoxTrailing',
      desc: '',
      args: [role],
    );
  }

  /// `Who’s this card for?`
  String get selectCardHolderScreenTitle {
    return Intl.message(
      'Who’s this card for?',
      name: 'selectCardHolderScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `Share only a card with them. You set the limits and stay in full control. You can stop the card anytime.`
  String get selectParticipantTypeBottomSheetAbove18ButtonSubtitle {
    return Intl.message(
      'Share only a card with them. You set the limits and stay in full control. You can stop the card anytime.',
      name: 'selectParticipantTypeBottomSheetAbove18ButtonSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Over 18`
  String get selectParticipantTypeBottomSheetAbove18ButtonTitle {
    return Intl.message(
      'Over 18',
      name: 'selectParticipantTypeBottomSheetAbove18ButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `They’ll get their own money space and card, with you in control of spending.`
  String get selectParticipantTypeBottomSheetBelow18ButtonSubtitle {
    return Intl.message(
      'They’ll get their own money space and card, with you in control of spending.',
      name: 'selectParticipantTypeBottomSheetBelow18ButtonSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Under 18`
  String get selectParticipantTypeBottomSheetBelow18ButtonTitle {
    return Intl.message(
      'Under 18',
      name: 'selectParticipantTypeBottomSheetBelow18ButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `We’ll tailor their access based on their age.`
  String get selectParticipantTypeBottomSheetSubTitle {
    return Intl.message(
      'We’ll tailor their access based on their age.',
      name: 'selectParticipantTypeBottomSheetSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `How old is the person you’re adding?`
  String get selectParticipantTypeBottomSheetTitle {
    return Intl.message(
      'How old is the person you’re adding?',
      name: 'selectParticipantTypeBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `You & {name}`
  String sharedAccountsTabFamilyAccountCardTitle(String name) {
    return Intl.message(
      'You & $name',
      name: 'sharedAccountsTabFamilyAccountCardTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Pocket`
  String get sharedAccountsTabPocketCardSubTitle {
    return Intl.message(
      'Pocket',
      name: 'sharedAccountsTabPocketCardSubTitle',
      desc: '',
      args: [],
    );
  }

  /// `Close`
  String get sharedCardHolderBottomSheetActiveCardParticipantButton {
    return Intl.message(
      'Close',
      name: 'sharedCardHolderBottomSheetActiveCardParticipantButton',
      desc: '',
      args: [],
    );
  }

  /// `You can only share one card with them, or replace it in case of termination of a previous card.`
  String get sharedCardHolderBottomSheetActiveCardParticipantSubtitle {
    return Intl.message(
      'You can only share one card with them, or replace it in case of termination of a previous card.',
      name: 'sharedCardHolderBottomSheetActiveCardParticipantSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `This family member already\nhas a card`
  String get sharedCardHolderBottomSheetActiveCardParticipantTitle {
    return Intl.message(
      'This family member already\nhas a card',
      name: 'sharedCardHolderBottomSheetActiveCardParticipantTitle',
      desc: '',
      args: [],
    );
  }

  /// `Okay`
  String get sharedCardHolderBottomSheetCurrentAccountPrimaryButton {
    return Intl.message(
      'Okay',
      name: 'sharedCardHolderBottomSheetCurrentAccountPrimaryButton',
      desc: '',
      args: [],
    );
  }

  /// `Link to personal account`
  String get sharedCardHolderBottomSheetCurrentAccountSecondaryButton {
    return Intl.message(
      'Link to personal account',
      name: 'sharedCardHolderBottomSheetCurrentAccountSecondaryButton',
      desc: '',
      args: [],
    );
  }

  /// `Prefer to create a virtual card linked to your personal account instead? Head to the Cards section to set it up.`
  String get sharedCardHolderBottomSheetCurrentAccountSubtitle {
    return Intl.message(
      'Prefer to create a virtual card linked to your personal account instead? Head to the Cards section to set it up.',
      name: 'sharedCardHolderBottomSheetCurrentAccountSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `This card will be linked  to your family account`
  String get sharedCardHolderBottomSheetCurrentAccountTitle {
    return Intl.message(
      'This card will be linked  to your family account',
      name: 'sharedCardHolderBottomSheetCurrentAccountTitle',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get sharedCardHolderBottomSheetTerminatedCardParticipantButton {
    return Intl.message(
      'Continue',
      name: 'sharedCardHolderBottomSheetTerminatedCardParticipantButton',
      desc: '',
      args: [],
    );
  }

  /// `This member has their card terminated, \nset up new card for them to be able to spend.`
  String get sharedCardHolderBottomSheetTerminatedCardParticipantSubtitle {
    return Intl.message(
      'This member has their card terminated, \nset up new card for them to be able to spend.',
      name: 'sharedCardHolderBottomSheetTerminatedCardParticipantSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `This card will be linked to the family member’s pocket`
  String get sharedCardHolderBottomSheetTerminatedCardParticipantTitle {
    return Intl.message(
      'This card will be linked to the family member’s pocket',
      name: 'sharedCardHolderBottomSheetTerminatedCardParticipantTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add a family member from the\n‘My family’ tab to get started.`
  String get sharedCardTabNoFamilyEmptyStateSubtitle {
    return Intl.message(
      'Add a family member from the\n‘My family’ tab to get started.',
      name: 'sharedCardTabNoFamilyEmptyStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Your shared cards\nwill show up here`
  String get sharedCardTabNoFamilyEmptyStateTitle {
    return Intl.message(
      'Your shared cards\nwill show up here',
      name: 'sharedCardTabNoFamilyEmptyStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `Add`
  String get sharedCardsTabAddButtonTitle {
    return Intl.message(
      'Add',
      name: 'sharedCardsTabAddButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family members cards (18+)`
  String get sharedCardsTabAdultParticipantCardsSectionTitle {
    return Intl.message(
      'Family members cards (18+)',
      name: 'sharedCardsTabAdultParticipantCardsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Create a new card`
  String get sharedCardsTabButtonTitle {
    return Intl.message(
      'Create a new card',
      name: 'sharedCardsTabButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Create a new card`
  String get sharedCardsTabEmptyStateButtonTitle {
    return Intl.message(
      'Create a new card',
      name: 'sharedCardsTabEmptyStateButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Give a card to someone you trust and keep control with ease.`
  String get sharedCardsTabEmptyStateSubtitle {
    return Intl.message(
      'Give a card to someone you trust and keep control with ease.',
      name: 'sharedCardsTabEmptyStateSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Share the power to pay`
  String get sharedCardsTabEmptyStateTitle {
    return Intl.message(
      'Share the power to pay',
      name: 'sharedCardsTabEmptyStateTitle',
      desc: '',
      args: [],
    );
  }

  /// `{cardOwnerType, select, owner {Co-owners cards created manually for your other family leads - you can create it once your family account co-owner is activated.} currentOwner {Your personal card created manually - you can create it once your family account is activated.} adultParticipant {Family members card is created automatically the moment you invite them. It's active once you activate the member and top its balance.} participant {Family members card is created automatically the moment you invite them. It's active once you activate the member and top its balance.} other {}}`
  String sharedCardsTabInfoBottomSheetHowCreatedDesc(String cardOwnerType) {
    return Intl.select(
      cardOwnerType,
      {
        'owner':
            'Co-owners cards created manually for your other family leads - you can create it once your family account co-owner is activated.',
        'currentOwner':
            'Your personal card created manually - you can create it once your family account is activated.',
        'adultParticipant':
            'Family members card is created automatically the moment you invite them. It\'s active once you activate the member and top its balance.',
        'participant':
            'Family members card is created automatically the moment you invite them. It\'s active once you activate the member and top its balance.',
        'other': '',
      },
      name: 'sharedCardsTabInfoBottomSheetHowCreatedDesc',
      desc: '',
      args: [cardOwnerType],
    );
  }

  /// `How card is created?`
  String get sharedCardsTabInfoBottomSheetHowCreatedTitle {
    return Intl.message(
      'How card is created?',
      name: 'sharedCardsTabInfoBottomSheetHowCreatedTitle',
      desc: '',
      args: [],
    );
  }

  /// `{cardOwnerType, select, owner {You can allocate card to any of your family leads, card using family account money, you have full visibility on transactions made from this card.} currentOwner {Your card uses family account money, your co-owners have full visibility on transactions made from this card.} adultParticipant {Senior family member card can spend from your family account, or your personal account where you can choose between debit and credit card.} participant {You can top up junior family member card from your family account or personal wio account.} other {}}`
  String sharedCardsTabInfoBottomSheetHowWorksDesc(String cardOwnerType) {
    return Intl.select(
      cardOwnerType,
      {
        'owner':
            'You can allocate card to any of your family leads, card using family account money, you have full visibility on transactions made from this card.',
        'currentOwner':
            'Your card uses family account money, your co-owners have full visibility on transactions made from this card.',
        'adultParticipant':
            'Senior family member card can spend from your family account, or your personal account where you can choose between debit and credit card.',
        'participant':
            'You can top up junior family member card from your family account or personal wio account.',
        'other': '',
      },
      name: 'sharedCardsTabInfoBottomSheetHowWorksDesc',
      desc: '',
      args: [cardOwnerType],
    );
  }

  /// `How does it work?`
  String get sharedCardsTabInfoBottomSheetHowWorksTitle {
    return Intl.message(
      'How does it work?',
      name: 'sharedCardsTabInfoBottomSheetHowWorksTitle',
      desc: '',
      args: [],
    );
  }

  /// `{cardOwnerType, select, owner {Card created for family lead} currentOwner {Card created for myself} adultParticipant {Family members cards 18+} participant {Family members cards 18-} other {}}`
  String sharedCardsTabInfoBottomSheetTitle(String cardOwnerType) {
    return Intl.select(
      cardOwnerType,
      {
        'owner': 'Card created for family lead',
        'currentOwner': 'Card created for myself',
        'adultParticipant': 'Family members cards 18+',
        'participant': 'Family members cards 18-',
        'other': '',
      },
      name: 'sharedCardsTabInfoBottomSheetTitle',
      desc: '',
      args: [cardOwnerType],
    );
  }

  /// `Created for myself`
  String get sharedCardsTabMyCardsSectionTitle {
    return Intl.message(
      'Created for myself',
      name: 'sharedCardsTabMyCardsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Created for family leads`
  String get sharedCardsTabOwnerCardsSectionTitle {
    return Intl.message(
      'Created for family leads',
      name: 'sharedCardsTabOwnerCardsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Family members cards (18-)`
  String get sharedCardsTabParticipantCardsSectionTitle {
    return Intl.message(
      'Family members cards (18-)',
      name: 'sharedCardsTabParticipantCardsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `SHARED`
  String get sharedCardsTabSharedLabel {
    return Intl.message(
      'SHARED',
      name: 'sharedCardsTabSharedLabel',
      desc: '',
      args: [],
    );
  }

  /// `You have no cards associated with you yet. \n\nTap “create new card” and start new way of family budgeting`
  String get sharedMyCardsEmptyStateText {
    return Intl.message(
      'You have no cards associated with you yet. \n\nTap “create new card” and start new way of family budgeting',
      name: 'sharedMyCardsEmptyStateText',
      desc: '',
      args: [],
    );
  }

  /// `You have no cards created for your family leads. \n\nTap “create new card” and start new way of family budgeting`
  String get sharedOwnerCardsEmptytStateText {
    return Intl.message(
      'You have no cards created for your family leads. \n\nTap “create new card” and start new way of family budgeting',
      name: 'sharedOwnerCardsEmptytStateText',
      desc: '',
      args: [],
    );
  }

  /// `You have no family member cards yet. \n\nCard will be created automatically when you create a new family member.`
  String get sharedParticipantsCardsEmptyStateText {
    return Intl.message(
      'You have no family member cards yet. \n\nCard will be created automatically when you create a new family member.',
      name: 'sharedParticipantsCardsEmptyStateText',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get sharedPocketInvitationAcceptedScreenButtonTitle {
    return Intl.message(
      'Go back',
      name: 'sharedPocketInvitationAcceptedScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `You have accepted invite to share access to this pocket.\n\nWe’ll notify you once it’s activated.`
  String get sharedPocketInvitationAcceptedScreenSubtitle {
    return Intl.message(
      'You have accepted invite to share access to this pocket.\n\nWe’ll notify you once it’s activated.',
      name: 'sharedPocketInvitationAcceptedScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `MABROOK!`
  String get sharedPocketInvitationAcceptedScreenTitle {
    return Intl.message(
      'MABROOK!',
      name: 'sharedPocketInvitationAcceptedScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `You successfully rejected shared pocket invite`
  String get sharedPocketInvitationRejectedText {
    return Intl.message(
      'You successfully rejected shared pocket invite',
      name: 'sharedPocketInvitationRejectedText',
      desc: '',
      args: [],
    );
  }

  /// `Go to hub`
  String get sharedPocketInviteSentStatusPageButtonTitle {
    return Intl.message(
      'Go to hub',
      name: 'sharedPocketInviteSentStatusPageButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Your invite to share pocket access to {name} has been sent.`
  String sharedPocketInviteSentStatusPageSubTitle(String name) {
    return Intl.message(
      'Your invite to share pocket access to $name has been sent.',
      name: 'sharedPocketInviteSentStatusPageSubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Invite sent!`
  String get sharedPocketInviteSentStatusPageTitle {
    return Intl.message(
      'Invite sent!',
      name: 'sharedPocketInviteSentStatusPageTitle',
      desc: '',
      args: [],
    );
  }

  /// `{name} will receive a notification in app`
  String sharedPocketInviteSentStatusStep1SubTitle(String name) {
    return Intl.message(
      '$name will receive a notification in app',
      name: 'sharedPocketInviteSentStatusStep1SubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `Invite sent`
  String get sharedPocketInviteSentStatusStep1Title {
    return Intl.message(
      'Invite sent',
      name: 'sharedPocketInviteSentStatusStep1Title',
      desc: '',
      args: [],
    );
  }

  /// `Once {name} accepts, we’ll send you a notification`
  String sharedPocketInviteSentStatusStep2SubTitle(String name) {
    return Intl.message(
      'Once $name accepts, we’ll send you a notification',
      name: 'sharedPocketInviteSentStatusStep2SubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `{name} accepts the invite`
  String sharedPocketInviteSentStatusStep2Title(String name) {
    return Intl.message(
      '$name accepts the invite',
      name: 'sharedPocketInviteSentStatusStep2Title',
      desc: '',
      args: [name],
    );
  }

  /// `Make sure {name}’s details are correct, hit approve, and you’re done!`
  String sharedPocketInviteSentStatusStep3SubTitle(String name) {
    return Intl.message(
      'Make sure $name’s details are correct, hit approve, and you’re done!',
      name: 'sharedPocketInviteSentStatusStep3SubTitle',
      desc: '',
      args: [name],
    );
  }

  /// `You approve sharing pocket`
  String get sharedPocketInviteSentStatusStep3Title {
    return Intl.message(
      'You approve sharing pocket',
      name: 'sharedPocketInviteSentStatusStep3Title',
      desc: '',
      args: [],
    );
  }

  /// `Shared accounts`
  String get sharedProductsListAccountsSectionTitle {
    return Intl.message(
      'Shared accounts',
      name: 'sharedProductsListAccountsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Shared cards`
  String get sharedProductsListCardsSectionTitle {
    return Intl.message(
      'Shared cards',
      name: 'sharedProductsListCardsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Shared saving spaces`
  String get sharedProductsListFixedSavingSpacesSectionTitle {
    return Intl.message(
      'Shared saving spaces',
      name: 'sharedProductsListFixedSavingSpacesSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Shared pockets`
  String get sharedProductsListPocketsSectionTitle {
    return Intl.message(
      'Shared pockets',
      name: 'sharedProductsListPocketsSectionTitle',
      desc: '',
      args: [],
    );
  }

  /// `Limit will reset tomorrow`
  String get spendingLimitFrequencyInfoDaily {
    return Intl.message(
      'Limit will reset tomorrow',
      name: 'spendingLimitFrequencyInfoDaily',
      desc: '',
      args: [],
    );
  }

  /// `Limit will reset on the 1st of {month}.`
  String spendingLimitFrequencyInfoMonthly(String month) {
    return Intl.message(
      'Limit will reset on the 1st of $month.',
      name: 'spendingLimitFrequencyInfoMonthly',
      desc: '',
      args: [month],
    );
  }

  /// `Limit will not reset, but you can edit it`
  String get spendingLimitFrequencyInfoOverall {
    return Intl.message(
      'Limit will not reset, but you can edit it',
      name: 'spendingLimitFrequencyInfoOverall',
      desc: '',
      args: [],
    );
  }

  /// `Limit will reset next Monday`
  String get spendingLimitFrequencyInfoWeekly {
    return Intl.message(
      'Limit will reset next Monday',
      name: 'spendingLimitFrequencyInfoWeekly',
      desc: '',
      args: [],
    );
  }

  /// `{frequency, select, daily{{limit} / day} weekly{{limit} / week} monthly{{limit} / month} other{{limit} / overall}}`
  String spendingLimitItemSubTitle(String frequency, String limit) {
    return Intl.select(
      frequency,
      {
        'daily': '$limit / day',
        'weekly': '$limit / week',
        'monthly': '$limit / month',
        'other': '$limit / overall',
      },
      name: 'spendingLimitItemSubTitle',
      desc: '',
      args: [frequency, limit],
    );
  }

  /// `Card spending limit`
  String get spendingLimitItemTitle {
    return Intl.message(
      'Card spending limit',
      name: 'spendingLimitItemTitle',
      desc: '',
      args: [],
    );
  }

  /// `{status, select, cancelledByInviter{We were unable to cancel your invite request at the moment. Please try again later.} rejectedByInviter{We were unable to reject your invite request at the moment. Please try again later.} confirmedByInviter{We were unable to activate this card at the moment. Please try again later.} expired{We were unable to resend your invite request at the moment. Please try again later.} other{ }}`
  String updateAdultParticipantInvitationErrorSubTitle(String status) {
    return Intl.select(
      status,
      {
        'cancelledByInviter':
            'We were unable to cancel your invite request at the moment. Please try again later.',
        'rejectedByInviter':
            'We were unable to reject your invite request at the moment. Please try again later.',
        'confirmedByInviter':
            'We were unable to activate this card at the moment. Please try again later.',
        'expired':
            'We were unable to resend your invite request at the moment. Please try again later.',
        'other': ' ',
      },
      name: 'updateAdultParticipantInvitationErrorSubTitle',
      desc: '',
      args: [status],
    );
  }

  /// `Alright`
  String get updateInvitationErrorButtonTitle {
    return Intl.message(
      'Alright',
      name: 'updateInvitationErrorButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `{status, select, cancelledByInviter{We were unable to cancel your invite request at the moment. Please try again later.} rejectedByInviter{We were unable to reject your invite request at the moment. Please try again later.} confirmedByInviter{We were unable to activate your account at the moment. Please try again later.} expired{We were unable to resend your invite request at the moment. Please try again later.} other{Other}}`
  String updateInvitationErrorSubTitle(String status) {
    return Intl.select(
      status,
      {
        'cancelledByInviter':
            'We were unable to cancel your invite request at the moment. Please try again later.',
        'rejectedByInviter':
            'We were unable to reject your invite request at the moment. Please try again later.',
        'confirmedByInviter':
            'We were unable to activate your account at the moment. Please try again later.',
        'expired':
            'We were unable to resend your invite request at the moment. Please try again later.',
        'other': 'Other',
      },
      name: 'updateInvitationErrorSubTitle',
      desc: '',
      args: [status],
    );
  }

  /// `Uh Oh!`
  String get updateInvitationErrorTitle {
    return Intl.message(
      'Uh Oh!',
      name: 'updateInvitationErrorTitle',
      desc: '',
      args: [],
    );
  }

  /// `Account relationship has been updated successfully.`
  String get updateRelationshipSuccessMsg {
    return Intl.message(
      'Account relationship has been updated successfully.',
      name: 'updateRelationshipSuccessMsg',
      desc: '',
      args: [],
    );
  }

  /// `Watch how it works`
  String get wioFamilyExplainedWalkthroughBannerCta {
    return Intl.message(
      'Watch how it works',
      name: 'wioFamilyExplainedWalkthroughBannerCta',
      desc: '',
      args: [],
    );
  }

  /// `Wio Family\nexplained`
  String get wioFamilyExplainedWalkthroughBannerTitle {
    return Intl.message(
      'Wio Family\nexplained',
      name: 'wioFamilyExplainedWalkthroughBannerTitle',
      desc: '',
      args: [],
    );
  }

  /// `Watch how it works`
  String get wioPocketExplainedWalkthroughBannerCta {
    return Intl.message(
      'Watch how it works',
      name: 'wioPocketExplainedWalkthroughBannerCta',
      desc: '',
      args: [],
    );
  }

  /// `Your Pocket\nexplained`
  String get wioPocketExplainedWalkthroughBannerTitle {
    return Intl.message(
      'Your Pocket\nexplained',
      name: 'wioPocketExplainedWalkthroughBannerTitle',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<FamilyBankingLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) =>
      FamilyBankingLocalizations.supportedLocales.any((supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode);

  @override
  Future<FamilyBankingLocalizations> load(Locale locale) =>
      FamilyBankingLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
