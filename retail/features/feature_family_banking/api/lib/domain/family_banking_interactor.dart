import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

abstract interface class FamilyBankingInteractor implements Clearable {
  /// Returns my family data (members and invitees).
  Future<Family> getMyFamily();

  /// Emits my family data whenever it is updated.
  ///
  /// Setting [forceRefresh] to `true` will force-fetch data upon subscription.
  Stream<Data<Family>> observeMyFamily({bool forceRefresh = false});

  /// Refreshes my family data.
  Future<void> reloadMyFamily();

  /// Set new relationship for a member with [memberId].
  Future<void> updateRelationship({
    required String memberId,
    required String relationship,
  });

  /// To get the walkthrough video url for [type]
  Future<Uri> getWalkthroughVideoUrl({required WalkthroughVideoType type});

  /// To get the FamilyTutorial
  Future<FamilyTutorial> getFamilyTutorial({required FamilyTutorialType type});

  /// Fetches available avatars.
  ///
  /// If [category] is specified, only avatars with the given category
  /// will be fetched.
  Future<List<AvatarImage>> getAvatarImages({String? category});

  /// Fetches avatar background colors.
  Future<List<AvatarBackground>> getAvatarBackgrounds();

  /// Returns family hub data (current member avatar, family hub name).
  Future<CurrentUserConfig> getCurrentUserConfig();

  /// Emits data whenever it is updated.
  ///
  /// Setting [forceRefresh] to `true` will force-fetch data upon subscription.
  Stream<Data<CurrentUserConfig>> observeCurrentUserConfig({
    bool forceRefresh = false,
  });

  /// Fetches latest data for current user config
  /// and sends it to observers of [observeCurrentUserConfig].
  Future<void> reloadCurrentUserConfig();

  /// Update current member avatar for family hub config
  Future<void> updateCurrentUserAvatar({
    required String avatarId,
    required int backgroundColorHex,
  });

  /// Update name for hub config for this user
  Future<void> updateCurrentUserHubName(String newName);
}
