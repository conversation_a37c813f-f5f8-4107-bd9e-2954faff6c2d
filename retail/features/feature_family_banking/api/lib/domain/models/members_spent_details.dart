import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_family_banking_api/domain/models/member.dart';

part 'members_spent_details.freezed.dart';

typedef MaxSpentMemberInfo = ({String name, String percentage});

@freezed
class MembersTotalSpentDetails with _$MembersTotalSpentDetails {
  static const int maxMembersToDisplayCount = 3;

  const MembersTotalSpentDetails._();

  const factory MembersTotalSpentDetails({
    required List<MemberSpentDetails> topMembersSpentDetails,
  }) = _MembersTotalSpentDetails;

  /// Spend balance for top members only, not for all members.
  Money get totalSpentBalance => topMembersSpentDetails.fold(
        Money.fromIntWithCurrency(0, Currency.aed),
        (acc, memberSpentDetails) => acc + memberSpentDetails.spentMoney,
      );

  MaxSpentMemberInfo? get maxSpentMemberInfo {
    if (!totalSpentBalance.isPositive) {
      return null;
    }

    final maxSpendMember = topMembersSpentDetails.first;
    final maxSpendMemberName = maxSpendMember.name;
    final maxSpendPercentage = (maxSpendMember.spentMoney.minorUnits /
            totalSpentBalance.minorUnits *
            100)
        .ceil();

    return (name: maxSpendMemberName, percentage: '$maxSpendPercentage%');
  }

  bool get hasData => topMembersSpentDetails.isNotEmpty;
}

@freezed
class MemberSpentDetails with _$MemberSpentDetails {
  const factory MemberSpentDetails({
    required MemberAvatar avatar,
    required String name,
    required Money spentMoney,
    required int transactionsCount,
  }) = _MemberSpentDetails;
}

@freezed
class MoneyMovementDetails with _$MoneyMovementDetails {
  const MoneyMovementDetails._();

  const factory MoneyMovementDetails({
    required Money movedInMoney,
    required Money movedOutMoney,
  }) = _MoneyMovementDetails;

  Money get savedMoney => movedInMoney - movedOutMoney;

  bool get hasData => !movedInMoney.isZero || !movedOutMoney.isZero;
}

final class AnalyticsMemberInfo {
  final String id;
  final String name;
  final MemberAvatar avatar;

  const AnalyticsMemberInfo({
    required this.id,
    required this.name,
    required this.avatar,
  });
}
