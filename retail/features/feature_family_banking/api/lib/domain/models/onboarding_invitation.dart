import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';

part 'onboarding_invitation.freezed.dart';

@freezed
class OnboardingInvitation with _$OnboardingInvitation implements Invitation {
  const OnboardingInvitation._();

  const factory OnboardingInvitation({
    required String id,
    required String inviterId,
    required String inviterName,
    required String inviterMobile,
    required String inviteeName,
    required MemberRole role,
    required DateTime createdOn,
    required InvitationType type,
    @Default(InvitationStatus.created) InvitationStatus status,
    DateTime? invitationAcceptedOn,
  }) = _OnboardingInvitation;
}
