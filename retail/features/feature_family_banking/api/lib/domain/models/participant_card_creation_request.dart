import 'package:feature_cards_api/feature_cards_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'participant_card_creation_request.freezed.dart';

/// Represents a card details request to create a participant invite.
@freezed
class ParticipantCardCreationRequest with _$ParticipantCardCreationRequest {
  const factory ParticipantCardCreationRequest({
    required String cardName,
    required String cardImageId,
    required SpendingLimitRecord spendingLimit,
    DateTime? expiryDate,
  }) = _ParticipantCardCreationRequest;
}
