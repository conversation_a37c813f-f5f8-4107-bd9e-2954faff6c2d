import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import 'package:wio_feature_user_api/index.dart';

class BrokerProductInteractorImpl implements BrokerProductInteractor {
  final CustomerIdInteractor _customerIdInteractor;
  final BrokerProductRepository _brokerProductRepository;
  final BrokerUserInteractor _userInteractor;
  final TokenInteractor _tokenInteractor;

  const BrokerProductInteractorImpl({
    required CustomerIdInteractor customerIdInteractor,
    required BrokerProductRepository brokerProductRepository,
    required BrokerUserInteractor userInteractor,
    required TokenInteractor tokenInteractor,
  })  : _customerIdInteractor = customerIdInteractor,
        _userInteractor = userInteractor,
        _tokenInteractor = tokenInteractor,
        _brokerProductRepository = brokerProductRepository;

  @override
  Future<ProductHubItem?> getBrokerProduct() async {
    final item = await getBrokerProductWithCustomerId();

    if (item == null) {
      return null;
    }

    return item.productHubItem;
  }

  @override
  Future<BrokerProductWithCustomerId?> getBrokerProductWithCustomerId() async {
    final customerId = await _customerIdInteractor.getCustomerId();
    if (customerId == null) return null;
    final ProductHubItem broker;

    try {
      broker = await getBroker(customerId: customerId);
    } on Exception catch (_) {
      return null;
    }

    try {
      final _ = await _userInteractor.getUserId();
    } on Exception catch (_) {
      final userId = broker.externalProductId;
      if (userId != null) {
        await _userInteractor.updateUserId(userId);
        final token = _tokenInteractor.getCacheToken();
        if (token != null) {
          try {
            await _tokenInteractor.updateToken(
              refreshToken: token.refreshToken,
            );
          } on Exception catch (_) {}
        }
      }
    }

    return BrokerProductWithCustomerId(
      customerId: customerId,
      productHubItem: broker,
    );
  }

  @override
  Future<ProductHubItem> brokerOnboard({required String customerId}) {
    return _brokerProductRepository.brokerOnboard(customerId: customerId);
  }

  @override
  Future<ProductHubItem> getBroker({
    required String customerId,
  }) async {
    final broker = await _brokerProductRepository.getBroker(
      customerId: customerId,
    );
    await _cacheBrokerUpdate(broker);

    return broker;
  }

  Future<void> _cacheBrokerUpdate(ProductHubItem broker) async {
    final brokerFromCache = await getBrokerFromCache();

    // If broker is blocked or unavailable, clear cache
    if (brokerFromCache != null && !broker.status.isActiveProduct) {
      await _brokerProductRepository.clear();
    }

    if (broker.status == ItemStatus.active &&
        brokerFromCache?.status != broker.status) {
      await _brokerProductRepository.saveBrokerProduct(broker);
    }
  }

  @override
  Future<ProductHubItem?> getBrokerFromCache() {
    return _brokerProductRepository.getBrokerProduct();
  }
}
