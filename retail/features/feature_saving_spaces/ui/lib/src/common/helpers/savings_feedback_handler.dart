import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart' hide SavingSpace;
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_feature_error_domain_api/handlers/common_error_handler.dart';
import 'package:wio_feature_saving_spaces_api/domain/models/saving_space.dart';
import 'package:wio_feature_saving_spaces_ui/feature_saving_spaces_ui.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/extensions.dart';

// FIXME(esultanli): rename to something more descriptive
class SavingsFeedbackHandler {
  final ToastMessageProvider _toastProvider;
  final SavingSpacesLocalizations _l10n;
  final CommonErrorHandler _errorHandler;

  const SavingsFeedbackHandler({
    required ToastMessageProvider toastProvider,
    required SavingSpacesLocalizations l10n,
    required CommonErrorHandler errorHandler,
  })  : _toastProvider = toastProvider,
        _l10n = l10n,
        _errorHandler = errorHandler;

  void onImageUpdated(SavingsAccount account) {
    _toastProvider.showSuccess(
      switch (account) {
        SavingSpace() => _l10n.manageSavingSpaceImageUpdatedMessage,
        FixedDeposit() ||
        FamilyFixedDeposit() ||
        EtihadTermDeposit() =>
          _l10n.editTermDepositImageUpdatedMessage,
      },
    );
  }

  void onError(Object error, [StackTrace? stackTrace]) {
    _errorHandler.handleError(error);
  }

  void showWarning(String message) {
    _toastProvider.showToastMessage(
      NotificationToastMessageConfiguration.warning(
        message,
        showProgress: false,
      ),
    );
  }

  void showError(String message) {
    _toastProvider.showToastMessage(
      NotificationToastMessageConfiguration.error(
        message,
        showProgress: false,
      ),
    );
  }
}
