// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'confirm_etihad_term_deposit_creation_bottom_sheet_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConfirmEtihadTermDepositCreationBottomSheetConfig {
  EtihadTermDepositCreateRequest get request =>
      throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWith<
          ConfirmEtihadTermDepositCreationBottomSheetConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWith<
    $Res> {
  factory $ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWith(
          ConfirmEtihadTermDepositCreationBottomSheetConfig value,
          $Res Function(ConfirmEtihadTermDepositCreationBottomSheetConfig)
              then) =
      _$ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWithImpl<$Res,
          ConfirmEtihadTermDepositCreationBottomSheetConfig>;
  @useResult
  $Res call({EtihadTermDepositCreateRequest request});

  $EtihadTermDepositCreateRequestCopyWith<$Res> get request;
}

/// @nodoc
class _$ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWithImpl<$Res,
        $Val extends ConfirmEtihadTermDepositCreationBottomSheetConfig>
    implements
        $ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWith<$Res> {
  _$ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWithImpl(
      this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_value.copyWith(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as EtihadTermDepositCreateRequest,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $EtihadTermDepositCreateRequestCopyWith<$Res> get request {
    return $EtihadTermDepositCreateRequestCopyWith<$Res>(_value.request,
        (value) {
      return _then(_value.copyWith(request: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWith<
        $Res>
    implements
        $ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWith<$Res> {
  factory _$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWith(
          _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl value,
          $Res Function(_$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl)
              then) =
      __$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWithImpl<
          $Res>;
  @override
  @useResult
  $Res call({EtihadTermDepositCreateRequest request});

  @override
  $EtihadTermDepositCreateRequestCopyWith<$Res> get request;
}

/// @nodoc
class __$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWithImpl<
        $Res>
    extends _$ConfirmEtihadTermDepositCreationBottomSheetConfigCopyWithImpl<
        $Res, _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl>
    implements
        _$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWith<$Res> {
  __$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWithImpl(
      _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl _value,
      $Res Function(_$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl)
          _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? request = null,
  }) {
    return _then(_$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl(
      request: null == request
          ? _value.request
          : request // ignore: cast_nullable_to_non_nullable
              as EtihadTermDepositCreateRequest,
    ));
  }
}

/// @nodoc

class _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl
    extends _ConfirmEtihadTermDepositCreationBottomSheetConfig {
  const _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl(
      {required this.request})
      : super._();

  @override
  final EtihadTermDepositCreateRequest request;

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl &&
            (identical(other.request, request) || other.request == request));
  }

  @override
  int get hashCode => Object.hash(runtimeType, request);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWith<
          _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl>
      get copyWith =>
          __$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWithImpl<
                  _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl>(
              this, _$identity);
}

abstract class _ConfirmEtihadTermDepositCreationBottomSheetConfig
    extends ConfirmEtihadTermDepositCreationBottomSheetConfig {
  const factory _ConfirmEtihadTermDepositCreationBottomSheetConfig(
          {required final EtihadTermDepositCreateRequest request}) =
      _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl;
  const _ConfirmEtihadTermDepositCreationBottomSheetConfig._() : super._();

  @override
  EtihadTermDepositCreateRequest get request;
  @override
  @JsonKey(ignore: true)
  _$$ConfirmEtihadTermDepositCreationBottomSheetConfigImplCopyWith<
          _$ConfirmEtihadTermDepositCreationBottomSheetConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ConfirmEtihadTermDepositCreationResult {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EtihadTermDeposit etihadTermDeposit)
        showDetailsOnSuccess,
    required TResult Function() goBackOnSuccess,
    required TResult Function() checkDetailsOnError,
    required TResult Function() goBackOnError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EtihadTermDeposit etihadTermDeposit)?
        showDetailsOnSuccess,
    TResult? Function()? goBackOnSuccess,
    TResult? Function()? checkDetailsOnError,
    TResult? Function()? goBackOnError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EtihadTermDeposit etihadTermDeposit)? showDetailsOnSuccess,
    TResult Function()? goBackOnSuccess,
    TResult Function()? checkDetailsOnError,
    TResult Function()? goBackOnError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)
        showDetailsOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)
        goBackOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)
        checkDetailsOnError,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)
        goBackOnError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConfirmEtihadTermDepositCreationResultCopyWith<$Res> {
  factory $ConfirmEtihadTermDepositCreationResultCopyWith(
          ConfirmEtihadTermDepositCreationResult value,
          $Res Function(ConfirmEtihadTermDepositCreationResult) then) =
      _$ConfirmEtihadTermDepositCreationResultCopyWithImpl<$Res,
          ConfirmEtihadTermDepositCreationResult>;
}

/// @nodoc
class _$ConfirmEtihadTermDepositCreationResultCopyWithImpl<$Res,
        $Val extends ConfirmEtihadTermDepositCreationResult>
    implements $ConfirmEtihadTermDepositCreationResultCopyWith<$Res> {
  _$ConfirmEtihadTermDepositCreationResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWith<
    $Res> {
  factory _$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWith(
          _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl value,
          $Res Function(
                  _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl)
              then) =
      __$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWithImpl<
          $Res>;
  @useResult
  $Res call({EtihadTermDeposit etihadTermDeposit});

  $EtihadTermDepositCopyWith<$Res> get etihadTermDeposit;
}

/// @nodoc
class __$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWithImpl<
        $Res>
    extends _$ConfirmEtihadTermDepositCreationResultCopyWithImpl<$Res,
        _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl>
    implements
        _$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWith<
            $Res> {
  __$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWithImpl(
      _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl _value,
      $Res Function(
              _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl)
          _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? etihadTermDeposit = null,
  }) {
    return _then(
        _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl(
      etihadTermDeposit: null == etihadTermDeposit
          ? _value.etihadTermDeposit
          : etihadTermDeposit // ignore: cast_nullable_to_non_nullable
              as EtihadTermDeposit,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $EtihadTermDepositCopyWith<$Res> get etihadTermDeposit {
    return $EtihadTermDepositCopyWith<$Res>(_value.etihadTermDeposit, (value) {
      return _then(_value.copyWith(etihadTermDeposit: value));
    });
  }
}

/// @nodoc

class _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl
    implements _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess {
  const _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl(
      {required this.etihadTermDeposit});

  @override
  final EtihadTermDeposit etihadTermDeposit;

  @override
  String toString() {
    return 'ConfirmEtihadTermDepositCreationResult.showDetailsOnSuccess(etihadTermDeposit: $etihadTermDeposit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl &&
            (identical(other.etihadTermDeposit, etihadTermDeposit) ||
                other.etihadTermDeposit == etihadTermDeposit));
  }

  @override
  int get hashCode => Object.hash(runtimeType, etihadTermDeposit);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWith<
          _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl>
      get copyWith =>
          __$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWithImpl<
                  _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EtihadTermDeposit etihadTermDeposit)
        showDetailsOnSuccess,
    required TResult Function() goBackOnSuccess,
    required TResult Function() checkDetailsOnError,
    required TResult Function() goBackOnError,
  }) {
    return showDetailsOnSuccess(etihadTermDeposit);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EtihadTermDeposit etihadTermDeposit)?
        showDetailsOnSuccess,
    TResult? Function()? goBackOnSuccess,
    TResult? Function()? checkDetailsOnError,
    TResult? Function()? goBackOnError,
  }) {
    return showDetailsOnSuccess?.call(etihadTermDeposit);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EtihadTermDeposit etihadTermDeposit)? showDetailsOnSuccess,
    TResult Function()? goBackOnSuccess,
    TResult Function()? checkDetailsOnError,
    TResult Function()? goBackOnError,
    required TResult orElse(),
  }) {
    if (showDetailsOnSuccess != null) {
      return showDetailsOnSuccess(etihadTermDeposit);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)
        showDetailsOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)
        goBackOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)
        checkDetailsOnError,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)
        goBackOnError,
  }) {
    return showDetailsOnSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
  }) {
    return showDetailsOnSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
    required TResult orElse(),
  }) {
    if (showDetailsOnSuccess != null) {
      return showDetailsOnSuccess(this);
    }
    return orElse();
  }
}

abstract class _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess
    implements ConfirmEtihadTermDepositCreationResult {
  const factory _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess(
          {required final EtihadTermDeposit etihadTermDeposit}) =
      _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl;

  EtihadTermDeposit get etihadTermDeposit;
  @JsonKey(ignore: true)
  _$$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImplCopyWith<
          _$ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccessImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImplCopyWith<
    $Res> {
  factory _$$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImplCopyWith(
          _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl value,
          $Res Function(
                  _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl)
              then) =
      __$$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImplCopyWithImpl<
          $Res>;
}

/// @nodoc
class __$$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImplCopyWithImpl<
        $Res>
    extends _$ConfirmEtihadTermDepositCreationResultCopyWithImpl<$Res,
        _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl>
    implements
        _$$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImplCopyWith<
            $Res> {
  __$$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImplCopyWithImpl(
      _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl _value,
      $Res Function(_$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl)
          _then)
      : super(_value, _then);
}

/// @nodoc

class _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl
    implements _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess {
  const _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl();

  @override
  String toString() {
    return 'ConfirmEtihadTermDepositCreationResult.goBackOnSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EtihadTermDeposit etihadTermDeposit)
        showDetailsOnSuccess,
    required TResult Function() goBackOnSuccess,
    required TResult Function() checkDetailsOnError,
    required TResult Function() goBackOnError,
  }) {
    return goBackOnSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EtihadTermDeposit etihadTermDeposit)?
        showDetailsOnSuccess,
    TResult? Function()? goBackOnSuccess,
    TResult? Function()? checkDetailsOnError,
    TResult? Function()? goBackOnError,
  }) {
    return goBackOnSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EtihadTermDeposit etihadTermDeposit)? showDetailsOnSuccess,
    TResult Function()? goBackOnSuccess,
    TResult Function()? checkDetailsOnError,
    TResult Function()? goBackOnError,
    required TResult orElse(),
  }) {
    if (goBackOnSuccess != null) {
      return goBackOnSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)
        showDetailsOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)
        goBackOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)
        checkDetailsOnError,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)
        goBackOnError,
  }) {
    return goBackOnSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
  }) {
    return goBackOnSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
    required TResult orElse(),
  }) {
    if (goBackOnSuccess != null) {
      return goBackOnSuccess(this);
    }
    return orElse();
  }
}

abstract class _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess
    implements ConfirmEtihadTermDepositCreationResult {
  const factory _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess() =
      _$ConfirmEtihadTermDepositCreationResultGoBackOnSuccessImpl;
}

/// @nodoc
abstract class _$$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImplCopyWith<
    $Res> {
  factory _$$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImplCopyWith(
          _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl value,
          $Res Function(
                  _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl)
              then) =
      __$$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImplCopyWithImpl<
          $Res>;
}

/// @nodoc
class __$$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImplCopyWithImpl<
        $Res>
    extends _$ConfirmEtihadTermDepositCreationResultCopyWithImpl<$Res,
        _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl>
    implements
        _$$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImplCopyWith<
            $Res> {
  __$$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImplCopyWithImpl(
      _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl _value,
      $Res Function(
              _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl)
          _then)
      : super(_value, _then);
}

/// @nodoc

class _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl
    implements _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError {
  const _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl();

  @override
  String toString() {
    return 'ConfirmEtihadTermDepositCreationResult.checkDetailsOnError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other
                is _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EtihadTermDeposit etihadTermDeposit)
        showDetailsOnSuccess,
    required TResult Function() goBackOnSuccess,
    required TResult Function() checkDetailsOnError,
    required TResult Function() goBackOnError,
  }) {
    return checkDetailsOnError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EtihadTermDeposit etihadTermDeposit)?
        showDetailsOnSuccess,
    TResult? Function()? goBackOnSuccess,
    TResult? Function()? checkDetailsOnError,
    TResult? Function()? goBackOnError,
  }) {
    return checkDetailsOnError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EtihadTermDeposit etihadTermDeposit)? showDetailsOnSuccess,
    TResult Function()? goBackOnSuccess,
    TResult Function()? checkDetailsOnError,
    TResult Function()? goBackOnError,
    required TResult orElse(),
  }) {
    if (checkDetailsOnError != null) {
      return checkDetailsOnError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)
        showDetailsOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)
        goBackOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)
        checkDetailsOnError,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)
        goBackOnError,
  }) {
    return checkDetailsOnError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
  }) {
    return checkDetailsOnError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
    required TResult orElse(),
  }) {
    if (checkDetailsOnError != null) {
      return checkDetailsOnError(this);
    }
    return orElse();
  }
}

abstract class _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError
    implements ConfirmEtihadTermDepositCreationResult {
  const factory _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError() =
      _$ConfirmEtihadTermDepositCreationResultCheckDetailsOnErrorImpl;
}

/// @nodoc
abstract class _$$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImplCopyWith<
    $Res> {
  factory _$$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImplCopyWith(
          _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl value,
          $Res Function(
                  _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl)
              then) =
      __$$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImplCopyWithImpl<
          $Res>;
}

/// @nodoc
class __$$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImplCopyWithImpl<
        $Res>
    extends _$ConfirmEtihadTermDepositCreationResultCopyWithImpl<$Res,
        _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl>
    implements
        _$$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImplCopyWith<
            $Res> {
  __$$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImplCopyWithImpl(
      _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl _value,
      $Res Function(_$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl)
          _then)
      : super(_value, _then);
}

/// @nodoc

class _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl
    implements _ConfirmEtihadTermDepositCreationResultGoBackOnError {
  const _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl();

  @override
  String toString() {
    return 'ConfirmEtihadTermDepositCreationResult.goBackOnError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EtihadTermDeposit etihadTermDeposit)
        showDetailsOnSuccess,
    required TResult Function() goBackOnSuccess,
    required TResult Function() checkDetailsOnError,
    required TResult Function() goBackOnError,
  }) {
    return goBackOnError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EtihadTermDeposit etihadTermDeposit)?
        showDetailsOnSuccess,
    TResult? Function()? goBackOnSuccess,
    TResult? Function()? checkDetailsOnError,
    TResult? Function()? goBackOnError,
  }) {
    return goBackOnError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EtihadTermDeposit etihadTermDeposit)? showDetailsOnSuccess,
    TResult Function()? goBackOnSuccess,
    TResult Function()? checkDetailsOnError,
    TResult Function()? goBackOnError,
    required TResult orElse(),
  }) {
    if (goBackOnError != null) {
      return goBackOnError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)
        showDetailsOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)
        goBackOnSuccess,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)
        checkDetailsOnError,
    required TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)
        goBackOnError,
  }) {
    return goBackOnError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult? Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
  }) {
    return goBackOnError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultShowDetailsOnSuccess value)?
        showDetailsOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnSuccess value)?
        goBackOnSuccess,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultCheckDetailsOnError value)?
        checkDetailsOnError,
    TResult Function(
            _ConfirmEtihadTermDepositCreationResultGoBackOnError value)?
        goBackOnError,
    required TResult orElse(),
  }) {
    if (goBackOnError != null) {
      return goBackOnError(this);
    }
    return orElse();
  }
}

abstract class _ConfirmEtihadTermDepositCreationResultGoBackOnError
    implements ConfirmEtihadTermDepositCreationResult {
  const factory _ConfirmEtihadTermDepositCreationResultGoBackOnError() =
      _$ConfirmEtihadTermDepositCreationResultGoBackOnErrorImpl;
}
