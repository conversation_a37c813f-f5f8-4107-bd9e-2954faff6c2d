import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_api/wio_feature_account_api.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

part 'withdraw_money_state.freezed.dart';

enum InputAmount { valid, exceedsBalance }

// Poor man's data class
typedef InputState = ({InputAmount input, Money balance});
typedef AccountId = String;

@freezed
sealed class WithdrawMoneyState with _$WithdrawMoneyState {
  const WithdrawMoneyState._();

  const factory WithdrawMoneyState.initial() = InitialWithdrawMoneyState;

  const factory WithdrawMoneyState.loading() = LoadingWithdrawMoneyState;

  const factory WithdrawMoneyState.completed() = CompletedWithdrawMoneyState;

  const factory WithdrawMoneyState.failed({
    required TermDeposit fromAccount,
  }) = FailedWithdrawMoneyState;

  @With<_AccountSelector>()
  const factory WithdrawMoneyState.idle({
    required WithdrawalInfoState withdrawalInfoState,
    required TermDeposit fromAccount,
    required List<AccountDetails> accounts,
    required AccountId toAccountId,
    required Money amount,
  }) = IdleWithdrawMoneyState;

  @With<_AccountSelector>()
  const factory WithdrawMoneyState.inProgress({
    required WithdrawalInfoState withdrawalInfoState,
    required TermDeposit fromAccount,
    required List<AccountDetails> accounts,
    required AccountId toAccountId,
    required Money amount,
  }) = InProgressWithdrawMoneyState;

  bool get isInProgress => this is InProgressWithdrawMoneyState;

  bool get canCancel => this is! InProgressWithdrawMoneyState;

  bool get canSubmit => switch (this) {
        IdleWithdrawMoneyState(:final amount, :final fromAccount) =>
          amount.isPositive && amount <= fromAccount.balance,
        _ => false,
      };

  bool get isFullWithdrawal => switch (this) {
        IdleWithdrawMoneyState(:final amount, :final fromAccount) ||
        InProgressWithdrawMoneyState(:final amount, :final fromAccount) =>
          amount == fromAccount.balance,
        _ => false,
      };

  InputState? get inputState => switch (this) {
        IdleWithdrawMoneyState(:final amount, :final fromAccount)
            when amount > fromAccount.balance =>
          (
            input: InputAmount.exceedsBalance,
            balance: fromAccount.balance,
          ),
        IdleWithdrawMoneyState(:final fromAccount) ||
        InProgressWithdrawMoneyState(:final fromAccount) =>
          (
            input: InputAmount.valid,
            balance: fromAccount.balance,
          ),
        _ => null,
      };

  WithdrawMoneyState toInProgress() => switch (this) {
        final IdleWithdrawMoneyState it when it.canSubmit =>
          InProgressWithdrawMoneyState(
            withdrawalInfoState: it.withdrawalInfoState,
            amount: it.amount,
            fromAccount: it.fromAccount,
            toAccountId: it.toAccountId,
            accounts: it.accounts,
          ),
        _ => throw Exception('Illegal state transition'),
      };
}

@freezed
class WithdrawalInfoState with _$WithdrawalInfoState {
  const WithdrawalInfoState._();

  /// The state when the user typed a new withdrawal amount.
  const factory WithdrawalInfoState.inProgress() =
      _WithdrawalInfoInProgressState;

  /// The state when the withdrawal info is fetched.
  const factory WithdrawalInfoState.idle({
    required TermDepositWithdrawalInfo withdrawalInfo,
  }) = _WithdrawalInfoIdleState;

  /// The state when fetching withdrawal info is failed.
  const factory WithdrawalInfoState.failed() = _WithdrawalInfoFailedState;
}

mixin _AccountSelector {
  List<AccountDetails> get accounts;

  AccountId get toAccountId;

  AccountDetails get toAccount =>
      accounts.firstWhere((it) => it.id == toAccountId);
}
