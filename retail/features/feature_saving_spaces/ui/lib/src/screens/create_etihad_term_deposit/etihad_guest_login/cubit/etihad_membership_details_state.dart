import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_saving_spaces_api/domain/models/etihad_config_details.dart';

part 'etihad_membership_details_state.freezed.dart';

@freezed
class EtihadMemberShipDetailState with _$EtihadMemberShipDetailState {
  const factory EtihadMemberShipDetailState.initial() =
      _EtihadMemberShipDetailInitialState;

  const factory EtihadMemberShipDetailState.loading() =
      _EtihadMemberShipDetailLoadingState;

  const factory EtihadMemberShipDetailState.idle({
    @Default('') String firstName,
    @Default('') String lastName,
    @Default('') String loyaltyNumber,
    EtihadConfigDetails? configDetails,
  }) = _EtihadMemberShipDetailIdleState;
}

extension EtihadMemberShipDetailStateExt on EtihadMemberShipDetailState {
  bool get canProceed => maybeMap(
        orElse: () => false,
        idle: (it) =>
            it.firstName.isNotEmpty &&
            it.lastName.isNotEmpty &&
            it.loyaltyNumber.isNotEmpty,
      );
}
