part of '../set_up_etihad_term_deposit_page.dart';

class _TenorInfo extends StatelessWidget {
  const _TenorInfo();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SetUpEtihadTermDepositCubit,
        SetUpEtihadTermDepositState>(
      builder: (_, state) => state.maybeMap(
        idle: (it) {
          final tenorMaturityDate = it.selectedTenorMaturityDate;
          final tenorMonths = it.selectedTenorMonths;
          final tenorEndOfDepositMiles = it.selectedTenorEndOfDepositMiles;

          return _TenorItemCard(
            tenorMaturityDate: tenorMaturityDate,
            tenorMonths: tenorMonths,
            tenorEndOfDepositMiles: tenorEndOfDepositMiles,
            tenorInfoState: it.tenorInfoState,
            params: it.params,
          );
        },
        inProgress: (it) {
          final tenorMaturityDate = it.selectedTenorMaturityDate;
          final tenorMonths = it.selectedTenorMonths;
          final tenorEndOfDepositMiles = it.selectedTenorEndOfDepositMiles;

          return _TenorItemCard(
            tenorMaturityDate: tenorMaturityDate,
            tenorMonths: tenorMonths,
            tenorEndOfDepositMiles: tenorEndOfDepositMiles,
            tenorInfoState: it.tenorInfoState,
            params: it.params,
          );
        },
        orElse: () => const SizedBox.shrink(),
      ),
    );
  }
}

class _TenorItemCard extends StatelessWidget {
  // NOTE: have to format it manually cause localization generator does not
  // support decimalPatternDigits yet.
  static final _mileFormatter =
      NumberFormat.decimalPatternDigits(decimalDigits: 0);

  final DateTime? tenorMaturityDate;
  final int? tenorMonths;
  final double? tenorEndOfDepositMiles;
  final EtihadTenorInfoState tenorInfoState;
  final CreateEtihadTermDepositParams params;

  const _TenorItemCard({
    required this.tenorMaturityDate,
    required this.tenorMonths,
    required this.tenorEndOfDepositMiles,
    required this.tenorInfoState,
    required this.params,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);
    final maturityDate = tenorMaturityDate;
    final months = tenorMonths;
    final endOfDepositMiles = tenorEndOfDepositMiles;
    final hasData =
        maturityDate != null && months != null && endOfDepositMiles != null;
    const spacing = Spacing.s4;

    if (!hasData) {
      return Space.fromSpacingVertical(spacing);
    }

    return Padding(
      padding: EdgeInsets.only(top: spacing.value),
      child: tenorInfoState.maybeMap(
        inProgress: (_) => const _ItemCardShimmer(),
        orElse: () => ItemCard(
          ItemCardModel.information(
            mainText: l10n.createEtihadTermDepositSetUpPageTenorInfoTitle(
              _mileFormatter.format(endOfDepositMiles),
              endOfDepositMiles,
            ),
            bottomText: l10n.createEtihadTermDepositSetUpPageTenorInfoSubtitle,
          ),
        ),
      ),
    );
  }
}
