part of '../manage_saving_space_page.dart';

class _RecurringTransferOption extends StatelessWidget {
  final SavingSpace space;
  final VoidCallback? onEditRecurringTransfer;

  const _RecurringTransferOption({
    required this.space,
    this.onEditRecurringTransfer,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);

    final rule = space.recurringRule;
    final amount = rule == null || rule.amount == null || !rule.active
        ? null
        : rule.amount;

    final subtitle = _getSubtitleFromRecurringRule(
      context,
      space.recurringRule,
    );

    return ListDetailsContainer(
      onValuePressed: _handlePressed,
      model: ListDetailsContainerModel(
        items: [
          if (amount != null)
            _getListModel(
              amount.toDisplayFormat(),
              _editIcon,
              subtitle: subtitle,
            )
          else
            _getListModel(
              l10n.manageSavingSpaceSetUpRecurringTransferTitle,
              _addIcon,
            ),
        ],
      ),
    );
  }

  ListDetailsModel _getListModel(
    String title,
    CompanyIconPointer icon, {
    String? subtitle,
  }) {
    return ListDetailsModel(
      textLabelModel: ListDetailsTextLabelModel(
        text: title,
        subtitle: subtitle,
        textColor: CompanyColorPointer.secondary1,
      ),
      valueModel: ListDetailsValueModel.icon(iconPointer: icon),
    );
  }

  String? _getSubtitleFromRecurringRule(
    BuildContext context,
    SavingSpaceRecurringRule? recurringRule,
  ) {
    final frequency = recurringRule?.frequency;
    if (frequency == null) return null;
    final l10n = SavingSpacesLocalizations.of(context);

    // Get localization weekdays
    final weekdays = _getWeekdays(context);

    switch (frequency) {
      case RecurringRuleFrequency.daily:
        return l10n.recurringTransferFrequencyOptionDailySelectedText;
      case RecurringRuleFrequency.weekly:
        final date = recurringRule?.recurringDate;
        if (date == null) return null;

        return l10n.recurringTransferFrequencyOptionWeeklySelectedText(
          weekdays[date.weekday % DateTime.daysPerWeek],
        );
      case RecurringRuleFrequency.monthly:
        final date = recurringRule?.recurringDate;
        if (date == null) return null;

        return l10n.recurringTransferFrequencyOptionMonthlySelectedText(
          date.day.toString(),
        );
    }
  }

  List<String> _getWeekdays(BuildContext context) {
    return DateFormat(
      null,
      Localizations.localeOf(context).languageCode,
    ).dateSymbols.WEEKDAYS;
  }

  void _handlePressed(int index) {
    if (index == 0) {
      return onEditRecurringTransfer?.call();
    }
  }
}
