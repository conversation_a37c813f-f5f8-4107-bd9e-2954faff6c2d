part of '../saving_spaces_page.dart';

const _noBalanceMarker = '------';

class _SavingSpacesHeaderDelegate extends SliverPersistentHeaderDelegate {
  static const _duration = Duration(milliseconds: 150);

  final double topPadding;
  final double collapsedHeight;
  final double expandedHeight;
  final Widget collapsedChild;
  final Widget expandedChild;

  const _SavingSpacesHeaderDelegate({
    required this.topPadding,
    required this.collapsedHeight,
    required this.expandedHeight,
    required this.collapsedChild,
    required this.expandedChild,
  });

  @override
  double get maxExtent => max(minExtent, expandedHeight + topPadding);

  @override
  double get minExtent => collapsedHeight + topPadding;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) =>
      true;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    const topExpandedPadding = 40; // from figma
    final collapsedOpacity = _calculateOpacity(shrinkOffset);
    final expandedOpacity = 1 - collapsedOpacity;

    return StatusBar.light(
      child: Container(
        // NOTE: it sometimes throws this assertion error on scrolling the page:
        // SliverGeometry is not valid: The "layoutExtent" exceeds the
        // "paintExtent". Check the issue for more:
        // https://github.com/flutter/flutter/issues/78748
        height: maxExtent,
        decoration: BoxDecoration(gradient: CompanyGradients.hero()),
        child: SafeArea(
          bottom: false,
          child: Stack(
            children: [
              Positioned.fill(
                top: topExpandedPadding - shrinkOffset,
                bottom: _pagePadding,
                child: Opacity(
                  opacity: expandedOpacity,
                  child: expandedChild,
                ),
              ),
              Positioned.fill(
                child: AnimatedOpacity(
                  opacity: collapsedOpacity >= 0.9 ? collapsedOpacity : 0,
                  duration: _duration,
                  child: collapsedChild,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _calculateOpacity(double offset) {
    return (offset / (maxExtent - minExtent)).clamp(0, 1);
  }
}

class _SavingSpacesExpandedHeader extends StatelessWidget {
  final Money? totalBalance;
  final VoidCallback? onAdd;

  const _SavingSpacesExpandedHeader({
    this.totalBalance,
    this.onAdd,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: _pagePadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Expanded(
                child: _Label(
                  text: l10n.savingSpacesTitle,
                  style: CompanyTextStylePointer.h2,
                ),
              ),
              if (onAdd != null)
                _AddButton(
                  key: SavingSpacesPage.saveAddIconKey,
                  onPressed: onAdd,
                ),
            ],
          ),
          const Spacer(),
          _Label(text: l10n.savingSpacesTotalBalanceTitle),
          Space.fromSpacingVertical(Spacing.s1),
          Row(
            children: [
              _AnimatedBalance(amount: totalBalance),
              const Space.horizontal(16),
              const _PrivacyButton(),
            ],
          ),
        ],
      ),
    );
  }
}

class _SavingSpacesCollapsedHeader extends StatelessWidget {
  final Money? totalBalance;
  final VoidCallback? onAdd;

  const _SavingSpacesCollapsedHeader({
    this.totalBalance,
    this.onAdd,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: _pagePadding),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _Label(text: l10n.savingSpacesTotalBalanceTitle),
                AmountLabel(amount: totalBalance, collapsed: true),
              ],
            ),
          ),
          if (onAdd != null)
            _AddButton(
              key: SavingSpacesPage.currencySelectionKey,
              onPressed: onAdd,
            ),
        ],
      ),
    );
  }
}

class _Label extends StatelessWidget {
  final String text;
  final CompanyTextStylePointer style;

  const _Label({
    required this.text,
    this.style = CompanyTextStylePointer.b3,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Label(
      model: LabelModel(
        text: text,
        textStyle: style,
        color: CompanyColorPointer.primary2,
      ),
    );
  }
}

class _AddButton extends StatelessWidget {
  final VoidCallback? onPressed;

  const _AddButton({
    this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Add new savings space',
      button: true,
      excludeSemantics: true,
      child: GestureDetector(
        onTap: onPressed,
        behavior: HitTestBehavior.opaque,
        child: CompanyIcon(
          CompanyIconModel(
            icon: CompanyIconPointer.add_boxed.toGraphicAsset(),
            size: CompanyIconSize.xxLarge,
            color: CompanyColorPointer.primary2,
          ),
        ),
      ),
    );
  }
}

class _PrivacyButton extends StatelessWidget {
  const _PrivacyButton();

  @override
  Widget build(BuildContext context) {
    final balancePrivacyState = context.watch<BalancePrivacyCubit>().state;
    if (!balancePrivacyState.isHideEnabled) {
      return const SizedBox.shrink();
    }
    return Semantics(
      label: 'Toggle total savings visibility',
      button: true,
      excludeSemantics: true,
      child: GestureDetector(
        onTap: () =>
            context.read<BalancePrivacyCubit>().storeAmountHiddenStatus(),
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 8),
          child: CompanyIcon(
            CompanyIconModel(
              icon: (balancePrivacyState.isHidden
                      ? CompanyIconPointer.see
                      : CompanyIconPointer.hide)
                  .toGraphicAsset(),
              size: CompanyIconSize.large,
              color: CompanyColorPointer.primary2,
            ),
          ),
        ),
      ),
    );
  }
}

class _AnimatedBalance extends StatelessWidget {
  final Money? amount;

  const _AnimatedBalance({this.amount});

  @override
  Widget build(BuildContext context) {
    final key = ValueKey(amount);

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      layoutBuilder: (currentChild, previousChildren) => Stack(
        children: [
          ...previousChildren,
          if (currentChild != null) currentChild,
        ],
      ),
      transitionBuilder: (child, animation) => FadeTransition(
        opacity: animation,
        child: child,
      ),
      child: amount == null
          ? _Label(
              key: key,
              text: _noBalanceMarker,
              style: CompanyTextStylePointer.h1,
            )
          : AmountLabel(amount: amount, collapsed: false, key: key),
    );
  }
}

class AmountLabel extends StatelessWidget {
  final Money? amount;
  final bool collapsed;

  const AmountLabel({
    required this.amount,
    required this.collapsed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (amount == null) {
      return Figure(
        model: FigureModel.h1EmptyMoney(
          colorScheme: FigureColorScheme.light,
        ),
      );
    }
    final balancePrivacyState = context.watch<BalancePrivacyCubit>().state;
    final semanticLabel = balancePrivacyState.isHidden
        ? 'Total saving hidden'
        : 'Total savings: ${amount?.semanticsLabel}';
    return Semantics(
      label: semanticLabel,
      excludeSemantics: true,
      child: Figure(
        model: collapsed
            ? FigureModel.b1OneLineOneSize(
                amount!,
                colorScheme: FigureColorScheme.light,
                shouldSnap: balancePrivacyState.isHidden,
                spoilerModel: const SpoilerModel(particlesAmount: 170),
              )
            : FigureModel.h1OneLineTwoSizes(
                amount!,
                colorScheme: FigureColorScheme.light,
                shouldSnap: balancePrivacyState.isHidden,
                spoilerModel: const SpoilerModel(particlesAmount: 170),
              ),
      ),
    );
  }
}
