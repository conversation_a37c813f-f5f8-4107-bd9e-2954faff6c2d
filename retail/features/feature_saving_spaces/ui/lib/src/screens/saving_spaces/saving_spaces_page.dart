import 'dart:math';

import 'package:di/di.dart';
import 'package:flutter/material.dart' hide Banner;
import 'package:instruments_ui/widgets/instruments_promo_banner/index.dart';
import 'package:provider/provider.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_balance_privacy_ui/feature_balance_privacy_ui.dart';
import 'package:wio_feature_family_scope_ui/feature_family_scope_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_saving_spaces_api/navigation/saving_spaces_page_navigation_config.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart' as api;
import 'package:wio_feature_saving_spaces_ui/feature_saving_spaces_ui.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/constants.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/extensions.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/models/goal_status.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/views/ui_config_provider.dart';
import 'package:wio_feature_saving_spaces_ui/src/screens/saving_spaces/saving_spaces_cubit.dart';
import 'package:wio_feature_saving_spaces_ui/src/screens/saving_spaces/saving_spaces_state.dart';

part 'views/etihad_term_deposit_item.dart';
part 'views/family_term_deposit_item.dart';
part 'views/saving_space_banners.dart';
part 'views/saving_space_item.dart';
part 'views/saving_spaces_header.dart';
part 'views/term_deposit_item.dart';

const _pagePadding = 24.0;
const _fabHeight = 64.0;
const _bottomPadding = _pagePadding * 3 + _fabHeight;

class SavingSpacesPage extends BasePage<SavingSpacesState, SavingSpacesCubit> {
  static const saveAddIconKey = ValueKey('saveAddIconKey');
  static const currencySelectionKey = ValueKey('currencySelectionKey');

  final api.ScreenOrigin? fromScreen;

  const SavingSpacesPage({
    this.fromScreen,
    super.key,
  });

  @override
  SavingSpacesCubit createBloc() => DependencyProvider.getWithParams<
      SavingSpacesCubit, api.ScreenOrigin?, void>(param1: fromScreen);

  @override
  void initBloc(SavingSpacesCubit bloc) => bloc.initialize();

  @override
  Widget buildPage(
    BuildContext context,
    SavingSpacesCubit bloc,
    SavingSpacesState state,
  ) =>
      const UIConfigProvider(
        child: BalancePrivacyScope(
          screenId: SavingSpacesPageNavigationConfig.screenName,
          child: FamilyScope(child: _SavingSpacesContent()),
        ),
      );
}

class _SavingSpacesContent extends StatelessWidget {
  const _SavingSpacesContent();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: CustomScrollView(
        physics: BouncingScrollPhysics(parent: ClampingScrollPhysics()),
        slivers: [
          _Header(),
          _Body(),
          SliverPadding(padding: EdgeInsets.only(bottom: _bottomPadding)),
        ],
      ),
      floatingActionButton: _HubButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}

class _HubButton extends StatelessWidget {
  const _HubButton();

  @override
  Widget build(BuildContext context) {
    return FloatingBottomNavigation(
      const FloatingBottomNavigationModel.floatingButton(
        floatingActionButtonModel: FloatingBottomActionButtonModel(
          icon: GraphicAssetPointer.icon(CompanyIconPointer.hub),
        ),
      ),
      onFloatingButtonTap: context.read<SavingSpacesCubit>().onShowHub,
    );
  }
}

class _Header extends StatelessWidget {
  const _Header();

  @override
  Widget build(BuildContext context) {
    final viewPadding = MediaQuery.viewPaddingOf(context);
    final totalBalance = context.select<SavingSpacesCubit, Money?>(
      (cubit) => cubit.state.balance,
    );
    final canAdd = context.select<SavingSpacesCubit, bool>(
      (cubit) => cubit.state.canAdd,
    );
    final onAddPressed =
        canAdd ? context.read<SavingSpacesCubit>().onAddNew : null;

    return SliverPersistentHeader(
      pinned: true,
      delegate: _SavingSpacesHeaderDelegate(
        topPadding: viewPadding.top,
        collapsedHeight: 64,
        expandedHeight: 196,
        collapsedChild: _SavingSpacesCollapsedHeader(
          totalBalance: totalBalance,
          onAdd: onAddPressed,
        ),
        expandedChild: _SavingSpacesExpandedHeader(
          totalBalance: totalBalance,
          onAdd: onAddPressed,
        ),
      ),
    );
  }
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final state = context.watch<SavingSpacesCubit>().state;

    return state.map(
      loading: (_) => const SliverPadding(
        padding: EdgeInsets.all(_pagePadding),
        sliver: SliverToBoxAdapter(child: _Loading()),
      ),
      idle: (it) => _Loaded(
        spaces: it.savingSpaces,
        onItemPressed: context.read<SavingSpacesCubit>().onShowDetails,
      ),
      failed: (_) => const SliverFillRemaining(child: _Failed()),
    );
  }
}

class _Loading extends StatelessWidget {
  const _Loading();

  @override
  Widget build(BuildContext context) {
    return const ClipRRect(
      borderRadius: BorderRadius.all(Radius.circular(16)),
      child: CompanyShimmer(
        model: CompanyShimmerModel(),
        child: AspectRatio(
          aspectRatio: 327 / 190,
          child: ColoredBox(color: Colors.white),
        ),
      ),
    );
  }
}

class _Loaded extends StatelessWidget {
  final List<SavingSpaceItem> spaces;
  final ValueSetter<api.SavingsAccount>? onItemPressed;

  const _Loaded({
    required this.spaces,
    this.onItemPressed,
  });

  @override
  Widget build(BuildContext context) {
    if (spaces.isEmpty) {
      return const SliverToBoxAdapter(
        child: _NoSavingSpace(),
      );
    }

    return SliverMainAxisGroup(
      slivers: [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            _buildItem,
            childCount: spaces.length + 1, // +1 is for the ads banner
          ),
        ),
        const SliverToBoxAdapter(
          child: LendingXsellBanner(
            target: LendingXsellPromotionTarget.savingSpaces,
            padding: EdgeInsets.only(
              top: 12,
              left: _pagePadding,
              right: _pagePadding,
            ),
          ),
        ),
        if (context.read<SavingSpacesCubit>().isBrokerPromoEnabled)
          const SliverPadding(
            padding: EdgeInsetsDirectional.symmetric(
              horizontal: _pagePadding,
              vertical: 16,
            ),
            sliver: SliverToBoxAdapter(
              child: InstrumentsPromoBanner(
                origin: InstrumentsPromoBannerOrigin.savingSpace,
                featureName: api.SavingSpacesPageNavigationConfig.screenName,
                semanticLabel: 'Start investing',
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildItem(BuildContext context, int index) {
    if (index == 1) {
      return const _AdvertisementItem();
    }

    final itemIndex = index == 0 ? 0 : index - 1;
    final item = spaces[itemIndex];
    final onPressed =
        onItemPressed == null ? null : () => onItemPressed?.call(item.account);

    return Padding(
      padding: const EdgeInsets.all(_pagePadding).copyWith(bottom: 0),
      child: item.map(
        space: (it) => _SavingSpaceItem(
          space: it.account,
          goal: it.goalStatus,
          onPressed: onPressed,
        ),
        termDeposit: (it) => _TermDepositItem(
          account: it.account,
          onPressed: onPressed,
        ),
        etihadTermDeposit: (it) => _EtihadTermDepositItem(
          account: it.account,
          onPressed: onPressed,
        ),
        familyTermDeposit: (it) => _FamilyTermDepositItem(
          account: it.account,
          onPressed: onPressed,
        ),
      ),
    );
  }
}

class _Failed extends StatelessWidget {
  const _Failed();

  @override
  Widget build(BuildContext context) {
    final l10n = CommonLocalizations.of(context);

    return Center(
      child: GenericError(
        GenericErrorModel(
          title: l10n.genericErrorComponentTitle,
          subtitle: l10n.genericErrorComponentSubtitle,
          buttonLabel: l10n.genericErrorComponentTryAgainButton,
        ),
        onPressed: context.read<SavingSpacesCubit>().onRetry,
      ),
    );
  }
}

class _NoSavingSpace extends StatelessWidget {
  const _NoSavingSpace();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SavingSpacesCubit>();

    return Padding(
      padding: const EdgeInsets.all(_pagePadding),
      child: Column(
        children: [
          const _CreateSavingSpaceBanner(),
          const LendingXsellBanner(
            target: LendingXsellPromotionTarget.savingSpaces,
            padding: EdgeInsets.only(top: 12),
          ),
          if (cubit.isBrokerPromoEnabled) ...[
            Space.fromSpacingVertical(Spacing.s4),
            const InstrumentsPromoBanner(
              origin: InstrumentsPromoBannerOrigin.savingSpace,
              featureName: api.SavingSpacesPageNavigationConfig.screenName,
              semanticLabel: 'Start investing',
            ),
          ],
        ],
      ),
    );
  }
}
