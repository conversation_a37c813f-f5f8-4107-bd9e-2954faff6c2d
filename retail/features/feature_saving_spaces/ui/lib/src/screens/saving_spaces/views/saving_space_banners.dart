part of '../saving_spaces_page.dart';

class _CreateSavingSpaceBanner extends StatelessWidget {
  const _CreateSavingSpaceBanner();

  @override
  Widget build(BuildContext context) {
    final l10n = SavingSpacesLocalizations.of(context);
    final cubit = context.read<SavingSpacesCubit>();
    final promo = context.select<SavingSpacesCubit, SavingSpacePromo>(
      (cubit) => cubit.state.promo,
    );

    return switch (promo) {
      NoSavingSpacePromo() => const Space.shrink(),
      LoadingSavingSpacePromo() => const _Loading(),
      final ActiveSavingSpacePromo it => _EmptySavingSpaceCard(
          onPressed: cubit.onAddNew,
          title: l10n.openSavingSpaceTitle(it.rate.toDisplayFormat()),
          buttonTitle: l10n.openSavingSpaceButtonTitle,
        ),
      final MustUpgradeForSavingSpacePromo it => _EmptySavingSpaceCard(
          onPressed: cubit.navigateToAdvertisedPlan,
          title: l10n.openSavingSpaceTitle(it.rate.toDisplayFormat()),
          buttonTitle: l10n.upgradeToPlusPlanCta,
        ),
    };
  }
}

class _EmptySavingSpaceCard extends StatelessWidget {
  final String title;
  final String buttonTitle;
  final VoidCallback? onPressed;

  const _EmptySavingSpaceCard({
    required this.title,
    required this.buttonTitle,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final image = context.read<UIConfig>().defaultImage;

    return InfoCard(
      onButtonTap: onPressed,
      model: InfoCardModel.variant5(
        description: title,
        buttonTitle: buttonTitle,
        icon: const GraphicAssetPointer.pictogram(
          CompanyPictogramPointer.business_savings,
        ),
        background: CardBackground.image(
          imagePath: image.path,
          package: SavingSpaceConstants.packageName,
        ),
      ),
    );
  }
}

class _AdvertisementItem extends StatelessWidget {
  const _AdvertisementItem();

  @override
  Widget build(BuildContext context) {
    final advertisementText = context.select<SavingSpacesCubit, String?>(
      (cubit) => cubit.state.planAdvertisementsText,
    );

    if (advertisementText == null) {
      return const Space.shrink();
    }

    final localizations = SavingSpacesLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.all(_pagePadding).copyWith(bottom: 0),
      child: Banner(
        BannerModel.wioOne(
          text: advertisementText,
          buttonText: localizations.pricingPlanConnect,
        ),
        onButtonPressed:
            context.read<SavingSpacesCubit>().navigateToAdvertisedPlan,
      ),
    );
  }
}
