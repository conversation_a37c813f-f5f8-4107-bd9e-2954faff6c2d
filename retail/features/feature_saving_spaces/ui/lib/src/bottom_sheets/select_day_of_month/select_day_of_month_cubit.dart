import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_saving_spaces_ui/src/bottom_sheets/select_day_of_month/select_day_of_month_state.dart';

class SelectDayOfMonthCubit extends BaseCubit<SelectDayOfMonthState> {
  final NavigationProvider _navigationProvider;
  final Logger _logger;

  SelectDayOfMonthCubit({
    required NavigationProvider navigationProvider,
    required Logger logger,
    int? selectedDayOfMonth,
  })  : _navigationProvider = navigationProvider,
        _logger = logger,
        super(
          SelectDayOfMonthState.idle(
            selectedDayOfMonth: selectedDayOfMonth,
          ),
        );

  void onSelectDayOfMonth(int dayOfMonth) {
    _logger.debug('New day of month selected: $dayOfMonth');

    _navigationProvider.goBack(dayOfMonth);
  }

  @override
  String toString() => 'SelectDayOfMonthCubit';
}
