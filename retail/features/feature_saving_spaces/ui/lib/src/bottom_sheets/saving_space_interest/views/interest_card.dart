import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/money.dart';

class InterestCard extends StatelessWidget {
  final String title;
  final Money amount;
  final String? description;

  const InterestCard({
    required this.title,
    required this.amount,
    this.description,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: context.colorStyling.surface2,
        borderRadius: BorderRadius.circular(14),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Label(
              model: LabelModel(
                text: title,
                textStyle: CompanyTextStylePointer.b3,
                color: CompanyColorPointer.secondary1,
              ),
            ),
            Space.fromSpacingVertical(Spacing.s1),
            Figure(model: FigureModel.h2OneLineTwoSizes(amount)),
            if (description != null) ...[
              Space.fromSpacingVertical(Spacing.s2),
              Label(
                model: LabelModel(
                  text: description ?? '',
                  textStyle: CompanyTextStylePointer.b3,
                  color: CompanyColorPointer.secondary4,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
