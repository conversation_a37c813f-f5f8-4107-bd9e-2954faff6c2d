import 'package:rxdart/rxdart.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_error_domain_api/handlers/common_error_handler.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_saving_spaces_ui/feature_saving_spaces_ui.dart';
import 'package:wio_feature_saving_spaces_ui/src/bottom_sheets/remove_saving_space_deadline/remove_saving_space_deadline_analytics.dart';
import 'package:wio_feature_saving_spaces_ui/src/bottom_sheets/remove_saving_space_deadline/remove_saving_space_deadline_state.dart';
import 'package:wio_feature_saving_spaces_ui/src/common/extensions.dart';

class RemoveSavingSpaceDeadlineCubit
    extends BaseCubit<RemoveSavingSpaceDeadlineState> {
  final SavingSpacesInteractor _interactor;
  final ToastMessageProvider _toastMessageProvider;
  final SavingSpacesLocalizations _localizations;
  final NavigationProvider _navigationProvider;
  final RemoveSavingSpaceDeadlineAnalytics _analytics;
  final CommonErrorHandler _errorHandler;

  RemoveSavingSpaceDeadlineCubit({
    required SavingSpace savingSpace,
    required SavingSpacesInteractor interactor,
    required ToastMessageProvider toastMessageProvider,
    required SavingSpacesLocalizations localizations,
    required NavigationProvider navigationProvider,
    required RemoveSavingSpaceDeadlineAnalytics analytics,
    required CommonErrorHandler errorHandler,
  })  : _interactor = interactor,
        _toastMessageProvider = toastMessageProvider,
        _localizations = localizations,
        _navigationProvider = navigationProvider,
        _analytics = analytics,
        _errorHandler = errorHandler,
        super(RemoveSavingSpaceDeadlineState.idle(savingSpace: savingSpace));

  void onCancel() {
    state.mapOrNull(
      idle: (it) {
        _analytics.removeCancelled(currency: it.savingSpace.balance.currency);
        _navigationProvider.goBack();
      },
    );
  }

  void onConfirm() {
    assert(state.canRemove, 'Remove space is confirmed from wrong state');

    state.mapOrNull(
      idle: (it) {
        _analytics.removeConfirmed(currency: it.savingSpace.balance.currency);
        _handleRemoveDeadline(it.savingSpace);
      },
    );
  }

  void _handleRemoveDeadline(SavingSpace space) {
    assert(state.canRemove);

    _interactor
        .removeSavingSpaceDeadline(space.id)
        .toStream()
        .doOnListen(
          () => emit(
            RemoveSavingSpaceDeadlineState.inProgress(savingSpace: space),
          ),
        )
        .withError<Object>(_handleErrors)
        .doOnData(_handleRemoved)
        .drain<void>();
  }

  void _handleErrors(Object error) {
    state.mapOrNull(
      inProgress: (it) {
        _analytics.deadlineRemovingFailed(currency: it.savingSpace.currency);

        emit(RemoveSavingSpaceDeadlineState.idle(savingSpace: it.savingSpace));
        _errorHandler.handleError(error);
      },
    );
  }

  void _handleRemoved([void _]) {
    state.mapOrNull(
      inProgress: (it) {
        _analytics.deadlineRemoved(currency: it.savingSpace.balance.currency);

        emit(RemoveSavingSpaceDeadlineState.done(savingSpace: it.savingSpace));
        _navigationProvider.goBack();
        _toastMessageProvider.showSuccess(
          _localizations.removeSavingSpaceDeadlineSucceededMessage,
        );
      },
    );
  }

  @override
  String toString() => 'RemoveSavingSpaceDeadlineCubit';
}
