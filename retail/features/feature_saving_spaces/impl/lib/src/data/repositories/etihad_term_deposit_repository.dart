import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';

abstract interface class EtihadTermDepositRepository {
  Future<EtihadTermDeposit> createEtihadTermDeposit({
    required EtihadTermDepositCreateRequest request,
  });

  Future<CloseEtihadTermDepositDetails> closeEtihadTermDeposit({
    required EtihadTermDeposit etihadTermDeposit,
  });

  Future<EtihadTermDepositClosureInfo> getEtihadTermDepositClosureDetails({
    required String termDepositAccountId,
  });

  Future<EtihadTermDepositTenorInfo> getEtihadTermDepositTenorInfo({
    required Money termDepositAmount,
  });

  Future<EtihadConfigDetails> getEtihadTermDepositConfig();

  Future<EtihadEarlyClosureFees> getEtihadEarlyClosureFees();
}
