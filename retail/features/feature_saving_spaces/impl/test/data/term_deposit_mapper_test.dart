import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_account_data_api/account_data_api.dart' as dtos;
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_saving_spaces_impl/src/data/mappers/saving_space_image_mapper.dart';
import 'package:wio_feature_saving_spaces_impl/src/data/mappers/saving_space_money_mapper.dart';
import 'package:wio_feature_saving_spaces_impl/src/data/mappers/term_deposit_mapper.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  const moneyMapper = SavingSpaceMoneyMapperImpl();
  late SavingSpaceImageMapper imageMapper;
  late TermDepositMapper mapper;

  setUp(() {
    final featureToggles = MockFeatureToggleProvider();
    when(
      () => featureToggles.get(SavingSpacesFeatureToggles.areNewImagesEnabled),
    ).thenReturn(false);

    imageMapper = SavingSpaceImageMapperImpl(featureToggles: featureToggles);
    mapper = TermDepositMapperImpl(
      moneyMapper: moneyMapper,
      imageMapper: imageMapper,
    );
  });

  group('Fixed deposit mapping >', () {
    test('maps to fixed deposit domain model', () {
      // Arrange
      final id = randomString();
      final balance = randomDouble(end: 1000);
      final name = randomString();
      final dto = dtos.SavingSpaceListItem.fromJson({
        'Id': id,
        'Balance': balance,
        'Name': name,
        'Currency': 'AED',
        'GoalTargetDate': '2023-12-05',
        'ImageRef': '1.png',
        'AccountType': 'FIXED_DEPOSIT',
        'Tenor': 1,
      });

      // Act
      final actual = mapper.mapToFixedDeposit(dto);

      // Assert
      expect(actual.id, id);
      expect(actual.name, name);
      expect(
        actual.balance,
        Money.fromNumWithCurrency(balance, Currency.aed),
      );
      expect(actual.tenorInMonths, 1);
      expect(actual.maturityDate, DateTime(2023, 12, 5));
    });

    test('maps creation response to fixed deposit domain model', () {
      // Arrange
      final id = randomString();
      final name = randomString();
      final balance = randomDouble(end: 100500);
      final maturityDate = randomDate();
      final regularInterestRate = randomDouble(end: 10);
      final correctedInterestRate = randomDouble(end: 10);
      final endOfDepositInterest = randomDouble(end: 110000);
      const tenorInMonths = 3;

      final dto = dtos.CreateFixedTermDepositResponse(
        id: id,
        name: name,
        currency: dtos.CreateFixedTermDepositResponseCurrency.aed,
        imageRef: 'image',
        maturityDate: maturityDate,
        balance: balance,
        regularInterestRate: regularInterestRate,
        correctedInterestRate: correctedInterestRate,
        endOfDepositInterest: endOfDepositInterest,
        tenor: tenorInMonths,
      );

      // Act
      final actual = mapper.mapToCreatedTermDeposit(dto);

      final expectedCurrency = Currency.aed;
      final expectedInterestSettings = FixedDepositInterestSettings(
        regularRate: regularInterestRate,
        correctedRate: correctedInterestRate,
        accruedAmount: Money.fromNumWithCurrency(0, expectedCurrency),
        expectedAmount:
            Money.fromNumWithCurrency(endOfDepositInterest, expectedCurrency),
      );

      // Assert
      expect(actual.id, id);
      expect(actual.name, name);
      expect(actual.maturityDate, maturityDate);
      expect(
        actual.balance,
        Money.fromNumWithCurrency(balance, expectedCurrency),
      );
      expect(actual.interestSettings, expectedInterestSettings);
      expect(actual.tenorInMonths, tenorInMonths);
    });
  });

  group('Money withdrawal mapping >', () {
    test('maps to withdrawal result domain model', () {
      // Arrange
      final aedAccountId = randomString();
      final fixedDepositId = randomString();
      final currency = Currency.aed;
      final creditAccount = TestEntities.randAccountDto(
        id: aedAccountId,
        balance: 1337,
      );
      final debitAccount = TestEntities.randAccountDto(
        id: fixedDepositId,
        balance: 42,
        type: dtos.AccountDetailsResponseRetailType.fixedDeposit,
      );
      final dto = dtos.FixedTermDepositWithdrawalResponse(
        amount: 1000,
        appliedInterestAmount: 2,
        creditAccount: creditAccount,
        debitAccount: debitAccount,
        fullWithdrawal: false,
      );

      final expected = WithdrawalResult(
        amount: Money.fromNumWithCurrency(1000, currency),
        appliedInterestAmount: Money.fromNumWithCurrency(2, currency),
        currentAccount: TransferAccount(
          id: aedAccountId,
          name: creditAccount.name,
          nickname: creditAccount.accountNickName,
          balance: Money.fromNumWithCurrency(1337, currency),
        ),
        savingsAccount: TransferAccount(
          id: fixedDepositId,
          name: debitAccount.name,
          nickname: debitAccount.accountNickName,
          balance: Money.fromNumWithCurrency(42, currency),
        ),
        isFullWithdrawal: false,
      );

      // Act
      final actual = mapper.mapToWithdrawalResult(dto);

      // Assert
      expect(actual, expected);
    });
  });

  group('Tenor info mapping >', () {
    group('mapToTermDepositTieredTenorInfo >', () {
      final currency = Currency.aed;

      test('maps response with valid visible active tiers', () {
        final response = dtos.FixedTermDepositTieredTenorInfo(
          available: true,
          totalAmount: 1500,
          totalCount: 2,
          tenorDetails: [
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 6,
              amount: 500,
              active: true,
              display: true,
              tierTo: 1000,
            ),
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 6,
              amount: 1000,
              active: true,
              display: false,
            ),
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 6,
              active: false,
              display: true,
              tierTo: 2000,
              nextTierRegularInterestRate: 1.8,
            ),
          ],
        );

        final result = mapper.mapToTermDepositTenorInfo(response, currency);

        expect(result.isAvailable, isTrue);
        expect(result.tenorDetails.length, 1);

        final detail = result.tenorDetails.first;
        expect(detail.tenorMonths, 6);
        expect(detail.activeTier.appliedAmount.toDouble(), 500);
        expect(detail.unavailableTiers.length, 1);
      });

      test('skips tenor with no visible active tier', () {
        final response = dtos.FixedTermDepositTieredTenorInfo(
          available: false,
          totalAmount: 0,
          totalCount: 1,
          tenorDetails: [
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 3,
              amount: 400,
              active: true,
              display: false,
            ),
          ],
        );

        final result = mapper.mapToTermDepositTenorInfo(response, currency);

        expect(result.isAvailable, isFalse);
        expect(result.tenorDetails, isEmpty);
      });

      test('handles multiple tenor groups', () {
        final response = dtos.FixedTermDepositTieredTenorInfo(
          available: true,
          totalAmount: 3000,
          totalCount: 4,
          tenorDetails: [
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 3,
              amount: 300,
              active: true,
              display: true,
            ),
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 6,
              amount: 700,
              active: true,
              display: true,
            ),
            TestEntities.createTermDepositTieredTenorDetailsDto(
              tenor: 6,
              amount: 1000,
              active: true,
              display: false,
            ),
          ],
        );

        final result = mapper.mapToTermDepositTenorInfo(response, currency);

        expect(result.tenorDetails.length, 2);

        final tenors = result.tenorDetails.map((t) => t.tenorMonths);
        expect(tenors, containsAll([3, 6]));
      });
    });
  });

  group('Withdrawal info mapping >', () {
    test('maps to term deposit withdrawal info domain model', () {
      // Arrange
      final maturityDate = randomDate();
      final regularInterestRate = randomDouble(end: 10);
      final correctedInterestRate = randomDouble(end: 10);
      final amountWithAppliedCorrectedInterest = randomDouble(end: 110000);
      final isFullWithdrawal = randomBool();

      final dto = dtos.FixedTermDepositWithdrawalInfoResponse(
        currency: dtos.FixedTermDepositWithdrawalInfoResponseCurrency.aed,
        correctedInterestRate: correctedInterestRate,
        regularInterestRate: regularInterestRate,
        amountWithAppliedCorrectedInterest: amountWithAppliedCorrectedInterest,
        maturityDate: maturityDate,
        fullWithdrawal: isFullWithdrawal,
      );

      final expected = TermDepositWithdrawalInfo(
        correctedInterestRate: correctedInterestRate,
        regularInterestRate: regularInterestRate,
        amountWithAppliedCorrectedInterest: Money.fromNumWithCurrency(
          amountWithAppliedCorrectedInterest,
          Currency.aed,
        ),
        maturityDate: maturityDate,
        isFullWithdrawal: isFullWithdrawal,
      );

      // Act
      final actual = mapper.mapToTermDepositWithdrawalInfo(dto);

      // Assert
      expect(actual, expected);
    });
  });

  group('Creation request mapping >', () {
    test('maps to creation request dto', () {
      // Arrange
      final sourceId = randomString();
      final name = randomString();
      const amount = 1000.0;
      const tenorInMonths = 3;
      final request = TermDepositFromCurrentAccountCreateRequest(
        amount: Money.fromNumWithCurrency(amount, Currency.aed),
        sourceAccountId: sourceId,
        tenorInMonths: tenorInMonths,
        name: name,
      );
      final expected = dtos.CreateFixedTermDepositRequest(
        currency: dtos.CreateFixedTermDepositRequestCurrency.aed,
        amount: amount,
        sourceAccountId: sourceId,
        tenor: tenorInMonths,
        name: name,
      );

      // Act
      final actual = mapper.mapToCreateFixedTermDepositRequestDto(request);

      // Assert
      expect(actual, expected);
    });

    test('maps to conversion request dto', () {
      // Arrange
      const tenorInMonths = 6;
      const amount = 13.37;
      final space = TestEntities.randSavingSpace(
        balance: amount,
        currency: Currency.aed,
      );
      final request = TermDepositFromSavingSpaceCreateRequest(
        space,
        tenorInMonths: tenorInMonths,
      );
      final expected = dtos.CreateFixedTermDepositFromSavingSpaceRequest(
        currency: dtos.CreateFixedTermDepositFromSavingSpaceRequestCurrency.aed,
        sourceAccountId: space.id,
        tenor: tenorInMonths,
        name: space.name,
        imageRef: space.name,
      );

      // Act
      final actual = mapper.mapToConvertToFixedTermDepositRequestDto(request);

      // Assert
      expect(actual, expected);
    });
  });

  group('Family members info >', () {
    test('mapToFamilyFixedTermDepositMembersInfo', () {
      // Arrange

      final response = [
        const dtos.FamilyMemberInfo(
          customerIdentifier: 'test1',
          familyFixedTermDepositEligible: true,
        ),
        const dtos.FamilyMemberInfo(
          customerIdentifier: 'test2',
          familyFixedTermDepositEligible: false,
        ),
      ];

      final expected = [
        const FamilyMemberInfo(
          customerId: 'test1',
          eligibleForFamilyFixedTermDeposit: true,
        ),
        const FamilyMemberInfo(
          customerId: 'test2',
          eligibleForFamilyFixedTermDeposit: false,
        ),
      ];

      // Act
      final actual = mapper.mapToFamilyFixedTermDepositMembersInfo(response);

      // Assert
      expect(actual, expected);
    });
  });
}
