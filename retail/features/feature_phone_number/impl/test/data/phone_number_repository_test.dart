import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_identity_data_api/index.dart';
import 'package:wio_feature_phone_number_api/index.dart';
import 'package:wio_feature_phone_number_impl/src/data/phone_number_mapper.dart';
import 'package:wio_feature_phone_number_impl/src/data/phone_number_repository_impl.dart';

import '../mocks.dart';

void main() {
  late MockPhoneNumberService mockService;
  late PhoneNumberRepositoryImpl repository;

  setUp(
    () {
      mockService = MockPhoneNumberService();
      repository = PhoneNumberRepositoryImpl(
        phoneNumberService: mockService,
        phoneNumberMapper: PhoneNumberMapperImpl(MockErrorReporter()),
      );
    },
  );

  test(
    'change phone number',
    () async {
      // arrange
      when(
        () => mockService.changePhoneNumber(
          phoneNumber: any(named: 'phoneNumber'),
        ),
      ).justAnswerAsync(OperationResult(result: ResultType.ok));

      // act
      final result = repository.changePhoneNumber(
        phoneNumber: 'phoneNumber',
      );

      // assert
      expect(result, completion(ChangePhoneNumberResult.success));
      verify(
        () => mockService.changePhoneNumber(
          phoneNumber: any(named: 'phoneNumber'),
        ),
      ).calledOnce;
    },
  );
}
