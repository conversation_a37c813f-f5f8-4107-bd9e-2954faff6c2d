// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'change_phone_number_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$ChangePhoneNumberNavigationConfig {}

/// @nodoc
abstract class $ChangePhoneNumberNavigationConfigCopyWith<$Res> {
  factory $ChangePhoneNumberNavigationConfigCopyWith(
          ChangePhoneNumberNavigationConfig value,
          $Res Function(ChangePhoneNumberNavigationConfig) then) =
      _$ChangePhoneNumberNavigationConfigCopyWithImpl<$Res>;
}

/// @nodoc
class _$ChangePhoneNumberNavigationConfigCopyWithImpl<$Res>
    implements $ChangePhoneNumberNavigationConfigCopyWith<$Res> {
  _$ChangePhoneNumberNavigationConfigCopyWithImpl(this._value, this._then);

  final ChangePhoneNumberNavigationConfig _value;
  // ignore: unused_field
  final $Res Function(ChangePhoneNumberNavigationConfig) _then;
}

/// @nodoc
abstract class _$$_ChangePhoneNumberNavigationConfigCopyWith<$Res> {
  factory _$$_ChangePhoneNumberNavigationConfigCopyWith(
          _$_ChangePhoneNumberNavigationConfig value,
          $Res Function(_$_ChangePhoneNumberNavigationConfig) then) =
      __$$_ChangePhoneNumberNavigationConfigCopyWithImpl<$Res>;
}

/// @nodoc
class __$$_ChangePhoneNumberNavigationConfigCopyWithImpl<$Res>
    extends _$ChangePhoneNumberNavigationConfigCopyWithImpl<$Res>
    implements _$$_ChangePhoneNumberNavigationConfigCopyWith<$Res> {
  __$$_ChangePhoneNumberNavigationConfigCopyWithImpl(
      _$_ChangePhoneNumberNavigationConfig _value,
      $Res Function(_$_ChangePhoneNumberNavigationConfig) _then)
      : super(_value, (v) => _then(v as _$_ChangePhoneNumberNavigationConfig));

  @override
  _$_ChangePhoneNumberNavigationConfig get _value =>
      super._value as _$_ChangePhoneNumberNavigationConfig;
}

/// @nodoc

class _$_ChangePhoneNumberNavigationConfig
    extends _ChangePhoneNumberNavigationConfig {
  const _$_ChangePhoneNumberNavigationConfig() : super._();

  @override
  String toString() {
    return 'ChangePhoneNumberNavigationConfig()';
  }
}

abstract class _ChangePhoneNumberNavigationConfig
    extends ChangePhoneNumberNavigationConfig {
  const factory _ChangePhoneNumberNavigationConfig() =
      _$_ChangePhoneNumberNavigationConfig;
  const _ChangePhoneNumberNavigationConfig._() : super._();
}
