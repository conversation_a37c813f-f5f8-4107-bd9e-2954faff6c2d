import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_pricing_plan_ui/bottom_sheets/pricing_plan_comparison/cubit/plan_comparison_cubit.dart';
import 'package:wio_feature_pricing_plan_ui/bottom_sheets/pricing_plan_comparison/cubit/plan_comparison_state.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late PricingPlanInteractor interactor;
  late PlanComparisonCubit cubit;
  late Logger logger;
  setUpAll(() {});

  setUp(() {
    interactor = MockPricingPlanInteractor();
    logger = MockLogger();

    cubit = PlanComparisonCubit(
      interactor: interactor,
      logger: logger,
    );
  });

  group('Verify plan details', () {
    blocTest<PlanComparisonCubit, PlanComparisonState>(
      'Fetch plans success',
      build: () => cubit,
      setUp: () {
        when(() => interactor.getPricingPlan(PricingPlanSource.settings))
            .justAnswerAsync(
          TestEntities.planContent,
        );
      },
      act: (it) => it.init(source: PricingPlanSource.settings),
      verify: (_) {
        verify(() => interactor.getPricingPlan(PricingPlanSource.settings))
            .calledOnce;
      },
      expect: () => [
        const PlanComparisonState.loading(source: PricingPlanSource.settings),
        PlanComparisonState.idle(
          source: PricingPlanSource.settings,
          pricingPlansContent: TestEntities.planContent,
        ),
      ],
    );

    blocTest<PlanComparisonCubit, PlanComparisonState>(
      'Fetch plans failure',
      build: () => cubit,
      setUp: () {
        when(() => interactor.getPricingPlan(PricingPlanSource.settings))
            .justThrowAsync(Exception());
      },
      act: (it) => it.init(source: PricingPlanSource.settings),
      verify: (_) {
        verify(() => interactor.getPricingPlan(PricingPlanSource.settings))
            .calledOnce;
      },
      expect: () => [
        const PlanComparisonState.loading(source: PricingPlanSource.settings),
        const PlanComparisonState.error(source: PricingPlanSource.settings),
      ],
    );
  });
}
