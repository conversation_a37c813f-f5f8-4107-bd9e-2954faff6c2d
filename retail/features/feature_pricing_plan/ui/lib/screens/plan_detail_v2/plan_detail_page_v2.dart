import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_pricing_plan_api/models/plan_contenful_details.dart';
import 'package:wio_feature_pricing_plan_api/models/pricing_plan.dart';

import 'package:wio_feature_pricing_plan_ui/screens/plan_detail_v2/cubit/plan_detail_cubit.dart';
import 'package:wio_feature_pricing_plan_ui/screens/plan_detail_v2/cubit/plan_detail_state.dart';
import 'package:wio_feature_pricing_plan_ui/screens/plan_detail_v2/widgets/plan_details_content.dart';

class PlanDetailPageV2 extends BasePage<PlanDetailState, PlanDetailCubit> {
  final PlanContentDetails planContentDetails;
  final PricingPlanSource source;

  const PlanDetailPageV2({
    required this.planContentDetails,
    required this.source,
    super.key,
  });

  @override
  Widget buildPage(
    BuildContext context,
    PlanDetailCubit bloc,
    PlanDetailState state,
  ) =>
      Scaffold(
        appBar: TopNavigation(
          const TopNavigationModel(
            state: TopNavigationState.positive,
          ),
          onLeftIconPressed: Navigator.of(context).pop,
        ),
        body: _PlanDetailsContent(),
      );

  @override
  PlanDetailCubit createBloc() => DependencyProvider.get<PlanDetailCubit>();

  @override
  void initBloc(PlanDetailCubit bloc) =>
      bloc.init(planInfo: planContentDetails, source: source);
}

class _PlanDetailsContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final state = context.watch<PlanDetailCubit>().state;

    return state.maybeMap(
      orElse: () => const SizedBox.shrink(),
      idle: (it) => PlanDetailsContentView(
        planContentDetails: it.planInfo,
      ),
    );
  }
}
