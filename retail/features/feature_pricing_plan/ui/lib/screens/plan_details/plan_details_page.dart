import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_pricing_plan_ui/index.dart';

class PlanDetailsPage extends StatelessWidget {
  final PlanDetailsPageModel model;
  static const planDetailsScreenTitle = ValueKey('planDetailsScreenTitle');
  static const viewPlanDetailsButtonKey = ValueKey('viewPlanDetailsButton');

  const PlanDetailsPage(
    this.model, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = PricingPlanLocalizations.of(context);

    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async => true,
      child: Scaffold(
        backgroundColor: context.colorStyling.background1,
        appBar: const TopNavigation(
          TopNavigationModel(
            state: TopNavigationState.positive,
          ),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          children: [
            const SizedBox(height: 16),
            Label(
              key: planDetailsScreenTitle,
              model: LabelModel(
                text: localizations.planDetailsScreenHeader(model.planName),
                textStyle: CompanyTextStylePointer.h3,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 48),
            ...model.detailsBlocks.map(_DetailsGroup.new),
          ],
        ),
      ),
    );
  }
}

class _DetailsGroup extends StatelessWidget {
  final DetailsBlock block;

  const _DetailsGroup(
    this.block, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Label(
          model: LabelModel(
            text: block.header,
            color: CompanyColorPointer.primary3,
          ),
        ),
        const SizedBox(height: 12),
        ListDetailsContainer(
          model: ListDetailsContainerModel(
            items: block.details
                .map(
                  (details) => ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: details.title,
                    ),
                    valueModel: ListDetailsValueModel.text(
                      textModel: ListDetailValueTextModel.label(
                        content: details.value,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
