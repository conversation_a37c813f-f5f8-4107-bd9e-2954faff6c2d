import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_pricing_plan_api/models/plan_contenful_details.dart';
import 'package:wio_feature_pricing_plan_ui/screens/plans_list_v2/widgets/plan_info_view.dart';

class PlanInfoLoadingView extends StatelessWidget {
  const PlanInfoLoadingView({super.key});

  PlanContentDetails get planContent => const PlanContentDetails(
        planId: '9',
        name: 'Salary Plus',
        planTitle:
            'FREE for Life if you transfer your salary to Wio within 2 months, '
            'or downgrade to Plus Plan',
        minimumBalance: 3000,
        benefits: [
          BenefitContent(
            benefitDetailType: BenefitDetailType.exclusive,
            benefits: [
              BenefitContentItem(
                benefitType: BenefitType.saving,
                benefitDetail: 'Earn up to 5.5% p.a on Savings',
              ),
              BenefitContentItem(
                benefitType: BenefitType.saving,
                benefitDetail: 'Earn up to 5.5% p.a on Savings',
              ),
              BenefitContentItem(
                benefitType: BenefitType.saving,
                benefitDetail: 'Earn up to 5.5% p.a on Savings',
              ),
            ],
            sectionIcon: '',
            sectionTitle: '',
          ),
        ],
        titleDetails: TitleDetails(
          headerTitle: 'Choose your plan',
          viewDetailsTitle: 'viewDetailsTitle',
          planComparisonTitle: 'Compare all plans',
          getPlanTitle: '',
        ),
        getPlanCta: '',
        planType: PlanType.unknown,
        fee: 'Free',
        feeInfo: '',
        isSpecialPlan: false,
        imageUrl: '',
        planConfirmationSection: PlanConfirmationSection(
          sectionButtonTitle: 'sectionButtonTitle',
          sectionHeader: 'sectionHeader',
          sectionItem: [],
        ),
      );

  @override
  Widget build(BuildContext context) {
    return CompanyShimmer(
      model: const CompanyShimmerModel(),
      child: PlanInfoView(
        plans: [planContent, planContent],
      ),
    );
  }
}
