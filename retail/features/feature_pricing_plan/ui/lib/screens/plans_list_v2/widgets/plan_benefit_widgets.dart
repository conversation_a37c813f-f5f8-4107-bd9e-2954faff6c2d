import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_pricing_plan_api/models/plan_contenful_details.dart';
import 'package:wio_feature_pricing_plan_ui/l10n/pricing_plan_localization.g.dart';

class PlanBenefitDetail extends StatelessWidget {
  static const _rowPadding = 10.0;
  static const _bottomPadding = 18.0;
  static const _maxRowsShouldBeShown = 3;
  static const _iconSize = 22.0;

  final PlanContentDetails planContentDetails;

  const PlanBenefitDetail({
    required this.planContentDetails,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = PricingPlanLocalizations.of(context);

    return LayoutBuilder(
      builder: (sizeContext, availableSize) {
        if (planContentDetails.benefits.isEmpty) {
          return const SizedBox.shrink();
        }

        final exclusive = planContentDetails.exclusive;
        if (exclusive == null) {
          return const SizedBox.shrink();
        }

        final heightAndMaxHeight = _geMaxPossibleHeight(exclusive, context);
        final totalBenefitsCount = planContentDetails.totalBenefits;
        final availableHeight = availableSize.maxHeight;

        // Total detail height with least padding from the CTAs
        final totalDetailHeight = heightAndMaxHeight.$1 + _bottomPadding;

        var afterComputeRowsCanBeShown = _maxRowsShouldBeShown;
        if (availableHeight != 0.0) {
          // Identify the possible overflow rows if any
          // and reduce the number of rows can be from
          // details UI
          if (totalDetailHeight > availableHeight) {
            final excessSpace = totalDetailHeight - availableHeight;
            final extraRows = (excessSpace / heightAndMaxHeight.$2).round();
            afterComputeRowsCanBeShown = max(
              0,
              _maxRowsShouldBeShown - extraRows,
            );
          }
        }

        // calculate the benefit counts left to be shown on UI
        // example : total = 8, overflow row = 2, otherBenefit = 6
        // "+ 6 other benefits"
        final otherBenefits = totalBenefitsCount -
            min(afterComputeRowsCanBeShown, _maxRowsShouldBeShown);
        final hasOtherBenefits = otherBenefits > 0 &&
            planContentDetails.planType != PlanType.salaryPlus;
        final hasSalaryBenefits = otherBenefits > 0 &&
            planContentDetails.planType == PlanType.salaryPlus;

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...exclusive.benefits
                .take(availableHeight != 0.0 ? afterComputeRowsCanBeShown : 0)
                .map(
                  (e) => Padding(
                    padding: const EdgeInsets.only(bottom: _rowPadding),
                    child: _BenefitItemBody(e),
                  ),
                ),
            if (hasOtherBenefits)
              Padding(
                padding: const EdgeInsets.only(bottom: _rowPadding),
                child: _BenefitItemBody(
                  BenefitContentItem(
                    benefitType: BenefitType.other,
                    benefitDetail: localizations.otherBenefitsRowText(
                      otherBenefits.toString(),
                    ),
                  ),
                ),
              ),
            if (hasSalaryBenefits)
              Padding(
                padding: const EdgeInsets.only(bottom: _rowPadding),
                child: _BenefitItemBody(
                  BenefitContentItem(
                    benefitType: BenefitType.other,
                    benefitDetail: localizations.salaryPlanOtherBenefitsTitle,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Size _getTextSize(
    String text,
    BuildContext context, {
    double maxWidth = double.infinity,
  }) {
    final textPainter = TextPainter()
      ..text = TextSpan(
        text: text,
        style: CompanyTextStylePointer.b3medium.textStyleOf(context),
      )
      ..textDirection = Directionality.of(context)
      ..textScaler = TextScaler.noScaling
      ..maxLines = 2
      ..layout(maxWidth: maxWidth);

    return textPainter.size;
  }

  (double, double) _geMaxPossibleHeight(
    BenefitContent exclusive,
    BuildContext context,
  ) {
    var totalTextHeight = 0.0;
    var maxRowHeight = 0.0;

    exclusive.benefits.take(_maxRowsShouldBeShown).forEach((it) {
      final rowHeight = _getTextSize(it.benefitDetail, context).height;
      totalTextHeight += rowHeight;
      maxRowHeight = max(max(_iconSize, rowHeight), maxRowHeight);
    });

    totalTextHeight += _maxRowsShouldBeShown * _rowPadding;

    return (totalTextHeight, maxRowHeight);
  }
}

class _BenefitItemBody extends StatelessWidget {
  final BenefitContentItem item;

  const _BenefitItemBody(this.item);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        CompanyIcon(
          CompanyIconModel(
            icon: _getIcon(item),
            size: CompanyIconSize.small,
            color: CompanyColorPointer.primary1,
          ),
        ),
        Space.fromSpacingHorizontal(Spacing.s2),
        Expanded(
          child: Label(
            model: LabelModel(
              text: item.benefitDetail,
              textStyle: CompanyTextStylePointer.b3,
              color: CompanyColorPointer.primary3,
              maxLines: 2,
            ),
          ),
        ),
      ],
    );
  }

  GraphicAssetPointer _getIcon(BenefitContentItem item) {
    return switch (item.icon) {
      'SavingsPicto' => CompanyIconPointer.savings.toGraphicAsset(),
      'ExpandSPicto' =>
        CompanyPictogramPointer.functions_expand.toGraphicAsset(),
      'SpeedSPicto' => CompanyIconPointer.speed_spicto.toGraphicAsset(),
      'MessageRightSPicto' =>
        CompanyPictogramPointer.functions_message_right.toGraphicAsset(),
      'DollarSPicto' => CompanyIconPointer.dollar_spicto.toGraphicAsset(),
      'BitcoinSPicto' => CompanyIconPointer.bitcoin_spicto.toGraphicAsset(),
      'Invest' => CompanyIconPointer.invest.toGraphicAsset(),
      'Cards' => CompanyIconPointer.cards.toGraphicAsset(),
      _ => CompanyIconPointer.plus.toGraphicAsset(),
    };
  }
}

class ViewDetailsButton extends StatelessWidget {
  final String ctaText;
  final VoidCallback callback;

  const ViewDetailsButton(this.ctaText, this.callback, {super.key});

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: callback,
        child: Label(
          model: LabelModel(
            text: ctaText,
            textStyle: CompanyTextStylePointer.b3,
            textAlign: LabelTextAlign.center,
          ),
        ),
      );
}

extension on PlanContentDetails {
  BenefitContent? get exclusive => benefits
      .where(
        (element) => element.benefitDetailType == BenefitDetailType.exclusive,
      )
      .firstOrNull;

  BenefitContent? get others => benefits
      .where(
        (element) => element.benefitDetailType == BenefitDetailType.others,
      )
      .firstOrNull;

  int get totalBenefits =>
      (exclusive?.benefits.length ?? 0) + (others?.benefits.length ?? 0);
}
