import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_account_maintenance_api/models/tag_cloud_template/selected_tag_model.dart';
import 'package:wio_feature_pricing_plan_api/feature_toggle/models/plan_advertisements.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';

abstract class PricingPlanInteractor implements Clearable {
  /// Returns [PricingPlansContent], that consist of available plans
  /// for the given [source].
  Future<PricingPlansContent> getPricingPlan(PricingPlanSource source);

  /// Used to submit pricing plan only during onboarding phase.
  Future<void> submitPricingPlan(
    String planId, [
    List<SelectedTagModel> tags = const [],
  ]);

  /// Used to connect new pricing plan from settings.
  ///
  /// Changes current subscription plan to the given [planId]
  /// for already onboarded user.
  Future<void> connectPricingPlan(String planId);

  /// Returns user's current [PricingPlan].
  Future<PricingPlan> getUserPlan();

  /// Returns [PlanAdvertisements] for the current user,
  /// depending on currently active [PricingPlan]
  /// and plan advertisements from feature toggles.
  Future<PlanAdvertisements> getUserPlanAdvertisements();

  /// Yields user's current [PricingPlan], whenever it updates.
  Stream<Data<PricingPlan>> getUserPlanStream();

  /// Yields [PlanAdvertisements] depending on user current plan,
  /// whenever plan updates.
  Stream<PlanAdvertisements> getPlanAdvertisementsStream();

  /// Returns the current subscription plan details.
  Future<SubscriptionPlanInfo> getSubscriptionPlanInfo();

  /// Returns the details of all subscription plans.
  Future<List<SubscriptionPlanInfo>> getSubscriptionPlans();

  /// Returns the details of a special (Plus) plan if one is available.
  Future<SubscriptionPlanInfo?> getSpecialPlanInfo();

  /// Fetches all available plan details from content-full.
  Future<List<PlanContentDetails>> getAllPlanContent();

  /// Fetches 'PromoPlanConfig' for PAYROLL_UPSELL feature
  Future<PromoPlanConfig> getPromoPlanConfig();
}
