// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'plan_advertisements.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PlanAdvertisements {
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String planId, String? settings, String? savingSpaces, String? ipos)
        $default, {
    required TResult Function() empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String planId, String? settings, String? savingSpaces,
            String? ipos)?
        $default, {
    TResult? Function()? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String planId, String? settings, String? savingSpaces,
            String? ipos)?
        $default, {
    TResult Function()? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PlanAdvertisements value) $default, {
    required TResult Function(_EmptyPlanAdvertisements value) empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PlanAdvertisements value)? $default, {
    TResult? Function(_EmptyPlanAdvertisements value)? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PlanAdvertisements value)? $default, {
    TResult Function(_EmptyPlanAdvertisements value)? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PlanAdvertisementsCopyWith<$Res> {
  factory $PlanAdvertisementsCopyWith(
          PlanAdvertisements value, $Res Function(PlanAdvertisements) then) =
      _$PlanAdvertisementsCopyWithImpl<$Res, PlanAdvertisements>;
}

/// @nodoc
class _$PlanAdvertisementsCopyWithImpl<$Res, $Val extends PlanAdvertisements>
    implements $PlanAdvertisementsCopyWith<$Res> {
  _$PlanAdvertisementsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$PlanAdvertisementsImplCopyWith<$Res> {
  factory _$$PlanAdvertisementsImplCopyWith(_$PlanAdvertisementsImpl value,
          $Res Function(_$PlanAdvertisementsImpl) then) =
      __$$PlanAdvertisementsImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String planId, String? settings, String? savingSpaces, String? ipos});
}

/// @nodoc
class __$$PlanAdvertisementsImplCopyWithImpl<$Res>
    extends _$PlanAdvertisementsCopyWithImpl<$Res, _$PlanAdvertisementsImpl>
    implements _$$PlanAdvertisementsImplCopyWith<$Res> {
  __$$PlanAdvertisementsImplCopyWithImpl(_$PlanAdvertisementsImpl _value,
      $Res Function(_$PlanAdvertisementsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? planId = null,
    Object? settings = freezed,
    Object? savingSpaces = freezed,
    Object? ipos = freezed,
  }) {
    return _then(_$PlanAdvertisementsImpl(
      planId: null == planId
          ? _value.planId
          : planId // ignore: cast_nullable_to_non_nullable
              as String,
      settings: freezed == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as String?,
      savingSpaces: freezed == savingSpaces
          ? _value.savingSpaces
          : savingSpaces // ignore: cast_nullable_to_non_nullable
              as String?,
      ipos: freezed == ipos
          ? _value.ipos
          : ipos // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$PlanAdvertisementsImpl implements _PlanAdvertisements {
  const _$PlanAdvertisementsImpl(
      {required this.planId, this.settings, this.savingSpaces, this.ipos});

  @override
  final String planId;
  @override
  final String? settings;
  @override
  final String? savingSpaces;
  @override
  final String? ipos;

  @override
  String toString() {
    return 'PlanAdvertisements(planId: $planId, settings: $settings, savingSpaces: $savingSpaces, ipos: $ipos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PlanAdvertisementsImpl &&
            (identical(other.planId, planId) || other.planId == planId) &&
            (identical(other.settings, settings) ||
                other.settings == settings) &&
            (identical(other.savingSpaces, savingSpaces) ||
                other.savingSpaces == savingSpaces) &&
            (identical(other.ipos, ipos) || other.ipos == ipos));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, planId, settings, savingSpaces, ipos);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PlanAdvertisementsImplCopyWith<_$PlanAdvertisementsImpl> get copyWith =>
      __$$PlanAdvertisementsImplCopyWithImpl<_$PlanAdvertisementsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String planId, String? settings, String? savingSpaces, String? ipos)
        $default, {
    required TResult Function() empty,
  }) {
    return $default(planId, settings, savingSpaces, ipos);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String planId, String? settings, String? savingSpaces,
            String? ipos)?
        $default, {
    TResult? Function()? empty,
  }) {
    return $default?.call(planId, settings, savingSpaces, ipos);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String planId, String? settings, String? savingSpaces,
            String? ipos)?
        $default, {
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(planId, settings, savingSpaces, ipos);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PlanAdvertisements value) $default, {
    required TResult Function(_EmptyPlanAdvertisements value) empty,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PlanAdvertisements value)? $default, {
    TResult? Function(_EmptyPlanAdvertisements value)? empty,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PlanAdvertisements value)? $default, {
    TResult Function(_EmptyPlanAdvertisements value)? empty,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _PlanAdvertisements implements PlanAdvertisements {
  const factory _PlanAdvertisements(
      {required final String planId,
      final String? settings,
      final String? savingSpaces,
      final String? ipos}) = _$PlanAdvertisementsImpl;

  String get planId;
  String? get settings;
  String? get savingSpaces;
  String? get ipos;
  @JsonKey(ignore: true)
  _$$PlanAdvertisementsImplCopyWith<_$PlanAdvertisementsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EmptyPlanAdvertisementsImplCopyWith<$Res> {
  factory _$$EmptyPlanAdvertisementsImplCopyWith(
          _$EmptyPlanAdvertisementsImpl value,
          $Res Function(_$EmptyPlanAdvertisementsImpl) then) =
      __$$EmptyPlanAdvertisementsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmptyPlanAdvertisementsImplCopyWithImpl<$Res>
    extends _$PlanAdvertisementsCopyWithImpl<$Res,
        _$EmptyPlanAdvertisementsImpl>
    implements _$$EmptyPlanAdvertisementsImplCopyWith<$Res> {
  __$$EmptyPlanAdvertisementsImplCopyWithImpl(
      _$EmptyPlanAdvertisementsImpl _value,
      $Res Function(_$EmptyPlanAdvertisementsImpl) _then)
      : super(_value, _then);
}

/// @nodoc

class _$EmptyPlanAdvertisementsImpl implements _EmptyPlanAdvertisements {
  const _$EmptyPlanAdvertisementsImpl();

  @override
  String toString() {
    return 'PlanAdvertisements.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmptyPlanAdvertisementsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String planId, String? settings, String? savingSpaces, String? ipos)
        $default, {
    required TResult Function() empty,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String planId, String? settings, String? savingSpaces,
            String? ipos)?
        $default, {
    TResult? Function()? empty,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String planId, String? settings, String? savingSpaces,
            String? ipos)?
        $default, {
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_PlanAdvertisements value) $default, {
    required TResult Function(_EmptyPlanAdvertisements value) empty,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_PlanAdvertisements value)? $default, {
    TResult? Function(_EmptyPlanAdvertisements value)? empty,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_PlanAdvertisements value)? $default, {
    TResult Function(_EmptyPlanAdvertisements value)? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class _EmptyPlanAdvertisements implements PlanAdvertisements {
  const factory _EmptyPlanAdvertisements() = _$EmptyPlanAdvertisementsImpl;
}
