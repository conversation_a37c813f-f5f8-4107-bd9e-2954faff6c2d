// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'email_feature_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$EmailFeatureNavigationConfig {
  ScreenNavigationConfig? get destination => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $EmailFeatureNavigationConfigCopyWith<EmailFeatureNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmailFeatureNavigationConfigCopyWith<$Res> {
  factory $EmailFeatureNavigationConfigCopyWith(
          EmailFeatureNavigationConfig value,
          $Res Function(EmailFeatureNavigationConfig) then) =
      _$EmailFeatureNavigationConfigCopyWithImpl<$Res>;
  $Res call({ScreenNavigationConfig? destination});
}

/// @nodoc
class _$EmailFeatureNavigationConfigCopyWithImpl<$Res>
    implements $EmailFeatureNavigationConfigCopyWith<$Res> {
  _$EmailFeatureNavigationConfigCopyWithImpl(this._value, this._then);

  final EmailFeatureNavigationConfig _value;
  // ignore: unused_field
  final $Res Function(EmailFeatureNavigationConfig) _then;

  @override
  $Res call({
    Object? destination = freezed,
  }) {
    return _then(_value.copyWith(
      destination: destination == freezed
          ? _value.destination
          : destination // ignore: cast_nullable_to_non_nullable
              as ScreenNavigationConfig?,
    ));
  }
}

/// @nodoc
abstract class _$$_EmailFeatureNavigationConfigCopyWith<$Res>
    implements $EmailFeatureNavigationConfigCopyWith<$Res> {
  factory _$$_EmailFeatureNavigationConfigCopyWith(
          _$_EmailFeatureNavigationConfig value,
          $Res Function(_$_EmailFeatureNavigationConfig) then) =
      __$$_EmailFeatureNavigationConfigCopyWithImpl<$Res>;
  @override
  $Res call({ScreenNavigationConfig? destination});
}

/// @nodoc
class __$$_EmailFeatureNavigationConfigCopyWithImpl<$Res>
    extends _$EmailFeatureNavigationConfigCopyWithImpl<$Res>
    implements _$$_EmailFeatureNavigationConfigCopyWith<$Res> {
  __$$_EmailFeatureNavigationConfigCopyWithImpl(
      _$_EmailFeatureNavigationConfig _value,
      $Res Function(_$_EmailFeatureNavigationConfig) _then)
      : super(_value, (v) => _then(v as _$_EmailFeatureNavigationConfig));

  @override
  _$_EmailFeatureNavigationConfig get _value =>
      super._value as _$_EmailFeatureNavigationConfig;

  @override
  $Res call({
    Object? destination = freezed,
  }) {
    return _then(_$_EmailFeatureNavigationConfig(
      destination: destination == freezed
          ? _value.destination
          : destination // ignore: cast_nullable_to_non_nullable
              as ScreenNavigationConfig?,
    ));
  }
}

/// @nodoc

class _$_EmailFeatureNavigationConfig extends _EmailFeatureNavigationConfig {
  const _$_EmailFeatureNavigationConfig({this.destination}) : super._();

  @override
  final ScreenNavigationConfig? destination;

  @override
  String toString() {
    return 'EmailFeatureNavigationConfig(destination: $destination)';
  }

  @JsonKey(ignore: true)
  @override
  _$$_EmailFeatureNavigationConfigCopyWith<_$_EmailFeatureNavigationConfig>
      get copyWith => __$$_EmailFeatureNavigationConfigCopyWithImpl<
          _$_EmailFeatureNavigationConfig>(this, _$identity);
}

abstract class _EmailFeatureNavigationConfig
    extends EmailFeatureNavigationConfig {
  const factory _EmailFeatureNavigationConfig(
          {final ScreenNavigationConfig? destination}) =
      _$_EmailFeatureNavigationConfig;
  const _EmailFeatureNavigationConfig._() : super._();

  @override
  ScreenNavigationConfig? get destination => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_EmailFeatureNavigationConfigCopyWith<_$_EmailFeatureNavigationConfig>
      get copyWith => throw _privateConstructorUsedError;
}
