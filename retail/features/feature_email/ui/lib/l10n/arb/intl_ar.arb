{"@@locale": "ar", "@@last_modified": "2024-04-04T09:11:11+02:00", "accountAlreadyExist": "الحساب موجود بالفعل.", "accountWithEmailAlreadyExist": "الحساب مع {email} موجود بالفعل. الرجاء الاستمرار في تسجيل الدخول.", "@accountWithEmailAlreadyExist": {"placeholders": {"email": {"example": "email", "type": "String"}}}, "allowLoginRequestOnUaePass": "يرجى السماح بطلب تسجيل الدخول على تطبيق UAE PASS الخاص بك للمتابعة.", "allowSignupRequestOnUaePass": "يرجى السماح بطلب التسجيل على تطبيق UAE PASS الخاص بك للمتابعة.", "continueButtonTitle": "استمرار", "continueWithUaePass": "استمرار مع تطبيق UAE PASS", "emailAlreadyRegistered": "عنوان البريد الإلكترونى هذا مسجل بالفعل", "emailAlreadyRegisteredInSystem": "البريد الإلكتروني {email} المسجل بالفعل في النظام", "@emailAlreadyRegisteredInSystem": {"placeholders": {"email": {"example": "<EMAIL>", "type": "String"}}}, "emailInputScreenHint": "بريد إلكتروني", "emailInputScreenTitle": "ما هو عنوان البريد الإلكتروني الخاص بك؟", "emailLoginPageWeNeedYouEmail": "نحتاج إلى بريدك الإلكتروني للتعرف عليك", "emailUpdatedSuccessfully": "تم تحديث البريد الإلكتروني بنجاح", "emailWrongFormat": "تنسيق بريد إلكتروني خاطئ", "generalErrorScreenTitleError": "نعتذر!", "loginCancelled": "<PERSON><PERSON>غى المستخدم تسجيل الدخول", "or": "أو", "signInByUaePassButtonTitle": "تسجيل الدخول عن طريق UAE PASS", "signInWithEmail": "تسجيل الدخول باستخدام البريد الإلكتروني", "signUpWithEmail": "سجل عن طريق البريد الإلكتروني", "signupCancelled": "<PERSON><PERSON>غى المستخدم التسجيل", "somethingWentWrongDuringLogin": "حدث خطأ ما أثناء تسجيل الدخول ، يرجى المحاولة مرة أخرى لاحقًا!", "takeMeBackToLogin": "أعدني لتسجيل الدخول", "uaePassErrorEmailMismatchBody": "تأكد من أنك تستخدم نفس عناوين البريد الإلكتروني لحسابات Wio وUASS الخاصة بك لمتابعة تسجيل الدخول.", "uaePassErrorEmailMismatchHeader": "عدم تطابق البريد الإلكتروني", "unverifiedUserRestriction": "قيود على مستخدم لم يتم التحقق منه", "walkthroughSignUpByUaePassButtonTitle": "سجل مع UAE PASS", "weCouldntFindAccount": "لم نتمكن من العثور على الحساب", "weCouldntFindAccountWithEmail": "لم نتمكن من العثور على حساب مع {email}", "@weCouldntFindAccountWithEmail": {"placeholders": {"email": {"example": "<EMAIL>", "type": "String"}}}, "youAreNotEligibleToAccessThisService": "أنت غير مؤهل لهذه الخدمة. لم يتم ترقية حسابك أو لديك حساب زائر.\n\nيرجى الاتصال بـ Wio Care للحصول على مزيد من التفاصيل."}