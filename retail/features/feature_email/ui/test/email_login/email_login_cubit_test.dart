import 'dart:ui';

import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_digital_trust_api/feature_digital_trust_api.dart';
import 'package:wio_feature_email_api/navigation/email_feature_navigation_config.dart';
import 'package:wio_feature_email_ui/feature_email_ui.dart';
import 'package:wio_feature_email_ui/l10n/email_localization.g.dart';
import 'package:wio_feature_onboarding_domain_api/index.dart';
import 'package:wio_feature_retail_identity_domain_api/index.dart';
import 'package:wio_feature_uae_pass_auth_api/feature_toggles/uae_pass_feature_toggles.dart';
import 'package:wio_feature_uae_pass_auth_api/navigation/uae_pass_exception_bottom_sheet_navigation_config.dart';

import '../feature_email_ui_mocks.dart';

void main() {
  late EmailLoginCubit cubit;

  late MockNavigationProvider navigationProvider;
  late MockLogger logger;
  late EmailLocalizations emailLocalizations;
  late MockAuthInteractor authInteractor;
  late MockExhaustStreamExecutor exhaustStreamExecutor;
  late MockOnboardingStagesDelegate onboardingStagesDelegate;
  late MockEmailAnalytics analytics;
  late MockFeatureToggleProvider featureToggles;
  late MockUaePassInteractor uaePassInteractor;
  late MockTokenInteractor tokenInteractor;
  late MockCommonErrorHandler commonErrorHandler;
  late MockDigitalTrustInteractor mockDigitalTrustInteractor;
  late MockPlatformInfo mockPlatformInfo;
  late MockDigitalTrustAnalytics mockDigitalTrustAnalytics;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    emailLocalizations = await EmailLocalizations.load(const Locale('en'));
  });

  setUp(() {
    navigationProvider = MockNavigationProvider();
    logger = MockLogger();
    authInteractor = MockAuthInteractor();
    exhaustStreamExecutor = MockExhaustStreamExecutor();
    navigationProvider = MockNavigationProvider();
    onboardingStagesDelegate = MockOnboardingStagesDelegate();
    analytics = MockEmailAnalytics();
    featureToggles = MockFeatureToggleProvider();
    uaePassInteractor = MockUaePassInteractor();
    tokenInteractor = MockTokenInteractor();
    commonErrorHandler = MockCommonErrorHandler();
    mockDigitalTrustInteractor = MockDigitalTrustInteractor();
    mockPlatformInfo = MockPlatformInfo();
    mockDigitalTrustAnalytics = MockDigitalTrustAnalytics();

    cubit = EmailLoginCubit(
      emailLocalizations: emailLocalizations,
      logger: logger,
      authInteractor: authInteractor,
      exhaustStreamExecutor: exhaustStreamExecutor,
      navigator: navigationProvider,
      onboardingStagesDelegate: onboardingStagesDelegate,
      analytics: analytics,
      featureToggles: featureToggles,
      uaePassAuthInteractor: uaePassInteractor,
      tokenInteractor: tokenInteractor,
      commonErrorHandler: commonErrorHandler,
      digitalTrustInteractor: mockDigitalTrustInteractor,
      platformInfo: mockPlatformInfo,
      digitalTrustAnalytics: mockDigitalTrustAnalytics,
    );

    registerFallbackValue(BottomSheetNavigationConfigFake());
    when(
      () => featureToggles
          .get(DigitalTrustFeatureToggle.isDigitalTrustTrackingEnabled),
    ).thenAnswer(
      (_) => true,
    );
    when(
      () => navigationProvider
          .showBottomSheet<UaePassExceptionBottomSheetResult>(any()),
    ).justCompleteAsync();
    when(() => mockDigitalTrustInteractor.setSessionId({'': ''}))
        .justAnswerAsync('DT');
    when(() => mockDigitalTrustInteractor.setUserId('userId'))
        .justAnswerAsync('DT');
    when(
      () => mockDigitalTrustAnalytics.sessionUpdated(
        any(),
      ),
    ).thenReturn(null);

    when(() => mockDigitalTrustInteractor.clearSession()).justCompleteAsync();
    when(
      () => mockDigitalTrustAnalytics.sdkStarted(
        any(),
      ),
    ).thenReturn(null);

    when(
      () => mockDigitalTrustAnalytics.sdkExceptionReceived(
        any(),
      ),
    ).thenReturn(null);
    registerFallbackValue(MockToastMessageConfigurationFake());
    when(() => mockDigitalTrustInteractor.setSessionId(any())).justAnswerAsync(
      'value',
    );
    when(() => authInteractor.getCurrentUserAndSaveLocally()).justAnswerAsync(
      const UserIdentity(
        email: 'email',
        factors: [],
        customerIdentifier: 'customerIdentifier',
      ),
    );

    when(() => mockPlatformInfo.isIOS).thenReturn(false);
  });

  const mockedSessionResponse = Token(
    accessToken: 'accessToken',
    refreshToken: 'refreshToken',
    id: 'id',
  );

  test('validate initial state', () async {
    // arrange
    final hintText = emailLocalizations.emailInputScreenHint;
    when(
      () => featureToggles.get(UaePassFeatureToggles.enableLoginUaePassFeature),
    ).thenAnswer((_) => true);

    // act
    cubit.init();

    // assert
    expect(cubit.state.model.hint, hintText);
  });

  test(
    'When attempting to submit email with invalid format error '
    "should contain data & submit button isn't available",
    () async {
      // Arrange
      const expectedErrorIsNotEmpty = true;
      const expectedSubmitButtonAvailable = false;

      // Act
      cubit
        ..onEmailChanged('<EMAIL>@gmail.com')
        ..onEmailSubmitAttempt('<EMAIL>@gmail.com');

      // Assert
      expect(cubit.state.model.error!.isNotEmpty, expectedErrorIsNotEmpty);
      expect(cubit.state.submitAvailable, expectedSubmitButtonAvailable);
    },
  );

  test(
    'When attempting to submit email with valid format error '
    "shouldn't contain data & submit button is available",
    () async {
      // Arrange
      const expectedErrorIsEmpty = true;
      const expectedSubmitButtonAvailable = true;

      // Act
      cubit.onEmailChanged('<EMAIL>');

      // Assert
      expect(cubit.state.model.error!.isEmpty, expectedErrorIsEmpty);
      expect(cubit.state.submitAvailable, expectedSubmitButtonAvailable);
    },
  );

  group('Create session request and it behaviour', () {
    test(
      'Success sending request and call checkStagesAndNavigateNextPage',
      () async {
        // arrange
        when(
          () => authInteractor.createSession(
            twoFactorId: any(named: 'twoFactorId'),
            email: any(named: 'email'),
          ),
        ).justAnswerAsync(mockedSessionResponse);

        when(() => onboardingStagesDelegate.getNextStageNavigationConfig())
            .justAnswerAsync(const EmailFeatureNavigationConfig());
        when(() => mockDigitalTrustInteractor.setSessionId(any()))
            .justAnswerAsync(
          'value',
        );
        when(() => authInteractor.getCurrentUserAndSaveLocally())
            .justAnswerAsync(
          const UserIdentity(
            email: 'email',
            factors: [],
            customerIdentifier: 'customerIdentifier',
          ),
        );

        when(() => mockPlatformInfo.isIOS).thenReturn(false);

        // act
        cubit.emit(cubit.state.copyWith(submitAvailable: true));
        await cubit.submitEmail('<EMAIL>');
        await flushFutures();

        // assert
        verify(() => onboardingStagesDelegate.getNextStageNavigationConfig());
        verify(
          () => navigationProvider.removeStackAndPush(
            const EmailFeatureNavigationConfig(),
          ),
        );
        verify(() => mockPlatformInfo.isIOS).calledOnce;
        verify(() => authInteractor.getCurrentUserAndSaveLocally()).calledOnce;
        verify(() => mockDigitalTrustInteractor.setSessionId(any())).calledOnce;
        verify(() => mockDigitalTrustAnalytics.sessionUpdated(any()))
            .calledOnce;
        verify(() => mockDigitalTrustInteractor.setUserId(any())).calledOnce;
      },
    );

    test(
      'Digital trust called failed should send failure event',
      () async {
        // arrange
        when(
          () => authInteractor.createSession(
            twoFactorId: any(named: 'twoFactorId'),
            email: any(named: 'email'),
          ),
        ).justAnswerAsync(mockedSessionResponse);

        when(() => onboardingStagesDelegate.getNextStageNavigationConfig())
            .justAnswerAsync(const EmailFeatureNavigationConfig());
        when(() => mockDigitalTrustInteractor.setSessionId(any()))
            .justAnswerAsync(
          'value',
        );
        when(() => authInteractor.getCurrentUserAndSaveLocally())
            .justThrowAsync(Exception('Exception'));

        when(() => mockPlatformInfo.isIOS).thenReturn(false);

        // act
        cubit.emit(cubit.state.copyWith(submitAvailable: true));
        await cubit.submitEmail('<EMAIL>');
        await flushFutures();

        // assert
        verify(() => onboardingStagesDelegate.getNextStageNavigationConfig());
        verify(
          () => navigationProvider.removeStackAndPush(
            const EmailFeatureNavigationConfig(),
          ),
        );
        verify(() => authInteractor.getCurrentUserAndSaveLocally()).calledOnce;
        verify(
          () => mockDigitalTrustAnalytics.sdkExceptionReceived(any()),
        ).calledOnce;
      },
    );

    test(
      'Sign in with UAE PASS',
      () async {
        // arrange
        when(
          () => uaePassInteractor.getAccessCode(),
        ).justAnswerAsync('uaePassAuthCode');

        when(
          () => uaePassInteractor.getUser(code: any(named: 'code')),
        ).justAnswerAsync(
          const UserDetailsWithToken(
            allowedAction: UserDetailsAllowedAction.login,
            token: 'uaepassToken',
            userAttributes: UserAttributes(
              email: 'email',
              mobile: 'mobile',
              uaePassId: 'uaepassid',
            ),
          ),
        );

        when(
          () => authInteractor.createSession(
            uaePassToken: any(named: 'uaePassToken'),
          ),
        ).justAnswerAsync(mockedSessionResponse);

        when(
          () => onboardingStagesDelegate.getNextStageNavigationConfig(
            uaePassEntrypoint: any(
              named: 'uaePassEntrypoint',
            ),
          ),
        ).justAnswerAsync(const EmailFeatureNavigationConfig());

        // act
        cubit
          ..emit(cubit.state.copyWith(isUaePassLoginButtonEnabled: true))
          ..signInByUaePassButtonClicked().ignore();

        await flushFutures();

        // assert
        verify(
          () => onboardingStagesDelegate.getNextStageNavigationConfig(
            uaePassEntrypoint: any(
              named: 'uaePassEntrypoint',
            ),
          ),
        );
        verify(
          () => tokenInteractor.saveUaePassToken(token: any(named: 'token')),
        );
        verify(
          () => navigationProvider.removeStackAndPush(
            const EmailFeatureNavigationConfig(),
          ),
        );
      },
    );
  });

  group('Test fatal syntax error during typing', () {
    test(
      'Test error when email syntax does not follow rules',
      () async {
        // Assign
        final wrongEmails = [
          'invalidFormat__',
          '_invalid',
          'invalid..',
          '.invalid',
          'invalid.@',
          'demo__demo',
          '<EMAIL>@gmail',
          'demo@gm/ail.com',
          '<EMAIL>/',
        ];

        // Act
        for (final email in wrongEmails) {
          cubit.onEmailChanged(email);
          expect(cubit.state.model.error!.isNotEmpty, true);
        }
      },
    );

    test(
      'Test no error when invalid format can be corrected during typing',
      () async {
        // Assign
        final testEmails = [
          'demo_',
          'demo',
          'demo.',
          'demo.demo',
          'demo@',
          'demo@gmail',
        ];

        // Act
        for (final email in testEmails) {
          cubit.onEmailChanged(email);
          expect(cubit.state.model.error!.isEmpty, true);
        }
      },
    );
  });

  group('Test show/hide email suggestions feature', () {
    test(
      'Hide suggestions when email is invalid',
      () async {
        // Act
        cubit.onEmailChanged('invalidFormat-@');

        // Assert
        expect(cubit.state.emailSuggestionsShown, false);
      },
    );

    test(
      'Show suggestions when email is not empty',
      () async {
        // Act
        cubit.onEmailChanged('a');

        // Assert
        expect(cubit.state.emailSuggestionsShown, true);
      },
    );

    test(
      'Hide suggestions when email already contains domain name',
      () async {
        // Act
        cubit.onEmailChanged('<EMAIL>');

        // Assert
        expect(cubit.state.emailSuggestionsShown, false);
      },
    );

    test(
      'Hide suggestions when syntax with domain name will be invalid',
      () async {
        // Act
        cubit.onEmailChanged('aaaa-');

        // Assert
        expect(cubit.state.emailSuggestionsShown, false);
      },
    );
  });

  group('Test on suggestion selected', () {
    test(
      'Selected suggestion when email does not contain @ sign',
      () async {
        const emailSuggestionIndex = 0;
        final emailSuggestion =
            cubit.state.emailSuggestions[emailSuggestionIndex];

        // Act
        cubit
          ..onEmailChanged('demo')
          ..onEmailSuggestionSelected(emailSuggestionIndex);

        // Assert
        expect(cubit.state.email, 'demo$emailSuggestion');
      },
    );

    test(
      'Selected suggestion when @ sign exists in email',
      () async {
        const emailSuggestionIndex = 0;
        final emailSuggestion =
            cubit.state.emailSuggestions[emailSuggestionIndex];

        // Act
        cubit
          ..onEmailChanged('demo@')
          ..onEmailSuggestionSelected(emailSuggestionIndex);

        // Assert
        expect(cubit.state.email, 'demo$emailSuggestion');
      },
    );
  });

  group('Analytics', () {
    test('Should send an event when user enters the email correctly', () async {
      // Act
      cubit.onEmailChanged('<EMAIL>');

      // Assert
      verify(() => analytics.inputEmailInputFieldSuccess());
    });

    test(
      'Should send an event when user enters the email in '
      'incorrect format and the error flashes on the screen',
      () async {
        // Arrange

        // Act
        cubit.onEmailChanged('someemail@@');

        // Assert
        verify(() => analytics.inputEmailInputFieldError());
      },
    );

    test('Should send an event when user selects an email domain', () async {
      // Act
      cubit
        ..emit(cubit.state.copyWith(emailSuggestionsShown: true))
        ..onEmailSuggestionSelected(0);

      // Assert
      verify(() => analytics.clickEmailSuggestion());
    });

    test(
      'Should send an event when domain options'
      ' are given to user to select from',
      () async {
        // Act
        cubit.onEmailChanged('someemail@');

        // Assert
        verify(() => analytics.viewEmailSuggestion());
      },
    );

    test(
      'Should send an event when user swipes the email domain list '
      'to look at all the options',
      () async {
        // Act
        cubit.onEmailSuggestionScrollEnd(isUserEvent: true);

        // Assert
        verify(() => analytics.swipeEmailSuggestion());
      },
    );

    test('Should send an event when proceed button enabled and pressed', () {
      // Arrange
      when(
        () => authInteractor.createSession(
          twoFactorId: any(named: 'twoFactorId'),
          email: any(named: 'email'),
        ),
      ).justAnswerAsync(mockedSessionResponse);
      when(() => onboardingStagesDelegate.getNextStageNavigationConfig())
          .justAnswerAsync(const EmailFeatureNavigationConfig());

      // Act
      cubit
        ..onEmailChanged('<EMAIL>')
        ..submitEmail('<EMAIL>');

      // Assert
      verify(() => analytics.clickEmailLogin());
    });

    test('Should send an event when back arrow pressed', () {
      // Act
      cubit.onBackPressed();

      // Assert
      verify(() => analytics.clickBackButton());
    });

    test('Should send an event when UAE pass login clicked', () {
      // Arrange
      when(
        () => uaePassInteractor.getAccessCode(),
      ).justAnswerAsync('uaePassAuthCode');

      // Act
      cubit.signInByUaePassButtonClicked();

      // Assert
      verify(() => analytics.clickUaePassLogin());
    });
  });
}
