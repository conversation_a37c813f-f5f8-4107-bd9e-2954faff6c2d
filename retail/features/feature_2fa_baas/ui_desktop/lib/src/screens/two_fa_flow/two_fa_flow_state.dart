import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_common_feature_2fa_baas_api/baas_two_factor_auth_api.dart';
part 'two_fa_flow_state.freezed.dart';

@freezed
class TwoFaFlowState with _$TwoFaFlowState {
  const factory TwoFaFlowState.idle() = _TwoFaFlowStateIdle;

  const factory TwoFaFlowState.startedFlow(TwoFaParams twoFaParams) =
      _TwoFaFlowStateStartedFlow;

  const factory TwoFaFlowState.passChallenge({
    required TwoFaParams twoFaParams,
    required TwoFactorChallenge challenge,
    required TwoFactorAuthType? nextChallengeType,
    required TwoFaUiParamType uiParamType,
  }) = TwoFaFlowStatePassChallenge;

  const factory TwoFaFlowState.passed({
    required TwoFactorChallengeResult result,
  }) = TwoFaFlowStatePassedChallenge;

  /// Note: TwoFaPossibleScenariosModel should not be returned
  /// from this state.
  const factory TwoFaFlowState.finished({
    required TwoFaResultModel result,
  }) = _TwoFaFlowFinished;
}
