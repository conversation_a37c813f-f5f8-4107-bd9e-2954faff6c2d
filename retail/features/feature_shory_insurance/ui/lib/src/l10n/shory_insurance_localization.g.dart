// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class ShoryInsuranceLocalization {
  ShoryInsuranceLocalization._internal();

  static const LocalizationsDelegate<ShoryInsuranceLocalization> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'shoryCardNumberDetail': ['cardNumber'],
    'shoryCvvDetail': ['cvv'],
    'shoryExpiryDateDetails': ['expiry'],
    'shoryInsuranceExitBottomSheetCta': [],
    'shoryInsuranceExitBottomSheetDescription': [],
    'shoryInsuranceExitBottomSheetTitle': [],
    'shoryIntroBottomSheetCtaText': [],
    'shoryIntroBottomSheetParaOne': [],
    'shoryIntroBottomSheetParaTwo': [],
    'shoryIntroBottomSheetPointOne': [],
    'shoryIntroBottomSheetPointTwo': [],
    'shoryIntroBottomSheetTitle': [],
    'shoryOnCopyCardNumber': [],
    'shoryOnCopyCvv': [],
    'shoryOnCopyExpiryDate': [],
    'shoryOpenSelectCardBottomSheetText': [],
    'shorySelectCardBottomsheetErrorSubtitle': [],
    'shorySelectCardBottomsheetErrorText': [],
    'shorySelectCardBottomsheetSubtitle': [],
    'shorySelectCardBottomsheetTitle': []
  };

  static Future<ShoryInsuranceLocalization> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = ShoryInsuranceLocalization._internal();
      return instance;
    });
  }

  static ShoryInsuranceLocalization of(BuildContext context) {
    final instance = Localizations.of<ShoryInsuranceLocalization>(
        context, ShoryInsuranceLocalization);
    assert(instance != null,
        'No instance of ShoryInsuranceLocalization present in the widget tree. Did you add ShoryInsuranceLocalization.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `Card: {cardNumber}`
  String shoryCardNumberDetail(String cardNumber) {
    return Intl.message(
      'Card: $cardNumber',
      name: 'shoryCardNumberDetail',
      desc: '',
      args: [cardNumber],
    );
  }

  /// `CVV: {cvv}`
  String shoryCvvDetail(String cvv) {
    return Intl.message(
      'CVV: $cvv',
      name: 'shoryCvvDetail',
      desc: '',
      args: [cvv],
    );
  }

  /// `Expiry: {expiry}`
  String shoryExpiryDateDetails(String expiry) {
    return Intl.message(
      'Expiry: $expiry',
      name: 'shoryExpiryDateDetails',
      desc: '',
      args: [expiry],
    );
  }

  /// `Go back to Wio`
  String get shoryInsuranceExitBottomSheetCta {
    return Intl.message(
      'Go back to Wio',
      name: 'shoryInsuranceExitBottomSheetCta',
      desc: '',
      args: [],
    );
  }

  /// `Closing this window will exit Shory, and you’ll lose any progress. You’ll be redirected back to Wio.`
  String get shoryInsuranceExitBottomSheetDescription {
    return Intl.message(
      'Closing this window will exit Shory, and you’ll lose any progress. You’ll be redirected back to Wio.',
      name: 'shoryInsuranceExitBottomSheetDescription',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to exit?`
  String get shoryInsuranceExitBottomSheetTitle {
    return Intl.message(
      'Are you sure you want to exit?',
      name: 'shoryInsuranceExitBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Agree and continue`
  String get shoryIntroBottomSheetCtaText {
    return Intl.message(
      'Agree and continue',
      name: 'shoryIntroBottomSheetCtaText',
      desc: '',
      args: [],
    );
  }

  /// `We've partnered with Shory, a leading insurance comparison platform, to bring you trusted coverage, fully digital and simple.`
  String get shoryIntroBottomSheetParaOne {
    return Intl.message(
      'We\'ve partnered with Shory, a leading insurance comparison platform, to bring you trusted coverage, fully digital and simple.',
      name: 'shoryIntroBottomSheetParaOne',
      desc: '',
      args: [],
    );
  }

  /// `You’ll be redirected to Shory’s secure platform to complete your quote and purchase.`
  String get shoryIntroBottomSheetParaTwo {
    return Intl.message(
      'You’ll be redirected to Shory’s secure platform to complete your quote and purchase.',
      name: 'shoryIntroBottomSheetParaTwo',
      desc: '',
      args: [],
    );
  }

  /// `Wio does not underwrite or manage insurance policies.`
  String get shoryIntroBottomSheetPointOne {
    return Intl.message(
      'Wio does not underwrite or manage insurance policies.',
      name: 'shoryIntroBottomSheetPointOne',
      desc: '',
      args: [],
    );
  }

  /// `Post purchase, you will receive details relating to your policy and claims process`
  String get shoryIntroBottomSheetPointTwo {
    return Intl.message(
      'Post purchase, you will receive details relating to your policy and claims process',
      name: 'shoryIntroBottomSheetPointTwo',
      desc: '',
      args: [],
    );
  }

  /// `Your insurance journey`
  String get shoryIntroBottomSheetTitle {
    return Intl.message(
      'Your insurance journey',
      name: 'shoryIntroBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Copied card number`
  String get shoryOnCopyCardNumber {
    return Intl.message(
      'Copied card number',
      name: 'shoryOnCopyCardNumber',
      desc: '',
      args: [],
    );
  }

  /// `Copied CVV`
  String get shoryOnCopyCvv {
    return Intl.message(
      'Copied CVV',
      name: 'shoryOnCopyCvv',
      desc: '',
      args: [],
    );
  }

  /// `Copied expiry date`
  String get shoryOnCopyExpiryDate {
    return Intl.message(
      'Copied expiry date',
      name: 'shoryOnCopyExpiryDate',
      desc: '',
      args: [],
    );
  }

  /// `View Wio card details`
  String get shoryOpenSelectCardBottomSheetText {
    return Intl.message(
      'View Wio card details',
      name: 'shoryOpenSelectCardBottomSheetText',
      desc: '',
      args: [],
    );
  }

  /// `Unable to fetch cards`
  String get shorySelectCardBottomsheetErrorSubtitle {
    return Intl.message(
      'Unable to fetch cards',
      name: 'shorySelectCardBottomsheetErrorSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong`
  String get shorySelectCardBottomsheetErrorText {
    return Intl.message(
      'Something went wrong',
      name: 'shorySelectCardBottomsheetErrorText',
      desc: '',
      args: [],
    );
  }

  /// `Select which card you’ll use to pay`
  String get shorySelectCardBottomsheetSubtitle {
    return Intl.message(
      'Select which card you’ll use to pay',
      name: 'shorySelectCardBottomsheetSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `Select Wio card`
  String get shorySelectCardBottomsheetTitle {
    return Intl.message(
      'Select Wio card',
      name: 'shorySelectCardBottomsheetTitle',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate
    extends LocalizationsDelegate<ShoryInsuranceLocalization> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) =>
      ShoryInsuranceLocalization.supportedLocales.any((supportedLocale) =>
          supportedLocale.languageCode == locale.languageCode);

  @override
  Future<ShoryInsuranceLocalization> load(Locale locale) =>
      ShoryInsuranceLocalization.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
