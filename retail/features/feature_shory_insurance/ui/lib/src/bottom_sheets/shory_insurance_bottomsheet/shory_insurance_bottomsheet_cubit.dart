import 'package:flutter/material.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_shory_insurance_api/shory_insurance_api.dart';

class ShoryInsuranceBottomsheetCubit extends BaseCubit<void> {
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final ShoryInsuranceAnalytics _analytics;

  ShoryInsuranceBottomsheetCubit({
    required Logger logger,
    required NavigationProvider navigationProvider,
    required ShoryInsuranceAnalytics analytics,
  })  : _logger = logger,
        _navigationProvider = navigationProvider,
        _analytics = analytics,
        super(null);

  @override
  String toString() => 'ShoryInsuranceBottomsheetCubit';

  Future<void> onContinue() async {
    _logger.info('Shory insurance webflow starting');

    _navigationProvider.goBack();

    // Wait for the bottom sheet to be completly dismissed
    await Future<Object?>.delayed(kThemeChangeDuration);

    _analytics.trackWebviewOpened();

    await _navigationProvider
        .navigateTo(const ShoryInsuranceFeatureNavigationConfig());
  }
}
