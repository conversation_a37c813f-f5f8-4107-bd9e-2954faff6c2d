import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_shory_insurance_ui/src/l10n/shory_insurance_localization.g.dart';

final class ExitConfirmationBottomSheet extends StatelessWidget {
  const ExitConfirmationBottomSheet({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = ShoryInsuranceLocalization.of(context);

    return Padding(
      padding: EdgeInsets.all(Spacing.s5.value),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Label(
            model: LabelModel(
              text: l10n.shoryInsuranceExitBottomSheetTitle,
              textStyle: CompanyTextStylePointer.h3medium,
              color: CompanyColorPointer.primary3,
            ),
          ),
          const Space.vertical(16.0),
          Label(
            model: LabelModel(
              text: l10n.shoryInsuranceExitBottomSheetDescription,
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.secondary3,
            ),
          ),
          const Space.vertical(24.0),
          SizedBox(
            width: double.infinity,
            child: Button(
              onPressed: () => Navigator.maybePop(context, true),
              model: ButtonModel(
                title: l10n.shoryInsuranceExitBottomSheetCta,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
