import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class ShimmerContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;

  const ShimmerContainer({
    required this.child,
    this.padding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: CompanyShimmer(
        model: const CompanyShimmerModel(
          baseColor: CompanyColorPointer.background1,
          highlightColor: CompanyColorPointer.secondary6,
        ),
        child: child,
      ),
    );
  }
}
