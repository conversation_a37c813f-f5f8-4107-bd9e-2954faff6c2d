// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fx_analytics_target.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FxAnalyticsPayload {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String fromCurrency, String toCurrency) swap,
    required TResult Function(String buyCurrency, String sellCurrency) convert,
    required TResult Function(String currency) account,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String fromCurrency, String toCurrency)? swap,
    TResult? Function(String buyCurrency, String sellCurrency)? convert,
    TResult? Function(String currency)? account,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String fromCurrency, String toCurrency)? swap,
    TResult Function(String buyCurrency, String sellCurrency)? convert,
    TResult Function(String currency)? account,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CurrencySwapPayload value) swap,
    required TResult Function(_CurrencyConversionPayload value) convert,
    required TResult Function(_AccountSelectionPayload value) account,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CurrencySwapPayload value)? swap,
    TResult? Function(_CurrencyConversionPayload value)? convert,
    TResult? Function(_AccountSelectionPayload value)? account,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CurrencySwapPayload value)? swap,
    TResult Function(_CurrencyConversionPayload value)? convert,
    TResult Function(_AccountSelectionPayload value)? account,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FxAnalyticsPayloadCopyWith<$Res> {
  factory $FxAnalyticsPayloadCopyWith(
          FxAnalyticsPayload value, $Res Function(FxAnalyticsPayload) then) =
      _$FxAnalyticsPayloadCopyWithImpl<$Res, FxAnalyticsPayload>;
}

/// @nodoc
class _$FxAnalyticsPayloadCopyWithImpl<$Res, $Val extends FxAnalyticsPayload>
    implements $FxAnalyticsPayloadCopyWith<$Res> {
  _$FxAnalyticsPayloadCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CurrencySwapPayloadImplCopyWith<$Res> {
  factory _$$CurrencySwapPayloadImplCopyWith(_$CurrencySwapPayloadImpl value,
          $Res Function(_$CurrencySwapPayloadImpl) then) =
      __$$CurrencySwapPayloadImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String fromCurrency, String toCurrency});
}

/// @nodoc
class __$$CurrencySwapPayloadImplCopyWithImpl<$Res>
    extends _$FxAnalyticsPayloadCopyWithImpl<$Res, _$CurrencySwapPayloadImpl>
    implements _$$CurrencySwapPayloadImplCopyWith<$Res> {
  __$$CurrencySwapPayloadImplCopyWithImpl(_$CurrencySwapPayloadImpl _value,
      $Res Function(_$CurrencySwapPayloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fromCurrency = null,
    Object? toCurrency = null,
  }) {
    return _then(_$CurrencySwapPayloadImpl(
      fromCurrency: null == fromCurrency
          ? _value.fromCurrency
          : fromCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      toCurrency: null == toCurrency
          ? _value.toCurrency
          : toCurrency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CurrencySwapPayloadImpl extends _CurrencySwapPayload {
  const _$CurrencySwapPayloadImpl(
      {required this.fromCurrency, required this.toCurrency})
      : super._();

  @override
  final String fromCurrency;
  @override
  final String toCurrency;

  @override
  String toString() {
    return 'FxAnalyticsPayload.swap(fromCurrency: $fromCurrency, toCurrency: $toCurrency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencySwapPayloadImpl &&
            (identical(other.fromCurrency, fromCurrency) ||
                other.fromCurrency == fromCurrency) &&
            (identical(other.toCurrency, toCurrency) ||
                other.toCurrency == toCurrency));
  }

  @override
  int get hashCode => Object.hash(runtimeType, fromCurrency, toCurrency);

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencySwapPayloadImplCopyWith<_$CurrencySwapPayloadImpl> get copyWith =>
      __$$CurrencySwapPayloadImplCopyWithImpl<_$CurrencySwapPayloadImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String fromCurrency, String toCurrency) swap,
    required TResult Function(String buyCurrency, String sellCurrency) convert,
    required TResult Function(String currency) account,
  }) {
    return swap(fromCurrency, toCurrency);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String fromCurrency, String toCurrency)? swap,
    TResult? Function(String buyCurrency, String sellCurrency)? convert,
    TResult? Function(String currency)? account,
  }) {
    return swap?.call(fromCurrency, toCurrency);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String fromCurrency, String toCurrency)? swap,
    TResult Function(String buyCurrency, String sellCurrency)? convert,
    TResult Function(String currency)? account,
    required TResult orElse(),
  }) {
    if (swap != null) {
      return swap(fromCurrency, toCurrency);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CurrencySwapPayload value) swap,
    required TResult Function(_CurrencyConversionPayload value) convert,
    required TResult Function(_AccountSelectionPayload value) account,
  }) {
    return swap(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CurrencySwapPayload value)? swap,
    TResult? Function(_CurrencyConversionPayload value)? convert,
    TResult? Function(_AccountSelectionPayload value)? account,
  }) {
    return swap?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CurrencySwapPayload value)? swap,
    TResult Function(_CurrencyConversionPayload value)? convert,
    TResult Function(_AccountSelectionPayload value)? account,
    required TResult orElse(),
  }) {
    if (swap != null) {
      return swap(this);
    }
    return orElse();
  }
}

abstract class _CurrencySwapPayload extends FxAnalyticsPayload {
  const factory _CurrencySwapPayload(
      {required final String fromCurrency,
      required final String toCurrency}) = _$CurrencySwapPayloadImpl;
  const _CurrencySwapPayload._() : super._();

  String get fromCurrency;
  String get toCurrency;

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencySwapPayloadImplCopyWith<_$CurrencySwapPayloadImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CurrencyConversionPayloadImplCopyWith<$Res> {
  factory _$$CurrencyConversionPayloadImplCopyWith(
          _$CurrencyConversionPayloadImpl value,
          $Res Function(_$CurrencyConversionPayloadImpl) then) =
      __$$CurrencyConversionPayloadImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String buyCurrency, String sellCurrency});
}

/// @nodoc
class __$$CurrencyConversionPayloadImplCopyWithImpl<$Res>
    extends _$FxAnalyticsPayloadCopyWithImpl<$Res,
        _$CurrencyConversionPayloadImpl>
    implements _$$CurrencyConversionPayloadImplCopyWith<$Res> {
  __$$CurrencyConversionPayloadImplCopyWithImpl(
      _$CurrencyConversionPayloadImpl _value,
      $Res Function(_$CurrencyConversionPayloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? buyCurrency = null,
    Object? sellCurrency = null,
  }) {
    return _then(_$CurrencyConversionPayloadImpl(
      buyCurrency: null == buyCurrency
          ? _value.buyCurrency
          : buyCurrency // ignore: cast_nullable_to_non_nullable
              as String,
      sellCurrency: null == sellCurrency
          ? _value.sellCurrency
          : sellCurrency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CurrencyConversionPayloadImpl extends _CurrencyConversionPayload {
  const _$CurrencyConversionPayloadImpl(
      {required this.buyCurrency, required this.sellCurrency})
      : super._();

  @override
  final String buyCurrency;
  @override
  final String sellCurrency;

  @override
  String toString() {
    return 'FxAnalyticsPayload.convert(buyCurrency: $buyCurrency, sellCurrency: $sellCurrency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CurrencyConversionPayloadImpl &&
            (identical(other.buyCurrency, buyCurrency) ||
                other.buyCurrency == buyCurrency) &&
            (identical(other.sellCurrency, sellCurrency) ||
                other.sellCurrency == sellCurrency));
  }

  @override
  int get hashCode => Object.hash(runtimeType, buyCurrency, sellCurrency);

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CurrencyConversionPayloadImplCopyWith<_$CurrencyConversionPayloadImpl>
      get copyWith => __$$CurrencyConversionPayloadImplCopyWithImpl<
          _$CurrencyConversionPayloadImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String fromCurrency, String toCurrency) swap,
    required TResult Function(String buyCurrency, String sellCurrency) convert,
    required TResult Function(String currency) account,
  }) {
    return convert(buyCurrency, sellCurrency);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String fromCurrency, String toCurrency)? swap,
    TResult? Function(String buyCurrency, String sellCurrency)? convert,
    TResult? Function(String currency)? account,
  }) {
    return convert?.call(buyCurrency, sellCurrency);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String fromCurrency, String toCurrency)? swap,
    TResult Function(String buyCurrency, String sellCurrency)? convert,
    TResult Function(String currency)? account,
    required TResult orElse(),
  }) {
    if (convert != null) {
      return convert(buyCurrency, sellCurrency);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CurrencySwapPayload value) swap,
    required TResult Function(_CurrencyConversionPayload value) convert,
    required TResult Function(_AccountSelectionPayload value) account,
  }) {
    return convert(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CurrencySwapPayload value)? swap,
    TResult? Function(_CurrencyConversionPayload value)? convert,
    TResult? Function(_AccountSelectionPayload value)? account,
  }) {
    return convert?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CurrencySwapPayload value)? swap,
    TResult Function(_CurrencyConversionPayload value)? convert,
    TResult Function(_AccountSelectionPayload value)? account,
    required TResult orElse(),
  }) {
    if (convert != null) {
      return convert(this);
    }
    return orElse();
  }
}

abstract class _CurrencyConversionPayload extends FxAnalyticsPayload {
  const factory _CurrencyConversionPayload(
      {required final String buyCurrency,
      required final String sellCurrency}) = _$CurrencyConversionPayloadImpl;
  const _CurrencyConversionPayload._() : super._();

  String get buyCurrency;
  String get sellCurrency;

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CurrencyConversionPayloadImplCopyWith<_$CurrencyConversionPayloadImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AccountSelectionPayloadImplCopyWith<$Res> {
  factory _$$AccountSelectionPayloadImplCopyWith(
          _$AccountSelectionPayloadImpl value,
          $Res Function(_$AccountSelectionPayloadImpl) then) =
      __$$AccountSelectionPayloadImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String currency});
}

/// @nodoc
class __$$AccountSelectionPayloadImplCopyWithImpl<$Res>
    extends _$FxAnalyticsPayloadCopyWithImpl<$Res,
        _$AccountSelectionPayloadImpl>
    implements _$$AccountSelectionPayloadImplCopyWith<$Res> {
  __$$AccountSelectionPayloadImplCopyWithImpl(
      _$AccountSelectionPayloadImpl _value,
      $Res Function(_$AccountSelectionPayloadImpl) _then)
      : super(_value, _then);

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currency = null,
  }) {
    return _then(_$AccountSelectionPayloadImpl(
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$AccountSelectionPayloadImpl extends _AccountSelectionPayload {
  const _$AccountSelectionPayloadImpl({required this.currency}) : super._();

  @override
  final String currency;

  @override
  String toString() {
    return 'FxAnalyticsPayload.account(currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountSelectionPayloadImpl &&
            (identical(other.currency, currency) ||
                other.currency == currency));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currency);

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountSelectionPayloadImplCopyWith<_$AccountSelectionPayloadImpl>
      get copyWith => __$$AccountSelectionPayloadImplCopyWithImpl<
          _$AccountSelectionPayloadImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String fromCurrency, String toCurrency) swap,
    required TResult Function(String buyCurrency, String sellCurrency) convert,
    required TResult Function(String currency) account,
  }) {
    return account(currency);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String fromCurrency, String toCurrency)? swap,
    TResult? Function(String buyCurrency, String sellCurrency)? convert,
    TResult? Function(String currency)? account,
  }) {
    return account?.call(currency);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String fromCurrency, String toCurrency)? swap,
    TResult Function(String buyCurrency, String sellCurrency)? convert,
    TResult Function(String currency)? account,
    required TResult orElse(),
  }) {
    if (account != null) {
      return account(currency);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_CurrencySwapPayload value) swap,
    required TResult Function(_CurrencyConversionPayload value) convert,
    required TResult Function(_AccountSelectionPayload value) account,
  }) {
    return account(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_CurrencySwapPayload value)? swap,
    TResult? Function(_CurrencyConversionPayload value)? convert,
    TResult? Function(_AccountSelectionPayload value)? account,
  }) {
    return account?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_CurrencySwapPayload value)? swap,
    TResult Function(_CurrencyConversionPayload value)? convert,
    TResult Function(_AccountSelectionPayload value)? account,
    required TResult orElse(),
  }) {
    if (account != null) {
      return account(this);
    }
    return orElse();
  }
}

abstract class _AccountSelectionPayload extends FxAnalyticsPayload {
  const factory _AccountSelectionPayload({required final String currency}) =
      _$AccountSelectionPayloadImpl;
  const _AccountSelectionPayload._() : super._();

  String get currency;

  /// Create a copy of FxAnalyticsPayload
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountSelectionPayloadImplCopyWith<_$AccountSelectionPayloadImpl>
      get copyWith => throw _privateConstructorUsedError;
}
