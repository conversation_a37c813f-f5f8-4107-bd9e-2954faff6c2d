// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fx_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FXState {
  FXInputFieldType get activeField => throw _privateConstructorUsedError;
  FXInputFieldType get lastUpdatedField => throw _privateConstructorUsedError;
  String get buyValue => throw _privateConstructorUsedError;
  String get buyRate => throw _privateConstructorUsedError;
  String get sellRate => throw _privateConstructorUsedError;
  String get sellValue => throw _privateConstructorUsedError;
  ExchangeRate get rateType => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isRetry => throw _privateConstructorUsedError;
  bool get isSellBalanceExceeded => throw _privateConstructorUsedError;
  AllowedCurrencies get allowedCurrencies => throw _privateConstructorUsedError;
  String get baseAccountId => throw _privateConstructorUsedError;
  String get counterAccountId => throw _privateConstructorUsedError;
  Map<String, AccountDetails> get accounts =>
      throw _privateConstructorUsedError;

  /// Create a copy of FXState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FXStateCopyWith<FXState> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FXStateCopyWith<$Res> {
  factory $FXStateCopyWith(FXState value, $Res Function(FXState) then) =
      _$FXStateCopyWithImpl<$Res, FXState>;
  @useResult
  $Res call(
      {FXInputFieldType activeField,
      FXInputFieldType lastUpdatedField,
      String buyValue,
      String buyRate,
      String sellRate,
      String sellValue,
      ExchangeRate rateType,
      bool isLoading,
      bool isRetry,
      bool isSellBalanceExceeded,
      AllowedCurrencies allowedCurrencies,
      String baseAccountId,
      String counterAccountId,
      Map<String, AccountDetails> accounts});

  $AllowedCurrenciesCopyWith<$Res> get allowedCurrencies;
}

/// @nodoc
class _$FXStateCopyWithImpl<$Res, $Val extends FXState>
    implements $FXStateCopyWith<$Res> {
  _$FXStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FXState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeField = null,
    Object? lastUpdatedField = null,
    Object? buyValue = null,
    Object? buyRate = null,
    Object? sellRate = null,
    Object? sellValue = null,
    Object? rateType = null,
    Object? isLoading = null,
    Object? isRetry = null,
    Object? isSellBalanceExceeded = null,
    Object? allowedCurrencies = null,
    Object? baseAccountId = null,
    Object? counterAccountId = null,
    Object? accounts = null,
  }) {
    return _then(_value.copyWith(
      activeField: null == activeField
          ? _value.activeField
          : activeField // ignore: cast_nullable_to_non_nullable
              as FXInputFieldType,
      lastUpdatedField: null == lastUpdatedField
          ? _value.lastUpdatedField
          : lastUpdatedField // ignore: cast_nullable_to_non_nullable
              as FXInputFieldType,
      buyValue: null == buyValue
          ? _value.buyValue
          : buyValue // ignore: cast_nullable_to_non_nullable
              as String,
      buyRate: null == buyRate
          ? _value.buyRate
          : buyRate // ignore: cast_nullable_to_non_nullable
              as String,
      sellRate: null == sellRate
          ? _value.sellRate
          : sellRate // ignore: cast_nullable_to_non_nullable
              as String,
      sellValue: null == sellValue
          ? _value.sellValue
          : sellValue // ignore: cast_nullable_to_non_nullable
              as String,
      rateType: null == rateType
          ? _value.rateType
          : rateType // ignore: cast_nullable_to_non_nullable
              as ExchangeRate,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRetry: null == isRetry
          ? _value.isRetry
          : isRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      isSellBalanceExceeded: null == isSellBalanceExceeded
          ? _value.isSellBalanceExceeded
          : isSellBalanceExceeded // ignore: cast_nullable_to_non_nullable
              as bool,
      allowedCurrencies: null == allowedCurrencies
          ? _value.allowedCurrencies
          : allowedCurrencies // ignore: cast_nullable_to_non_nullable
              as AllowedCurrencies,
      baseAccountId: null == baseAccountId
          ? _value.baseAccountId
          : baseAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      counterAccountId: null == counterAccountId
          ? _value.counterAccountId
          : counterAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as Map<String, AccountDetails>,
    ) as $Val);
  }

  /// Create a copy of FXState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AllowedCurrenciesCopyWith<$Res> get allowedCurrencies {
    return $AllowedCurrenciesCopyWith<$Res>(_value.allowedCurrencies, (value) {
      return _then(_value.copyWith(allowedCurrencies: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$FXIdleStateImplCopyWith<$Res>
    implements $FXStateCopyWith<$Res> {
  factory _$$FXIdleStateImplCopyWith(
          _$FXIdleStateImpl value, $Res Function(_$FXIdleStateImpl) then) =
      __$$FXIdleStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {FXInputFieldType activeField,
      FXInputFieldType lastUpdatedField,
      String buyValue,
      String buyRate,
      String sellRate,
      String sellValue,
      ExchangeRate rateType,
      bool isLoading,
      bool isRetry,
      bool isSellBalanceExceeded,
      AllowedCurrencies allowedCurrencies,
      String baseAccountId,
      String counterAccountId,
      Map<String, AccountDetails> accounts});

  @override
  $AllowedCurrenciesCopyWith<$Res> get allowedCurrencies;
}

/// @nodoc
class __$$FXIdleStateImplCopyWithImpl<$Res>
    extends _$FXStateCopyWithImpl<$Res, _$FXIdleStateImpl>
    implements _$$FXIdleStateImplCopyWith<$Res> {
  __$$FXIdleStateImplCopyWithImpl(
      _$FXIdleStateImpl _value, $Res Function(_$FXIdleStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FXState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeField = null,
    Object? lastUpdatedField = null,
    Object? buyValue = null,
    Object? buyRate = null,
    Object? sellRate = null,
    Object? sellValue = null,
    Object? rateType = null,
    Object? isLoading = null,
    Object? isRetry = null,
    Object? isSellBalanceExceeded = null,
    Object? allowedCurrencies = null,
    Object? baseAccountId = null,
    Object? counterAccountId = null,
    Object? accounts = null,
  }) {
    return _then(_$FXIdleStateImpl(
      activeField: null == activeField
          ? _value.activeField
          : activeField // ignore: cast_nullable_to_non_nullable
              as FXInputFieldType,
      lastUpdatedField: null == lastUpdatedField
          ? _value.lastUpdatedField
          : lastUpdatedField // ignore: cast_nullable_to_non_nullable
              as FXInputFieldType,
      buyValue: null == buyValue
          ? _value.buyValue
          : buyValue // ignore: cast_nullable_to_non_nullable
              as String,
      buyRate: null == buyRate
          ? _value.buyRate
          : buyRate // ignore: cast_nullable_to_non_nullable
              as String,
      sellRate: null == sellRate
          ? _value.sellRate
          : sellRate // ignore: cast_nullable_to_non_nullable
              as String,
      sellValue: null == sellValue
          ? _value.sellValue
          : sellValue // ignore: cast_nullable_to_non_nullable
              as String,
      rateType: null == rateType
          ? _value.rateType
          : rateType // ignore: cast_nullable_to_non_nullable
              as ExchangeRate,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRetry: null == isRetry
          ? _value.isRetry
          : isRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      isSellBalanceExceeded: null == isSellBalanceExceeded
          ? _value.isSellBalanceExceeded
          : isSellBalanceExceeded // ignore: cast_nullable_to_non_nullable
              as bool,
      allowedCurrencies: null == allowedCurrencies
          ? _value.allowedCurrencies
          : allowedCurrencies // ignore: cast_nullable_to_non_nullable
              as AllowedCurrencies,
      baseAccountId: null == baseAccountId
          ? _value.baseAccountId
          : baseAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      counterAccountId: null == counterAccountId
          ? _value.counterAccountId
          : counterAccountId // ignore: cast_nullable_to_non_nullable
              as String,
      accounts: null == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as Map<String, AccountDetails>,
    ));
  }
}

/// @nodoc

class _$FXIdleStateImpl extends _FXIdleState {
  const _$FXIdleStateImpl(
      {required this.activeField,
      required this.lastUpdatedField,
      required this.buyValue,
      required this.buyRate,
      required this.sellRate,
      required this.sellValue,
      required this.rateType,
      required this.isLoading,
      required this.isRetry,
      required this.isSellBalanceExceeded,
      required this.allowedCurrencies,
      this.baseAccountId = '',
      this.counterAccountId = '',
      final Map<String, AccountDetails> accounts =
          const <String, AccountDetails>{}})
      : _accounts = accounts,
        super._();

  @override
  final FXInputFieldType activeField;
  @override
  final FXInputFieldType lastUpdatedField;
  @override
  final String buyValue;
  @override
  final String buyRate;
  @override
  final String sellRate;
  @override
  final String sellValue;
  @override
  final ExchangeRate rateType;
  @override
  final bool isLoading;
  @override
  final bool isRetry;
  @override
  final bool isSellBalanceExceeded;
  @override
  final AllowedCurrencies allowedCurrencies;
  @override
  @JsonKey()
  final String baseAccountId;
  @override
  @JsonKey()
  final String counterAccountId;
  final Map<String, AccountDetails> _accounts;
  @override
  @JsonKey()
  Map<String, AccountDetails> get accounts {
    if (_accounts is EqualUnmodifiableMapView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_accounts);
  }

  @override
  String toString() {
    return 'FXState(activeField: $activeField, lastUpdatedField: $lastUpdatedField, buyValue: $buyValue, buyRate: $buyRate, sellRate: $sellRate, sellValue: $sellValue, rateType: $rateType, isLoading: $isLoading, isRetry: $isRetry, isSellBalanceExceeded: $isSellBalanceExceeded, allowedCurrencies: $allowedCurrencies, baseAccountId: $baseAccountId, counterAccountId: $counterAccountId, accounts: $accounts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FXIdleStateImpl &&
            (identical(other.activeField, activeField) ||
                other.activeField == activeField) &&
            (identical(other.lastUpdatedField, lastUpdatedField) ||
                other.lastUpdatedField == lastUpdatedField) &&
            (identical(other.buyValue, buyValue) ||
                other.buyValue == buyValue) &&
            (identical(other.buyRate, buyRate) || other.buyRate == buyRate) &&
            (identical(other.sellRate, sellRate) ||
                other.sellRate == sellRate) &&
            (identical(other.sellValue, sellValue) ||
                other.sellValue == sellValue) &&
            (identical(other.rateType, rateType) ||
                other.rateType == rateType) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRetry, isRetry) || other.isRetry == isRetry) &&
            (identical(other.isSellBalanceExceeded, isSellBalanceExceeded) ||
                other.isSellBalanceExceeded == isSellBalanceExceeded) &&
            (identical(other.allowedCurrencies, allowedCurrencies) ||
                other.allowedCurrencies == allowedCurrencies) &&
            (identical(other.baseAccountId, baseAccountId) ||
                other.baseAccountId == baseAccountId) &&
            (identical(other.counterAccountId, counterAccountId) ||
                other.counterAccountId == counterAccountId) &&
            const DeepCollectionEquality().equals(other._accounts, _accounts));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      activeField,
      lastUpdatedField,
      buyValue,
      buyRate,
      sellRate,
      sellValue,
      rateType,
      isLoading,
      isRetry,
      isSellBalanceExceeded,
      allowedCurrencies,
      baseAccountId,
      counterAccountId,
      const DeepCollectionEquality().hash(_accounts));

  /// Create a copy of FXState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FXIdleStateImplCopyWith<_$FXIdleStateImpl> get copyWith =>
      __$$FXIdleStateImplCopyWithImpl<_$FXIdleStateImpl>(this, _$identity);
}

abstract class _FXIdleState extends FXState {
  const factory _FXIdleState(
      {required final FXInputFieldType activeField,
      required final FXInputFieldType lastUpdatedField,
      required final String buyValue,
      required final String buyRate,
      required final String sellRate,
      required final String sellValue,
      required final ExchangeRate rateType,
      required final bool isLoading,
      required final bool isRetry,
      required final bool isSellBalanceExceeded,
      required final AllowedCurrencies allowedCurrencies,
      final String baseAccountId,
      final String counterAccountId,
      final Map<String, AccountDetails> accounts}) = _$FXIdleStateImpl;
  const _FXIdleState._() : super._();

  @override
  FXInputFieldType get activeField;
  @override
  FXInputFieldType get lastUpdatedField;
  @override
  String get buyValue;
  @override
  String get buyRate;
  @override
  String get sellRate;
  @override
  String get sellValue;
  @override
  ExchangeRate get rateType;
  @override
  bool get isLoading;
  @override
  bool get isRetry;
  @override
  bool get isSellBalanceExceeded;
  @override
  AllowedCurrencies get allowedCurrencies;
  @override
  String get baseAccountId;
  @override
  String get counterAccountId;
  @override
  Map<String, AccountDetails> get accounts;

  /// Create a copy of FXState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FXIdleStateImplCopyWith<_$FXIdleStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
