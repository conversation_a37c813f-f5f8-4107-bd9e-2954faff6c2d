// GENERATED CODE
//
// After the template files .arb have been changed,
// generate this class by the command in the terminal:
// flutter pub run lokalise_flutter_sdk:gen-lok-l10n
//
// Please see https://pub.dev/packages/lokalise_flutter_sdk

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes
// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:lokalise_flutter_sdk/lokalise_flutter_sdk.dart';
import 'intl/messages_all.dart';

class FxLocalizations {
  FxLocalizations._internal();

  static const LocalizationsDelegate<FxLocalizations> delegate =
      _AppLocalizationDelegate();

  static const List<Locale> supportedLocales = [
    Locale.fromSubtags(languageCode: 'en'),
    Locale.fromSubtags(languageCode: 'ar')
  ];

  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  static final Map<String, List<String>> _metadata = {
    'accountSelectionAccountTitle': ['currency'],
    'accountSelectionBottomSheetTitle': [],
    'amountInCreditCurrency': [],
    'amountInDebitCurrency': [],
    'amountInLabel': ['currency'],
    'areYouReadyToExchangeLabel': [],
    'atRateOfLabel': ['rate'],
    'balanceExceededLabel': ['balance'],
    'balanceLabel': ['value'],
    'boughtForLabel': ['amount'],
    'closeButtonTitle': [],
    'confirmButtonLabel': [],
    'convertButtonLabel': ['buyCurrency', 'sellCurrency'],
    'errorScreenButtonTitle': [],
    'errorScreenMainTitle': [],
    'errorScreenSubtitle': [],
    'errorScreenTitle': [],
    'exchangeLabel': [],
    'iWantToBuyTitle': ['currency'],
    'marbookLabel': [],
    'noFeeLabel': [],
    'plusLabel': ['amount'],
    'rateLabel': [],
    'ratesExpiredLabel': [],
    'reviewScreenInfoBanner': [],
    'reviewTitle': [],
    'shareLinkButtonLabel': [],
    'somethingWentWrongLabel': [],
    'somethingWentWrongLongLabel': [],
    'swipeRightConfirmLabel': [],
    'swipeToCloseLabel': [],
    'toLabel': ['currencyFrom', 'currencyTo'],
    'transferFeesLabel': [],
    'tryAgainButtonTitle': []
  };

  static Future<FxLocalizations> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    Lokalise.instance.metadata = _metadata;

    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = FxLocalizations._internal();
      return instance;
    });
  }

  static FxLocalizations of(BuildContext context) {
    final instance =
        Localizations.of<FxLocalizations>(context, FxLocalizations);
    assert(instance != null,
        'No instance of FxLocalizations present in the widget tree. Did you add FxLocalizations.delegate in localizationsDelegates?');
    return instance!;
  }

  /// `{currency} account`
  String accountSelectionAccountTitle(String currency) {
    return Intl.message(
      '$currency account',
      name: 'accountSelectionAccountTitle',
      desc: '',
      args: [currency],
    );
  }

  /// `Select account`
  String get accountSelectionBottomSheetTitle {
    return Intl.message(
      'Select account',
      name: 'accountSelectionBottomSheetTitle',
      desc: '',
      args: [],
    );
  }

  /// `Amount in credit currency`
  String get amountInCreditCurrency {
    return Intl.message(
      'Amount in credit currency',
      name: 'amountInCreditCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Amount in debit currency`
  String get amountInDebitCurrency {
    return Intl.message(
      'Amount in debit currency',
      name: 'amountInDebitCurrency',
      desc: '',
      args: [],
    );
  }

  /// `Amount in {currency}`
  String amountInLabel(String currency) {
    return Intl.message(
      'Amount in $currency',
      name: 'amountInLabel',
      desc: '',
      args: [currency],
    );
  }

  /// `Are you ready to exchange?`
  String get areYouReadyToExchangeLabel {
    return Intl.message(
      'Are you ready to exchange?',
      name: 'areYouReadyToExchangeLabel',
      desc: '',
      args: [],
    );
  }

  /// `At the rate of {rate}`
  String atRateOfLabel(String rate) {
    return Intl.message(
      'At the rate of $rate',
      name: 'atRateOfLabel',
      desc: '',
      args: [rate],
    );
  }

  /// `The amount exceeds your balance: {balance}`
  String balanceExceededLabel(String balance) {
    return Intl.message(
      'The amount exceeds your balance: $balance',
      name: 'balanceExceededLabel',
      desc: '',
      args: [balance],
    );
  }

  /// `Balance: {value}`
  String balanceLabel(String value) {
    return Intl.message(
      'Balance: $value',
      name: 'balanceLabel',
      desc: '',
      args: [value],
    );
  }

  /// `Bought for {amount}`
  String boughtForLabel(String amount) {
    return Intl.message(
      'Bought for $amount',
      name: 'boughtForLabel',
      desc: '',
      args: [amount],
    );
  }

  /// `Close`
  String get closeButtonTitle {
    return Intl.message(
      'Close',
      name: 'closeButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `Confirm`
  String get confirmButtonLabel {
    return Intl.message(
      'Confirm',
      name: 'confirmButtonLabel',
      desc: '',
      args: [],
    );
  }

  /// `Buy {buyCurrency} with {sellCurrency}`
  String convertButtonLabel(String buyCurrency, String sellCurrency) {
    return Intl.message(
      'Buy $buyCurrency with $sellCurrency',
      name: 'convertButtonLabel',
      desc: '',
      args: [buyCurrency, sellCurrency],
    );
  }

  /// `Top up`
  String get errorScreenButtonTitle {
    return Intl.message(
      'Top up',
      name: 'errorScreenButtonTitle',
      desc: '',
      args: [],
    );
  }

  /// `UH-OH!`
  String get errorScreenMainTitle {
    return Intl.message(
      'UH-OH!',
      name: 'errorScreenMainTitle',
      desc: '',
      args: [],
    );
  }

  /// `Top up your account and try again`
  String get errorScreenSubtitle {
    return Intl.message(
      'Top up your account and try again',
      name: 'errorScreenSubtitle',
      desc: '',
      args: [],
    );
  }

  /// `The payment has failed`
  String get errorScreenTitle {
    return Intl.message(
      'The payment has failed',
      name: 'errorScreenTitle',
      desc: '',
      args: [],
    );
  }

  /// `exchange`
  String get exchangeLabel {
    return Intl.message(
      'exchange',
      name: 'exchangeLabel',
      desc: '',
      args: [],
    );
  }

  /// `I want to buy {currency}`
  String iWantToBuyTitle(String currency) {
    return Intl.message(
      'I want to buy $currency',
      name: 'iWantToBuyTitle',
      desc: '',
      args: [currency],
    );
  }

  /// `MABROOK!`
  String get marbookLabel {
    return Intl.message(
      'MABROOK!',
      name: 'marbookLabel',
      desc: '',
      args: [],
    );
  }

  /// `no fee`
  String get noFeeLabel {
    return Intl.message(
      'no fee',
      name: 'noFeeLabel',
      desc: '',
      args: [],
    );
  }

  /// `+ {amount}`
  String plusLabel(String amount) {
    return Intl.message(
      '+ $amount',
      name: 'plusLabel',
      desc: '',
      args: [amount],
    );
  }

  /// `Rate`
  String get rateLabel {
    return Intl.message(
      'Rate',
      name: 'rateLabel',
      desc: '',
      args: [],
    );
  }

  /// `The exchange rates have expired, please confirm the new rates.`
  String get ratesExpiredLabel {
    return Intl.message(
      'The exchange rates have expired, please confirm the new rates.',
      name: 'ratesExpiredLabel',
      desc: '',
      args: [],
    );
  }

  /// `Once confirmed, the transaction can’t be reversed`
  String get reviewScreenInfoBanner {
    return Intl.message(
      'Once confirmed, the transaction can’t be reversed',
      name: 'reviewScreenInfoBanner',
      desc: '',
      args: [],
    );
  }

  /// `review`
  String get reviewTitle {
    return Intl.message(
      'review',
      name: 'reviewTitle',
      desc: '',
      args: [],
    );
  }

  /// `Share link`
  String get shareLinkButtonLabel {
    return Intl.message(
      'Share link',
      name: 'shareLinkButtonLabel',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong...`
  String get somethingWentWrongLabel {
    return Intl.message(
      'Something went wrong...',
      name: 'somethingWentWrongLabel',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong, please try again later.`
  String get somethingWentWrongLongLabel {
    return Intl.message(
      'Something went wrong, please try again later.',
      name: 'somethingWentWrongLongLabel',
      desc: '',
      args: [],
    );
  }

  /// `Swipe right to confirm`
  String get swipeRightConfirmLabel {
    return Intl.message(
      'Swipe right to confirm',
      name: 'swipeRightConfirmLabel',
      desc: '',
      args: [],
    );
  }

  /// `Swipe up to close`
  String get swipeToCloseLabel {
    return Intl.message(
      'Swipe up to close',
      name: 'swipeToCloseLabel',
      desc: '',
      args: [],
    );
  }

  /// `{currencyFrom} to {currencyTo}`
  String toLabel(String currencyFrom, String currencyTo) {
    return Intl.message(
      '$currencyFrom to $currencyTo',
      name: 'toLabel',
      desc: '',
      args: [currencyFrom, currencyTo],
    );
  }

  /// `Transfer fees (Incl. VAT)`
  String get transferFeesLabel {
    return Intl.message(
      'Transfer fees (Incl. VAT)',
      name: 'transferFeesLabel',
      desc: '',
      args: [],
    );
  }

  /// `Try again`
  String get tryAgainButtonTitle {
    return Intl.message(
      'Try again',
      name: 'tryAgainButtonTitle',
      desc: '',
      args: [],
    );
  }
}

class _AppLocalizationDelegate extends LocalizationsDelegate<FxLocalizations> {
  const _AppLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => FxLocalizations.supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode);

  @override
  Future<FxLocalizations> load(Locale locale) => FxLocalizations.load(locale);

  @override
  bool shouldReload(_AppLocalizationDelegate old) => false;
}
