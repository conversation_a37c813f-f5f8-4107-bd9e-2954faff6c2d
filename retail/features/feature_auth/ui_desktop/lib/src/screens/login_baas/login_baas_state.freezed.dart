// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_baas_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LoginBaasState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)
        idle,
    required TResult Function(Object? error) showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)?
        idle,
    TResult? Function(Object? error)? showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)?
        idle,
    TResult Function(Object? error)? showError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoginBaasIdleState value) idle,
    required TResult Function(LoginBaasShowErrorState value) showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoginBaasIdleState value)? idle,
    TResult? Function(LoginBaasShowErrorState value)? showError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoginBaasIdleState value)? idle,
    TResult Function(LoginBaasShowErrorState value)? showError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginBaasStateCopyWith<$Res> {
  factory $LoginBaasStateCopyWith(
          LoginBaasState value, $Res Function(LoginBaasState) then) =
      _$LoginBaasStateCopyWithImpl<$Res, LoginBaasState>;
}

/// @nodoc
class _$LoginBaasStateCopyWithImpl<$Res, $Val extends LoginBaasState>
    implements $LoginBaasStateCopyWith<$Res> {
  _$LoginBaasStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;
}

/// @nodoc
abstract class _$$LoginBaasIdleStateImplCopyWith<$Res> {
  factory _$$LoginBaasIdleStateImplCopyWith(_$LoginBaasIdleStateImpl value,
          $Res Function(_$LoginBaasIdleStateImpl) then) =
      __$$LoginBaasIdleStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {bool submitAvailable,
      String email,
      bool hasError,
      LoginBaasPageNavigationConfig? config});

  $LoginBaasPageNavigationConfigCopyWith<$Res>? get config;
}

/// @nodoc
class __$$LoginBaasIdleStateImplCopyWithImpl<$Res>
    extends _$LoginBaasStateCopyWithImpl<$Res, _$LoginBaasIdleStateImpl>
    implements _$$LoginBaasIdleStateImplCopyWith<$Res> {
  __$$LoginBaasIdleStateImplCopyWithImpl(_$LoginBaasIdleStateImpl _value,
      $Res Function(_$LoginBaasIdleStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? submitAvailable = null,
    Object? email = null,
    Object? hasError = null,
    Object? config = freezed,
  }) {
    return _then(_$LoginBaasIdleStateImpl(
      submitAvailable: null == submitAvailable
          ? _value.submitAvailable
          : submitAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      hasError: null == hasError
          ? _value.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      config: freezed == config
          ? _value.config
          : config // ignore: cast_nullable_to_non_nullable
              as LoginBaasPageNavigationConfig?,
    ));
  }

  @override
  @pragma('vm:prefer-inline')
  $LoginBaasPageNavigationConfigCopyWith<$Res>? get config {
    if (_value.config == null) {
      return null;
    }

    return $LoginBaasPageNavigationConfigCopyWith<$Res>(_value.config!,
        (value) {
      return _then(_value.copyWith(config: value));
    });
  }
}

/// @nodoc

class _$LoginBaasIdleStateImpl implements LoginBaasIdleState {
  const _$LoginBaasIdleStateImpl(
      {this.submitAvailable = false,
      this.email = '',
      this.hasError = false,
      this.config});

  @override
  @JsonKey()
  final bool submitAvailable;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final bool hasError;
  @override
  final LoginBaasPageNavigationConfig? config;

  @override
  String toString() {
    return 'LoginBaasState.idle(submitAvailable: $submitAvailable, email: $email, hasError: $hasError, config: $config)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginBaasIdleStateImpl &&
            (identical(other.submitAvailable, submitAvailable) ||
                other.submitAvailable == submitAvailable) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.config, config) || other.config == config));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, submitAvailable, email, hasError, config);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginBaasIdleStateImplCopyWith<_$LoginBaasIdleStateImpl> get copyWith =>
      __$$LoginBaasIdleStateImplCopyWithImpl<_$LoginBaasIdleStateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)
        idle,
    required TResult Function(Object? error) showError,
  }) {
    return idle(submitAvailable, email, hasError, config);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)?
        idle,
    TResult? Function(Object? error)? showError,
  }) {
    return idle?.call(submitAvailable, email, hasError, config);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)?
        idle,
    TResult Function(Object? error)? showError,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(submitAvailable, email, hasError, config);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoginBaasIdleState value) idle,
    required TResult Function(LoginBaasShowErrorState value) showError,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoginBaasIdleState value)? idle,
    TResult? Function(LoginBaasShowErrorState value)? showError,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoginBaasIdleState value)? idle,
    TResult Function(LoginBaasShowErrorState value)? showError,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class LoginBaasIdleState implements LoginBaasState {
  const factory LoginBaasIdleState(
      {final bool submitAvailable,
      final String email,
      final bool hasError,
      final LoginBaasPageNavigationConfig? config}) = _$LoginBaasIdleStateImpl;

  bool get submitAvailable;
  String get email;
  bool get hasError;
  LoginBaasPageNavigationConfig? get config;
  @JsonKey(ignore: true)
  _$$LoginBaasIdleStateImplCopyWith<_$LoginBaasIdleStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoginBaasShowErrorStateImplCopyWith<$Res> {
  factory _$$LoginBaasShowErrorStateImplCopyWith(
          _$LoginBaasShowErrorStateImpl value,
          $Res Function(_$LoginBaasShowErrorStateImpl) then) =
      __$$LoginBaasShowErrorStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Object? error});
}

/// @nodoc
class __$$LoginBaasShowErrorStateImplCopyWithImpl<$Res>
    extends _$LoginBaasStateCopyWithImpl<$Res, _$LoginBaasShowErrorStateImpl>
    implements _$$LoginBaasShowErrorStateImplCopyWith<$Res> {
  __$$LoginBaasShowErrorStateImplCopyWithImpl(
      _$LoginBaasShowErrorStateImpl _value,
      $Res Function(_$LoginBaasShowErrorStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$LoginBaasShowErrorStateImpl(
      error: freezed == error ? _value.error : error,
    ));
  }
}

/// @nodoc

class _$LoginBaasShowErrorStateImpl implements LoginBaasShowErrorState {
  const _$LoginBaasShowErrorStateImpl({this.error});

  @override
  final Object? error;

  @override
  String toString() {
    return 'LoginBaasState.showError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginBaasShowErrorStateImpl &&
            const DeepCollectionEquality().equals(other.error, error));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(error));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginBaasShowErrorStateImplCopyWith<_$LoginBaasShowErrorStateImpl>
      get copyWith => __$$LoginBaasShowErrorStateImplCopyWithImpl<
          _$LoginBaasShowErrorStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)
        idle,
    required TResult Function(Object? error) showError,
  }) {
    return showError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)?
        idle,
    TResult? Function(Object? error)? showError,
  }) {
    return showError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(bool submitAvailable, String email, bool hasError,
            LoginBaasPageNavigationConfig? config)?
        idle,
    TResult Function(Object? error)? showError,
    required TResult orElse(),
  }) {
    if (showError != null) {
      return showError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoginBaasIdleState value) idle,
    required TResult Function(LoginBaasShowErrorState value) showError,
  }) {
    return showError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoginBaasIdleState value)? idle,
    TResult? Function(LoginBaasShowErrorState value)? showError,
  }) {
    return showError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoginBaasIdleState value)? idle,
    TResult Function(LoginBaasShowErrorState value)? showError,
    required TResult orElse(),
  }) {
    if (showError != null) {
      return showError(this);
    }
    return orElse();
  }
}

abstract class LoginBaasShowErrorState implements LoginBaasState {
  const factory LoginBaasShowErrorState({final Object? error}) =
      _$LoginBaasShowErrorStateImpl;

  Object? get error;
  @JsonKey(ignore: true)
  _$$LoginBaasShowErrorStateImplCopyWith<_$LoginBaasShowErrorStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
