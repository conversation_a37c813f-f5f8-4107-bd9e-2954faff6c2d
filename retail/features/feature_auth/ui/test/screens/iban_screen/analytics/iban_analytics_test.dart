import 'package:feature_auth_ui/src/screens/iban_screen/analytics/iban_analytics.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';

void main() {
  late MockAnalyticsAbstractTrackerFactory trackerFactory;
  late MockAnalyticsEventTracker tracker;
  late IbanAnalytics analytics;

  setUp(() {
    final analyticsMocks = getAnalyticsTracker();
    trackerFactory = analyticsMocks.analyticsAbstractTrackerFactory;
    tracker = analyticsMocks.tracker;

    analytics = IbanAnalytics(analyticsFactory: trackerFactory);
  });

  test('Should send next event', () async {
    // Act
    analytics.clickNextButton();

    // Assert
    verify(() => tracker.clickNext());
  });

  test('Should send back event', () async {
    // Act
    analytics.clickBackButton();

    // Assert
    verify(() => tracker.clickBack());
  });
}
