import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_auth_ui/src/screens/questions_for_existing_customers/sof/existing_customer_sof_cubit.dart';
import 'package:feature_auth_ui/src/screens/questions_for_existing_customers/sof/existing_customer_sof_state.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';

import '../../feature_auth_ui_mocks.dart';

void main() {
  late ExistingCustomerSofCubit cubit;
  late MockSofInteractor mockInteractor;
  late MockKycInteractor mockKycInteractor;
  late MockPersonalDetailsFlowStepScreenController mockController;
  late MockCommonErrorHandler mockCommonErrorHandler;
  late MockLogger mockLogger;
  late MockSofAnalytics mockAnalytics;
  late MockNavigationProvider mockNavigationProvider;

  setUpAll(() {
    mockInteractor = MockSofInteractor();
    mockKycInteractor = MockKycInteractor();
    mockController = MockPersonalDetailsFlowStepScreenController();
    mockCommonErrorHandler = MockCommonErrorHandler();
    mockLogger = MockLogger();
    mockAnalytics = MockSofAnalytics();
    mockNavigationProvider = MockNavigationProvider();

    cubit = ExistingCustomerSofCubit(
      sofInteractor: mockInteractor,
      kycInteractor: mockKycInteractor,
      commonErrorHandler: mockCommonErrorHandler,
      logger: mockLogger,
      analytics: mockAnalytics,
      flowName: 'personalDetailsFlow',
      navigationProvider: mockNavigationProvider,
    );

    when(
      () => mockInteractor.getSofOptionsList(employmentType: 'employmentType'),
    ).justAnswerAsync(
      [const SofOptionsModel(code: 'test1', value: 'value1')],
    );
    when(
      () => mockInteractor.submitSof(
        sofOptions: any(named: 'sofOptions'),
        otherReason: any(named: 'otherReason'),
      ),
    ).justCompleteAsync();
    when(() => mockController.getGlobalState())
        .thenReturn(MockPersonalDetailsFlowGlobalState());
    registerFallbackValue(MockNavigationConfig());
    when(() => mockNavigationProvider.showBottomSheet(any()))
        .justCompleteAsync();
  });

  const employmentOption = 'employmentType';

  test('check if selected returns false before initialization', () {
    final isSelected = cubit.checkIfSelected(0);
    expect(isSelected, false);
  });
  test('On other text changed should emit updated loaded state', () async {
    // act
    await cubit.init(employmentOption);
    cubit.onOtherTextChanged('new text');
    //assert
    expect(
      cubit.state,
      const ExistingCustomerSofState.loaded(
        options: [SofOptionsModel(code: 'test1', value: 'value1')],
        otherText: 'new text',
      ),
    );
  });
  test(
    'check if selected is false on initial',
    () => expect(cubit.checkIfSelected(0), false),
  );
  test('check if selected is false when loaded state does not contain id', () {
    cubit.init(employmentOption);
    expect(cubit.checkIfSelected(0), false);
  });
  test(
    'check if confirm option adds id of option to selected ids if not present',
    () {
      cubit
        ..init(employmentOption)
        ..confirmOption(0);
      const selectedState = ExistingCustomerSofState.loaded(
        options: [SofOptionsModel(code: 'test1', value: 'value1')],
        selectedIds: {0},
      );
      expect(cubit.state, selectedState);
    },
  );
  test(
    'check if confirm option twice will not have selected id',
    () {
      cubit
        ..init(employmentOption)
        ..confirmOption(0)
        ..confirmOption(0);
      const selectedState = ExistingCustomerSofState.loaded(
        options: [SofOptionsModel(code: 'test1', value: 'value1')],
      );
      expect(cubit.state, selectedState);
    },
  );

  test('check if error is handled when fetching sof', () async {
    final exception = Exception('test exception');
    when(
      () => mockInteractor.getSofOptionsList(employmentType: 'employmentType'),
    ).justThrowAsync(exception);
    await cubit.init(employmentOption);
    verify(() => mockCommonErrorHandler.handleError(exception)).calledOnce;
  });

  test('check show bottomsheet', () async {
    when(() => mockKycInteractor.getRetailSofVideo()).justAnswerAsync(
      StaticDocumentModel(
        documentType: 'documentType',
        documentVersion: 'documentVersion',
        localizedUrls: {
          'en': 'testUrl',
        },
      ),
    );
    await cubit.showBottomSheet('title', 'description');
    verify(() => mockNavigationProvider.showBottomSheet<void>(any()))
        .calledOnce;
  });

  test('check bottomsheet error handling', () async {
    final exception = Exception('test exception');
    when(() => mockKycInteractor.getRetailSofVideo()).justThrowAsync(exception);
    await cubit.showBottomSheet('title', 'description');
    verify(() => mockCommonErrorHandler.handleError(exception)).calledOnce;
  });
}
