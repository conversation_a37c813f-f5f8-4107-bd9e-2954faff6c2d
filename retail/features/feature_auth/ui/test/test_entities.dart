import 'package:feature_auth_api/domain/models/reask_model.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_address_api/wio_common_feature_address_api.dart';
import 'package:wio_common_feature_customer_address_api/index.dart';
import 'package:wio_feature_centralised_questionnaire_service_api/centralised_questionnaire_service_api.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_onboarding_domain_api/index.dart';

class TestEntities {
  TestEntities._();

  static const email = '<EMAIL>';
  static const reaskModel = ReaskModel(
    userStatus: OnBoardingStatus.inReview,
    reasks: reasks,
  );
  static const reasks = [
    Reask(reaskReason: ReviewReason.passport, title: 'Upload Passport'),
  ];

  static const passportReask = Reask(
    reaskReason: ReviewReason.passport,
    title: 'Scan the first page of your passport',
  );

  static Questionnaire questionnaire = Questionnaire(
    questionnaireWorkFlowInstanceId: randomString(),
    showQuestionDirectly: false,
    directOpenSectionId: null,
    questionnaireName: 'test-questionnaire',
    userActionListTemplateConfig: UserActionListTemplateConfig(
      title: randomString(),
      description: randomString(),
      total: randomInt(),
      completed: randomInt(),
      items: const [],
    ),
    questionnaireStatus: Status.pending,
  );

  static const addressResult = AddressSuccessResult(
    addressRecord: AddressRecord(
      country: 'country',
      city: 'city',
      postalCode: 'postalCode',
      buildingName: 'buildingName',
      addressStreet: 'addressStreet',
      apartmentNumber: 'apartmentNumber',
      formattedAddress: 'formattedAddress',
    ),
  );

  static const address = CustomerAddress(
    apartmentNumber: 'apartmentNumber',
    addressStreet: 'addressStreet',
    buildingName: 'buildingName',
    city: 'city',
    postalCode: 'postalCode',
    country: 'country',
  );

  static OnboardingPageInfo getOnboardingPageInfo({
    required OnBoardingPageType pageType,
  }) =>
      OnboardingPageInfo(
        pageType: pageType,
        subTypes: [],
        optional: false,
        editable: false,
        sources: [],
        tinInfo: [],
        questions: [],
        status: OnBoardingPageInfoStatus.notCompleted,
        refreshNextPage: false,
      );

  static OnboardingInvitation getOnboardingInvitation() => OnboardingInvitation(
        id: randomString(),
        inviterId: randomString(),
        inviterName: randomString(),
        inviteeName: randomString(),
        inviterMobile: randomString(),
        role: pickRandomly(MemberRole.values),
        createdOn: randomDate(),
        type: InvitationType.createFamilyAccount,
      );

  static const tinReason = TinReason(
    reason: TinAbsenceReason.unableToObtainATin,
    explanation: 'test',
  );

  static final lendingOffers = Offers(
    total: 1,
    offers: [
      LendingOffer(
        amount: Money.fromNumWithCurrency(1200, Currency.aed),
        productType: ProductType.creditCard,
      ),
    ],
    customerDetails: CustomerDetails(
      employerName: 'employerName',
      workEmail: 'workEmail',
      monthlyIncome: Money.fromNumWithCurrency(1000, Currency.aed),
      pricingPlan: OnboardingPricingPlan.standard,
    ),
  );
}
