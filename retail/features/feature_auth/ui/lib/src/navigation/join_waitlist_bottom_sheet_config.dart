import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

part 'join_waitlist_bottom_sheet_config.freezed.dart';

@freezed
class JoinWaitlistBottomSheetConfig
    with _$JoinWaitlistBottomSheetConfig
    implements BottomSheetNavigationConfig<bool> {
  const factory JoinWaitlistBottomSheetConfig({
    String? email,
  }) = _JoinWaitlistBottomSheetConfig;

  const JoinWaitlistBottomSheetConfig._();

  @override
  String get feature => AuthFeatureNavigationConfig.name;

  @override
  String toString() {
    return 'JoinWaitlistBottomSheetConfig';
  }
}
