// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'additional_questions_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AdditionalQuestionsState {
  double? get monthlyIncome => throw _privateConstructorUsedError;
  double? get deposits => throw _privateConstructorUsedError;
  double? get withdrawalAmount => throw _privateConstructorUsedError;
  double? get cashPercentage => throw _privateConstructorUsedError;

  /// Create a copy of AdditionalQuestionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AdditionalQuestionsStateCopyWith<AdditionalQuestionsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdditionalQuestionsStateCopyWith<$Res> {
  factory $AdditionalQuestionsStateCopyWith(AdditionalQuestionsState value,
          $Res Function(AdditionalQuestionsState) then) =
      _$AdditionalQuestionsStateCopyWithImpl<$Res, AdditionalQuestionsState>;
  @useResult
  $Res call(
      {double? monthlyIncome,
      double? deposits,
      double? withdrawalAmount,
      double? cashPercentage});
}

/// @nodoc
class _$AdditionalQuestionsStateCopyWithImpl<$Res,
        $Val extends AdditionalQuestionsState>
    implements $AdditionalQuestionsStateCopyWith<$Res> {
  _$AdditionalQuestionsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdditionalQuestionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyIncome = freezed,
    Object? deposits = freezed,
    Object? withdrawalAmount = freezed,
    Object? cashPercentage = freezed,
  }) {
    return _then(_value.copyWith(
      monthlyIncome: freezed == monthlyIncome
          ? _value.monthlyIncome
          : monthlyIncome // ignore: cast_nullable_to_non_nullable
              as double?,
      deposits: freezed == deposits
          ? _value.deposits
          : deposits // ignore: cast_nullable_to_non_nullable
              as double?,
      withdrawalAmount: freezed == withdrawalAmount
          ? _value.withdrawalAmount
          : withdrawalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      cashPercentage: freezed == cashPercentage
          ? _value.cashPercentage
          : cashPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AdditionalQuestionsInitialStateImplCopyWith<$Res>
    implements $AdditionalQuestionsStateCopyWith<$Res> {
  factory _$$AdditionalQuestionsInitialStateImplCopyWith(
          _$AdditionalQuestionsInitialStateImpl value,
          $Res Function(_$AdditionalQuestionsInitialStateImpl) then) =
      __$$AdditionalQuestionsInitialStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double? monthlyIncome,
      double? deposits,
      double? withdrawalAmount,
      double? cashPercentage});
}

/// @nodoc
class __$$AdditionalQuestionsInitialStateImplCopyWithImpl<$Res>
    extends _$AdditionalQuestionsStateCopyWithImpl<$Res,
        _$AdditionalQuestionsInitialStateImpl>
    implements _$$AdditionalQuestionsInitialStateImplCopyWith<$Res> {
  __$$AdditionalQuestionsInitialStateImplCopyWithImpl(
      _$AdditionalQuestionsInitialStateImpl _value,
      $Res Function(_$AdditionalQuestionsInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdditionalQuestionsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? monthlyIncome = freezed,
    Object? deposits = freezed,
    Object? withdrawalAmount = freezed,
    Object? cashPercentage = freezed,
  }) {
    return _then(_$AdditionalQuestionsInitialStateImpl(
      monthlyIncome: freezed == monthlyIncome
          ? _value.monthlyIncome
          : monthlyIncome // ignore: cast_nullable_to_non_nullable
              as double?,
      deposits: freezed == deposits
          ? _value.deposits
          : deposits // ignore: cast_nullable_to_non_nullable
              as double?,
      withdrawalAmount: freezed == withdrawalAmount
          ? _value.withdrawalAmount
          : withdrawalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      cashPercentage: freezed == cashPercentage
          ? _value.cashPercentage
          : cashPercentage // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc

class _$AdditionalQuestionsInitialStateImpl
    implements _AdditionalQuestionsInitialState {
  const _$AdditionalQuestionsInitialStateImpl(
      {this.monthlyIncome,
      this.deposits,
      this.withdrawalAmount,
      this.cashPercentage});

  @override
  final double? monthlyIncome;
  @override
  final double? deposits;
  @override
  final double? withdrawalAmount;
  @override
  final double? cashPercentage;

  @override
  String toString() {
    return 'AdditionalQuestionsState(monthlyIncome: $monthlyIncome, deposits: $deposits, withdrawalAmount: $withdrawalAmount, cashPercentage: $cashPercentage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AdditionalQuestionsInitialStateImpl &&
            (identical(other.monthlyIncome, monthlyIncome) ||
                other.monthlyIncome == monthlyIncome) &&
            (identical(other.deposits, deposits) ||
                other.deposits == deposits) &&
            (identical(other.withdrawalAmount, withdrawalAmount) ||
                other.withdrawalAmount == withdrawalAmount) &&
            (identical(other.cashPercentage, cashPercentage) ||
                other.cashPercentage == cashPercentage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, monthlyIncome, deposits, withdrawalAmount, cashPercentage);

  /// Create a copy of AdditionalQuestionsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AdditionalQuestionsInitialStateImplCopyWith<
          _$AdditionalQuestionsInitialStateImpl>
      get copyWith => __$$AdditionalQuestionsInitialStateImplCopyWithImpl<
          _$AdditionalQuestionsInitialStateImpl>(this, _$identity);
}

abstract class _AdditionalQuestionsInitialState
    implements AdditionalQuestionsState {
  const factory _AdditionalQuestionsInitialState(
      {final double? monthlyIncome,
      final double? deposits,
      final double? withdrawalAmount,
      final double? cashPercentage}) = _$AdditionalQuestionsInitialStateImpl;

  @override
  double? get monthlyIncome;
  @override
  double? get deposits;
  @override
  double? get withdrawalAmount;
  @override
  double? get cashPercentage;

  /// Create a copy of AdditionalQuestionsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AdditionalQuestionsInitialStateImplCopyWith<
          _$AdditionalQuestionsInitialStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
