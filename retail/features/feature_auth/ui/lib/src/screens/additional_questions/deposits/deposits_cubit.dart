import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:common_feature_user_financial_details_api/feature_user_financial_details_api.dart';
import 'package:feature_auth_ui/src/screens/additional_questions/deposits/deposits_state.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';

class DepositsCubit extends BaseCubit<DepositsState> {
  final Logger _logger;
  final ValidationInteractor _validationInteractor;
  final FeatureToggleProvider _featureToggles;
  StreamSubscription<AmountInputState>? _validationSubscription;
  String _recentInput = '';

  DepositsCubit({
    required ValidationInteractor validationInteractor,
    required FeatureToggleProvider featureToggles,
    required Logger logger,
  })  : _validationInteractor = validationInteractor,
        _featureToggles = featureToggles,
        _logger = logger,
        super(const DepositsState()) {
    _subscribeToValidationStream();
  }

  bool isInputValidationEnabled() {
    return _featureToggles.get<bool>(
      FinancialDetailsFeatureToggles.isInputValidationEnabled,
    );
  }

  void _subscribeToValidationStream() {
    _validationSubscription = _validationInteractor.inputStateStream().listen(
      (inputState) {
        emit(state.copyWith(inputState: inputState));
      },
      // ignore: inference_failure_on_untyped_parameter
      onError: (error) {
        _logger.error('Validation stream error: $error');
      },
    );
  }

  bool isAmountValid() {
    return _recentInput.isNotEmpty && state.inputState is AmountInputStateValid;
  }

  void onInputChanged({
    required String amount,
    required double monthlyIncome,
  }) {
    _recentInput = amount;
    if (isInputValidationEnabled()) {
      if (amount.isEmpty) {
        emit(
          state.copyWith(
            inputState: const AmountInputState.idle(),
          ),
        );
        return;
      }
      final deposit = double.tryParse(amount.replaceAll(',', ''));
      _validationInteractor.onAmountChanged(
        validationPayload: ValidationPayload(
          income: monthlyIncome,
          deposit: deposit,
        ),
      );
    } else {
      emit(
        state.copyWith(
          inputState: amount.isNotEmpty
              ? const AmountInputState.valid()
              : const AmountInputState.idle(),
        ),
      );
    }
  }

  @override
  String toString() => 'DepositsCubit';

  @override
  Future<void> close() {
    _validationSubscription?.cancel();
    return super.close();
  }
}
