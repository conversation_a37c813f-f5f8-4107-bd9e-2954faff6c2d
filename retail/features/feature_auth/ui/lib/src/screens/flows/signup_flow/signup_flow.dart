import 'package:di/di.dart';
import 'package:feature_auth_ui/src/screens/email_input/email_input_page.dart';
import 'package:feature_auth_ui/src/screens/flows/signup_flow/cubit/signup_flow_cubit.dart';
import 'package:feature_auth_ui/src/screens/flows/signup_flow/cubit/signup_flow_global_state.dart';
import 'package:feature_auth_ui/src/screens/info_and_terms/info_and_terms_page.dart';
import 'package:feature_auth_ui/src/screens/phone_input/phone_input_page.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class SignupFlow extends StatelessWidget {
  const SignupFlow({super.key});

  @override
  Widget build(BuildContext context) {
    return StepHostBuilder<SignupFlowGlobalState>(
      create: () => DependencyProvider.get<SignupFlowCubit>(),
      builder: (context, screenId, controller) {
        final stepScreenType = SignUpFlowStepScreensIds.values
            .firstWhere((e) => e.name == screenId);

        switch (stepScreenType) {
          case SignUpFlowStepScreensIds.onboard_view_terms_and_conditions:
            return InfoAndTermsPage(stepScreenController: controller);
          case SignUpFlowStepScreensIds.onboard_enter_email_address:
            return EmailInputPage(stepScreenController: controller);
          case SignUpFlowStepScreensIds.onboard_enter_phone_number:
            return PhoneInputPage(
              stepScreenController: controller,
            );
        }
      },
    );
  }
}
