//ignore_for_file: constant_identifier_names

import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:flutter/foundation.dart';
import 'package:wio_app_core_api/index.dart';

enum ApplicationOverviewAnalyticsTarget {
  logout,
  iban,
  sourceOfFunds,
}

@immutable
class ApplicationOverviewAnalytics {
  final AnalyticsEventTracker _analytics;

  ApplicationOverviewAnalytics({
    required AnalyticsAbstractTrackerFactory analyticsFactory,
  }) : _analytics = analyticsFactory.get(
          screenName: ApplicationOverviewPageNavigationConfig.screenId,
          tracker: AnalyticsTracker.mixpanel,
        );

  void clickLogoutButton() {
    _analytics.click(
      target: ApplicationOverviewAnalyticsTarget.logout,
      targetType: AnalyticsTargetType.button,
    );
  }

  void clickIbanButton() {
    _analytics.click(
      target: ApplicationOverviewAnalyticsTarget.iban,
      targetType: AnalyticsTargetType.button,
    );
  }

  void clickSourceOfFundsButton() {
    _analytics.click(
      target: ApplicationOverviewAnalyticsTarget.sourceOfFunds,
      targetType: AnalyticsTargetType.button,
    );
  }

  void clickBackButton() => _analytics.clickBack();
}
