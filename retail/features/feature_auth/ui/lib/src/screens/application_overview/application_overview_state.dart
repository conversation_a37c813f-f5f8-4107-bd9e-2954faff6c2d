import 'package:freezed_annotation/freezed_annotation.dart';

part 'application_overview_state.freezed.dart';

@freezed
class ApplicationOverviewState with _$ApplicationOverviewState {
  const factory ApplicationOverviewState({
    @Default('') String topTitle,
    @Default([]) List<TrackerStepModel> steps,
    AdditionalButtonType? additionalButtonType,
    @Default(false) bool shouldShowOnboardingCreditBanner,
  }) = _ApplicationOverviewState;
}

@freezed
class TrackerStepModel with _$TrackerStepModel {
  const factory TrackerStepModel({
    required String title,
    required TrackerState state,
    String? subtitle,
    String? additionalText,
  }) = _TrackerStepModel;
}

enum AdditionalButtonType {
  iban,
  sourceOfFunds,
}

enum TrackerState {
  upComing,
  pending,
  passed,
  error,
}
