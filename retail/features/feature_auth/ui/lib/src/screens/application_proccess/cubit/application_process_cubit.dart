import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_auth_ui/src/navigation/welcome_page_navigation_config.dart';
import 'package:feature_auth_ui/src/screens/application_proccess/analytics/application_process_analytics.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_onboarding_domain_api/index.dart';

part 'application_process_cubit.freezed.dart';
part 'application_process_state.dart';

const _timeoutBeforeAppOverview = Duration(seconds: 30);

class ApplicationProcessCubit extends BaseCubit<ApplicationProccesState> {
  final OnboardingStageInteractor _interactor;
  final NavigationProvider _navigator;
  final ApplicationProcessAnalytics _applicationProcessAnalytics;
  final FeatureToggleProvider _featureToggleProvider;
  final LendingInteractor _lendingInteractor;
  final Logger _logger;

  Timer? _timer;
  Timer? _requestStageTimer;

  ApplicationProcessCubit({
    required OnboardingStageInteractor interactor,
    required NavigationProvider navigator,
    required ApplicationProcessAnalytics applicationProcessAnalytics,
    required FeatureToggleProvider featureToggleProvider,
    required LendingInteractor lendingInteractor,
    required Logger logger,
  })  : _interactor = interactor,
        _navigator = navigator,
        _applicationProcessAnalytics = applicationProcessAnalytics,
        _featureToggleProvider = featureToggleProvider,
        _lendingInteractor = lendingInteractor,
        _logger = logger,
        super(
          const ApplicationProccesState(),
        ) {
    _fetchStages();
  }

  void initialize() {
    // Send analytics of the account submission process
    _applicationProcessAnalytics.accountSubmissionAnalytics();

    _timer = Timer(_timeoutBeforeAppOverview, () {
      safeEmit(state.copyWith(timedOut: true));
    });
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    _requestStageTimer?.cancel();

    return super.close();
  }

  void _fetchStages() {
    _requestStageTimer = Timer(const Duration(seconds: 3), () async {
      final (creditCardOffer, pricingPlan) =
          await _fetchCreditCardOfferAndPricingPlan();

      if (creditCardOffer != null && pricingPlan != null) {
        final shouldStartCreditApplication =
            await _shouldStartCreditApplicationFlow(pricingPlan);

        if (shouldStartCreditApplication) {
          await _navigator.removeStackAndPush(
            LoanFeatureNavigationConfig(
              destination: LoanThingsToKnowPageNavigationConfig(
                delegateConfig:
                    const LendingThingsToKnowDelegateFromOnboardingConfig(),
              ),
            ),
          );

          return;
        }
      }

      if (state.timedOut) {
        _navigateToApplicationOverview();

        return;
      }

      try {
        final result = await _interactor.getOnboardingStage();

        switch (result.status) {
          case OnBoardingStatus.prospective:
            await _navigator
                .removeStackAndPush(const OnboardingStepsNavigationConfig());
            break;
          case OnBoardingStatus.approved:
          case OnBoardingStatus.submitted:
            _fetchStages();
            break;
          case OnBoardingStatus.active:
            await _navigator.removeStackAndPush(
              const WelcomePageNavigationConfig(),
            );
            break;
          case OnBoardingStatus.rejected:
          case OnBoardingStatus.inReview:
            _navigateToApplicationOverview();
            break;
          case OnBoardingStatus.unknown:
          case OnBoardingStatus.inactive:
          case OnBoardingStatus.waitlisted:
        }
      } on Object catch (_) {
        _fetchStages();
      }
    });
  }

  void _navigateToApplicationOverview() {
    _navigator
        .removeStackAndPush(const ApplicationOverviewPageNavigationConfig());
  }

  Future<bool> _shouldStartCreditApplicationFlow(
    OnboardingPricingPlan pricingPlan,
  ) async {
    final result = await _navigator.showBottomSheet(
      OnboardingCreditBottomSheetNavigationConfig(pricingPlan: pricingPlan),
    );

    return result ?? false;
  }

  Future<(LendingOffer?, OnboardingPricingPlan? pricingPlan)>
      _fetchCreditCardOfferAndPricingPlan() async {
    final isCreditCardApplicationFromOnboardingEnabled =
        _featureToggleProvider.get(
      LendingFeatureToggles.isCreditCardApplicationFromOnboardingEnabled,
    );

    if (!isCreditCardApplicationFromOnboardingEnabled) return (null, null);

    try {
      final offers = await _lendingInteractor.getOffers(
        applicationInitiationSource:
            LendingApplicationInitiationSource.onboarding,
      );

      return (
        offers.offerForProduct(ProductType.creditCard),
        offers.customerDetails?.pricingPlan
      );
    } on Object catch (e, stackTrace) {
      _logger.error(
        'Error getting credit offers',
        error: e,
        stackTrace: stackTrace,
      );

      return (null, null);
    }
  }

  @override
  String toString() {
    return 'ApplicationProcessCubit';
  }
}
