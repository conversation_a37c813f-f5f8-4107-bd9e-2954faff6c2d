import 'dart:async';

import 'package:feature_auth_api/domain/models/industry.dart';
import 'package:feature_auth_api/domain/onboarding_steps_interactor.dart';
import 'package:feature_auth_ui/src/screens/industry/industry_selection_state.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_app_core_api/toast_message_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';

class IndustrySelectionCubit extends BaseCubit<IndustrySelectionState> {
  final OnboardingStageInteractor _onboardingStageInteractor;
  final Logger _logger;
  final NavigationProvider _navigationProvider;
  final ToastMessageProvider _toastMessageProvider;
  Timer? _debounce;

  IndustrySelectionCubit({
    required OnboardingStageInteractor onboardingStageInteractor,
    required Logger logger,
    required NavigationProvider navigationProvider,
    required ToastMessageProvider toastMessageProvider,
  })  : _onboardingStageInteractor = onboardingStageInteractor,
        _toastMessageProvider = toastMessageProvider,
        _logger = logger,
        _navigationProvider = navigationProvider,
        super(
          const IndustrySelectionState(industries: [], matchedIndustries: []),
        );

  Future<void> initialize() async {
    try {
      final industries = await _onboardingStageInteractor.getIndustries();
      safeEmit(
        state.copyWith(
          industries: industries,
          matchedIndustries: industries,
        ),
      );
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Error in initialize: ${error.toString()}',
        error: error,
        stackTrace: stackTrace,
      );
      unawaited(_showErrorToastMessage(message: error.toString()));
    }
  }

  Future<void> onSearch(String query, String languageCode) async {
    // Cancel previous timer if still active
    _debounce?.cancel();

    // If query is empty, reset immediately
    if (query.isEmpty) {
      reset();
      return;
    }

    // Set a debounce duration (e.g., 300ms)
    _debounce = Timer(const Duration(milliseconds: 300), () async {
      final matchedIndustries =
          await _onboardingStageInteractor.getIndustriesByName(
        query: query,
        languageCode: languageCode,
      );

      emit(
        state.copyWith(matchedIndustries: matchedIndustries),
      );
    });
  }

  @override
  Future<void> close() {
    _debounce?.cancel();
    return super.close();
  }

  void updateSelectedIndustry(Industry industry) =>
      emit(state.copyWith(selectedIndustry: industry));

  void reset() => emit(state.copyWith(matchedIndustries: state.industries));

  Future<void> submit() async {
    try {
      if (state.selectedIndustry == null) return;
      emit(state.copyWith(showLoading: true));
      await _onboardingStageInteractor.submitIndustry(
        industry: state.selectedIndustry!,
      );
      safeEmit(state.copyWith(showLoading: false));
      _navigationProvider.goBack(true);
    } on Object catch (error, stackTrace) {
      _logger.error(
        'Failed to submit selected industry',
        error: error,
        stackTrace: stackTrace,
      );
      safeEmit(state.copyWith(showLoading: false));
      unawaited(_showErrorToastMessage(message: error.toString()));
    }
  }

  Future<void> onBackPressed() async => _navigationProvider.goBack();

  Future<void> _showErrorToastMessage({required String message}) async {
    _toastMessageProvider.showRetailMobileThemedToastMessage(
      NotificationToastMessageConfiguration.error(message),
    );
  }

  @override
  String toString() => 'IndustrySelectionCubit';
}
