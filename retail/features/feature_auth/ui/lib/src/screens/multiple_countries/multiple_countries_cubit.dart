import 'dart:ui';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_auth_ui/l10n/auth_localization.g.dart';
import 'package:feature_auth_ui/src/screens/country_of_birth/manual_complete_result.dart';
import 'package:feature_auth_ui/src/screens/flows/personal_details_flow/cubit/personal_details_flow_global_state.dart';
import 'package:feature_auth_ui/src/screens/flows/personal_details_flow/cubit/personal_details_flow_steps.dart';
import 'package:feature_auth_ui/src/screens/multiple_countries/multiple_countries_delegate.dart';
import 'package:feature_auth_ui/src/screens/multiple_countries/multiple_coutries_state.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_centralised_questionnaire_service_api/centralised_questionnaire_service_api.dart';

class MultipleCountriesCubit extends StepScreenBaseCubit<
    PersonalDetailsFlowGlobalState, MultipleCountriesState> {
  final NavigationProvider navigationProvider;
  final AuthLocalizations localizations;
  final FeatureToggleProvider featureToggleProvider;

  MultipleCountriesCubit({
    required this.navigationProvider,
    required this.localizations,
    required this.featureToggleProvider,
  }) : super(MultipleCountriesState());

  Future<void> initialize(VoidCallback? backNavigationCallback) async {
    final result = await navigationProvider.push(
      ListItemTemplateNavigationConfig(
        delegateInstanceIdentifier:
            MultipleCountriesListItemDelegate.delegateInstanceIdentifier,
        config: ListItemTemplateConfig(
          title: localizations.multipleCountriesQuestionTitle,
          description: localizations.multipleCountriesQuestionDescription,
          listItems: [
            ListItemConfig(
              title: localizations.pepOptionNo,
              id: localizations.pepOptionNo,
            ),
            ListItemConfig(
              title: localizations.pepOptionYes,
              id: localizations.pepOptionYes,
            ),
          ],
        ),
      ),
    );
    if (result is bool && result) {
      final result =
          featureToggleProvider.get(AuthFeatureToggles.isNewFatcaCRSFlowEnabled)
              ? ManualCompleteResult(
                  PersonalDetailsFlowStepsIds.onboard_multiple_countries,
                )
              : null;
      stepScreenController.onComplete(
        StepScreenResultType.ready,
        result: result,
      );
    } else {
      backNavigationCallback != null
          ? backNavigationCallback.call()
          : stepScreenController.onComplete(StepScreenResultType.cancel);
    }
  }

  @override
  String toString() => 'MultipleCountriesCubit';
}
