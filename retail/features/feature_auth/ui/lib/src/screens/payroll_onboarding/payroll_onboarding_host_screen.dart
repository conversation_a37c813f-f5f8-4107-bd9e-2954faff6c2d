import 'package:di/di.dart';
import 'package:feature_auth_ui/src/screens/payroll_onboarding/payroll_onboarding_cubit.dart';
import 'package:feature_auth_ui/src/screens/payroll_onboarding/payroll_onboarding_state.dart';
import 'package:flutter/material.dart';
import 'package:wio_app_core_api/flutter_index.dart';

class PayrollOnboardingHostScreen extends StatelessWidget {
  const PayrollOnboardingHostScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: PageBlocProvider<PayrollOnboardingCubit, PayrollOnboardingState>(
        createBloc: () {
          final cubit = DependencyProvider.get<PayrollOnboardingCubit>();
          _handleNavigation(cubit.startFlow);
          return cubit;
        },
        child: const Scaffold(body: SizedBox.shrink()),
      ),
    );
  }

  void _handleNavigation(VoidCallback callback) {
    WidgetsBinding.instance.addPostFrameCallback((_) => callback.call());
  }
}
