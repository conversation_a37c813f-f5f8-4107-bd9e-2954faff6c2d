import 'package:di/di.dart';
import 'package:feature_auth_api/domain/models/sof_options_model.dart';
import 'package:feature_auth_ui/feature_auth_ui.dart';
import 'package:feature_auth_ui/src/screens/flows/personal_details_flow/cubit/personal_details_flow_global_state.dart';
import 'package:feature_auth_ui/src/screens/sof/sof_cubit.dart';
import 'package:feature_auth_ui/src/screens/sof/sof_state.dart';
import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class _Constants {
  static const String otherCode = 'other';
}

class SofPage
    extends StepScreenBase<PersonalDetailsFlowGlobalState, SofState, SofCubit> {
  final String _screenId;

  SofPage({
    required super.stepScreenController,
    required String screenId,
    super.key,
  }) : _screenId = screenId;

  @override
  SofCubit createBloc() =>
      DependencyProvider.getWithParams<SofCubit, String, void>(
        param1: _screenId,
      );

  @override
  void initBloc(SofCubit bloc) {
    super.initBloc(bloc);
    bloc.init();
  }

  @override
  Widget buildStepScreen(
    BuildContext context,
    SofCubit bloc,
    SofState state,
  ) {
    final localizations = AuthLocalizations.of(context);

    return FixedButtonsScrollablePageLayout(
      model: FixedButtonsScrollablePageLayoutModel(
        primaryButton: FixedButtonsScrollablePageLayoutButton(
          label: localizations.sofProceedButton,
        ),
      ),
      onPrimaryButtonPressed: state.canSubmit ? bloc.submit : null,
      children: [
        PageText(
          PageTextModel(
            title: localizations.sof_title,
            subtitle: localizations.sof_subtitle,
          ),
        ),
        Padding(
          padding: const EdgeInsetsDirectional.only(top: 4.0),
          child: InkWell(
            onTap: () => bloc.showSofBottomSheet(
              localizations.sofBottomsheetTitle,
              localizations.sofBottomsheetDescription,
            ),
            child: Row(
              children: [
                CompanyIcon(
                  CompanyIconModel(
                    icon: CompanyIconPointer.play.toGraphicAsset(),
                    size: CompanyIconSize.small,
                    color: CompanyColorPointer.secondary9,
                  ),
                ),
                Padding(
                  padding: const EdgeInsetsDirectional.only(start: 4.0),
                  child: Text(
                    localizations.sofBottomsheetTitle,
                    style: context.textStyling.b2.copyWith(
                      color: context.colorStyling.secondary9,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 36),
        state.map(
          initial: (_) => Center(
            child: Spinner(
              model: SpinnerModel(type: SpinnerType.primary),
            ),
          ),
          loaded: (state) => _SofOptionsLoadedView(
            options: state.options,
            checkIsSelected: bloc.checkIfSelected,
            onPressed: bloc.confirmOption,
            onOtherTextChanged: bloc.onOtherTextChanged,
          ),
        ),
      ],
    );
  }
}

class _SofOptionsLoadedView extends StatelessWidget {
  final List<SofOptionsModel> options;
  final bool Function(int id) checkIsSelected;
  final void Function(int id) onPressed;
  final void Function(String text) onOtherTextChanged;

  const _SofOptionsLoadedView({
    required this.options,
    required this.checkIsSelected,
    required this.onPressed,
    required this.onOtherTextChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (var i = 0; i < options.length; i++) ...[
          _ListElement(
            label: options[i].value,
            selected: checkIsSelected(i),
            onOtherTextChanged: onOtherTextChanged,
            onPressed: () => onPressed(i),
          ),
          if (i < options.length - 1) const SizedBox(height: 8),
        ],
      ],
    );
  }
}

class _ListElement extends StatefulWidget {
  final String label;
  final bool selected;
  final void Function()? onPressed;
  final ValueChanged<String> onOtherTextChanged;

  const _ListElement({
    required this.label,
    required this.selected,
    required this.onOtherTextChanged,
    this.onPressed,
  });

  @override
  State<_ListElement> createState() => _ListElementState();
}

class _ListElementState extends State<_ListElement> {
  @override
  Widget build(BuildContext context) {
    final localizations = AuthLocalizations.of(context);

    return Column(
      children: [
        ListBox(
          onPressed: widget.onPressed,
          listBoxModel: ListBoxModel(
            isBoxed: true,
            rightPartModel: ListBoxPartModel.checkbox(value: widget.selected),
            textModel: ListBoxTextModel(title: widget.label),
            isSelected: widget.selected,
          ),
        ),
        if (widget.label.toLowerCase() == _Constants.otherCode &&
            widget.selected)
          Padding(
            padding: const EdgeInsetsDirectional.only(top: 4.0),
            child: InputField(
              model: InputFieldModel(
                hint: '',
                size: InputFieldSize.small,
                theme: InputFieldTheme.light,
                label: localizations.sofOtherLabel,
                rightIcon:
                    const GraphicAssetPointer.icon(CompanyIconPointer.edit),
              ),
              onFieldSubmitted: (_) {},
              onInputChanged: widget.onOtherTextChanged,
              autoFocus: true,
            ),
          ),
      ],
    );
  }
}
