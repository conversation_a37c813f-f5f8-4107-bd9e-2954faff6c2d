import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:wio_feature_user_api/index.dart';

class CustomerIdInteractorImpl implements CustomerIdInteractor {
  final CustomerIdRepository _customerIdRepository;

  const CustomerIdInteractorImpl({
    required CustomerIdRepository customerIdRepository,
  }) : _customerIdRepository = customerIdRepository;

  @override
  Future<String?> getCustomerId() => _customerIdRepository.readCustomerId();

  @override
  Future<void> saveCustomerId(String customerId) =>
      _customerIdRepository.saveCustomerId(customerId);
}
