import 'package:async/async.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_auth_impl/src/data/services/auth_service.dart';
import 'package:feature_auth_impl/src/data/token_parser.dart';
import 'package:rxdart/rxdart.dart';
import 'package:wio_feature_identity_data_api/index.dart';
import 'package:wio_feature_retail_identity_domain_api/index.dart';

class TokenRepositoryImpl implements TokenRepository {
  final TokenParser _tokenParser;
  final AuthService _authService;
  final IdentityMapper _identityMapper;
  final _pendingRequests = <CancelableOperation<Session>>[];

  // ignore: close_sinks
  final _tokenSubject = BehaviorSubject<Token?>();

  Token? _token;
  String? _uaePassToken;

  TokenRepositoryImpl({
    required TokenParser tokenParser,
    required AuthService authService,
    required IdentityMapper identityMapper,
  })  : _tokenParser = tokenParser,
        _identityMapper = identityMapper,
        _authService = authService;

  @override
  Stream<bool> get isUserLoggedInAndActive => _tokenSubject.map(
        (token) {
          if (token == null) {
            return false;
          }

          return !_tokenParser.isExpired(token: token.accessToken);
        },
      );

  @override
  ValueStream<Token?> observeToken() => _tokenSubject;

  @override
  Token? getCacheToken() => _token;

  @override
  void saveToken({required Token token}) {
    _token = token;
    _tokenSubject.add(token);
  }

  @override
  String? getCacheBrokerId() {
    final currentToken = getCacheToken();
    if (currentToken != null) {
      return _tokenParser.getBrokerId(token: currentToken.accessToken);
    }

    return null;
  }

  @override
  bool isExpired() {
    final currentToken = getCacheToken();
    if (currentToken != null) {
      return _tokenParser.isExpired(token: currentToken.accessToken);
    }

    return true;
  }

  @override
  Future<Token> updateToken({required String refreshToken}) async {
    final operation = CancelableOperation.fromFuture(
      _authService.updateToken(refreshToken),
    );
    _pendingRequests.add(operation);
    final result = await operation.value;
    _pendingRequests.remove(operation);
    final newToken = _identityMapper.toTokenResponse(result);

    return newToken;
  }

  @override
  String? getUaePassCacheToken() => _uaePassToken;

  @override
  void saveUaePassToken({required String token}) {
    _uaePassToken = token;
  }

  @override
  Future<void> clear() async {
    for (final operation in _pendingRequests) {
      await operation.cancel();
    }
    _pendingRequests.clear();
    _token = null;
    _uaePassToken = null;
  }
}
