import 'package:feature_required_actions_impl/src/data/required_actions_repository_impl.dart';
import 'package:feature_required_actions_impl/src/data/required_actions_service.dart';
import 'package:feature_required_actions_impl/src/data/required_details_dto_mapper.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_onboarding_data_api/models/customer_external_details_secure_specs.swagger.dart';
import 'package:wio_feature_required_action_common_api/feature_required_action_common.dart';

import 'mocks.dart';

void main() {
  late KycDtoMapper dtoMapper;

  late TestEnvProvider envProvider;
  late IRestApiClient httpClient;

  setUp(() {
    dtoMapper = KycDtoMapperImpl();
    envProvider = TestEnvProvider();

    httpClient = MockIRestApiClient();

    registerFallbackValue(
      const CustomerPostOnboardingUpdateDataDto(),
    );
    registerFallbackValue(FakeEnvKey());
    registerFallbackValue(FakeRestApiRequest());
  });

  test('''fetch required action''', () async {
    // Arrange

    const screens = [
      'SOURCE_OF_FUND',
      'ESTIMATED_DEPOSIT',
      'ESTIMATED_WITHDRAWAL',
      'RESIDENTIAL_ADDRESS',
      'MONTHLY_INCOME',
      'FATCA_CRS',
      'OCCUPATION',
    ];

    const occupationId = 'SELF_EMPLOYED';

    when(
      () => httpClient.execute<Object>(any()),
    ).justAnswerAsync(
      RestApiResponse<Map<String, dynamic>>(
        data: {
          'screenAction': {
            'name': 'ANNUAL_KYC_DETAILS_SCREEN',
            'skippable': false,
            'screens': screens,
            'additionalParameters': {'employmentInfo': occupationId},
          },
        },
      ),
    );
    final service = RequiredActionsServiceImpl(httpClient, envProvider);

    final repository = RequiredActionsRepositoryImpl(
      mapper: dtoMapper,
      service: service,
    );

    // Act
    final requiredAction = await repository.fetchRequiredAction();

    expect(requiredAction!.screens.length, screens.length);
    expect(requiredAction.screens.contains(ActionScreen.sourceOfFund), true);
    expect(
      requiredAction.screens.contains(ActionScreen.estimatedDeposit),
      true,
    );
    expect(
      requiredAction.screens.contains(ActionScreen.estimatedWithdrawal),
      true,
    );
    expect(
      requiredAction.screens.contains(ActionScreen.residentialAddress),
      true,
    );
    expect(requiredAction.screens.contains(ActionScreen.monthlyIncome), true);
    expect(requiredAction.screens.contains(ActionScreen.fatcaCrs), true);
    expect(requiredAction.screens.contains(ActionScreen.occupation), true);

    expect(requiredAction.occupationalStatusId, occupationId);
    expect(requiredAction.type, ActionType.kyc);
  });
}
