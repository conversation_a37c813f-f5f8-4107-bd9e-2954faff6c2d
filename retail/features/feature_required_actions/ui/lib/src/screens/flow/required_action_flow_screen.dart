import 'package:di/di.dart';
import 'package:feature_required_actions_api/feature_required_actions_api.dart';
import 'package:feature_required_actions_ui/src/screens/flow/cubit/required_action_flow_cubit.dart';
import 'package:feature_required_actions_ui/src/screens/flow/cubit/required_action_flow_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RequiredActionFlowScreen extends StatelessWidget {
  final RequiredAction requiredAction;

  const RequiredActionFlowScreen({
    required this.requiredAction,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<RequiredActionFlowCubit>(
      create: (_) => DependencyProvider.getWithParams<RequiredActionFlowCubit,
          RequiredAction, void>(
        param1: requiredAction,
      ),
      child: Builder(
        builder: (context) {
          return BlocListener<RequiredActionFlowCubit, RequiredActionFlowState>(
            child: const FlowStarter(),
            listenWhen: (prev, cur) =>
                cur.currentScreenIndex > prev.currentScreenIndex,
            listener: (context, state) {
              final cubit = context.read<RequiredActionFlowCubit>();
              final currentScreen = state.screens[state.currentScreenIndex];
              cubit.navigateToScreen(currentScreen);
            },
          );
        },
      ),
    );
  }
}

class FlowStarter extends StatefulWidget {
  const FlowStarter({super.key});

  @override
  State<FlowStarter> createState() => _FlowStarterState();
}

class _FlowStarterState extends State<FlowStarter> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RequiredActionFlowCubit>().moveToNextStep();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(color: Colors.transparent);
  }
}
