// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'multi_sections_answer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MultiSectionsAnswer _$MultiSectionsAnswerFromJson(Map<String, dynamic> json) =>
    MultiSectionsAnswer(
      exactType: $enumDecode(_$AnswerTypeEnumMap, json['type']),
      answers: (json['answers'] as List<dynamic>?)
              ?.map((e) => SectionAnswer.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$MultiSectionsAnswerToJson(
        MultiSectionsAnswer instance) =>
    <String, dynamic>{
      'type': _$AnswerTypeEnumMap[instance.exactType]!,
      'answers': instance.answers.map((e) => e.toJson()).toList(),
    };

const _$AnswerTypeEnumMap = {
  AnswerType.select: 'SELECT',
  AnswerType.textInput: 'TEXT_INPUT',
  AnswerType.address: 'ADDRESS',
  AnswerType.termsAndConditions: 'TERMS_AND_CONDITIONS',
  AnswerType.multiSelect: 'MULTI_SELECT',
  AnswerType.multiSections: 'MULTI_SECTIONS',
  AnswerType.uploadDocuments: 'UPLOAD_DOCUMENTS',
  AnswerType.congratulations: 'CONGRATULATIONS',
};
