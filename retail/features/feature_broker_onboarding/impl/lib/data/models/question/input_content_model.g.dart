// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'input_content_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InputContent _$InputContentFromJson(Map<String, dynamic> json) => InputContent(
      headerText: json['headerText'] as String,
      buttonText: json['buttonText'] as String,
      options: optionsFromJson(json['options'] as List?),
      text: json['text'] as String?,
      bottomText: json['bottomText'] as String?,
    );

Map<String, dynamic> _$InputContentToJson(InputContent instance) =>
    <String, dynamic>{
      'headerText': instance.headerText,
      'buttonText': instance.buttonText,
      'options': instance.options,
      if (instance.text case final value?) 'text': value,
      if (instance.bottomText case final value?) 'bottomText': value,
    };
