// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_with_language_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DocumentWithLanguage _$DocumentWithLanguageFromJson(
        Map<String, dynamic> json) =>
    DocumentWithLanguage(
      language: json['language'] as String,
      documents: (json['documents'] as List<dynamic>?)
              ?.map((e) => Document.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );

Map<String, dynamic> _$DocumentWithLanguageToJson(
        DocumentWithLanguage instance) =>
    <String, dynamic>{
      'language': instance.language,
      'documents': instance.documents,
    };
