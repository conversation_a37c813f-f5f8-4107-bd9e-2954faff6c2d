import 'package:feature_broker_onboarding_impl/data/models/question/input_answer_model.dart';
import 'package:feature_broker_onboarding_impl/data/models/question/input_content_model.dart';
import 'package:feature_broker_onboarding_impl/data/models/question/question_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'input_question_model.g.dart';

@JsonSerializable()
class InputQuestion extends Question {
  @JsonKey(name: 'id', includeIfNull: false)
  final String id;
  @JsonKey(
    name: 'type',
    includeIfNull: false,
  )
  final QuestionType type;
  @JsonKey(name: 'answer', includeIfNull: false)
  final InputAnswer? answer;
  @J<PERSON><PERSON>ey(name: 'content', includeIfNull: false)
  final InputContent content;

  InputQuestion({
    required this.id,
    required this.type,
    this.answer,
    required this.content,
  }) : super();

  factory InputQuestion.fromJson(Map<String, dynamic> json) =>
      _$InputQuestionFromJson(json);
}
