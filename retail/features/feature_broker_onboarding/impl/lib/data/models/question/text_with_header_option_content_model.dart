import 'package:json_annotation/json_annotation.dart';

part 'text_with_header_option_content_model.g.dart';

@JsonSerializable()
class TextWithHeaderOptionContent {
  @Json<PERSON>ey(name: 'text', includeIfNull: false)
  final String text;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'headerText', includeIfNull: false)
  final String headerText;

  TextWithHeaderOptionContent({
    required this.text,
    required this.headerText,
  });

  factory TextWithHeaderOptionContent.fromJson(Map<String, dynamic> json) =>
      _$TextWithHeaderOptionContentFromJson(json);
}
