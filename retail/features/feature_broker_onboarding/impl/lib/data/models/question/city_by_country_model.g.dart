// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'city_by_country_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CityByCountry _$CityByCountryFromJson(Map<String, dynamic> json) =>
    CityByCountry(
      id: json['id'] as String,
      countryName: json['countryName'] as String,
      cities: (json['cities'] as List<dynamic>)
          .map((e) => City.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$CityByCountryToJson(CityByCountry instance) =>
    <String, dynamic>{
      'id': instance.id,
      'countryName': instance.countryName,
      'cities': instance.cities,
    };
