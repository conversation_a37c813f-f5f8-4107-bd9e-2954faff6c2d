// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'multi_sections_question_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MultiSectionsQuestion _$MultiSectionsQuestionFromJson(
        Map<String, dynamic> json) =>
    MultiSectionsQuestion(
      id: json['id'] as String,
      type: $enumDecode(_$QuestionTypeEnumMap, json['type']),
      content: MultiSectionsContent.fromJson(
          json['content'] as Map<String, dynamic>),
      answer: json['answer'] == null
          ? null
          : MultiSectionsAnswer.fromJson(
              json['answer'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MultiSectionsQuestionToJson(
        MultiSectionsQuestion instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$QuestionTypeEnumMap[instance.type]!,
      if (instance.answer case final value?) 'answer': value,
      'content': instance.content,
    };

const _$QuestionTypeEnumMap = {
  QuestionType.select: 'SELECT',
  QuestionType.selectWithSearch: 'SELECT_WITH_SEARCH',
  QuestionType.input: 'INPUT',
  QuestionType.address: 'ADDRESS',
  QuestionType.termsAndConditions: 'TERMS_AND_CONDITIONS',
  QuestionType.empty: 'EMPTY',
  QuestionType.multiSelect: 'MULTI_SELECT',
  QuestionType.multiSections: 'MULTI_SECTIONS',
  QuestionType.uploadDocuments: 'UPLOAD_DOCUMENTS',
  QuestionType.congratulations: 'CONGRATULATIONS',
};

MultiSectionsContent _$MultiSectionsContentFromJson(
        Map<String, dynamic> json) =>
    MultiSectionsContent(
      headerText: json['headerText'] as String,
      text: json['text'] as String,
      sectionsContent: (json['sectionsContent'] as List<dynamic>?)
              ?.map((e) => SectionContent.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      buttonText: json['buttonText'] as String,
      noticeTextGroups: (json['noticeTextGroups'] as List<dynamic>?)
          ?.map((e) => NoticeTextGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      textLinks: (json['textLinks'] as List<dynamic>?)
          ?.map((e) => TextLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      secondaryButtonText: json['secondaryButtonText'] as String?,
      bottomText: json['bottomText'] as String?,
      checkBoxTexts: (json['checkBoxTexts'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$MultiSectionsContentToJson(
        MultiSectionsContent instance) =>
    <String, dynamic>{
      'headerText': instance.headerText,
      'text': instance.text,
      'sectionsContent': instance.sectionsContent,
      'buttonText': instance.buttonText,
      if (instance.textLinks case final value?) 'textLinks': value,
      'noticeTextGroups': instance.noticeTextGroups,
      if (instance.checkBoxTexts case final value?) 'checkBoxTexts': value,
      if (instance.secondaryButtonText case final value?)
        'secondaryButtonText': value,
      if (instance.bottomText case final value?) 'bottomText': value,
    };

TextLink _$TextLinkFromJson(Map<String, dynamic> json) => TextLink(
      bottomSheetContent: BottomSheetContent.fromJson(
          json['bottomSheetContent'] as Map<String, dynamic>),
      highlightedText: json['highlightedText'] as String?,
      buttonLink: $enumDecodeNullable(_$ButtonLinkEnumMap, json['buttonLink']),
    );

Map<String, dynamic> _$TextLinkToJson(TextLink instance) => <String, dynamic>{
      if (instance.highlightedText case final value?) 'highlightedText': value,
      'bottomSheetContent': instance.bottomSheetContent,
      if (_$ButtonLinkEnumMap[instance.buttonLink] case final value?)
        'buttonLink': value,
    };

const _$ButtonLinkEnumMap = {
  ButtonLink.primary: 'PRIMARY',
  ButtonLink.secondary: 'SECONDARY',
};

BottomSheetContent _$BottomSheetContentFromJson(Map<String, dynamic> json) =>
    BottomSheetContent(
      headerText: json['headerText'] as String?,
      text: json['text'] as String?,
      buttonText: json['buttonText'] as String?,
      navigationType:
          $enumDecodeNullable(_$NavigationTypeEnumMap, json['navigationType']),
      navigationFlow:
          $enumDecodeNullable(_$NavigationFlowEnumMap, json['navigationFlow']),
    );

Map<String, dynamic> _$BottomSheetContentToJson(BottomSheetContent instance) =>
    <String, dynamic>{
      if (instance.headerText case final value?) 'headerText': value,
      if (instance.text case final value?) 'text': value,
      if (instance.buttonText case final value?) 'buttonText': value,
      if (_$NavigationTypeEnumMap[instance.navigationType] case final value?)
        'navigationType': value,
      if (_$NavigationFlowEnumMap[instance.navigationFlow] case final value?)
        'navigationFlow': value,
    };

const _$NavigationTypeEnumMap = {
  NavigationType.onboarding: 'ONBOARDING',
};

const _$NavigationFlowEnumMap = {
  NavigationFlow.complexInstruments: 'COMPLEX_INSTRUMENTS',
};

SectionContent _$SectionContentFromJson(Map<String, dynamic> json) =>
    SectionContent(
      sectionId: json['sectionId'] as String,
      type: $enumDecode(_$QuestionTypeEnumMap, json['type']),
      label: json['label'] as String?,
      content: SectionContent._baseContentFromJson(
          json['content'] as Map<String, dynamic>),
      conditions: (json['conditions'] as List<dynamic>)
          .map((e) => SectionCondition.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SectionContentToJson(SectionContent instance) =>
    <String, dynamic>{
      'sectionId': instance.sectionId,
      'type': _$QuestionTypeEnumMap[instance.type]!,
      if (instance.label case final value?) 'label': value,
      'conditions': instance.conditions,
    };

SectionCondition _$SectionConditionFromJson(Map<String, dynamic> json) =>
    SectionCondition(
      type: $enumDecode(_$SectionConditionTypeEnumMap, json['type']),
      answer: const AnswerJsonConverter()
          .fromJson(json['answer'] as Map<String, dynamic>),
      nextSectionId: json['nextSectionId'] as String,
    );

Map<String, dynamic> _$SectionConditionToJson(SectionCondition instance) =>
    <String, dynamic>{
      if (_$SectionConditionTypeEnumMap[instance.type] case final value?)
        'type': value,
      'nextSectionId': instance.nextSectionId,
      'answer': const AnswerJsonConverter().toJson(instance.answer),
    };

const _$SectionConditionTypeEnumMap = {
  SectionConditionType.swaggerGeneratedUnknown: null,
  SectionConditionType.nextSection: 'NEXT_SECTION',
};

NoticeTextGroup _$NoticeTextGroupFromJson(Map<String, dynamic> json) =>
    NoticeTextGroup(
      noticeTexts: (json['noticeTexts'] as List<dynamic>)
          .map((e) => NoticeText.fromJson(e as Map<String, dynamic>))
          .toList(),
      headerText: json['headerText'] as String?,
    );

Map<String, dynamic> _$NoticeTextGroupToJson(NoticeTextGroup instance) =>
    <String, dynamic>{
      'noticeTexts': instance.noticeTexts.map((e) => e.toJson()).toList(),
      if (instance.headerText case final value?) 'headerText': value,
    };

NoticeText _$NoticeTextFromJson(Map<String, dynamic> json) => NoticeText(
      text: json['text'] as String,
      color: $enumDecode(_$NoticeTextColorEnumMap, json['textColor'],
          unknownValue: NoticeTextColor.black),
      type: $enumDecode(_$NoticeTextTypeEnumMap, json['textType'],
          unknownValue: NoticeTextType.medium),
      hasIconBackground: json['hasIconBackground'] as bool? ?? false,
      subtext: json['subtext'] as String? ?? '',
      subtextColor: $enumDecodeNullable(
              _$NoticeTextColorEnumMap, json['subtextColor'],
              unknownValue: NoticeTextColor.black) ??
          NoticeTextColor.black,
      subtextType: $enumDecodeNullable(
              _$NoticeTextTypeEnumMap, json['subtextType'],
              unknownValue: NoticeTextType.medium) ??
          NoticeTextType.medium,
      icon: $enumDecodeNullable(_$NoticeIconTypeEnumMap, json['icon'],
              unknownValue: NoticeIconType.unknown) ??
          NoticeIconType.unknown,
    );

Map<String, dynamic> _$NoticeTextToJson(NoticeText instance) =>
    <String, dynamic>{
      'text': instance.text,
      'textColor': _$NoticeTextColorEnumMap[instance.color]!,
      'textType': _$NoticeTextTypeEnumMap[instance.type]!,
      'subtext': instance.subtext,
      'subtextColor': _$NoticeTextColorEnumMap[instance.subtextColor]!,
      'subtextType': _$NoticeTextTypeEnumMap[instance.subtextType]!,
      'icon': _$NoticeIconTypeEnumMap[instance.icon],
      'hasIconBackground': instance.hasIconBackground,
    };

const _$NoticeTextColorEnumMap = {
  NoticeTextColor.black: 'BLACK',
  NoticeTextColor.gray: 'GRAY',
};

const _$NoticeTextTypeEnumMap = {
  NoticeTextType.medium: 'TYPE_2',
  NoticeTextType.small: 'TYPE_1',
};

const _$NoticeIconTypeEnumMap = {
  NoticeIconType.alert: 'ICON_ALERT',
  NoticeIconType.info: 'ICON_INFO',
  NoticeIconType.lock: 'ICON_LOCK',
  NoticeIconType.percentage: 'ICON_PERCENTAGE',
  NoticeIconType.clock: 'ICON_CLOCK',
  NoticeIconType.calendar: 'ICON_CALENDAR',
  NoticeIconType.unknown: 'unknown',
};
