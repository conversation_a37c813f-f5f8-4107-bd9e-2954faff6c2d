import 'package:feature_broker_onboarding_impl/data/models_index.dart' as dto;
import 'package:feature_broker_onboarding_impl/index.dart';
import 'package:feature_neobroker_onboarding_api/index.dart';
import 'package:tests/tests.dart';

void main() {
  test(
    'convert to OnboardingStepUploadDocuments from dto.UploadDocumentsQuestion',
    () {
      final dtoUploadDocumentsQuestion = dto.UploadDocumentsQuestion(
        id: 'UPLOAD_DOCUMENTS',
        type: dto.QuestionType.uploadDocuments,
        content: dto.UploadDocumentsContent(
          headerText: 'headerText',
          buttonText: 'buttonText',
          description: 'description',
          documents: [
            dto.UploadableDocument(
              name: 'name',
              documentType: 'documentType',
              validations: dto.UploadableDocumentValidations(
                contentTypes: ['png', 'jpg'],
                maxFileSize: 12,
                maxFilesAmount: 14,
                minFilesAmount: 11,
              ),
            ),
          ],
          categoryType: 'categoryType',
        ),
      );

      final response = dto.OnboardingQuestionResponse(
        question: dtoUploadDocumentsQuestion,
        screen: dto.Screen(
          actions: dto.Actions(back: false, isCompleted: false),
          progress: dto.Progress(
            answered: 1,
            total: 2,
          ),
        ),
      );

      final domainModel = response.toDomain();

      const onboardingDomainStep = OnboardingStep.uploadDocuments(
        id: 'UPLOAD_DOCUMENTS',
        flowId: OnboardingAccountFlowScreensIds.upload_documents,
        progress: OnboardingProgress(currentStep: 1, totalStepsCount: 2),
        action: OnboardingAction(
          canGoBack: false,
        ),
        headerText: 'headerText',
        buttonText: 'buttonText',
        description: 'description',
        categoryType: 'categoryType',
        documents: [
          UploadableDocument(
            documentType: 'documentType',
            name: 'name',
            validations: UploadableDocumentValidations(
              allowedExtensions: ['png', 'jpg'],
              maxFileSize: 12,
              maxFilesAmount: 14,
              minFilesAmount: 11,
            ),
          ),
        ],
      );

      expect(domainModel, equals(onboardingDomainStep));
    },
  );

  test('should convert to UploadableDocument from dto.UploadableDocument', () {
    final dtoUploadableDocument = dto.UploadableDocument(
      name: 'name',
      documentType: 'documentType',
      validations: dto.UploadableDocumentValidations(
        contentTypes: ['png', 'jpg'],
        maxFileSize: 12,
        maxFilesAmount: 14,
        minFilesAmount: 11,
      ),
    );

    final domainModel = dtoUploadableDocument.toDomain();

    const uploadableDocument = UploadableDocument(
      documentType: 'documentType',
      name: 'name',
      validations: UploadableDocumentValidations(
        allowedExtensions: ['png', 'jpg'],
        maxFileSize: 12,
        maxFilesAmount: 14,
        minFilesAmount: 11,
      ),
    );

    expect(domainModel, equals(uploadableDocument));
  });

  test('''
convert to UploadableDocumentValidations from UploadableDocumentValidations''',
      () {
    final dtoUploadableDocumentValidations = dto.UploadableDocumentValidations(
      contentTypes: ['png', 'jpg'],
      maxFileSize: 12,
      maxFilesAmount: 14,
      minFilesAmount: 11,
    );

    final domainModel = dtoUploadableDocumentValidations.toDomain();

    const uploadableDocumentValidations = UploadableDocumentValidations(
      allowedExtensions: ['png', 'jpg'],
      maxFileSize: 12,
      maxFilesAmount: 14,
      minFilesAmount: 11,
    );

    expect(domainModel, equals(uploadableDocumentValidations));
  });

  test('''
should convert to OnboardingAnswerUploadDocuments from dto.UploadDocumentsAnswer''',
      () {
    final dtoUploadDocumentsAnswer = dto.UploadDocumentsAnswer(
      exactType: dto.AnswerType.uploadDocuments,
      categoryType: 'categoryType',
      documents: [
        dto.UploadedDocument(
          documentType: 'documentType',
          documentId: 'documentId',
        ),
      ],
    );

    final domainModel = dtoUploadDocumentsAnswer.toDomain('questionId');

    const onboardingAnswerUploadDocuments = OnboardingAnswerUploadDocuments(
      questionId: 'questionId',
      categoryType: 'categoryType',
      documents: [
        UploadedDocument(
          documentType: 'documentType',
          documentId: 'documentId',
        ),
      ],
    );

    expect(domainModel, equals(onboardingAnswerUploadDocuments));
  });

  test('should convert to UploadedDocument from dto.UploadedDocument', () {
    final dtoUploadedDocument = dto.UploadedDocument(
      documentType: 'documentType',
      documentId: 'documentId',
    );

    final domainModel = dtoUploadedDocument.toDomain();

    const uploadedDocument = UploadedDocument(
      documentType: 'documentType',
      documentId: 'documentId',
    );

    expect(domainModel, equals(uploadedDocument));
  });

  test('''
should convert to UploadedDocument from dto.OnboardingDocumentUploadResponse''',
      () {
    final dtoOnboardingDocumentUploadResponse =
        dto.OnboardingDocumentUploadResponse(
      documentType: 'documentType',
      documentId: 'documentId',
    );

    final domainModel = dtoOnboardingDocumentUploadResponse.toDomain();

    const uploadedDocument = UploadedDocument(
      documentType: 'documentType',
      documentId: 'documentId',
    );

    expect(domainModel, equals(uploadedDocument));
  });

  test('should convert OnboardingAnswer to dto answer', () {
    const domain = OnboardingAnswerUploadDocuments(
      categoryType: 'categoryType',
      documents: [
        UploadedDocument(
          documentType: 'documentType',
          documentId: 'documentId',
        ),
      ],
      questionId: 'questionId',
    );
    final dtoAnswer = dto.UploadDocumentsAnswer(
      exactType: dto.AnswerType.uploadDocuments,
      categoryType: 'categoryType',
      documents: [
        dto.UploadedDocument(
          documentType: 'documentType',
          documentId: 'documentId',
        ),
      ],
    );

    expect(domain.categoryType, equals(dtoAnswer.categoryType));
    expect(
      domain.documents.first.documentType,
      equals(dtoAnswer.documents.first.documentType),
    );
    expect(
      domain.documents.first.documentId,
      equals(dtoAnswer.documents.first.documentId),
    );
  });

  ///All tests for fromJson Dto
  group('Tests for fromJson for Dtos', () {
    test('should convert to UploadableDocument from dto.UploadableDocument',
        () {
      final jsonData = {
        'name': 'name',
        'type': 'documentType',
        'validations': {
          'contentTypes': ['png', 'jpg'],
          'maxFileSize': 12,
          'maxFilesAmount': 14,
          'minFilesAmount': 11,
        },
      };

      const domainModel = UploadableDocument(
        documentType: 'documentType',
        name: 'name',
        validations: UploadableDocumentValidations(
          allowedExtensions: ['png', 'jpg'],
          maxFileSize: 12,
          maxFilesAmount: 14,
          minFilesAmount: 11,
        ),
      );

      final fromJsonDomain =
          dto.UploadableDocument.fromJson(jsonData).toDomain();

      expect(
        fromJsonDomain,
        equals(domainModel),
      );
    });

    test('''
should convert to UploadableDocumentValidations from dto.UploadableDocumentValidations''',
        () {
      final jsonData = {
        'contentTypes': ['png', 'jpg'],
        'maxFileSize': 12,
        'maxFilesAmount': 14,
        'minFilesAmount': 11,
      };

      const domainModel = UploadableDocumentValidations(
        allowedExtensions: ['png', 'jpg'],
        maxFileSize: 12,
        maxFilesAmount: 14,
        minFilesAmount: 11,
      );

      final fromJsonDomain =
          dto.UploadableDocumentValidations.fromJson(jsonData).toDomain();

      expect(
        fromJsonDomain,
        equals(domainModel),
      );
    });

    test('should convert to UploadedDocument from dto.UploadedDocument', () {
      final jsonData = {
        'documentType': 'documentType',
        'documentId': 'documentId',
      };

      const domainModel = UploadedDocument(
        documentType: 'documentType',
        documentId: 'documentId',
      );

      final fromJsonDomain = dto.UploadedDocument.fromJson(jsonData).toDomain();

      expect(
        fromJsonDomain,
        equals(domainModel),
      );
    });
  });
}
