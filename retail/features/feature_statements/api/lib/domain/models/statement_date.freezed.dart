// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target

part of 'statement_date.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$StatementDate {
  int get year => throw _privateConstructorUsedError;
  int get month => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $StatementDateCopyWith<StatementDate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StatementDateCopyWith<$Res> {
  factory $StatementDateCopyWith(
          StatementDate value, $Res Function(StatementDate) then) =
      _$StatementDateCopyWithImpl<$Res>;
  $Res call({int year, int month});
}

/// @nodoc
class _$StatementDateCopyWithImpl<$Res>
    implements $StatementDateCopyWith<$Res> {
  _$StatementDateCopyWithImpl(this._value, this._then);

  final StatementDate _value;
  // ignore: unused_field
  final $Res Function(StatementDate) _then;

  @override
  $Res call({
    Object? year = freezed,
    Object? month = freezed,
  }) {
    return _then(_value.copyWith(
      year: year == freezed
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: month == freezed
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
abstract class _$$_StatementDateCopyWith<$Res>
    implements $StatementDateCopyWith<$Res> {
  factory _$$_StatementDateCopyWith(
          _$_StatementDate value, $Res Function(_$_StatementDate) then) =
      __$$_StatementDateCopyWithImpl<$Res>;
  @override
  $Res call({int year, int month});
}

/// @nodoc
class __$$_StatementDateCopyWithImpl<$Res>
    extends _$StatementDateCopyWithImpl<$Res>
    implements _$$_StatementDateCopyWith<$Res> {
  __$$_StatementDateCopyWithImpl(
      _$_StatementDate _value, $Res Function(_$_StatementDate) _then)
      : super(_value, (v) => _then(v as _$_StatementDate));

  @override
  _$_StatementDate get _value => super._value as _$_StatementDate;

  @override
  $Res call({
    Object? year = freezed,
    Object? month = freezed,
  }) {
    return _then(_$_StatementDate(
      year: year == freezed
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: month == freezed
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$_StatementDate implements _StatementDate {
  const _$_StatementDate({required this.year, required this.month});

  @override
  final int year;
  @override
  final int month;

  @override
  String toString() {
    return 'StatementDate(year: $year, month: $month)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_StatementDate &&
            const DeepCollectionEquality().equals(other.year, year) &&
            const DeepCollectionEquality().equals(other.month, month));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(year),
      const DeepCollectionEquality().hash(month));

  @JsonKey(ignore: true)
  @override
  _$$_StatementDateCopyWith<_$_StatementDate> get copyWith =>
      __$$_StatementDateCopyWithImpl<_$_StatementDate>(this, _$identity);
}

abstract class _StatementDate implements StatementDate {
  const factory _StatementDate(
      {required final int year, required final int month}) = _$_StatementDate;

  @override
  int get year => throw _privateConstructorUsedError;
  @override
  int get month => throw _privateConstructorUsedError;
  @override
  @JsonKey(ignore: true)
  _$$_StatementDateCopyWith<_$_StatementDate> get copyWith =>
      throw _privateConstructorUsedError;
}
