import 'dart:typed_data';

import 'package:bloc_test/bloc_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_statements_api/statements_api.dart';
import 'package:wio_feature_statements_ui/src/common/delegates/statement_delegate.dart';
import 'package:wio_feature_statements_ui/src/screens/statement_preview/statement_preview_cubit.dart';
import 'package:wio_feature_statements_ui/src/screens/statement_preview/statement_preview_state.dart';

import '../feature_mocks.dart';

void main() {
  late StatementInteractor statementInteractor;
  late Logger logger;
  late ExhaustStreamExecutor streamExecutor;
  late MockCommonErrorHandler commonErrorHandler;
  late StatementDelegate statementDelegate;

  const date = StatementDate(year: 2042, month: 4);
  const param = StatementPreviewParam.monthly(date);
  final file = StatementFile(
    fileType: '',
    file: Uint8List(0),
    format: param.format,
  );
  const filters = StatementFilters(
    transactionTypes: [TransactionType.cash, TransactionType.card],
    transactionDirections: [TransactionDirection.income],
  );

  setUp(() {
    statementInteractor = MockStatementInteractor();
    logger = MockLogger();
    streamExecutor = MockExhaustStreamExecuter();
    commonErrorHandler = MockCommonErrorHandler();
    statementDelegate = MockStatementDelegate();
  });

  StatementPreviewCubit getCubit(StatementPreviewParam param) =>
      StatementPreviewCubit(
        param: param,
        statementInteractor: statementInteractor,
        logger: logger,
        streamExecutor: streamExecutor,
        commonErrorHandler: commonErrorHandler,
        statementDelegate: statementDelegate,
      );

  setUpAll(() async {
    registerFallbackValue(FakeFormatSelectionConfig());
    registerFallbackValue(const StatementDate(year: 2042, month: 1));
    await initializeDateFormatting('en');
  });

  group('Initialization >', () {
    blocTest<StatementPreviewCubit, StatementPreviewState>(
      'loads data for the given monthly param',
      // Arrange
      build: () => getCubit(param),
      setUp: () {
        when(
          () => statementInteractor.getStatementByDate(date),
        ).justAnswerAsync(file);
      },

      // Act
      act: (bloc) => bloc.initialize(),

      // Assert
      expect: () => [
        const StatementPreviewState.loading(param: param),
        StatementPreviewState.idle(
          param: param,
          files: {param.format: file},
        ),
      ],
      verify: (_) {
        verify(() => statementInteractor.getStatementByDate(date)).calledOnce;
      },
    );

    final dateFrom = DateTime(2042);
    final dateTo = DateTime(2048);
    final filteredParam = StatementPreviewParam.filtered(
      dateFrom: dateFrom,
      dateTo: dateTo,
      selectedFilters: filters,
    );
    blocTest<StatementPreviewCubit, StatementPreviewState>(
      'loads data for the filtered monthlyParam',
      // Arrange
      build: () => getCubit(filteredParam),
      setUp: () {
        when(
          () => statementInteractor.getStatementByFilters(
            dateFrom: dateFrom,
            dateTo: dateTo,
            filters: filters,
          ),
        ).justAnswerAsync(file);
      },

      // Act
      act: (bloc) => bloc.initialize(),

      // Assert
      expect: () => [
        StatementPreviewState.loading(param: filteredParam),
        StatementPreviewState.idle(
          param: filteredParam,
          files: {filteredParam.format: file},
        ),
      ],
      verify: (_) {
        verify(
          () => statementInteractor.getStatementByFilters(
            dateFrom: any(named: 'dateFrom'),
            dateTo: any(named: 'dateTo'),
            filters: filters,
          ),
        ).calledOnce;
      },
    );
  });

  group('Errors >', () {
    blocTest<StatementPreviewCubit, StatementPreviewState>(
      'can reload only from failed state',
      // Arrange
      build: () => getCubit(param),
      seed: () => const StatementPreviewState.failed(param: param),
      setUp: () {
        when(
          () => statementInteractor.getStatementByDate(date),
        ).justAnswerAsync(file);
      },

      // Act
      act: (bloc) => bloc.onRetry(),

      // Assert
      expect: () => [
        const StatementPreviewState.loading(param: param),
        StatementPreviewState.idle(
          param: param,
          files: {StatementFormat.pdf: file},
        ),
      ],
      verify: (_) {
        verify(() => statementInteractor.getStatementByDate(date)).calledOnce;
      },
    );

    blocTest<StatementPreviewCubit, StatementPreviewState>(
      'cannot reload from states other than failed',
      // Arrange
      build: () => getCubit(param),
      seed: () => const StatementPreviewState.initial(param: param),

      // Act
      act: (bloc) => bloc.onRetry(),

      // Assert
      expect: () => const <StatementPreviewState>[],
      verify: (_) {
        verifyNoMoreInteractions(statementInteractor);
      },
    );
  });

  group('Downloading >', () {
    blocTest<StatementPreviewCubit, StatementPreviewState>(
      'downloads and caches a file if it was not cached before',

      // Arrange
      build: () => getCubit(param),
      seed: () => const StatementPreviewState.idle(param: param, files: {}),
      setUp: () {
        when(
          () => statementInteractor.getStatementByDate(
            const StatementDate(year: 2042, month: 4),
          ),
        ).justAnswerAsync(file);
        when(
          () => statementDelegate.shareStatement(
            statement: file,
            dateString: '2042.04',
          ),
        ).justCompleteAsync();
      },

      // Act
      act: (bloc) => bloc.onDownload(),

      // Assert
      expect: () => [
        StatementPreviewState.idle(
          param: param,
          files: {param.format: file},
        ),
      ],
      verify: (_) {
        verify(
          () => statementInteractor.getStatementByDate(
            const StatementDate(year: 2042, month: 4),
          ),
        ).calledOnce;
        verify(
          () => statementDelegate.shareStatement(
            statement: file,
            dateString: '2042.04',
          ),
        ).calledOnce;
      },
    );

    blocTest<StatementPreviewCubit, StatementPreviewState>(
      'downloads and caches a file if it was not cached before',

      // Arrange
      build: () => getCubit(param),
      seed: () => const StatementPreviewState.idle(param: param, files: {}),
      setUp: () {
        when(
          () => statementInteractor.getStatementByDate(
            const StatementDate(year: 2042, month: 4),
          ),
        ).justAnswerAsync(file);

        when(
          () => statementDelegate.shareStatement(
            statement: file,
            dateString: '2042.04',
          ),
        ).justCompleteAsync();
      },

      // Act
      act: (bloc) => bloc.onDownload(),

      // Assert
      expect: () => [
        StatementPreviewState.idle(
          param: param,
          files: {param.format: file},
        ),
      ],
      verify: (_) {
        verify(
          () => statementInteractor.getStatementByDate(
            const StatementDate(year: 2042, month: 4),
          ),
        ).calledOnce;
        verify(
          () => statementDelegate.shareStatement(
            statement: file,
            dateString: '2042.04',
          ),
        ).calledOnce;
      },
    );
  });
}
