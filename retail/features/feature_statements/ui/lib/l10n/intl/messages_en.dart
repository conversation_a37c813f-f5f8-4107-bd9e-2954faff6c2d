// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.
// @dart=2.12
// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String? MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  @override
  String get localeName => 'en';

  static m0(currency) => "Your ${currency} account\'s monthly statements";

  static m1(currency) => "${currency} account";

  @override
  final Map<String, dynamic> messages =
      _notInlinedMessages(_notInlinedMessages);

  static Map<String, dynamic> _notInlinedMessages(_) => {
        'customStatementAllAccountsSubtitle':
            MessageLookupByLibrary.simpleMessage('All accounts'),
        'customStatementAllCardsSubtitle':
            MessageLookupByLibrary.simpleMessage('All cards'),
        'customStatementAllTransactionDirectionsSubtitle':
            MessageLookupByLibrary.simpleMessage('Incoming & outgoing'),
        'customStatementAllTransactionTypesSubtitle':
            MessageLookupByLibrary.simpleMessage('All transaction types'),
        'customStatementEndPeriodTitle':
            MessageLookupByLibrary.simpleMessage('End date'),
        'customStatementFiltersTitle':
            MessageLookupByLibrary.simpleMessage('Included data'),
        'customStatementPageTitle':
            MessageLookupByLibrary.simpleMessage('Custom statement'),
        'customStatementPeriodOneMonth':
            MessageLookupByLibrary.simpleMessage('1 month'),
        'customStatementPeriodOneYear':
            MessageLookupByLibrary.simpleMessage('1 year'),
        'customStatementPeriodSixMonths':
            MessageLookupByLibrary.simpleMessage('6 months'),
        'customStatementPeriodThreeMonths':
            MessageLookupByLibrary.simpleMessage('3 months'),
        'customStatementPrimaryButtonTitle':
            MessageLookupByLibrary.simpleMessage('Generate statement'),
        'customStatementStartPeriodTitle':
            MessageLookupByLibrary.simpleMessage('Start date'),
        'customStatementTimePeriodTitle':
            MessageLookupByLibrary.simpleMessage('Time period'),
        'statementDownloadButtonTitle':
            MessageLookupByLibrary.simpleMessage('Download statement'),
        'statementFilterAccountsTitle':
            MessageLookupByLibrary.simpleMessage('Accounts'),
        'statementFilterCardsTitle':
            MessageLookupByLibrary.simpleMessage('Cards'),
        'statementFilterClearAllButtonTitle':
            MessageLookupByLibrary.simpleMessage('Clear all'),
        'statementFilterClosedAccountLabel':
            MessageLookupByLibrary.simpleMessage('Closed account'),
        'statementFilterSaveButtonTitle':
            MessageLookupByLibrary.simpleMessage('Save'),
        'statementFilterSelectAllButtonTitle':
            MessageLookupByLibrary.simpleMessage('Select all'),
        'statementFilterTransactionDirectionIncome':
            MessageLookupByLibrary.simpleMessage('Incoming'),
        'statementFilterTransactionDirectionOutcome':
            MessageLookupByLibrary.simpleMessage('Outgoing'),
        'statementFilterTransactionDirectionTitle':
            MessageLookupByLibrary.simpleMessage('Transactions direction'),
        'statementFilterTransactionTypeTitle':
            MessageLookupByLibrary.simpleMessage('Transactions type'),
        'statementFormatBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Choose document format'),
        'statementTransactionTypeBills':
            MessageLookupByLibrary.simpleMessage('Bill payments'),
        'statementTransactionTypeBroker':
            MessageLookupByLibrary.simpleMessage('Broker'),
        'statementTransactionTypeCard':
            MessageLookupByLibrary.simpleMessage('Card'),
        'statementTransactionTypeCash':
            MessageLookupByLibrary.simpleMessage('Cash'),
        'statementTransactionTypeCashback':
            MessageLookupByLibrary.simpleMessage('Cashback'),
        'statementTransactionTypeCheque':
            MessageLookupByLibrary.simpleMessage('Cheque book'),
        'statementTransactionTypeCreditRepayment':
            MessageLookupByLibrary.simpleMessage('Credit repayment'),
        'statementTransactionTypeCurrencyExchange':
            MessageLookupByLibrary.simpleMessage('Currency exchange'),
        'statementTransactionTypeDds':
            MessageLookupByLibrary.simpleMessage('Direct Debiting System'),
        'statementTransactionTypeFees':
            MessageLookupByLibrary.simpleMessage('Fees'),
        'statementTransactionTypeInterest':
            MessageLookupByLibrary.simpleMessage('Interest'),
        'statementTransactionTypeIpo':
            MessageLookupByLibrary.simpleMessage('IPO'),
        'statementTransactionTypeOther':
            MessageLookupByLibrary.simpleMessage('Other'),
        'statementTransactionTypeReward':
            MessageLookupByLibrary.simpleMessage('Reward'),
        'statementTransactionTypeSalaryCredit':
            MessageLookupByLibrary.simpleMessage('Salary credit'),
        'statementTransactionTypeScf':
            MessageLookupByLibrary.simpleMessage('SCF'),
        'statementTransactionTypeTransfers':
            MessageLookupByLibrary.simpleMessage('Transfers'),
        'statementTransactionTypeUnknown':
            MessageLookupByLibrary.simpleMessage('Unknown'),
        'statementYearBottomSheetTitle':
            MessageLookupByLibrary.simpleMessage('Year'),
        'statementsFilterButtonTitle':
            MessageLookupByLibrary.simpleMessage('Custom statement'),
        'statementsPageAccountTitle': m0,
        'statementsPageAccountTitleHighlight': m1,
        'statementsPageTitle':
            MessageLookupByLibrary.simpleMessage('Your monthly statements'),
        'statementsYearLabel': MessageLookupByLibrary.simpleMessage('Year')
      };
}
