import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_odd_ui/feature_odd_ui.dart';
import 'package:wio_feature_odd_ui/src/screens/odd_country_selection/odd_country_selection_cubit.dart';
import 'package:wio_feature_odd_ui/src/screens/odd_country_selection/odd_country_selection_state.dart';

class OddCountrySelectionPage extends StatelessWidget {
  final List<String> preSelectedCodes;
  final String? questionnaireId;

  const OddCountrySelectionPage({
    required this.preSelectedCodes,
    this.questionnaireId,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<OddCountrySelectionCubit>(
      create: (_) => DependencyProvider.get<OddCountrySelectionCubit>()
        ..initialize(preSelectedCodes),
      child: Scaffold(
        body: SafeArea(
          bottom: false,
          child: _Body(questionnaireId),
        ),
      ),
    );
  }
}

class _Body extends StatefulWidget {
  final String? questionnaireId;

  const _Body(this.questionnaireId);

  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = OddLocalizations.of(context);
    final bloc = context.read<OddCountrySelectionCubit>();
    return FixedButtonsScrollablePageLayout.custom(
      contentPadding: EdgeInsets.zero,
      model: FixedButtonsScrollablePageLayoutModel(
        buttonAlignmentMode:
            FixedButtonScrollablePageLayoutButtonAlignmentMode.aboveContent,
        primaryButton: FixedButtonsScrollablePageLayoutButton(
          size: ButtonSize.medium,
          label: l10n.oddCountrySelectionSubmitLabel,
        ),
      ),
      onPrimaryButtonPressed: () => bloc.submit(
        questionnaireId: widget.questionnaireId,
        successMessage: l10n.settingsInformationSavedLabel,
      ),
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          const _Header(),
          const SliverToBoxAdapter(child: SizedBox(height: 24)),
          BlocBuilder<OddCountrySelectionCubit, OddCountrySelectionState>(
            builder: (context, state) => SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              sliver: SliverToBoxAdapter(
                child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.countries.length,
                  shrinkWrap: true,
                  itemBuilder: (_, index) {
                    final country = state.countries[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: ListBox(
                        onPressed: () => bloc.updateSelectedCountries(country),
                        onRightPartPressed: () =>
                            bloc.updateSelectedCountries(country),
                        listBoxModel: ListBoxModel(
                          isBoxed: true,
                          leftPartModel: ListBoxPartModel.flag(
                            flag: country.flag,
                          ),
                          rightPartModel: ListBoxPartModel.checkbox(
                            value: state.selectedCountries.contains(
                              country,
                            ),
                          ),
                          textModel: ListBoxTextModel(
                            title: country.name,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Header extends StatelessWidget {
  const _Header();

  @override
  Widget build(BuildContext context) {
    final l10n = OddLocalizations.of(context);
    return SliverHeader(
      largeTitle: Column(
        children: [
          Label(
            model: LabelModel(
              text: l10n.oddCountrySelectionTitle,
              textStyle: CompanyTextStylePointer.h2medium,
              color: CompanyColorPointer.primary3,
            ),
          ),
        ],
      ),
      largeTitleHeight: 130,
      stickyBottomHeight: 64,
      stickyBottom: const _Search(),
    );
  }
}

class _Search extends StatelessWidget {
  const _Search();

  @override
  Widget build(BuildContext context) {
    final bloc = context.read<OddCountrySelectionCubit>();
    final l10n = OddLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: SearchField(
            onChanged: (query) => bloc.onSearch(query),
            onCleared: bloc.resetCountries,
            model: SearchFieldModel(
              labelText: l10n.oddCountrySelectionSearchLabel,
            ),
          ),
        ),
        const Space.vertical(16.0),
      ],
    );
  }
}
