openapi: '3.0.2'
info:
  title: QuestionnaireSubmit
  version: '1.0'
servers:
  - url: http://localhost:9090
security:
  - Authorization: [ ]
tags:
  - name: QuestionnaireSubmit
    description: ODD questionnaire submit api
paths:
  /api/v1/questionnaire/submit:
    post:
      tags:
        - QuestionnaireSubmit
      parameters:
        - name: x-correlation-id
          in: header
          required: true
          schema:
            type: string
        - name: x-product
          in: header
          required: true
          schema:
            type: string

      summary: Return 200 Ok
      description: |
        Takes the questionnaireId as input.
        Returns 200 Ok

      operationId: submitQuestionnaire
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QuestionnaireSubmitRequest"
      responses:
        '200':
          description: OK

components:
  securitySchemes:
    Authorization:
      type: http
      scheme: bearer
      bearerFormat: JWT
