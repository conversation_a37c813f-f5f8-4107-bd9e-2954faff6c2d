// ignore_for_file: only_throw_errors, use_setters_to_change_properties

import 'package:bloc_test/bloc_test.dart';
import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/ui_tests.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_easy_cash_api/easy_cash_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/autopay_update_page_navigation_config.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/bottom_sheets/amount_on_hold_bottom_sheet_config.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/bottom_sheets/autopay_fee_calculation_details_bottom_sheet_config.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/manage_credit_page_nav_config.dart';
import 'package:wio_feature_lending_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_cubit.dart';
import 'package:wio_feature_lending_ui/src/screens/credit_dashboard/dashboard/credit_dashboard_state.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_payments_v2_api/payments_v2_api.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  const config = CreditDashboardConfig(
    accountId: '1337',
    entryPoint: LendingDashboardNavigationEntryPoint.accountsBanner,
  );
  late LendingInteractor lendingInteractor;
  late LoanAccountInteractor accountInteractor;
  late Logger logger;
  late NavigationProvider navigationProvider;
  late LendingExceptionHandler exceptionHandler;
  late CreditDashboardCubit cubit;
  late LendingAnalytics lendingAnalytics;
  late TransactionsMediator transactionsMediator;
  late FeatureToggleProvider featureToggleProvider;
  late LastAutoPaymentInteractor lastAutoPaymentInteractor;
  late EasyCashFlow easyCashFlow;
  late EasyCashInteractor easyCashInteractor;
  late PaymentsFlow paymentsFlow;
  late MockUrlLauncherPlatform urlLauncherPlatform;

  // Test data
  final currency = Currency.aed;
  final mainAccount = TestEntities.randAccount(
    currency: currency,
    id: config.accountId,
    totalAmount: 1337,
    availableAmount: 42,
  );
  final autoPayInfo = Autopay(
    nextPaymentDate: DateTime(2042),
    amount: Money.fromNumWithCurrency(42, currency),
    fee: Money.fromNumWithCurrency(0, currency),
    totalOutstanding: Money.fromNumWithCurrency(56, currency),
    fullRepaymentDoneInCurrentCycle: true,
    feeFreeDate: DateTime.now().add(const Duration(days: 3)),
    minimumPaymentAmount: Money.fromNumWithCurrency(123.4, Currency.aed),
    delinquentAccountDetails: null,
    accountId: 'accountId',
    isAutopayFromSavingSpaceDisabled: false,
    linkedAccountsOutstandingAmount:
        Money.fromNumWithCurrency(100, Currency.aed),
  );
  final lastAutoPayment = TestEntities.lastAutoPayment();
  final summary = LoanAccountSummary(
    loanAccount: mainAccount,
    autoPayment: autoPayInfo,
    lastAutoPayment: lastAutoPayment,
  );
  final payCreditPageConfig = PayCreditPageNavigationConfig(
    PayCreditConfig.forAccount(
      mainAccount,
    ),
  );
  final accounts = [summary, summary];
  final creditSummary = [mainAccount, mainAccount].creditSummary;

  const dashboardFeatureFlags = DashboardFeatureFlags(
    isLastAutopaymentBannerEnabled: true,
  );

  setUp(() {
    lendingInteractor = MockLendingInteractor();
    accountInteractor = MockLoanAccountInteractor();
    logger = MockLogger();
    navigationProvider = MockNavigationProvider();
    lendingAnalytics = MockLendingAnalytics();
    exceptionHandler = MockLendingExceptionHandler();
    transactionsMediator = MockTransactionsMediator();
    featureToggleProvider = MockFeatureToggleProvider();
    lastAutoPaymentInteractor = MockLastAutoPaymentInteractor();
    easyCashFlow = MockEasyCashFlow();
    easyCashInteractor = MockEasyCashInteractor();
    paymentsFlow = MockPaymentsFlow();
    urlLauncherPlatform = MockUrlLauncherPlatform();

    // Mock the URL launcher platform
    UrlLauncherPlatform.instance = urlLauncherPlatform;

    cubit = CreditDashboardCubit(
      config: config,
      accountInteractor: accountInteractor,
      logger: logger,
      exceptionHandler: exceptionHandler,
      navigationProvider: navigationProvider,
      lendingAnalytics: lendingAnalytics,
      transactionsMediator: transactionsMediator,
      featureToggleProvider: featureToggleProvider,
      lastAutoPaymentInteractor: lastAutoPaymentInteractor,
      lendingInteractor: lendingInteractor,
      easyCashFlow: easyCashFlow,
      easyCashInteractor: easyCashInteractor,
      paymentsFlow: paymentsFlow,
    );

    registerFallbackValue(FakeBottomSheetNavigationConfig<void>());
    registerFallbackValue(MockPaymentsFlowConfig());

    when(
      () => featureToggleProvider
          .get(LendingFeatureToggles.isLendingLastAutopaymentBannerEnabled),
    ).thenReturn(true);

    when(
      () => navigationProvider.showBottomSheet<void>(any()),
    ).justCompleteAsync();
  });

  group('Initialization >', () {
    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'subscribes to account updates and fetches auto pay info for the account',
      // Arrange
      build: () => cubit,
      setUp: () {
        when(
          () => accountInteractor.observeLoanDetails(refresh: true),
        ).thenAnswer(
          (_) => Stream.fromIterable(
            [
              Data.loading(null),
              Data.success(
                LoanDetails(
                  accounts: [mainAccount, mainAccount],
                  creditSummary: [mainAccount, mainAccount].creditSummary,
                ),
              ),
            ],
          ),
        );

        when(
          () => accountInteractor.getAutoPaymentInfo(mainAccount.id),
        ).justAnswerAsync(autoPayInfo);

        when(() => accountInteractor.getLastAutopayment(any()))
            .justAnswerAsync(lastAutoPayment);

        when(
          () => lastAutoPaymentInteractor.shouldShowIncompleteAutoPayment(
            accountId: any(named: 'accountId'),
            dueDate: any(named: 'dueDate'),
          ),
        ).justAnswerAsync(true);

        when(
          () => lendingInteractor.getLendingConfiguration(
            ProductType.easyCash,
          ),
        ).justAnswerAsync(TestEntities.generateLendingConfiguration());
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [
        const CreditDashboardState.loading(selectedTabIndex: 0),
        CreditDashboardState.idle(
          selectedTabIndex: 0,
          tabAccounts: accounts,
          dashboardFeatureFlags: dashboardFeatureFlags,
          lendingConfiguration: TestEntities.generateLendingConfiguration(),
          creditSummary: [mainAccount, mainAccount].creditSummary,
        ),
      ],
      verify: (bloc) {
        verify(
          () => lendingAnalytics.creditDashboardOpened(
            LendingUiId.main_dashboard,
            entryPoint: LendingDashboardNavigationEntryPoint.accountsBanner,
          ),
        ).calledOnce;
      },
    );

    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'goes to error state if auto payment info fetching fails',
      // Arrange
      build: () => cubit,
      setUp: () {
        when(
          () => accountInteractor.observeLoanDetails(refresh: true),
        ).thenAnswer(
          (_) => Stream.fromIterable(
            [Data.loading(null), Data.error(Exception('No autopayment data'))],
          ),
        );

        when(() => accountInteractor.getLastAutopayment(any()))
            .justAnswerEmptyAsync();

        when(() => accountInteractor.refreshAccounts()).justCompleteAsync();
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => const [
        CreditDashboardState.loading(selectedTabIndex: 0),
        CreditDashboardState.failed(selectedTabIndex: 0),
      ],
    );
  });

  group('Retry >', () {
    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'can retry loading only from error state',
      // Arrange
      build: () => cubit,
      seed: () => const CreditDashboardState.failed(selectedTabIndex: 0),
      setUp: () {
        when(() => accountInteractor.refreshAccounts()).justCompleteAsync();
      },

      // Act
      act: (cubit) => cubit.onRetry(),

      // Assert
      expect: () => [
        const CreditDashboardState.loading(selectedTabIndex: 0),
      ],
      verify: (cubit) {
        verify(() => accountInteractor.refreshAccounts()).calledOnce;
      },
    );

    final disallowedStates = [
      CreditDashboardState.idle(
        selectedTabIndex: 0,
        tabAccounts: accounts,
        dashboardFeatureFlags: dashboardFeatureFlags,
        lendingConfiguration: TestEntities.generateLendingConfiguration(),
        creditSummary: creditSummary,
      ),
      const CreditDashboardState.initial(selectedTabIndex: 0),
      const CreditDashboardState.loading(selectedTabIndex: 0),
    ];

    for (final state in disallowedStates) {
      blocTest<CreditDashboardCubit, CreditDashboardState>(
        'cannot retry loading from ${state.runtimeType} state',
        // Arrange
        build: () => cubit,
        seed: () => state,

        // Act
        act: (cubit) => cubit.onRetry(),

        // Assert
        expect: () => const <CreditDashboardState>[],
        verify: (_) {
          verifyNever(() => accountInteractor.refreshAccounts());
        },
      );
    }
  });

  group('onPayCredit >', () {
    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'pushes the credit pay navigation results',

      // Arrange
      build: () => cubit,
      seed: () => CreditDashboardState.idle(
        selectedTabIndex: 0,
        tabAccounts: accounts,
        dashboardFeatureFlags: dashboardFeatureFlags,
        lendingConfiguration: TestEntities.generateLendingConfiguration(),
        creditSummary: creditSummary,
      ),
      setUp: () {
        when(() => navigationProvider.push<CreditPayNavigationResult>(any()))
            .thenAnswer(
          (_) => Future<CreditPayNavigationResult>.value(
            const CreditPayNavigationResult(
              isSuccessful: true,
            ),
          ),
        );
        when(() => transactionsMediator.downloadTransactions()).justComplete();
      },

      // Act
      act: (cubit) => cubit.onPayCredit(),

      // Assert
      verify: (cubit) {
        verify(
          () => navigationProvider
              .push<CreditPayNavigationResult>(payCreditPageConfig),
        ).calledOnce;
        verify(() => transactionsMediator.downloadTransactions()).calledOnce;
      },
    );
  });

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'showAmountOnHoldBottomSheet test >',
    // Arrange
    build: () => cubit,
    setUp: () => when(
      () => navigationProvider.showBottomSheet(
        const AmountOnHoldBottomSheetConfig(),
      ),
    ).justCompleteAsync(),

    // Act
    act: (cubit) => cubit.showAmountOnHoldBottomSheet(),

    // Assert
    verify: (cubit) {
      verify(
        () => navigationProvider.showBottomSheet(
          const AmountOnHoldBottomSheetConfig(),
        ),
      ).calledOnce;
    },
  );

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'onManage test >',
    // Arrange
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      tabAccounts: accounts,
      dashboardFeatureFlags: dashboardFeatureFlags,
      lendingConfiguration: TestEntities.generateLendingConfiguration(),
      creditSummary: creditSummary,
    ),

    // Act
    act: (cubit) => cubit.onManage(),

    // Assert
    verify: (cubit) {
      verify(
        () => navigationProvider
            .push(ManageCreditPageNavigationConfig(account: mainAccount)),
      ).calledOnce;
    },
  );

  group('onRefresh Test >', () {
    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'onRefresh successful >',
      // Arrange
      build: () => cubit,
      seed: () => CreditDashboardState.idle(
        selectedTabIndex: 0,
        tabAccounts: accounts,
        dashboardFeatureFlags: dashboardFeatureFlags,
        lendingConfiguration: TestEntities.generateLendingConfiguration(),
        creditSummary: creditSummary,
      ),
      setUp: () {
        when(() => accountInteractor.refreshAccounts()).justCompleteAsync();
        when(() => transactionsMediator.downloadTransactions()).justComplete();
      },

      // Act
      act: (cubit) => cubit.onRefresh(),

      // Assert
      verify: (cubit) {
        verify(() => accountInteractor.refreshAccounts()).calledOnce;
        verify(() => transactionsMediator.downloadTransactions()).calledOnce;
      },
      expect: () => <CreditDashboardState>[],
    );

    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'onRefresh throws error >',
      // Arrange
      build: () => cubit,
      seed: () => CreditDashboardState.idle(
        selectedTabIndex: 0,
        tabAccounts: accounts,
        dashboardFeatureFlags: dashboardFeatureFlags,
        lendingConfiguration: TestEntities.generateLendingConfiguration(),
        creditSummary: creditSummary,
      ),
      setUp: () {
        when(() => accountInteractor.refreshAccounts())
            .justThrowAsync(Exception);
        when(() => transactionsMediator.downloadTransactions()).justComplete();
      },

      // Act
      act: (cubit) => cubit.onRefresh(),

      // Assert
      verify: (cubit) {
        verify(() => accountInteractor.refreshAccounts()).calledOnce;
        verify(() => transactionsMediator.downloadTransactions()).calledOnce;
        verify(
          () => exceptionHandler.handle(
            Exception,
            LendingUiId.credit_dashboard,
          ),
        ).calledOnce;
      },
    );
  });

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'showAutpayFeeCalculationInfo test >',
    // Arrange
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      tabAccounts: accounts,
      dashboardFeatureFlags: dashboardFeatureFlags,
      lendingConfiguration: TestEntities.generateLendingConfiguration(),
      creditSummary: creditSummary,
    ),
    setUp: () => when(
      () => navigationProvider.showBottomSheet(
        AutopayFeeCalculationInfoBottomSheetNavigationConfig(
          configuration: AutopayFeeCalculationInfoBottomSheetConfiguration(
            autoPayment: autoPayInfo,
            loanAccount: mainAccount,
          ),
        ),
      ),
    ).justCompleteAsync(),

    // Act
    act: (cubit) => cubit.showAutopayFeeCalculationInfo(),

    // Assert
    verify: (cubit) {
      verify(
        () => navigationProvider.showBottomSheet(
          AutopayFeeCalculationInfoBottomSheetNavigationConfig(
            configuration: AutopayFeeCalculationInfoBottomSheetConfiguration(
              autoPayment: autoPayInfo,
              loanAccount: mainAccount,
            ),
          ),
        ),
      ).calledOnce;
    },
  );

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'on show autopay fee calculation details bottomsheet',
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      tabAccounts: accounts,
      dashboardFeatureFlags: dashboardFeatureFlags,
      lendingConfiguration: TestEntities.generateLendingConfiguration(),
      creditSummary: creditSummary,
    ),
    setUp: () {
      when(
        () => navigationProvider.showBottomSheet<void>(
          AutoPayFeeCalculationDetailsBottomSheetNavigationConfig(
            configuration: AutoPayFeeCalculationDetailsBottomSheetConfig(
              autopay: autoPayInfo,
              account: mainAccount,
            ),
          ),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.showAutoPayFeeDetailsBottomSheet(),
    verify: (cubit) {
      verify(
        () => navigationProvider.showBottomSheet<void>(
          AutoPayFeeCalculationDetailsBottomSheetNavigationConfig(
            configuration: AutoPayFeeCalculationDetailsBottomSheetConfig(
              autopay: autoPayInfo,
              account: mainAccount,
            ),
          ),
        ),
      ).calledOnce;
    },
  );

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'onEditAutoPay test >',
    // Arrange
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      tabAccounts: accounts,
      dashboardFeatureFlags: dashboardFeatureFlags,
      lendingConfiguration: TestEntities.generateLendingConfiguration(),
      creditSummary: creditSummary,
    ),
    setUp: () => when(
      () => navigationProvider.push(
        AutopayUpdatePageNavigationConfig(
          account: mainAccount,
        ),
      ),
    ).justCompleteAsync(),

    // Act
    act: (cubit) => cubit.onEditAutoPay(),

    // Assert
    verify: (cubit) {
      verify(
        () => navigationProvider.push(
          AutopayUpdatePageNavigationConfig(
            account: mainAccount,
          ),
        ),
      ).calledOnce;
    },
  );

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'onPayOtherBankCreditBanner',
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      tabAccounts: accounts,
      dashboardFeatureFlags: dashboardFeatureFlags,
      lendingConfiguration: TestEntities.generateLendingConfiguration(),
      creditSummary: creditSummary,
    ),
    act: (cubit) => cubit.onPayOtherBankCreditBanner(),
    setUp: () {
      when(() => paymentsFlow.run(config: any(named: 'config')))
          .justCompleteAsync();
    },
    verify: (cubit) {
      verify(
        () => paymentsFlow.run(
          config: const PaymentsFlowConfig.payBillsDashboard(),
        ),
      ).calledOnce;
    },
  );

  blocTest<CreditDashboardCubit, CreditDashboardState>(
    'hide lastautopayment banner',
    build: () => cubit,
    seed: () => CreditDashboardState.idle(
      selectedTabIndex: 0,
      tabAccounts: accounts,
      dashboardFeatureFlags: dashboardFeatureFlags,
      lastAutoPayment: TestEntities.lastAutoPayment(),
      lendingConfiguration: TestEntities.generateLendingConfiguration(),
      creditSummary: creditSummary,
    ),
    setUp: () {
      when(
        () => lastAutoPaymentInteractor.markIncompleteAutoPaymentViewed(
          accountId: any(named: 'accountId'),
          dueDate: any(named: 'dueDate'),
        ),
      ).justCompleteAsync();
    },
    act: (cubit) => cubit.onHideLastAutoPay(),
    expect: () => [
      CreditDashboardState.idle(
        selectedTabIndex: 0,
        tabAccounts: accounts,
        dashboardFeatureFlags: dashboardFeatureFlags,
        lendingConfiguration: TestEntities.generateLendingConfiguration(),
        creditSummary: creditSummary,
      ),
    ],
  );

  group(
    'easy cash entry point click',
    () {
      blocTest<CreditDashboardCubit, CreditDashboardState>(
        'Easy Cash flow started',
        build: () => cubit,
        setUp: () {
          when(
            () => easyCashFlow.startFlow(
              creditAccountId: anyNamed('creditAccountId'),
              easyCashLimit: TestEntities.easyCashLimit(
                creditAccounts: [TestEntities.creditAccount()],
              ),
              source: EasyCashUiId.credit_dashboard,
              productType: LoanProductIdentifier.retailEasyCash,
            ),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          tabAccounts: accounts,
          dashboardFeatureFlags: dashboardFeatureFlags,
          lendingConfiguration: TestEntities.generateLendingConfiguration(),
          easyCashLimit: TestEntities.easyCashLimit(
            creditAccounts: [TestEntities.creditAccount()],
          ),
          creditSummary: creditSummary,
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verify(
            () => easyCashFlow.startFlow(
              creditAccountId: anyNamed('creditAccountId'),
              easyCashLimit: TestEntities.easyCashLimit(
                creditAccounts: [TestEntities.creditAccount()],
              ),
              source: EasyCashUiId.credit_dashboard,
              productType: LoanProductIdentifier.retailEasyCash,
            ),
          ).calledOnce;
        },
      );

      blocTest<CreditDashboardCubit, CreditDashboardState>(
        'Payment due bottom sheet shown',
        build: () => cubit,
        setUp: () {
          when(
            () => navigationProvider.showBottomSheet(any()),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          tabAccounts: accounts,
          dashboardFeatureFlags: dashboardFeatureFlags,
          lendingConfiguration: TestEntities.generateLendingConfiguration(),
          easyCashLimit: TestEntities.easyCashLimitWithEasyCashAccount(
            TestEntities.defaultLimit,
            mambuSubState: 'LOCKED',
          ),
          creditSummary: creditSummary,
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verify(
            () => navigationProvider.showBottomSheet<void>(
              EasyCashPaymentDueBottomSheetConfig(
                dueAmount: Money.fromNumWithCurrency(30000, Currency.aed),
                onRepaymentPressed: () {},
              ),
            ),
          ).calledOnce;
        },
      );

      blocTest<CreditDashboardCubit, CreditDashboardState>(
        'Limit reached bottom sheet shown',
        build: () => cubit,
        setUp: () {
          when(
            () => navigationProvider.showBottomSheet(any()),
          ).justCompleteAsync();
        },
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          tabAccounts: accounts,
          dashboardFeatureFlags: dashboardFeatureFlags,
          lendingConfiguration: TestEntities.generateLendingConfiguration(),
          easyCashLimit: TestEntities.easyCashLimitWithEasyCashAccount(
            TestEntities.zeroLimit,
          ),
          creditSummary: creditSummary,
        ),
        act: (c) => c.onEasyCash(),
        verify: (c) {
          verify(
            () => navigationProvider.showBottomSheet(
              EasyCashLimitReachedBottomSheetConfig(
                rePaymentDate: DateTime(2024),
                dueDays: null,
                onRepaymentPressed: () {},
              ),
            ),
          ).calledOnce;
        },
      );

      blocTest<CreditDashboardCubit, CreditDashboardState>(
        'tab changed',
        build: () => cubit,
        seed: () => CreditDashboardState.idle(
          selectedTabIndex: 0,
          tabAccounts: accounts,
          dashboardFeatureFlags: dashboardFeatureFlags,
          lendingConfiguration: TestEntities.generateLendingConfiguration(),
          easyCashLimit: TestEntities.easyCashLimitWithEasyCashAccount(
            TestEntities.zeroLimit,
          ),
          creditSummary: creditSummary,
        ),
        act: (c) => c.onTabChanged(1),
        expect: () => [
          CreditDashboardState.idle(
            selectedTabIndex: 1,
            tabAccounts: accounts,
            dashboardFeatureFlags: dashboardFeatureFlags,
            lendingConfiguration: TestEntities.generateLendingConfiguration(),
            easyCashLimit: TestEntities.easyCashLimitWithEasyCashAccount(
              TestEntities.zeroLimit,
            ),
            creditSummary: creditSummary,
          ),
        ],
      );
    },
  );

  group('launchEtihadBureauWebsite >', () {
    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'successfully launches Etihad Bureau website when URL can be launched',
      build: () => cubit,
      setUp: () {
        urlLauncherPlatform
          ..setCanLaunch(canLaunch: true)
          ..setLaunchSuccess(success: true)
          ..setException(null);
      },
      act: (cubit) => cubit.launchEtihadBureauWebsite(),
    );

    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'does not launch URL when canLaunchUrl returns false',
      build: () => cubit,
      setUp: () {
        urlLauncherPlatform
          ..setCanLaunch(canLaunch: false)
          ..setException(null);
      },
      act: (cubit) => cubit.launchEtihadBureauWebsite(),
    );

    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'handles exception when URL launch fails',
      build: () => cubit,
      setUp: () {
        final exception = Exception('Failed to launch URL');
        urlLauncherPlatform
          ..setCanLaunch(canLaunch: true)
          ..setException(exception);
      },
      act: (cubit) => cubit.launchEtihadBureauWebsite(),
      verify: (cubit) {
        verify(
          () => exceptionHandler.handle(
            any<Object>(),
            LendingUiId.credit_dashboard,
            message: 'Failed to launch Etihad Bureau Credit Report website',
          ),
        ).calledOnce;
      },
    );

    blocTest<CreditDashboardCubit, CreditDashboardState>(
      'handles exception when launchUrl throws an error',
      build: () => cubit,
      setUp: () {
        final exception = Exception('Launch failed');
        urlLauncherPlatform
          ..setCanLaunch(canLaunch: true)
          ..setException(exception);
      },
      act: (cubit) => cubit.launchEtihadBureauWebsite(),
      verify: (cubit) {
        verify(
          () => exceptionHandler.handle(
            any<Object>(),
            LendingUiId.credit_dashboard,
            message: 'Failed to launch Etihad Bureau Credit Report website',
          ),
        ).calledOnce;
      },
    );
  });
}
