import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_pdf_viewer/config/lending_pdf_viewer_config.dart';
import 'package:wio_feature_lending_ui/src/screens/lending_pdf_viewer/cubit/lending_pdf_cubit.dart';
import 'package:wio_feature_share_api/feature_share_api.dart';
import 'package:wio_feature_share_api/models/domain_share_result.dart';
import 'package:wio_feature_share_api/models/domain_share_status.dart';

import '../mocks.dart';

void main() {
  late LendingPdfCubit cubit;
  late ShareProvider shareProvider;

  const config = LendingPdfViewerConfig(
    documentUrl: 'url',
  );

  setUpAll(() {});

  setUp(() {
    shareProvider = MockShareProvider();

    cubit = LendingPdfCubit(
      config: config,
      shareProvider: shareProvider,
    );
  });

  blocTest<LendingPdfCubit, void>(
    'should call share provider when share is called',
    build: () => cubit,
    setUp: () {
      when(() => shareProvider.share(any())).justAnswerAsync(
        const DomainShareResult(status: DomainShareResultStatus.success),
      );
    },
    act: (cubit) => cubit.share('url'),
    verify: (cubit) {
      verify(() => shareProvider.share(any())).calledOnce;
    },
  );
}
