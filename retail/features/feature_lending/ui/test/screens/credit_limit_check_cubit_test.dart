import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/l10n/lending_localization.g.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/credit_limit_page_navigation_config.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_application/credit_limit_check/credit_limit_check_cubit.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_application/credit_limit_check/credit_limit_check_state.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late LendingInteractor interactor;
  late Logger logger;
  late NavigationProvider navigationProvider;
  late CreditLimitCheckCubit cubit;
  late LendingExceptionHandler exceptionHandler;
  late MockLendingAnalytics lendingAnalytics;
  late LoanAccountInteractor loanAccountInteractor;
  late LendingLocalizations lendingLocalizations;
  late MockPricingPlanInteractor pricingPlanInteractor;
  late CreditLimitConfig config;

  // Test data
  const previousLimit = 1000.0;
  final maxAmount = Money.fromNumWithCurrency(3000, Currency.aed);
  final previousAmount = Money.fromNumWithCurrency(previousLimit, Currency.aed);
  const nextStatus = OnboardingStatus.repaymentCheck;
  final app = TestEntities.getApplication().copyWith(
    decisionResult: CreditDecisionResult(
      status: CreditDecisionStatus.notStarted,
      maxAmount: maxAmount,
    ),
  );
  final appWithData = app.copyWith(
    data: const ApplicationData(creditLimit: previousLimit),
  );
  final applicationConfig =
      CreditLimitConfig(application: app, nextStatus: nextStatus);
  final reduceConfig = CreditLimitConfig.reduce(
    loanAccountId: '123',
    maxAmount: Money.fromNumWithCurrency(10000, Currency.aed),
    minAmount: Money.fromNumWithCurrency(5000, Currency.aed),
  );

  // ignore: prefer_const_constructors
  final lendingConfiguration = LendingConfiguration(
    salaryInputFieldLimits: const FieldLimits<double>(min: 0.0, max: 100000),
    expensesInputFieldLimits: const FieldLimits<double>(min: 0.0, max: 100000),
    cashBackPercentage: 2.0,
    creditPayCashback: 2.0,
    feeFreePeriod: 60,
    latePaymentFee: Money.fromNumWithCurrency(100, Currency.aed),
  );

  const savingSpaceRate = 6.0;

  final subscriptionPlanInfo = SubscriptionPlanInfo(
    tariffId: 'tariffId',
    name: 'name',
    description: 'description',
    interestRates: {
      Currency.aed: [const InterestRateTier(rate: savingSpaceRate)],
    },
  );

  setUp(() {
    interactor = MockLendingInteractor();
    logger = MockLogger();
    navigationProvider = MockNavigationProvider();
    exceptionHandler = MockLendingExceptionHandler();
    lendingAnalytics = MockLendingAnalytics();
    loanAccountInteractor = MockLoanAccountInteractor();
    lendingLocalizations = MockLendingLocalizations();
    pricingPlanInteractor = MockPricingPlanInteractor();
    config = applicationConfig;

    cubit = CreditLimitCheckCubit(
      interactor: interactor,
      logger: logger,
      navigationProvider: navigationProvider,
      exceptionHandler: exceptionHandler,
      lendingAnalytics: lendingAnalytics,
      loanAccountInteractor: loanAccountInteractor,
      lendingLocalizations: lendingLocalizations,
      pricingPlanInteractor: pricingPlanInteractor,
      config: config,
    );

    registerFallbackValue(OnboardingStatus.creditDecision);
    registerFallbackValue(ProductType.creditCard);
    registerFallbackValue(FakeDuration());
  });

  group('Initialization >', () {
    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'initialized with the provided values from application',
      // Arrange
      build: () => cubit,

      setUp: () {
        config = applicationConfig;
        when(() => interactor.getLendingConfiguration(ProductType.creditCard))
            .justAnswerAsync(lendingConfiguration);
        when(() => pricingPlanInteractor.getSpecialPlanInfo())
            .justAnswerAsync(subscriptionPlanInfo);
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [
        const CreditLimitCheckState.loading(),
        CreditLimitCheckState.idle(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: Money.fromNumWithCurrency(
            1000,
            app.currency,
          ),
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ],
    );

    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'initialized with the provided values from reduce config',
      // Arrange
      build: () => cubit,

      setUp: () {
        config = reduceConfig;
        cubit = CreditLimitCheckCubit(
          interactor: interactor,
          logger: logger,
          navigationProvider: navigationProvider,
          exceptionHandler: exceptionHandler,
          lendingAnalytics: lendingAnalytics,
          loanAccountInteractor: loanAccountInteractor,
          lendingLocalizations: lendingLocalizations,
          pricingPlanInteractor: pricingPlanInteractor,
          config: config,
        );
        when(() => interactor.getLendingConfiguration(ProductType.creditCard))
            .justAnswerAsync(lendingConfiguration);
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [
        CreditLimitCheckState.idle(
          amount: reduceConfig.maxMoney,
          maxAmount: reduceConfig.maxMoney,
          minAmount: reduceConfig.minMoney,
          limitConditions: const LimitConditions.forReduceLimit(),
        ),
      ],
    );

    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'restores previously submitted limit if there is any in application',
      // Arrange
      build: () => cubit,

      // Act
      act: (cubit) => cubit.initialize(),

      setUp: () {
        config = applicationConfig.mapOrNull(
              (value) => value.copyWith(application: appWithData),
            ) ??
            applicationConfig;
        cubit = CreditLimitCheckCubit(
          interactor: interactor,
          logger: logger,
          navigationProvider: navigationProvider,
          exceptionHandler: exceptionHandler,
          lendingAnalytics: lendingAnalytics,
          loanAccountInteractor: loanAccountInteractor,
          lendingLocalizations: lendingLocalizations,
          pricingPlanInteractor: pricingPlanInteractor,
          config: config,
        );
        when(() => interactor.getLendingConfiguration(ProductType.creditCard))
            .justAnswerAsync(lendingConfiguration);
        when(() => pricingPlanInteractor.getSpecialPlanInfo())
            .justAnswerAsync(subscriptionPlanInfo);
      },

      // Assert
      expect: () => [
        const CreditLimitCheckState.loading(),
        CreditLimitCheckState.idle(
          amount: previousAmount,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ],
    );

    final appWithExceedingLimit = appWithData.copyWith(
      data: const ApplicationData(creditLimit: 100500),
    );

    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'uses max amount as initial one if previously submitted limit exceeds it '
      'in application',
      // Arrange
      build: () => cubit,

      setUp: () {
        config = applicationConfig.mapOrNull(
              (value) => value.copyWith(application: appWithExceedingLimit),
            ) ??
            applicationConfig;
        when(() => interactor.getLendingConfiguration(ProductType.creditCard))
            .justAnswerAsync(lendingConfiguration);
        when(() => pricingPlanInteractor.getSpecialPlanInfo())
            .justAnswerAsync(subscriptionPlanInfo);
      },

      // Act
      act: (cubit) => cubit.initialize(),

      // Assert
      expect: () => [
        const CreditLimitCheckState.loading(),
        CreditLimitCheckState.idle(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: appWithExceedingLimit.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ],
    );
  });

  group('Editing >', () {
    group('Start editing >', () {
      blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
        'can edit amount only from the idle state',
        // Arrange
        build: () => cubit,
        setUp: () => config = applicationConfig,
        seed: () => CreditLimitCheckState.idle(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),

        // Act
        act: (cubit) => cubit.onEditAmount(),

        // Assert
        expect: () => [
          CreditLimitCheckState.editing(
            amount: config.maxMoney,
            maxAmount: config.maxMoney,
            minAmount: config.minMoney,
            limitConditions: LimitConditions.forApplication(
              freePeriodInDays: lendingConfiguration.feeFreePeriod,
              feeRate: app.decisionResult.feePercentage,
              autoPaySavingRate: savingSpaceRate,
            ),
          ),
        ],
      );

      config = applicationConfig;
      final disallowedStates = [
        const CreditLimitCheckState.initial(),
        const CreditLimitCheckState.loading(),
        const CreditLimitCheckState.failed(),
        CreditLimitCheckState.submitting(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
        CreditLimitCheckState.editing(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ];

      for (final state in disallowedStates) {
        blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
          'cannot edit amount while in ${state.runtimeType} state',
          // Arrange
          build: () => cubit,
          setUp: () => config = applicationConfig,
          seed: () => state,

          // Act
          act: (cubit) => cubit.onEditAmount(),

          // Assert
          errors: () => [isA<AssertionError>()],
          expect: () => const <CreditLimitCheckState>[], // no stage changes
        );
      }
    });

    group('Setting new amount >', () {
      const newAmount = 42.0;

      blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
        'setting an amount greater than max amount does nothing',
        // Arrange
        build: () => cubit,
        seed: () => CreditLimitCheckState.editing(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),

        // Act
        act: (cubit) => cubit.onSaveAmount(newAmount * 1000000),

        // Assert
        errors: () => [isA<AssertionError>()],
        expect: () => const <CreditLimitCheckState>[], // no changes
      );

      final disallowedStates = [
        const CreditLimitCheckState.initial(),
        const CreditLimitCheckState.loading(),
        const CreditLimitCheckState.failed(),
        CreditLimitCheckState.submitting(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
        CreditLimitCheckState.idle(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ];

      for (final state in disallowedStates) {
        blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
          'setting an amount in ${state.runtimeType} does nothing',
          // Arrange
          build: () => cubit,
          seed: () => state,

          // Act
          act: (cubit) => cubit.onSaveAmount(newAmount),

          // Assert
          expect: () => const <CreditLimitCheckState>[], // no stage changes
        );
      }
    });
  });

  group('Submission >', () {
    final validAmount = Money.fromNumWithCurrency(1234.56, app.currency);

    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'goes back to the idle state when submitting a positive amount',
      // Arrange
      build: () => cubit,
      seed: () => CreditLimitCheckState.idle(
        amount: validAmount,
        maxAmount: config.maxMoney,
        minAmount: config.minMoney,
        limitConditions: LimitConditions.forApplication(
          freePeriodInDays: lendingConfiguration.feeFreePeriod,
          feeRate: app.decisionResult.feePercentage,
          autoPaySavingRate: savingSpaceRate,
        ),
      ),
      setUp: () {
        when(
          () => interactor.updateApplication(
            applicationId: any(named: 'applicationId'),
            nextStatus: any(named: 'nextStatus'),
            updatedApplicationFields: any(named: 'updatedApplicationFields'),
          ),
        ).justAnswerAsync(TestEntities.getApplication());
      },

      // Act
      act: (cubit) => cubit.onSubmit(),

      // Assert
      expect: () => [
        CreditLimitCheckState.submitting(
          amount: validAmount,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
        CreditLimitCheckState.idle(
          amount: validAmount,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ],
      verify: (cubit) {
        expect(cubit.state.canSubmit, isTrue);

        verify(
          () => interactor.updateApplication(
            applicationId: any(named: 'applicationId'),
            nextStatus: any(named: 'nextStatus'),
            updatedApplicationFields: any(named: 'updatedApplicationFields'),
          ),
        ).calledOnce;
      },
    );

    final invalidAmounts =
        [-42, 0].map((it) => Money.fromNumWithCurrency(it, app.currency));

    for (final amount in invalidAmounts) {
      blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
        'cannot submit a zero or negative amount: $amount',
        // Arrange
        build: () => cubit,
        seed: () => CreditLimitCheckState.idle(
          amount: amount,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),

        // Act
        act: (cubit) => cubit.onSubmit(),

        // Assert
        errors: () => [isA<AssertionError>()],
        expect: () => const <CreditLimitCheckState>[],
        // no changes
        verify: (cubit) => expect(cubit.state.canSubmit, isFalse),
      );
    }

    final validReduceAmount = Money.fromNumWithCurrency(7000.0, app.currency);
    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'navigates to successPage after succesful submit for reduce limit',
      // Arrange
      build: () => cubit,
      seed: () => CreditLimitCheckState.idle(
        amount: validReduceAmount,
        maxAmount: config.maxMoney,
        minAmount: config.minMoney,
        limitConditions: const LimitConditions.forReduceLimit(),
      ),
      setUp: () {
        config = reduceConfig;
        cubit = CreditLimitCheckCubit(
          interactor: interactor,
          logger: logger,
          navigationProvider: navigationProvider,
          exceptionHandler: exceptionHandler,
          lendingAnalytics: lendingAnalytics,
          loanAccountInteractor: loanAccountInteractor,
          lendingLocalizations: lendingLocalizations,
          pricingPlanInteractor: pricingPlanInteractor,
          config: config,
        );
        when(
          () => loanAccountInteractor.updateLoanAmount(
            accountId: any(named: 'accountId'),
            amount: any(named: 'amount'),
          ),
        ).justAnswerAsync(
          TestEntities.randAccount(
            currency: Currency.aed,
            totalAmount: 7000.0,
          ),
        );
        when(
          () => loanAccountInteractor.getLoanDetails(),
        ).justAnswerAsync(
          TestEntities.loanDetails,
        );
        when(() => navigationProvider.navigateTo(any())).justCompleteAsync();
        when(() => navigationProvider.goBack()).justComplete();
        when(
          () => lendingLocalizations.lendingCreditLimitReduceSuccessPageTitle,
        ).thenReturn('');
        when(
          () => lendingLocalizations
              .lendingCreditLimitReduceSuccessPageDescription(any()),
        ).thenReturn('');
        when(
          () => lendingLocalizations
              .lendingCreditLimitReduceSuccessPageButtonTitle,
        ).thenReturn('');
      },

      // Act
      act: (cubit) => cubit.onSubmit(),

      // Assert
      expect: () => [
        CreditLimitCheckState.submitting(
          amount: validReduceAmount,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: const LimitConditions.forReduceLimit(),
        ),
        CreditLimitCheckState.idle(
          amount: validReduceAmount,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: const LimitConditions.forReduceLimit(),
        ),
      ],
      verify: (cubit) {
        expect(cubit.state.canSubmit, isTrue);

        verify(
          () => loanAccountInteractor.updateLoanAmount(
            accountId: any(named: 'accountId'),
            amount: any(named: 'amount'),
          ),
        ).calledOnce;
      },
    );

    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'exception handler handler the exception for reduce limit submission',
      // Arrange
      build: () => cubit,
      seed: () => CreditLimitCheckState.idle(
        amount: validReduceAmount,
        maxAmount: config.maxMoney,
        minAmount: config.minMoney,
        limitConditions: const LimitConditions.forReduceLimit(),
      ),
      setUp: () {
        config = reduceConfig;
        cubit = CreditLimitCheckCubit(
          interactor: interactor,
          logger: logger,
          navigationProvider: navigationProvider,
          exceptionHandler: exceptionHandler,
          lendingAnalytics: lendingAnalytics,
          loanAccountInteractor: loanAccountInteractor,
          lendingLocalizations: lendingLocalizations,
          pricingPlanInteractor: pricingPlanInteractor,
          config: config,
        );
        when(
          () => loanAccountInteractor.updateLoanAmount(
            accountId: any(named: 'accountId'),
            amount: any(named: 'amount'),
          ),
        ).thenThrow(Exception('Test exception'));
        when(
          () => exceptionHandler.handle(
            isA<Object>(),
            LendingUiId.credit_limit,
          ),
        ).justComplete();
      },

      // Act
      act: (cubit) => cubit.onSubmit(),

      // Assert
      errors: () => [isA<Object>()],

      verify: (cubit) {
        expect(cubit.state.canSubmit, isTrue);

        verify(
          () => loanAccountInteractor.updateLoanAmount(
            accountId: any(named: 'accountId'),
            amount: any(named: 'amount'),
          ),
        ).calledOnce;
      },
    );
  });

  group('Retry >', () {
    blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
      'can retry only from the failed state',
      // Arrange
      build: () => cubit,
      seed: () => const CreditLimitCheckState.failed(),

      setUp: () {
        when(() => interactor.getLendingConfiguration(ProductType.creditCard))
            .justAnswerAsync(lendingConfiguration);
        when(() => pricingPlanInteractor.getSpecialPlanInfo())
            .justAnswerAsync(subscriptionPlanInfo);
      },

      // Act
      act: (cubit) => cubit.onRetry(),

      // Assert
      expect: () => <CreditLimitCheckState>[
        const CreditLimitCheckState.initial(),
        const CreditLimitCheckState.loading(),
        CreditLimitCheckState.idle(
          amount: config.maxMoney,
          maxAmount: config.maxMoney,
          minAmount: config.minMoney,
          limitConditions: LimitConditions.forApplication(
            freePeriodInDays: lendingConfiguration.feeFreePeriod,
            feeRate: app.decisionResult.feePercentage,
            autoPaySavingRate: savingSpaceRate,
          ),
        ),
      ],
    );

    final disallowedStates = [
      const CreditLimitCheckState.loading(),
      CreditLimitCheckState.idle(
        amount: config.maxMoney,
        maxAmount: config.maxMoney,
        minAmount: config.minMoney,
        limitConditions: LimitConditions.forApplication(
          freePeriodInDays: lendingConfiguration.feeFreePeriod,
          feeRate: app.decisionResult.feePercentage,
          autoPaySavingRate: savingSpaceRate,
        ),
      ),
      CreditLimitCheckState.editing(
        amount: config.maxMoney,
        maxAmount: config.maxMoney,
        minAmount: config.minMoney,
        limitConditions: LimitConditions.forApplication(
          freePeriodInDays: lendingConfiguration.feeFreePeriod,
          feeRate: app.decisionResult.feePercentage,
          autoPaySavingRate: savingSpaceRate,
        ),
      ),
      CreditLimitCheckState.submitting(
        amount: config.maxMoney,
        maxAmount: config.maxMoney,
        minAmount: config.minMoney,
        limitConditions: LimitConditions.forApplication(
          freePeriodInDays: lendingConfiguration.feeFreePeriod,
          feeRate: app.decisionResult.feePercentage,
          autoPaySavingRate: savingSpaceRate,
        ),
      ),
    ];
    for (final state in disallowedStates) {
      blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
        'calling retry from ${state.runtimeType} does nothing',
        // Arrange
        build: () => cubit,
        seed: () => state,

        // Act
        act: (cubit) => cubit.onRetry(),

        // Assert
        expect: () => <CreditLimitCheckState>[], // no changes,
      );
    }
  });

  blocTest<CreditLimitCheckCubit, CreditLimitCheckState>(
    'send analytics event on pop for reduce limit attempt',
    // Arrange
    build: () => cubit,
    seed: () => CreditLimitCheckState.idle(
      amount: config.maxMoney,
      maxAmount: config.maxMoney,
      minAmount: config.minMoney,
      limitConditions: const LimitConditions.forReduceLimit(),
    ),
    setUp: () {
      config = reduceConfig;
      cubit = CreditLimitCheckCubit(
        interactor: interactor,
        logger: logger,
        navigationProvider: navigationProvider,
        exceptionHandler: exceptionHandler,
        lendingAnalytics: lendingAnalytics,
        loanAccountInteractor: loanAccountInteractor,
        lendingLocalizations: lendingLocalizations,
        pricingPlanInteractor: pricingPlanInteractor,
        config: config,
      );
      when(
        () => lendingAnalytics.creditLimitReduceAttempt(
          succesfulyReduced: false,
          duration: any(named: 'duration'),
        ),
      ).justComplete();
    },

    // Act
    act: (cubit) => cubit.onPop(),

    // Assert
    verify: (cubit) {
      verify(
        () => lendingAnalytics.creditLimitReduceAttempt(
          succesfulyReduced: false,
          duration: any(named: 'duration'),
        ),
      ).calledOnce;
    },
  );
}

extension on CreditLimitConfig {
  Money get maxMoney => map(
        (value) => value.application.decisionResult.maxAmount,
        reduce: (value) => value.maxAmount,
      );

  Money get minMoney => map(
        (value) => Money.fromNumWithCurrency(
          1000,
          value.application.decisionResult.maxAmount.currency,
        ),
        reduce: (value) => value.minAmount,
      );
}
