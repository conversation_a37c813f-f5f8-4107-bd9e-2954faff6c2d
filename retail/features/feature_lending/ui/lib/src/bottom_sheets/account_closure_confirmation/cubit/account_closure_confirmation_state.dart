part of 'account_closure_confirmation_cubit.dart';

enum AccountClosureAllowance {
  pendingTransactions,
  outstandingRepayment,
  confirmation;

  static AccountClosureAllowance fromAccountSummary(
    LoanAccountSummary summary,
  ) {
    if (!summary.unsettledAmount.isZero) {
      return AccountClosureAllowance.pendingTransactions;
    }

    if (!summary.autoPayment.totalOutstanding.isZero) {
      return AccountClosureAllowance.outstandingRepayment;
    }

    return AccountClosureAllowance.confirmation;
  }

  AccountClosureMessage toMessage() {
    switch (this) {
      case AccountClosureAllowance.pendingTransactions:
        return AccountClosureMessage.pendingTransactions;
      case AccountClosureAllowance.outstandingRepayment:
        return AccountClosureMessage.outstandingRepayment;
      case AccountClosureAllowance.confirmation:
        return AccountClosureMessage.confirmation;
    }
  }
}

@freezed
class AccountClosureConfirmationState with _$AccountClosureConfirmationState {
  const factory AccountClosureConfirmationState.initial() =
      _InitialAccountClosureConfirmationState;

  const factory AccountClosureConfirmationState.idle({
    required LoanAccountSummary loanAccountSummary,
    required AccountClosureAllowance closureAllowance,
  }) = _IdleAccountClosureConfirmationState;

  const factory AccountClosureConfirmationState.submitting() =
      _SubmittingAccountClosureConfirmationState;

  const AccountClosureConfirmationState._();

  String get pendingAmount => maybeMap(
        idle: (it) => it.loanAccountSummary.unsettledAmount.formattedTitle,
        orElse: () => '',
      );

  String get outstandingAmount => maybeMap(
        idle: (it) =>
            it.loanAccountSummary.autoPayment.totalOutstanding.formattedTitle,
        orElse: () => '',
      );
}
