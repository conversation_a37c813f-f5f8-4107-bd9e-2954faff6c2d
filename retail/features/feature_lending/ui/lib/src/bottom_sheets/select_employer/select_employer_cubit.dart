import 'dart:async';

import 'package:rxdart/rxdart.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/bottom_sheets/select_employer/select_employer_state.dart';
import 'package:wio_feature_lending_ui/src/common/mixins/duration_timer.dart';

class SelectEmployerCubit extends BaseCubit<SelectEmployerState>
    with DurationTimer {
  final StreamController<String> _queryController = StreamController();

  final EmployerInteractor _interactor;
  final LendingExceptionHandler _exceptionHandler;
  final NavigationProvider _navigationProvider;
  final Logger _logger;
  final LendingAnalytics _lendingAnalytics;

  SelectEmployerCubit({
    required EmployerInteractor interactor,
    required LendingExceptionHand<PERSON> exceptionHandler,
    required NavigationProvider navigationProvider,
    required Logger logger,
    required LendingAnalytics lendingAnalytics,
  })  : _interactor = interactor,
        _exceptionHandler = exceptionHandler,
        _navigationProvider = navigationProvider,
        _logger = logger,
        _lendingAnalytics = lendingAnalytics,
        super(const SelectEmployerState.initial());

  void initialize() {
    startTimer();
    _queryController.stream
        .distinct()
        .where((query) => query.length > 2)
        .debounceTime(const Duration(milliseconds: 300))
        .switchMap<List<String>>(
          (query) =>
              _interactor.searchEmployers(query: query).toStream().doOnListen(
                    () => emit(SelectEmployerState.loading(query: query)),
                  ),
        )
        .listenSafe(
          this,
          onData: _handleData,
          onError: _handleErrors,
        );

    state.mapOrNull(initial: (_) => _queryController.add(state.query));
  }

  void dispose() {
    _queryController.close();
  }

  void onInput(String value) {
    _queryController.add(value);
  }

  void onSelect(String employer, {bool isFromList = true}) {
    final employerSource =
        isFromList ? EmployerSource.search_list : EmployerSource.input;
    final result = isFromList
        ? SelectEmployerResult.fromList(employer: employer)
        : SelectEmployerResult.custom(employer: employer);
    final spentTime = stopTimer();
    _lendingAnalytics.employerSearch(employerSource, spentTime);

    _navigationProvider.goBack(result);
  }

  Future<void> _handleData(List<String> data) async {
    final employers = data;

    return emit(
      SelectEmployerState.idle(query: state.query, employers: employers),
    );
  }

  void _handleErrors(Object error) {
    _exceptionHandler.handle(
      error,
      LendingUiId.employer_search,
    );

    state.mapOrNull(
      loading: (it) {
        _logger.error(
          'Failed to search employers for ${it.query}.',
          error: error,
        );
        emit(SelectEmployerState.idle(query: state.query, employers: []));
      },
    );
  }

  @override
  String toString() {
    return 'SelectEmployerCubit';
  }
}
