import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';

class LendingPercentageSelector extends StatelessWidget {
  static const minimumPercentage = 5;
  static const _divisions = 4;
  final String topLabel;
  final LabelModel? topSubtitle;
  final String bottomLabel;
  final int selectedPercentage;
  final ValueSetter<int> onChangePercentage;

  const LendingPercentageSelector({
    required this.topLabel,
    required this.bottomLabel,
    required this.selectedPercentage,
    required this.onChangePercentage,
    this.topSubtitle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    const multiple = 100 ~/ _divisions;
    final percentages = List.generate(
      _divisions + 1,
      (index) => index * multiple,
    );
    percentages[0] = minimumPercentage;

    final topSubtitleModel = topSubtitle;

    return Column(
      children: [
        Label(
          model: LabelModel(
            text: topLabel,
            color: CompanyColorPointer.primary3,
            textStyle: CompanyTextStylePointer.h2,
          ),
        ),
        if (topSubtitleModel != null) ...[
          Space.fromSpacingVertical(Spacing.s1),
          Label(model: topSubtitleModel),
        ],
        Space.fromSpacingVertical(Spacing.s4),
        Semantics(
          label: 'Slide to adjust amount',
          slider: true,
          child: CompanySlider(
            CompanySliderModel(
              divisions: _divisions,
              value: _getSliderValue(percentages),
            ),
            onChanged: (value) => onChangePercentage(
              percentages[value ~/ (multiple / 100)],
            ),
          ),
        ),
        Space.fromSpacingVertical(Spacing.s4),
        Label(
          model: LabelModel(
            text: bottomLabel,
            textStyle: CompanyTextStylePointer.b2,
            color: CompanyColorPointer.secondary4,
            textAlign: LabelTextAlign.center,
          ),
        ),
      ],
    );
  }

  double _getSliderValue(List<int> percentages) {
    const multiple = 100 / _divisions;

    final percentage = (selectedPercentage / multiple).round() * multiple;

    if (percentage <= minimumPercentage) return 0.0;

    if (percentages.contains(percentage)) {
      return percentage / 100;
    }

    onChangePercentage(100);

    return 1.0;
  }
}
