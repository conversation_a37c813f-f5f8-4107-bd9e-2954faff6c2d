// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lending_xsell_banner_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LendingXsellBannerState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)
        idle,
    required TResult Function() empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult? Function()? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult Function()? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadingLendingXsellBannerState value) loading,
    required TResult Function(_LoadedLendingXsellBannerState value) idle,
    required TResult Function(_EmptyLendingXsellBannerState value) empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadingLendingXsellBannerState value)? loading,
    TResult? Function(_LoadedLendingXsellBannerState value)? idle,
    TResult? Function(_EmptyLendingXsellBannerState value)? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadingLendingXsellBannerState value)? loading,
    TResult Function(_LoadedLendingXsellBannerState value)? idle,
    TResult Function(_EmptyLendingXsellBannerState value)? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LendingXsellBannerStateCopyWith<$Res> {
  factory $LendingXsellBannerStateCopyWith(LendingXsellBannerState value,
          $Res Function(LendingXsellBannerState) then) =
      _$LendingXsellBannerStateCopyWithImpl<$Res, LendingXsellBannerState>;
}

/// @nodoc
class _$LendingXsellBannerStateCopyWithImpl<$Res,
        $Val extends LendingXsellBannerState>
    implements $LendingXsellBannerStateCopyWith<$Res> {
  _$LendingXsellBannerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LendingXsellBannerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadingLendingXsellBannerStateImplCopyWith<$Res> {
  factory _$$LoadingLendingXsellBannerStateImplCopyWith(
          _$LoadingLendingXsellBannerStateImpl value,
          $Res Function(_$LoadingLendingXsellBannerStateImpl) then) =
      __$$LoadingLendingXsellBannerStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingLendingXsellBannerStateImplCopyWithImpl<$Res>
    extends _$LendingXsellBannerStateCopyWithImpl<$Res,
        _$LoadingLendingXsellBannerStateImpl>
    implements _$$LoadingLendingXsellBannerStateImplCopyWith<$Res> {
  __$$LoadingLendingXsellBannerStateImplCopyWithImpl(
      _$LoadingLendingXsellBannerStateImpl _value,
      $Res Function(_$LoadingLendingXsellBannerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LendingXsellBannerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingLendingXsellBannerStateImpl
    implements _LoadingLendingXsellBannerState {
  const _$LoadingLendingXsellBannerStateImpl();

  @override
  String toString() {
    return 'LendingXsellBannerState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadingLendingXsellBannerStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)
        idle,
    required TResult Function() empty,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult? Function()? empty,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadingLendingXsellBannerState value) loading,
    required TResult Function(_LoadedLendingXsellBannerState value) idle,
    required TResult Function(_EmptyLendingXsellBannerState value) empty,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadingLendingXsellBannerState value)? loading,
    TResult? Function(_LoadedLendingXsellBannerState value)? idle,
    TResult? Function(_EmptyLendingXsellBannerState value)? empty,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadingLendingXsellBannerState value)? loading,
    TResult Function(_LoadedLendingXsellBannerState value)? idle,
    TResult Function(_EmptyLendingXsellBannerState value)? empty,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _LoadingLendingXsellBannerState
    implements LendingXsellBannerState {
  const factory _LoadingLendingXsellBannerState() =
      _$LoadingLendingXsellBannerStateImpl;
}

/// @nodoc
abstract class _$$LoadedLendingXsellBannerStateImplCopyWith<$Res> {
  factory _$$LoadedLendingXsellBannerStateImplCopyWith(
          _$LoadedLendingXsellBannerStateImpl value,
          $Res Function(_$LoadedLendingXsellBannerStateImpl) then) =
      __$$LoadedLendingXsellBannerStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String cashbackPercentage, LendingXsellPromotionTarget target});
}

/// @nodoc
class __$$LoadedLendingXsellBannerStateImplCopyWithImpl<$Res>
    extends _$LendingXsellBannerStateCopyWithImpl<$Res,
        _$LoadedLendingXsellBannerStateImpl>
    implements _$$LoadedLendingXsellBannerStateImplCopyWith<$Res> {
  __$$LoadedLendingXsellBannerStateImplCopyWithImpl(
      _$LoadedLendingXsellBannerStateImpl _value,
      $Res Function(_$LoadedLendingXsellBannerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LendingXsellBannerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cashbackPercentage = null,
    Object? target = null,
  }) {
    return _then(_$LoadedLendingXsellBannerStateImpl(
      cashbackPercentage: null == cashbackPercentage
          ? _value.cashbackPercentage
          : cashbackPercentage // ignore: cast_nullable_to_non_nullable
              as String,
      target: null == target
          ? _value.target
          : target // ignore: cast_nullable_to_non_nullable
              as LendingXsellPromotionTarget,
    ));
  }
}

/// @nodoc

class _$LoadedLendingXsellBannerStateImpl
    implements _LoadedLendingXsellBannerState {
  const _$LoadedLendingXsellBannerStateImpl(
      {required this.cashbackPercentage, required this.target});

  @override
  final String cashbackPercentage;
  @override
  final LendingXsellPromotionTarget target;

  @override
  String toString() {
    return 'LendingXsellBannerState.idle(cashbackPercentage: $cashbackPercentage, target: $target)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedLendingXsellBannerStateImpl &&
            (identical(other.cashbackPercentage, cashbackPercentage) ||
                other.cashbackPercentage == cashbackPercentage) &&
            (identical(other.target, target) || other.target == target));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cashbackPercentage, target);

  /// Create a copy of LendingXsellBannerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedLendingXsellBannerStateImplCopyWith<
          _$LoadedLendingXsellBannerStateImpl>
      get copyWith => __$$LoadedLendingXsellBannerStateImplCopyWithImpl<
          _$LoadedLendingXsellBannerStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)
        idle,
    required TResult Function() empty,
  }) {
    return idle(cashbackPercentage, target);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult? Function()? empty,
  }) {
    return idle?.call(cashbackPercentage, target);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(cashbackPercentage, target);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadingLendingXsellBannerState value) loading,
    required TResult Function(_LoadedLendingXsellBannerState value) idle,
    required TResult Function(_EmptyLendingXsellBannerState value) empty,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadingLendingXsellBannerState value)? loading,
    TResult? Function(_LoadedLendingXsellBannerState value)? idle,
    TResult? Function(_EmptyLendingXsellBannerState value)? empty,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadingLendingXsellBannerState value)? loading,
    TResult Function(_LoadedLendingXsellBannerState value)? idle,
    TResult Function(_EmptyLendingXsellBannerState value)? empty,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _LoadedLendingXsellBannerState
    implements LendingXsellBannerState {
  const factory _LoadedLendingXsellBannerState(
          {required final String cashbackPercentage,
          required final LendingXsellPromotionTarget target}) =
      _$LoadedLendingXsellBannerStateImpl;

  String get cashbackPercentage;
  LendingXsellPromotionTarget get target;

  /// Create a copy of LendingXsellBannerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedLendingXsellBannerStateImplCopyWith<
          _$LoadedLendingXsellBannerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EmptyLendingXsellBannerStateImplCopyWith<$Res> {
  factory _$$EmptyLendingXsellBannerStateImplCopyWith(
          _$EmptyLendingXsellBannerStateImpl value,
          $Res Function(_$EmptyLendingXsellBannerStateImpl) then) =
      __$$EmptyLendingXsellBannerStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EmptyLendingXsellBannerStateImplCopyWithImpl<$Res>
    extends _$LendingXsellBannerStateCopyWithImpl<$Res,
        _$EmptyLendingXsellBannerStateImpl>
    implements _$$EmptyLendingXsellBannerStateImplCopyWith<$Res> {
  __$$EmptyLendingXsellBannerStateImplCopyWithImpl(
      _$EmptyLendingXsellBannerStateImpl _value,
      $Res Function(_$EmptyLendingXsellBannerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LendingXsellBannerState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$EmptyLendingXsellBannerStateImpl
    implements _EmptyLendingXsellBannerState {
  const _$EmptyLendingXsellBannerStateImpl();

  @override
  String toString() {
    return 'LendingXsellBannerState.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EmptyLendingXsellBannerStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)
        idle,
    required TResult Function() empty,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult? Function()? empty,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(
            String cashbackPercentage, LendingXsellPromotionTarget target)?
        idle,
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadingLendingXsellBannerState value) loading,
    required TResult Function(_LoadedLendingXsellBannerState value) idle,
    required TResult Function(_EmptyLendingXsellBannerState value) empty,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadingLendingXsellBannerState value)? loading,
    TResult? Function(_LoadedLendingXsellBannerState value)? idle,
    TResult? Function(_EmptyLendingXsellBannerState value)? empty,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadingLendingXsellBannerState value)? loading,
    TResult Function(_LoadedLendingXsellBannerState value)? idle,
    TResult Function(_EmptyLendingXsellBannerState value)? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class _EmptyLendingXsellBannerState
    implements LendingXsellBannerState {
  const factory _EmptyLendingXsellBannerState() =
      _$EmptyLendingXsellBannerStateImpl;
}
