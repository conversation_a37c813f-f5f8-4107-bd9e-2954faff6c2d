import 'package:flutter/material.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/screens/easy_cash_manage/cubit/easy_cash_manage_cubit.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_loan_ui/feature_loan_ui.dart';

class EasyCashManageLoadedView extends StatelessWidget {
  final LoanAccount account;

  const EasyCashManageLoadedView({
    required this.account,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = LendingLocalizations.of(context);
    final cubit = context.read<EasyCashManageCubit>();

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const SizedBox(height: 24),
            Label(
              model: LabelModel(
                text: localizations.easyCashManageScreenFaqSectionTitle,
                textStyle: CompanyTextStylePointer.h4,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 16),
            ListDetailsContainer(
              model: ListDetailsContainerModel(
                items: [
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: localizations.faq,
                      textColor: CompanyColorPointer.secondary1,
                    ),
                    valueModel: ListDetailsValueModel.icon(
                      iconPointer:
                          Directionality.of(context) == TextDirection.rtl
                              ? CompanyIconPointer.chevron_left
                              : CompanyIconPointer.chevron_right,
                    ),
                  ),
                ],
              ),
              onValuePressed: (_) => cubit.goToFaq(),
            ),
            const SizedBox(height: 32),
            Label(
              model: LabelModel(
                text: localizations.lendingManageCreditPageDocuments,
                textStyle: CompanyTextStylePointer.h4,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 16),
            ListDetailsContainer(
              model: ListDetailsContainerModel(
                items: [
                  ListDetailsModel(
                    textLabelModel: ListDetailsTextLabelModel(
                      text: localizations.lendingManageScreenKfs,
                      textColor: CompanyColorPointer.secondary1,
                    ),
                    valueModel: const ListDetailsValueModel.icon(
                      iconPointer: CompanyIconPointer.doc,
                    ),
                  ),
                ],
              ),
              onValuePressed: (_) => cubit.goToKfs(),
            ),
            const LendingTermsAndConditionsTile(
              padding: EdgeInsets.only(top: 8),
            ),
          ],
        ),
      ),
    );
  }
}
