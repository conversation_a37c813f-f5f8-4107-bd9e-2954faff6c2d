import 'dart:async';

import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:wio_feature_content_domain_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

class RetailLendingDocument extends BaseDocument {
  final LendingDocument type;
  const RetailLendingDocument({
    required this.type,
    required super.enUrl,
    required super.arUrl,
  });
}

sealed class RetailDocumentViewerDelegateConfig
    implements DocumentViewerDelegateConfig {
  final LendingUiId sourceUiId;

  const RetailDocumentViewerDelegateConfig({required this.sourceUiId});
}

class RetailDocumentViewerDocumentConfig
    extends RetailDocumentViewerDelegateConfig {
  final RetailLendingDocument document;

  @override
  final LoanProductIdentifier productIdentifier;

  const RetailDocumentViewerDocumentConfig({
    required this.document,
    required super.sourceUiId,
    required this.productIdentifier,
  });
}

class RetailDocumentViewerTypeConfig
    extends RetailDocumentViewerDelegateConfig {
  final LendingDocument documentType;

  @override
  final LoanProductIdentifier productIdentifier;

  const RetailDocumentViewerTypeConfig({
    required this.documentType,
    required super.sourceUiId,
    required this.productIdentifier,
  });
}

class RetailLendingDocumentViewerDelegate implements DocumentViewerDelegate {
  final LendingAnalytics _lendingAnalytics;
  final LendingExceptionHandler _exceptionHandler;
  final RetailDocumentViewerDelegateConfig _config;
  final ContentInteractor _contentInteractor;

  const RetailLendingDocumentViewerDelegate({
    required LendingAnalytics lendingAnalytics,
    required LendingExceptionHandler exceptionHandler,
    required RetailDocumentViewerDelegateConfig config,
    required ContentInteractor contentInteractor,
  })  : _lendingAnalytics = lendingAnalytics,
        _exceptionHandler = exceptionHandler,
        _contentInteractor = contentInteractor,
        _config = config;

  LendingDocument get _type => switch (_config) {
        final RetailDocumentViewerDocumentConfig config => config.document.type,
        final RetailDocumentViewerTypeConfig config => config.documentType,
      };

  @override
  Future<BaseDocument?> get document async => switch (_config) {
        final RetailDocumentViewerDocumentConfig config => config.document,
        final RetailDocumentViewerTypeConfig _ => await _fetchDocument(),
      };

  @override
  FutureOr<void> handleException({
    required Object error,
    required StackTrace stackTrace,
  }) {
    _exceptionHandler.handle(error, _config.sourceUiId);
  }

  @override
  void trackDocumentClose(Duration duration) =>
      _lendingAnalytics.closeDocument(_config.sourceUiId, _type, duration);

  @override
  void trackDocumentOpen() =>
      _lendingAnalytics.viewDocument(_config.sourceUiId, _type);

  LegalDocumentType? get _legalDocumentType => switch (_type) {
        LendingDocument.termsAndConditions =>
          LegalDocumentType.lendingTermsAndConditions,
        LendingDocument.keyFactsStatement =>
          LegalDocumentType.lendingKeyFactsStatement,
        LendingDocument.credit_agreement => null,
        LendingDocument.personalLoanTermsAndConditions =>
          LegalDocumentType.lendingPersonalLoanTermsAndConditions,
        LendingDocument.personalLoanKeyFactsStatement =>
          LegalDocumentType.lendingPersonalLoanKeyFactsStatement,
        LendingDocument.autoLoanTermsAndConditions =>
          LegalDocumentType.lendingAutoLoanTermsAndConditions,
        LendingDocument.autoLoanKeyFactsStatement =>
          LegalDocumentType.lendingAutoLoanKeyFactsStatement,
      };
  Future<RetailLendingDocument?> _fetchDocument() async {
    final legalDocType = _legalDocumentType;
    if (legalDocType == null) {
      return null;
    }

    final doc = await _contentInteractor.getDocumentByType(legalDocType);

    return RetailLendingDocument(
      type: _type,
      enUrl: doc.urls.englishUrl,
      arUrl: doc.urls.arabicUrl,
    );
  }
}
