part of '../update_credit_limit_page.dart';

class _ThingsToKnow extends StatelessWidget {
  final bool canIncreaseLimit;

  const _ThingsToKnow({required this.canIncreaseLimit});

  @override
  Widget build(BuildContext context) {
    final localizations = LendingLocalizations.of(context);
    final condition3Subtitle = canIncreaseLimit
        ? localizations.lendingUpdateCreditLimitCondition3Subtitle
        : localizations.lendingUpdateCreditLimitCondition3SubtitleAfterCutoff;

    return Padding(
      padding: const EdgeInsets.only(top: 48),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Label(
            model: LabelModel(
              text: localizations.lendingSetupPaymentThingsToKnowTitle,
              textStyle: CompanyTextStylePointer.h4,
              color: CompanyColorPointer.primary2,
            ),
          ),
          const SizedBox(height: 24),
          if (!canIncreaseLimit) ...[
            _getFeatureCard(
              title: localizations.lendingUpdateCreditLimitCondition1Title,
              description:
                  localizations.lendingUpdateCreditLimitCondition1Subtitle,
              iconPointer: CompanyIconPointer.information,
            ),
            const SizedBox(height: 12),
          ],
          _getFeatureCard(
            title: localizations.lendingCreditLimitReduceCondition2Title,
            description:
                localizations.lendingCreditLimitReduceCondition2Subtitle,
            iconPointer: CompanyIconPointer.information,
          ),
          const SizedBox(height: 12),
          _getFeatureCard(
            title: localizations.lendingCreditLimitReduceCondition3Title,
            description: condition3Subtitle,
            iconPointer: CompanyIconPointer.action,
          ),
          const SizedBox(height: 32),
          Label(
            model: LabelModel(
              text: localizations.lendingCreditLimitReduceDetailsLabel,
              textStyle: CompanyTextStylePointer.b3,
              color: CompanyColorPointer.primary2,
              opacity: 0.56,
              textAlign: LabelTextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  FeatureCard _getFeatureCard({
    required String title,
    required String description,
    required CompanyIconPointer iconPointer,
  }) {
    return FeatureCard(
      model: FeatureCardModel(
        icon: iconPointer.toGraphicAsset(),
        headline: title,
        description: description,
      ),
    );
  }
}
