part of '../learn_more_page.dart';

class _WhatYouNeed extends StatelessWidget {
  const _WhatYouNeed();

  @override
  Widget build(BuildContext context) {
    final localizations = LendingLocalizations.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Label(
            model: LabelModel(
              text: localizations.personalLoanLearnMoreWhatYouNeedLabel,
              textStyle: CompanyTextStylePointer.b2,
              color: CompanyColorPointer.primary3,
            ),
          ),
          const SizedBox(height: 16),
          SequenceList(
            model: SequenceListModel(
              connectorStyle: SequenceListConnectorStyle.hidden,
              items: [
                _getSequenceListStep(
                  localizations.personalLoanLearnMoreWhatYouNeed1,
                ),
                _getSequenceListStep(
                  localizations.personalLoanLearnMoreWhatYouNeed2,
                ),
                _getSequenceListStep(
                  localizations.personalLoanLearnMoreWhatYouNeed3,
                ),
                _getSequenceListStep(
                  localizations.personalLoanLearnMoreWhatYouNeed4,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SequenceListStepModel _getSequenceListStep(String text) {
    return SequenceListStepModel.icon(
      content: SequenceListStepContent(
        title: text,
        titleColor: CompanyColorPointer.secondary4,
      ),
      iconPointer: CompanyIconPointer.selection_selected,
      iconColor: CompanyColorPointer.secondary4,
    );
  }
}
