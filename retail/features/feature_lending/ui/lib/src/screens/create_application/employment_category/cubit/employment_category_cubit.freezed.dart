// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'employment_category_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EmploymentCategoryState {
  EmploymentCategory? get selectedCategory =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EmploymentCategory? selectedCategory) idle,
    required TResult Function(EmploymentCategory selectedCategory) processing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EmploymentCategory? selectedCategory)? idle,
    TResult? Function(EmploymentCategory selectedCategory)? processing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EmploymentCategory? selectedCategory)? idle,
    TResult Function(EmploymentCategory selectedCategory)? processing,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IdleEmploymentCategoryState value) idle,
    required TResult Function(_ProcessingEmploymentCategoryState value)
        processing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IdleEmploymentCategoryState value)? idle,
    TResult? Function(_ProcessingEmploymentCategoryState value)? processing,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IdleEmploymentCategoryState value)? idle,
    TResult Function(_ProcessingEmploymentCategoryState value)? processing,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EmploymentCategoryStateCopyWith<EmploymentCategoryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EmploymentCategoryStateCopyWith<$Res> {
  factory $EmploymentCategoryStateCopyWith(EmploymentCategoryState value,
          $Res Function(EmploymentCategoryState) then) =
      _$EmploymentCategoryStateCopyWithImpl<$Res, EmploymentCategoryState>;
  @useResult
  $Res call({EmploymentCategory selectedCategory});
}

/// @nodoc
class _$EmploymentCategoryStateCopyWithImpl<$Res,
        $Val extends EmploymentCategoryState>
    implements $EmploymentCategoryStateCopyWith<$Res> {
  _$EmploymentCategoryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedCategory = null,
  }) {
    return _then(_value.copyWith(
      selectedCategory: null == selectedCategory
          ? _value.selectedCategory!
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as EmploymentCategory,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IdleEmploymentCategoryStateImplCopyWith<$Res>
    implements $EmploymentCategoryStateCopyWith<$Res> {
  factory _$$IdleEmploymentCategoryStateImplCopyWith(
          _$IdleEmploymentCategoryStateImpl value,
          $Res Function(_$IdleEmploymentCategoryStateImpl) then) =
      __$$IdleEmploymentCategoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({EmploymentCategory? selectedCategory});
}

/// @nodoc
class __$$IdleEmploymentCategoryStateImplCopyWithImpl<$Res>
    extends _$EmploymentCategoryStateCopyWithImpl<$Res,
        _$IdleEmploymentCategoryStateImpl>
    implements _$$IdleEmploymentCategoryStateImplCopyWith<$Res> {
  __$$IdleEmploymentCategoryStateImplCopyWithImpl(
      _$IdleEmploymentCategoryStateImpl _value,
      $Res Function(_$IdleEmploymentCategoryStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedCategory = freezed,
  }) {
    return _then(_$IdleEmploymentCategoryStateImpl(
      selectedCategory: freezed == selectedCategory
          ? _value.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as EmploymentCategory?,
    ));
  }
}

/// @nodoc

class _$IdleEmploymentCategoryStateImpl extends _IdleEmploymentCategoryState {
  const _$IdleEmploymentCategoryStateImpl({this.selectedCategory}) : super._();

  @override
  final EmploymentCategory? selectedCategory;

  @override
  String toString() {
    return 'EmploymentCategoryState.idle(selectedCategory: $selectedCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdleEmploymentCategoryStateImpl &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedCategory);

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdleEmploymentCategoryStateImplCopyWith<_$IdleEmploymentCategoryStateImpl>
      get copyWith => __$$IdleEmploymentCategoryStateImplCopyWithImpl<
          _$IdleEmploymentCategoryStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EmploymentCategory? selectedCategory) idle,
    required TResult Function(EmploymentCategory selectedCategory) processing,
  }) {
    return idle(selectedCategory);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EmploymentCategory? selectedCategory)? idle,
    TResult? Function(EmploymentCategory selectedCategory)? processing,
  }) {
    return idle?.call(selectedCategory);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EmploymentCategory? selectedCategory)? idle,
    TResult Function(EmploymentCategory selectedCategory)? processing,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(selectedCategory);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IdleEmploymentCategoryState value) idle,
    required TResult Function(_ProcessingEmploymentCategoryState value)
        processing,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IdleEmploymentCategoryState value)? idle,
    TResult? Function(_ProcessingEmploymentCategoryState value)? processing,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IdleEmploymentCategoryState value)? idle,
    TResult Function(_ProcessingEmploymentCategoryState value)? processing,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class _IdleEmploymentCategoryState extends EmploymentCategoryState {
  const factory _IdleEmploymentCategoryState(
          {final EmploymentCategory? selectedCategory}) =
      _$IdleEmploymentCategoryStateImpl;
  const _IdleEmploymentCategoryState._() : super._();

  @override
  EmploymentCategory? get selectedCategory;

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdleEmploymentCategoryStateImplCopyWith<_$IdleEmploymentCategoryStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProcessingEmploymentCategoryStateImplCopyWith<$Res>
    implements $EmploymentCategoryStateCopyWith<$Res> {
  factory _$$ProcessingEmploymentCategoryStateImplCopyWith(
          _$ProcessingEmploymentCategoryStateImpl value,
          $Res Function(_$ProcessingEmploymentCategoryStateImpl) then) =
      __$$ProcessingEmploymentCategoryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({EmploymentCategory selectedCategory});
}

/// @nodoc
class __$$ProcessingEmploymentCategoryStateImplCopyWithImpl<$Res>
    extends _$EmploymentCategoryStateCopyWithImpl<$Res,
        _$ProcessingEmploymentCategoryStateImpl>
    implements _$$ProcessingEmploymentCategoryStateImplCopyWith<$Res> {
  __$$ProcessingEmploymentCategoryStateImplCopyWithImpl(
      _$ProcessingEmploymentCategoryStateImpl _value,
      $Res Function(_$ProcessingEmploymentCategoryStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedCategory = null,
  }) {
    return _then(_$ProcessingEmploymentCategoryStateImpl(
      selectedCategory: null == selectedCategory
          ? _value.selectedCategory
          : selectedCategory // ignore: cast_nullable_to_non_nullable
              as EmploymentCategory,
    ));
  }
}

/// @nodoc

class _$ProcessingEmploymentCategoryStateImpl
    extends _ProcessingEmploymentCategoryState {
  const _$ProcessingEmploymentCategoryStateImpl(
      {required this.selectedCategory})
      : super._();

  @override
  final EmploymentCategory selectedCategory;

  @override
  String toString() {
    return 'EmploymentCategoryState.processing(selectedCategory: $selectedCategory)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProcessingEmploymentCategoryStateImpl &&
            (identical(other.selectedCategory, selectedCategory) ||
                other.selectedCategory == selectedCategory));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedCategory);

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProcessingEmploymentCategoryStateImplCopyWith<
          _$ProcessingEmploymentCategoryStateImpl>
      get copyWith => __$$ProcessingEmploymentCategoryStateImplCopyWithImpl<
          _$ProcessingEmploymentCategoryStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(EmploymentCategory? selectedCategory) idle,
    required TResult Function(EmploymentCategory selectedCategory) processing,
  }) {
    return processing(selectedCategory);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(EmploymentCategory? selectedCategory)? idle,
    TResult? Function(EmploymentCategory selectedCategory)? processing,
  }) {
    return processing?.call(selectedCategory);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(EmploymentCategory? selectedCategory)? idle,
    TResult Function(EmploymentCategory selectedCategory)? processing,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(selectedCategory);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IdleEmploymentCategoryState value) idle,
    required TResult Function(_ProcessingEmploymentCategoryState value)
        processing,
  }) {
    return processing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IdleEmploymentCategoryState value)? idle,
    TResult? Function(_ProcessingEmploymentCategoryState value)? processing,
  }) {
    return processing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IdleEmploymentCategoryState value)? idle,
    TResult Function(_ProcessingEmploymentCategoryState value)? processing,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(this);
    }
    return orElse();
  }
}

abstract class _ProcessingEmploymentCategoryState
    extends EmploymentCategoryState {
  const factory _ProcessingEmploymentCategoryState(
          {required final EmploymentCategory selectedCategory}) =
      _$ProcessingEmploymentCategoryStateImpl;
  const _ProcessingEmploymentCategoryState._() : super._();

  @override
  EmploymentCategory get selectedCategory;

  /// Create a copy of EmploymentCategoryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProcessingEmploymentCategoryStateImplCopyWith<
          _$ProcessingEmploymentCategoryStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
