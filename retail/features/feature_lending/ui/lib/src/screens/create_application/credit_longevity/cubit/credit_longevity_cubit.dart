import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/handler/application_update_request.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/credit_longevity/cubit/credit_longevity_state.dart';

class CreditLongevityCubit extends BaseCubit<CreditLongevityState> {
  final EnhancedFlowApplicationStageConfig _config;
  final LendingExceptionHandler _exceptionHandler;
  final Logger _logger;

  CreditLongevityCubit({
    required EnhancedFlowApplicationStageConfig config,
    required LendingExceptionHandler exceptionHandler,
    required Logger logger,
  })  : _config = config,
        _exceptionHandler = exceptionHandler,
        _logger = logger,
        super(const CreditLongevityState());

  Future<void> selectOption(CreditLongevityOption option) async {
    if (state.isSubmitting) return;

    try {
      _logger.info('Selecting credit longevity option: $option');
      safeEmit(state.copyWith(isSubmitting: true));

      await _config.flowHandler.updateApplicationData(
        ApplicationDataUpdateRequest(
          applicationId: _config.application.id,
          // TODO(hvadi): update application data and pass it here
          data: const ApplicationData(),
        ),
      );

      _logger.info(
        '''Successfully selected credit longevity option: $option. Moving to next stage''',
      );

      await _config.flowHandler.toNextStage();
    } on Object catch (e) {
      _exceptionHandler.handle(
        e,
        LendingUiId.credit_longevity,
        message:
            '''Error selecting credit longevity option: $option. Error: ${e.toString()}''',
      );
    } finally {
      safeEmit(state.copyWith(isSubmitting: false));
    }
  }

  void exitApplicationFlow() => _config.flowHandler.exitFlow();

  @override
  String toString() => 'CreditLongevityCubit';
}
