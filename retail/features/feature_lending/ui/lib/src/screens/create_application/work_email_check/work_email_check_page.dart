import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_ui/src/common/lending_application_app_bar.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/application_stage_config.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/work_email_check/cubit/work_email_check_cubit.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/work_email_check/cubit/work_email_check_state.dart';
import 'package:wio_feature_lending_ui/src/screens/create_application/work_email_check/views/work_email_check_loaded_view.dart';

class WorkEmailCheckPage extends StatelessWidget {
  final ApplicationStageConfig config;

  const WorkEmailCheckPage({
    required this.config,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DependencyProvider.getWithParams<WorkEmailCheckCubit,
          ApplicationStageConfig, void>(param1: config)
        ..initialize(),
      child: const _Content(),
    );
  }
}

class _Content extends StatelessWidget {
  const _Content();

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<WorkEmailCheckCubit>();

    return Scaffold(
      appBar: TopNavigation(
        LendingApplicationAppbarModel.get,
        onRightIconPressed: cubit.exitLendingApplication,
      ),
      body: SafeArea(
        child: BlocBuilder<WorkEmailCheckCubit, WorkEmailCheckState>(
          builder: (_, state) => state.map(
            initial: (_) => const CompanyShimmer(
              model: CompanyShimmerModel(),
              child: WorkEmailCheckLoadedView(),
            ),
            ready: (readyState) =>
                WorkEmailCheckLoadedView(email: readyState.email),
            error: (_) => FixedButtonsPageLayout(
              model: const FixedButtonsScrollablePageLayoutModel(),
              child: ErrorBox(
                model: defaultErroModel(context),
                onRetryPressed: () =>
                    context.read<WorkEmailCheckCubit>().initialize(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
