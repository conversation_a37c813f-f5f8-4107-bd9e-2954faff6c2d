part of 'employment_category_cubit.dart';

@freezed
sealed class EmploymentCategoryState with _$EmploymentCategoryState {
  const EmploymentCategoryState._();

  const factory EmploymentCategoryState.idle({
    EmploymentCategory? selectedCategory,
  }) = _IdleEmploymentCategoryState;

  const factory EmploymentCategoryState.processing({
    required EmploymentCategory selectedCategory,
  }) = _ProcessingEmploymentCategoryState;

  bool get isProcessing => switch (this) {
        _IdleEmploymentCategoryState() => false,
        _ProcessingEmploymentCategoryState() => true,
      };
}
