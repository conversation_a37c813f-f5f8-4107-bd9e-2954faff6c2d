// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'iban_check_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IbanCheckState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        ready,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IbanCheckInitialState value) initial,
    required TResult Function(_IbanCheckReadyState value) ready,
    required TResult Function(_IbanCheckLoadingState value) loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IbanCheckInitialState value)? initial,
    TResult? Function(_IbanCheckReadyState value)? ready,
    TResult? Function(_IbanCheckLoadingState value)? loading,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IbanCheckInitialState value)? initial,
    TResult Function(_IbanCheckReadyState value)? ready,
    TResult Function(_IbanCheckLoadingState value)? loading,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IbanCheckStateCopyWith<$Res> {
  factory $IbanCheckStateCopyWith(
          IbanCheckState value, $Res Function(IbanCheckState) then) =
      _$IbanCheckStateCopyWithImpl<$Res, IbanCheckState>;
}

/// @nodoc
class _$IbanCheckStateCopyWithImpl<$Res, $Val extends IbanCheckState>
    implements $IbanCheckStateCopyWith<$Res> {
  _$IbanCheckStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$IbanCheckInitialStateImplCopyWith<$Res> {
  factory _$$IbanCheckInitialStateImplCopyWith(
          _$IbanCheckInitialStateImpl value,
          $Res Function(_$IbanCheckInitialStateImpl) then) =
      __$$IbanCheckInitialStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IbanCheckInitialStateImplCopyWithImpl<$Res>
    extends _$IbanCheckStateCopyWithImpl<$Res, _$IbanCheckInitialStateImpl>
    implements _$$IbanCheckInitialStateImplCopyWith<$Res> {
  __$$IbanCheckInitialStateImplCopyWithImpl(_$IbanCheckInitialStateImpl _value,
      $Res Function(_$IbanCheckInitialStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IbanCheckInitialStateImpl extends _IbanCheckInitialState {
  const _$IbanCheckInitialStateImpl() : super._();

  @override
  String toString() {
    return 'IbanCheckState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IbanCheckInitialStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        ready,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        loading,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IbanCheckInitialState value) initial,
    required TResult Function(_IbanCheckReadyState value) ready,
    required TResult Function(_IbanCheckLoadingState value) loading,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IbanCheckInitialState value)? initial,
    TResult? Function(_IbanCheckReadyState value)? ready,
    TResult? Function(_IbanCheckLoadingState value)? loading,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IbanCheckInitialState value)? initial,
    TResult Function(_IbanCheckReadyState value)? ready,
    TResult Function(_IbanCheckLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _IbanCheckInitialState extends IbanCheckState {
  const factory _IbanCheckInitialState() = _$IbanCheckInitialStateImpl;
  const _IbanCheckInitialState._() : super._();
}

/// @nodoc
abstract class _$$IbanCheckReadyStateImplCopyWith<$Res> {
  factory _$$IbanCheckReadyStateImplCopyWith(_$IbanCheckReadyStateImpl value,
          $Res Function(_$IbanCheckReadyStateImpl) then) =
      __$$IbanCheckReadyStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {LendingApplication application,
      IbanInputStatus ibanInputStatus,
      IbanInputModel ibanInputModel});

  $LendingApplicationCopyWith<$Res> get application;
  $IbanInputStatusCopyWith<$Res> get ibanInputStatus;
  $IbanInputModelCopyWith<$Res> get ibanInputModel;
}

/// @nodoc
class __$$IbanCheckReadyStateImplCopyWithImpl<$Res>
    extends _$IbanCheckStateCopyWithImpl<$Res, _$IbanCheckReadyStateImpl>
    implements _$$IbanCheckReadyStateImplCopyWith<$Res> {
  __$$IbanCheckReadyStateImplCopyWithImpl(_$IbanCheckReadyStateImpl _value,
      $Res Function(_$IbanCheckReadyStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? application = null,
    Object? ibanInputStatus = null,
    Object? ibanInputModel = null,
  }) {
    return _then(_$IbanCheckReadyStateImpl(
      application: null == application
          ? _value.application
          : application // ignore: cast_nullable_to_non_nullable
              as LendingApplication,
      ibanInputStatus: null == ibanInputStatus
          ? _value.ibanInputStatus
          : ibanInputStatus // ignore: cast_nullable_to_non_nullable
              as IbanInputStatus,
      ibanInputModel: null == ibanInputModel
          ? _value.ibanInputModel
          : ibanInputModel // ignore: cast_nullable_to_non_nullable
              as IbanInputModel,
    ));
  }

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LendingApplicationCopyWith<$Res> get application {
    return $LendingApplicationCopyWith<$Res>(_value.application, (value) {
      return _then(_value.copyWith(application: value));
    });
  }

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IbanInputStatusCopyWith<$Res> get ibanInputStatus {
    return $IbanInputStatusCopyWith<$Res>(_value.ibanInputStatus, (value) {
      return _then(_value.copyWith(ibanInputStatus: value));
    });
  }

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IbanInputModelCopyWith<$Res> get ibanInputModel {
    return $IbanInputModelCopyWith<$Res>(_value.ibanInputModel, (value) {
      return _then(_value.copyWith(ibanInputModel: value));
    });
  }
}

/// @nodoc

class _$IbanCheckReadyStateImpl extends _IbanCheckReadyState {
  const _$IbanCheckReadyStateImpl(
      {required this.application,
      required this.ibanInputStatus,
      required this.ibanInputModel})
      : super._();

  @override
  final LendingApplication application;
  @override
  final IbanInputStatus ibanInputStatus;
  @override
  final IbanInputModel ibanInputModel;

  @override
  String toString() {
    return 'IbanCheckState.ready(application: $application, ibanInputStatus: $ibanInputStatus, ibanInputModel: $ibanInputModel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IbanCheckReadyStateImpl &&
            (identical(other.application, application) ||
                other.application == application) &&
            (identical(other.ibanInputStatus, ibanInputStatus) ||
                other.ibanInputStatus == ibanInputStatus) &&
            (identical(other.ibanInputModel, ibanInputModel) ||
                other.ibanInputModel == ibanInputModel));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, application, ibanInputStatus, ibanInputModel);

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IbanCheckReadyStateImplCopyWith<_$IbanCheckReadyStateImpl> get copyWith =>
      __$$IbanCheckReadyStateImplCopyWithImpl<_$IbanCheckReadyStateImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        ready,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        loading,
  }) {
    return ready(application, ibanInputStatus, ibanInputModel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
  }) {
    return ready?.call(application, ibanInputStatus, ibanInputModel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
    required TResult orElse(),
  }) {
    if (ready != null) {
      return ready(application, ibanInputStatus, ibanInputModel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IbanCheckInitialState value) initial,
    required TResult Function(_IbanCheckReadyState value) ready,
    required TResult Function(_IbanCheckLoadingState value) loading,
  }) {
    return ready(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IbanCheckInitialState value)? initial,
    TResult? Function(_IbanCheckReadyState value)? ready,
    TResult? Function(_IbanCheckLoadingState value)? loading,
  }) {
    return ready?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IbanCheckInitialState value)? initial,
    TResult Function(_IbanCheckReadyState value)? ready,
    TResult Function(_IbanCheckLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (ready != null) {
      return ready(this);
    }
    return orElse();
  }
}

abstract class _IbanCheckReadyState extends IbanCheckState {
  const factory _IbanCheckReadyState(
          {required final LendingApplication application,
          required final IbanInputStatus ibanInputStatus,
          required final IbanInputModel ibanInputModel}) =
      _$IbanCheckReadyStateImpl;
  const _IbanCheckReadyState._() : super._();

  LendingApplication get application;
  IbanInputStatus get ibanInputStatus;
  IbanInputModel get ibanInputModel;

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IbanCheckReadyStateImplCopyWith<_$IbanCheckReadyStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$IbanCheckLoadingStateImplCopyWith<$Res> {
  factory _$$IbanCheckLoadingStateImplCopyWith(
          _$IbanCheckLoadingStateImpl value,
          $Res Function(_$IbanCheckLoadingStateImpl) then) =
      __$$IbanCheckLoadingStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {LendingApplication application,
      IbanInputStatus ibanInputStatus,
      IbanInputModel ibanInputModel});

  $LendingApplicationCopyWith<$Res> get application;
  $IbanInputStatusCopyWith<$Res> get ibanInputStatus;
  $IbanInputModelCopyWith<$Res> get ibanInputModel;
}

/// @nodoc
class __$$IbanCheckLoadingStateImplCopyWithImpl<$Res>
    extends _$IbanCheckStateCopyWithImpl<$Res, _$IbanCheckLoadingStateImpl>
    implements _$$IbanCheckLoadingStateImplCopyWith<$Res> {
  __$$IbanCheckLoadingStateImplCopyWithImpl(_$IbanCheckLoadingStateImpl _value,
      $Res Function(_$IbanCheckLoadingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? application = null,
    Object? ibanInputStatus = null,
    Object? ibanInputModel = null,
  }) {
    return _then(_$IbanCheckLoadingStateImpl(
      application: null == application
          ? _value.application
          : application // ignore: cast_nullable_to_non_nullable
              as LendingApplication,
      ibanInputStatus: null == ibanInputStatus
          ? _value.ibanInputStatus
          : ibanInputStatus // ignore: cast_nullable_to_non_nullable
              as IbanInputStatus,
      ibanInputModel: null == ibanInputModel
          ? _value.ibanInputModel
          : ibanInputModel // ignore: cast_nullable_to_non_nullable
              as IbanInputModel,
    ));
  }

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LendingApplicationCopyWith<$Res> get application {
    return $LendingApplicationCopyWith<$Res>(_value.application, (value) {
      return _then(_value.copyWith(application: value));
    });
  }

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IbanInputStatusCopyWith<$Res> get ibanInputStatus {
    return $IbanInputStatusCopyWith<$Res>(_value.ibanInputStatus, (value) {
      return _then(_value.copyWith(ibanInputStatus: value));
    });
  }

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IbanInputModelCopyWith<$Res> get ibanInputModel {
    return $IbanInputModelCopyWith<$Res>(_value.ibanInputModel, (value) {
      return _then(_value.copyWith(ibanInputModel: value));
    });
  }
}

/// @nodoc

class _$IbanCheckLoadingStateImpl extends _IbanCheckLoadingState {
  const _$IbanCheckLoadingStateImpl(
      {required this.application,
      required this.ibanInputStatus,
      required this.ibanInputModel})
      : super._();

  @override
  final LendingApplication application;
  @override
  final IbanInputStatus ibanInputStatus;
  @override
  final IbanInputModel ibanInputModel;

  @override
  String toString() {
    return 'IbanCheckState.loading(application: $application, ibanInputStatus: $ibanInputStatus, ibanInputModel: $ibanInputModel)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IbanCheckLoadingStateImpl &&
            (identical(other.application, application) ||
                other.application == application) &&
            (identical(other.ibanInputStatus, ibanInputStatus) ||
                other.ibanInputStatus == ibanInputStatus) &&
            (identical(other.ibanInputModel, ibanInputModel) ||
                other.ibanInputModel == ibanInputModel));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, application, ibanInputStatus, ibanInputModel);

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IbanCheckLoadingStateImplCopyWith<_$IbanCheckLoadingStateImpl>
      get copyWith => __$$IbanCheckLoadingStateImplCopyWithImpl<
          _$IbanCheckLoadingStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        ready,
    required TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)
        loading,
  }) {
    return loading(application, ibanInputStatus, ibanInputModel);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult? Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
  }) {
    return loading?.call(application, ibanInputStatus, ibanInputModel);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        ready,
    TResult Function(LendingApplication application,
            IbanInputStatus ibanInputStatus, IbanInputModel ibanInputModel)?
        loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(application, ibanInputStatus, ibanInputModel);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_IbanCheckInitialState value) initial,
    required TResult Function(_IbanCheckReadyState value) ready,
    required TResult Function(_IbanCheckLoadingState value) loading,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_IbanCheckInitialState value)? initial,
    TResult? Function(_IbanCheckReadyState value)? ready,
    TResult? Function(_IbanCheckLoadingState value)? loading,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_IbanCheckInitialState value)? initial,
    TResult Function(_IbanCheckReadyState value)? ready,
    TResult Function(_IbanCheckLoadingState value)? loading,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _IbanCheckLoadingState extends IbanCheckState {
  const factory _IbanCheckLoadingState(
          {required final LendingApplication application,
          required final IbanInputStatus ibanInputStatus,
          required final IbanInputModel ibanInputModel}) =
      _$IbanCheckLoadingStateImpl;
  const _IbanCheckLoadingState._() : super._();

  LendingApplication get application;
  IbanInputStatus get ibanInputStatus;
  IbanInputModel get ibanInputModel;

  /// Create a copy of IbanCheckState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IbanCheckLoadingStateImplCopyWith<_$IbanCheckLoadingStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$IbanInputModel {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String primaryIban) salariedEmployee,
    required TResult Function(List<String> ibans) selfEmployed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String primaryIban)? salariedEmployee,
    TResult? Function(List<String> ibans)? selfEmployed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String primaryIban)? salariedEmployee,
    TResult Function(List<String> ibans)? selfEmployed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SalariedEmployeeIbanInput value)
        salariedEmployee,
    required TResult Function(_SelfEmployedIbanInput value) selfEmployed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SalariedEmployeeIbanInput value)? salariedEmployee,
    TResult? Function(_SelfEmployedIbanInput value)? selfEmployed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SalariedEmployeeIbanInput value)? salariedEmployee,
    TResult Function(_SelfEmployedIbanInput value)? selfEmployed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IbanInputModelCopyWith<$Res> {
  factory $IbanInputModelCopyWith(
          IbanInputModel value, $Res Function(IbanInputModel) then) =
      _$IbanInputModelCopyWithImpl<$Res, IbanInputModel>;
}

/// @nodoc
class _$IbanInputModelCopyWithImpl<$Res, $Val extends IbanInputModel>
    implements $IbanInputModelCopyWith<$Res> {
  _$IbanInputModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SalariedEmployeeIbanInputImplCopyWith<$Res> {
  factory _$$SalariedEmployeeIbanInputImplCopyWith(
          _$SalariedEmployeeIbanInputImpl value,
          $Res Function(_$SalariedEmployeeIbanInputImpl) then) =
      __$$SalariedEmployeeIbanInputImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String primaryIban});
}

/// @nodoc
class __$$SalariedEmployeeIbanInputImplCopyWithImpl<$Res>
    extends _$IbanInputModelCopyWithImpl<$Res, _$SalariedEmployeeIbanInputImpl>
    implements _$$SalariedEmployeeIbanInputImplCopyWith<$Res> {
  __$$SalariedEmployeeIbanInputImplCopyWithImpl(
      _$SalariedEmployeeIbanInputImpl _value,
      $Res Function(_$SalariedEmployeeIbanInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? primaryIban = null,
  }) {
    return _then(_$SalariedEmployeeIbanInputImpl(
      primaryIban: null == primaryIban
          ? _value.primaryIban
          : primaryIban // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SalariedEmployeeIbanInputImpl extends _SalariedEmployeeIbanInput {
  const _$SalariedEmployeeIbanInputImpl({required this.primaryIban})
      : super._();

  @override
  final String primaryIban;

  @override
  String toString() {
    return 'IbanInputModel.salariedEmployee(primaryIban: $primaryIban)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SalariedEmployeeIbanInputImpl &&
            (identical(other.primaryIban, primaryIban) ||
                other.primaryIban == primaryIban));
  }

  @override
  int get hashCode => Object.hash(runtimeType, primaryIban);

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SalariedEmployeeIbanInputImplCopyWith<_$SalariedEmployeeIbanInputImpl>
      get copyWith => __$$SalariedEmployeeIbanInputImplCopyWithImpl<
          _$SalariedEmployeeIbanInputImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String primaryIban) salariedEmployee,
    required TResult Function(List<String> ibans) selfEmployed,
  }) {
    return salariedEmployee(primaryIban);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String primaryIban)? salariedEmployee,
    TResult? Function(List<String> ibans)? selfEmployed,
  }) {
    return salariedEmployee?.call(primaryIban);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String primaryIban)? salariedEmployee,
    TResult Function(List<String> ibans)? selfEmployed,
    required TResult orElse(),
  }) {
    if (salariedEmployee != null) {
      return salariedEmployee(primaryIban);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SalariedEmployeeIbanInput value)
        salariedEmployee,
    required TResult Function(_SelfEmployedIbanInput value) selfEmployed,
  }) {
    return salariedEmployee(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SalariedEmployeeIbanInput value)? salariedEmployee,
    TResult? Function(_SelfEmployedIbanInput value)? selfEmployed,
  }) {
    return salariedEmployee?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SalariedEmployeeIbanInput value)? salariedEmployee,
    TResult Function(_SelfEmployedIbanInput value)? selfEmployed,
    required TResult orElse(),
  }) {
    if (salariedEmployee != null) {
      return salariedEmployee(this);
    }
    return orElse();
  }
}

abstract class _SalariedEmployeeIbanInput extends IbanInputModel {
  const factory _SalariedEmployeeIbanInput(
      {required final String primaryIban}) = _$SalariedEmployeeIbanInputImpl;
  const _SalariedEmployeeIbanInput._() : super._();

  String get primaryIban;

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SalariedEmployeeIbanInputImplCopyWith<_$SalariedEmployeeIbanInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SelfEmployedIbanInputImplCopyWith<$Res> {
  factory _$$SelfEmployedIbanInputImplCopyWith(
          _$SelfEmployedIbanInputImpl value,
          $Res Function(_$SelfEmployedIbanInputImpl) then) =
      __$$SelfEmployedIbanInputImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> ibans});
}

/// @nodoc
class __$$SelfEmployedIbanInputImplCopyWithImpl<$Res>
    extends _$IbanInputModelCopyWithImpl<$Res, _$SelfEmployedIbanInputImpl>
    implements _$$SelfEmployedIbanInputImplCopyWith<$Res> {
  __$$SelfEmployedIbanInputImplCopyWithImpl(_$SelfEmployedIbanInputImpl _value,
      $Res Function(_$SelfEmployedIbanInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ibans = null,
  }) {
    return _then(_$SelfEmployedIbanInputImpl(
      ibans: null == ibans
          ? _value._ibans
          : ibans // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$SelfEmployedIbanInputImpl extends _SelfEmployedIbanInput {
  const _$SelfEmployedIbanInputImpl({required final List<String> ibans})
      : _ibans = ibans,
        super._();

  final List<String> _ibans;
  @override
  List<String> get ibans {
    if (_ibans is EqualUnmodifiableListView) return _ibans;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ibans);
  }

  @override
  String toString() {
    return 'IbanInputModel.selfEmployed(ibans: $ibans)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SelfEmployedIbanInputImpl &&
            const DeepCollectionEquality().equals(other._ibans, _ibans));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_ibans));

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SelfEmployedIbanInputImplCopyWith<_$SelfEmployedIbanInputImpl>
      get copyWith => __$$SelfEmployedIbanInputImplCopyWithImpl<
          _$SelfEmployedIbanInputImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String primaryIban) salariedEmployee,
    required TResult Function(List<String> ibans) selfEmployed,
  }) {
    return selfEmployed(ibans);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String primaryIban)? salariedEmployee,
    TResult? Function(List<String> ibans)? selfEmployed,
  }) {
    return selfEmployed?.call(ibans);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String primaryIban)? salariedEmployee,
    TResult Function(List<String> ibans)? selfEmployed,
    required TResult orElse(),
  }) {
    if (selfEmployed != null) {
      return selfEmployed(ibans);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SalariedEmployeeIbanInput value)
        salariedEmployee,
    required TResult Function(_SelfEmployedIbanInput value) selfEmployed,
  }) {
    return selfEmployed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SalariedEmployeeIbanInput value)? salariedEmployee,
    TResult? Function(_SelfEmployedIbanInput value)? selfEmployed,
  }) {
    return selfEmployed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SalariedEmployeeIbanInput value)? salariedEmployee,
    TResult Function(_SelfEmployedIbanInput value)? selfEmployed,
    required TResult orElse(),
  }) {
    if (selfEmployed != null) {
      return selfEmployed(this);
    }
    return orElse();
  }
}

abstract class _SelfEmployedIbanInput extends IbanInputModel {
  const factory _SelfEmployedIbanInput({required final List<String> ibans}) =
      _$SelfEmployedIbanInputImpl;
  const _SelfEmployedIbanInput._() : super._();

  List<String> get ibans;

  /// Create a copy of IbanInputModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SelfEmployedIbanInputImplCopyWith<_$SelfEmployedIbanInputImpl>
      get copyWith => throw _privateConstructorUsedError;
}
