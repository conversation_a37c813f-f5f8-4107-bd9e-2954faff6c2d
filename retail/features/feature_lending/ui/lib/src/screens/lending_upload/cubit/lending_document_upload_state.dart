part of 'lending_document_upload_cubit.dart';

sealed class UploadDocument {
  String get docName;
}

@immutable
class UploadedDocument implements UploadDocument {
  final LendingUploadedDocument document;
  final Uint8List fileBytes;

  const UploadedDocument({required this.document, required this.fileBytes});

  @override
  String get docName => document.fileName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UploadedDocument &&
        other.document == document &&
        const DeepCollectionEquality().equals(other.fileBytes, fileBytes);
  }

  @override
  int get hashCode =>
      document.hashCode ^ const DeepCollectionEquality().hash(fileBytes);
}

@immutable
class UploadingDocument implements UploadDocument {
  final Uint8List fileBytes;
  final String fileName;
  final double uploadedPercentage;
  final int size;

  const UploadingDocument({
    required this.fileBytes,
    required this.fileName,
    required this.uploadedPercentage,
    required this.size,
  });

  @override
  String get docName => fileName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UploadingDocument &&
        const DeepCollectionEquality().equals(fileBytes, other.fileBytes) &&
        other.fileName == fileName &&
        other.uploadedPercentage == uploadedPercentage &&
        other.size == size;
  }

  @override
  int get hashCode =>
      const DeepCollectionEquality().hash(fileBytes) ^
      fileName.hashCode ^
      uploadedPercentage.hashCode ^
      size.hashCode;
}

@freezed
class LendingDocumentUploadState with _$LendingDocumentUploadState {
  const factory LendingDocumentUploadState.loading() = _Loading;

  const factory LendingDocumentUploadState.ready({
    required String documentExampleUrl,
    @Default([]) List<UploadDocument> documents,
  }) = _Ready;

  const factory LendingDocumentUploadState.processing({
    required String documentExampleUrl,
    @Default([]) List<UploadDocument> documents,
  }) = _Processing;

  const factory LendingDocumentUploadState.error({
    required String message,
  }) = _Error;

  const LendingDocumentUploadState._();

  LendingDocumentUploadState toReady() {
    return maybeMap(
      processing: (processingState) {
        return LendingDocumentUploadState.ready(
          documentExampleUrl: processingState.documentExampleUrl,
          documents: processingState.documents,
        );
      },
      orElse: () => throw Exception('Invalid state transition from $this'),
    );
  }

  LendingDocumentUploadState toProcessing() {
    return maybeMap(
      ready: (readyState) {
        return LendingDocumentUploadState.processing(
          documentExampleUrl: readyState.documentExampleUrl,
          documents: readyState.documents,
        );
      },
      orElse: () => throw Exception('Invalid state transition from $this'),
    );
  }

  bool get isProcessing => maybeMap(
        processing: (_) => true,
        orElse: () => false,
      );

  List<UploadDocument> get documents => maybeMap(
        ready: (readyState) => readyState.documents,
        processing: (processingState) => processingState.documents,
        orElse: () => [],
      );
}
