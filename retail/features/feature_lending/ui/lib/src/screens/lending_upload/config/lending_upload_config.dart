import 'package:flutter/widgets.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/common/lending_application_flow_handler.dart';

@immutable
sealed class LendingUploadConfig {
  final LendingUploadedDocumentType type;
  final LendingApplication application;

  const LendingUploadConfig({required this.type, required this.application});
}

class LegacyFlowLendingApplicationUploadConfig extends LendingUploadConfig {
  const LegacyFlowLendingApplicationUploadConfig({
    required super.type,
    required super.application,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LegacyFlowLendingApplicationUploadConfig &&
        other.type == type &&
        other.application == application;
  }

  @override
  int get hashCode => Object.hash(type, application);
}

class EnhancedFlowLendingApplicationUploadConfig extends LendingUploadConfig {
  final LendingApplicationFlowHandler flowHandler;

  const EnhancedFlowLendingApplicationUploadConfig({
    required super.type,
    required super.application,
    required this.flowHandler,
  });
}
