import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/l10n/lending_localization.g.dart';
import 'package:wio_feature_lending_ui/src/screens/resume_application/resume_application_cubit.dart';

class ResumeApplicationPage extends StatelessWidget {
  final LendingApplication application;
  final ProductType productType;

  const ResumeApplicationPage({
    required this.application,
    required this.productType,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DependencyProvider.get<ResumeApplicationCubit>(),
      child: _Body(
        application: application,
        productType: productType,
      ),
    );
  }
}

class _Body extends StatelessWidget {
  final LendingApplication application;
  final ProductType productType;

  const _Body({
    required this.application,
    required this.productType,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ResumeApplicationCubit>();
    final l10n = LendingLocalizations.of(context);
    return Scaffold(
      appBar: const TopNavigation(
        TopNavigationModel(state: TopNavigationState.positive),
      ),
      body: FixedButtonsPageLayout(
        onPrimaryButtonPressed: cubit.goBack,
        onSecondaryButtonPressed: () => cubit.resume(
          application: application,
          productType: productType,
        ),
        model: FixedButtonsScrollablePageLayoutModel(
          primaryButton: FixedButtonsScrollablePageLayoutButton(
            label: l10n.lendingResumeApplicationGoBack,
            size: ButtonSize.medium,
            type: ButtonType.secondary,
          ),
          secondaryButton: FixedButtonsScrollablePageLayoutButton(
            label: l10n.lendingResumeAppicationContinueButton,
            size: ButtonSize.medium,
            type: ButtonType.primary,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CompanyIcon(
              CompanyIconModel(
                icon: CompanyPictogramPointer.metaphors_path.toGraphicAsset(),
                size: CompanyIconSize.xxxxLarge,
              ),
            ),
            const SizedBox(height: 15),
            Label(
              model: LabelModel(
                text: l10n.lendingResumeApplicationActiveLabel,
                textStyle: CompanyTextStylePointer.h2medium,
                color: CompanyColorPointer.primary3,
              ),
            ),
            const SizedBox(height: 24),
            Label(
              model: LabelModel(
                text: l10n.lendingResumeApplicationActiveDescription,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.secondary3,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
