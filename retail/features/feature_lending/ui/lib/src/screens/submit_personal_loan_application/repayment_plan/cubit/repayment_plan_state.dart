import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

part 'repayment_plan_state.freezed.dart';

@freezed
class PersonalLoanRepaymentPlanState with _$PersonalLoanRepaymentPlanState {
  const PersonalLoanRepaymentPlanState._();

  const factory PersonalLoanRepaymentPlanState.loading() =
      PersonalLoanRepaymentPlanLoadingState;

  const factory PersonalLoanRepaymentPlanState.idle({
    required Schedule schedule,
    required Period period,
  }) = PersonalLoanRepaymentPlanIdleState;

  const factory PersonalLoanRepaymentPlanState.inProgress({
    required Schedule schedule,
    required Period period,
  }) = PersonalLoanRepaymentPlanInProgressState;

  const factory PersonalLoanRepaymentPlanState.failed() =
      PersonalLoanRepaymentPlanFailedState;

  PersonalLoanRepaymentPlanState toInProgress() => maybeMap(
        idle: (it) => PersonalLoanRepaymentPlanState.inProgress(
          schedule: it.schedule,
          period: it.period,
        ),
        orElse: () => _stateCannotBeChanged(),
      );

  PersonalLoanRepaymentPlanState toIdle() => maybeMap(
        inProgress: (it) => PersonalLoanRepaymentPlanState.idle(
          schedule: it.schedule,
          period: it.period,
        ),
        orElse: () => _stateCannotBeChanged(),
      );

  bool get isSubmitting => maybeMap(
        inProgress: (_) => true,
        orElse: () => false,
      );

  Never _stateCannotBeChanged() => throw Exception('Illegal state transition');
}
