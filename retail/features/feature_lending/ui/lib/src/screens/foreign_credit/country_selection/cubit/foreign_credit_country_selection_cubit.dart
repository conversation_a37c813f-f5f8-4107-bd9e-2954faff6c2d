import 'package:collection/collection.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_country_api/country_feature_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/l10n/lending_localization.g.dart';
import 'package:wio_feature_lending_ui/src/common/status_screen_config_factory.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/bottom_sheets/foreign_credit_info_bottom_sheet_config.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/nova_credit_page_navigation_config.dart';
import 'package:wio_feature_lending_ui/src/screens/foreign_credit/country_selection/cubit/foreign_credit_country_selection_state.dart';
import 'package:wio_feature_lending_ui/src/screens/foreign_credit/nova_credit/config/nova_credit_config.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_user_api/index.dart';

class ForeignCreditCountrySelectionCubit
    extends BaseCubit<ForeignCreditCountrySelectionState> {
  final LendingInteractor _lendingInteractor;
  final NavigationProvider _navigationProvider;
  final Logger _logger;
  final CountryInteractor _countryInteractor;
  final LendingLocalizations _localizations;
  final UserInteractor _userInteractor;
  final String _applicationId;
  final LoanProductIdentifier _loanProductIdentifier;

  ForeignCreditCountrySelectionCubit({
    required LendingInteractor lendingInteractor,
    required NavigationProvider navigationProvider,
    required Logger logger,
    required CountryInteractor countryInteractor,
    required LendingLocalizations localizations,
    required String applicationId,
    required UserInteractor userInteractor,
    required LoanProductIdentifier loanProductIdentifier,
  })  : _lendingInteractor = lendingInteractor,
        _navigationProvider = navigationProvider,
        _logger = logger,
        _countryInteractor = countryInteractor,
        _localizations = localizations,
        _applicationId = applicationId,
        _userInteractor = userInteractor,
        _loanProductIdentifier = loanProductIdentifier,
        super(const ForeignCreditCountrySelectionState.loading());

  Future<void> init() async {
    try {
      final (fcrIframeConfiguration, countries, user) = await (
        _lendingInteractor.getForeignCreditIframeConfiguration(_applicationId),
        _countryInteractor.fetchAllCountries(),
        _userInteractor.getUser(),
      ).wait;

      final userCountryCode = user?.customerInfo?.nationality;
      final userCountry = countries.firstWhereOrNull(
        (country) => country.codeAlpha3 == userCountryCode,
      );

      safeEmit(
        ForeignCreditCountrySelectionState.idle(
          fcrIframeConfiguration: fcrIframeConfiguration,
          countries: countries,
          selectedCountry: userCountry,
        ),
      );
    } on Object catch (e, stackTrace) {
      _logger.error(
        'Error initializing foreign credit country selection',
        error: e,
        stackTrace: stackTrace,
      );
      safeEmit(const ForeignCreditCountrySelectionState.error());
    }
  }

  void showForeignCreditInfoBottomSheet() {
    _navigationProvider.showBottomSheet(ForeignCreditInfoBottomSheetConfig());
  }

  void selectCountry(String isoCode3) {
    state.mapOrNull(
      idle: (it) {
        final country = it.countries.firstWhereOrNull(
              (country) => country.codeAlpha3 == isoCode3,
            ) ??
            it.selectedCountry;

        safeEmit(
          it.copyWith(
            selectedCountry: country,
          ),
        );
      },
    );
  }

  void submit() {
    state.mapOrNull(
      idle: (it) async {
        final country = it.selectedCountry;

        if (country == null) return;

        final selectedCountryCode = country.codeAlpha3;
        final isCountrySupported = _isCountrySupported(
          selectedCountryCode,
          it.fcrIframeConfiguration.countries,
        );

        if (!isCountrySupported) {
          await _navigationProvider.navigateTo(
            _getCountryNotSupportedConfig(),
          );

          return _navigationProvider.popUntilFirstRoute();
        }

        await _navigationProvider.push(
          NovaCreditPageNavigationConfig(
            loanProductIdentifier: _loanProductIdentifier,
            novaCreditConfig: NovaCreditConfig(
              countryCode: selectedCountryCode,
              fcrIframeConfiguration: it.fcrIframeConfiguration,
              applicationId: _applicationId,
            ),
          ),
        );
      },
    );
  }

  FeatureNavigationConfig _getCountryNotSupportedConfig() {
    return StatusScreenConfigFactory.error(
      title: _localizations.foreignCreditCountryNotSupportedTitle,
      subtitle: _localizations.foreignCreditCountryNotSupportedDesc,
      primaryButtonTitle:
          _localizations.foreignCreditCountryNotSupportedPrimaryButton,
      secondaryButtonTitle:
          _localizations.foreignCreditCountryNotSupportedSecondaryButton,
    );
  }

  bool _isCountrySupported(
    String countryCode,
    List<SupportedCountry> supportedCountries,
  ) {
    return supportedCountries.any((country) => country.isoCode3 == countryCode);
  }

  @override
  String toString() => 'ForeignCreditCountrySelectionCubit';
}
