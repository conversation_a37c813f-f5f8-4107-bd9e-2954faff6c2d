part of '../credit_dashboard_page.dart';

class _AutoPayDetails extends StatelessWidget {
  final Autopay autopay;
  final Money creditCardSpent;
  final num percentageRepayment;

  const _AutoPayDetails({
    required this.autopay,
    required this.creditCardSpent,
    required this.percentageRepayment,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = LendingLocalizations.of(context);
    final cubit = context.read<CreditDashboardCubit>();
    final isInstallmentsEnabled = cubit.isInstallmentsEnabled;

    final payingInFull = percentageRepayment == 100;
    final minimumPaymentAmount =
        autopay.minimumPaymentAmount.toFormattedString();
    final installmentsAmount = autopay.linkedAccountsOutstandingAmount;
    final totalAutopayAmount = autopay.totalAutopayAmount;
    final totalDue = totalAutopayAmount + autopay.fee;
    final isMinimumRepaymentLabelShown =
        autopay.minimumPaymentAmount.isPositive;
    final String fee;
    final isNoFeeLabelShown = payingInFull || autopay.isFeeAbsent;

    if (payingInFull) {
      fee = l10n.creditDashboardAutopayNoFeeLabel;
    } else if (autopay.isFeeAbsent) {
      fee = l10n.creditAutoPayFeeFreePeriodNoFee(
        autopay.feeFreeDate?.toAutoPayFormat() ?? '',
      );
    } else {
      fee = autopay.fee.toFormattedString();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListDetailsContainer(
          onValuePressed: (index) => _onValuePressed(index, cubit),
          model: ListDetailsContainerModel(
            items: [
              _AutopaySingleDetail(
                title: l10n.autopaySectionWioCreditAmount,
                value: creditCardSpent.toFormattedString(),
              ).listDetailsModel,
              _AutopaySingleDetail(
                title: l10n.autopaySectionWioCreditRepayment,
                value: '${percentageRepayment.toInt()}%',
              ).listDetailsModelWithValueTab(l10n.editLabel),
              if (isInstallmentsEnabled)
                _AutopaySingleDetail(
                  title: l10n.autopaySectionInstallmentsAmount,
                  value: installmentsAmount.toFormattedString(),
                ).listDetailsModel,
              _AutopaySingleDetail(
                title: l10n.creditDashboardAutopayFeeLabel,
                value: fee,
                icon: CompanyIconPointer.information,
                isValueLabeled: isNoFeeLabelShown,
                textColor: !isNoFeeLabelShown
                    ? CompanyColorPointer.secondary13
                    : CompanyColorPointer.secondary1,
              ).listDetailsModel,
              _AutopaySingleDetail(
                title: l10n.creditAutoPayTotalDue,
                value: '${totalDue.toFormattedString()}*',
                textStyle: CompanyTextStylePointer.b2medium,
                textColor: CompanyColorPointer.primary3,
              ).listDetailsModel,
            ],
          ),
        ),
        if (isMinimumRepaymentLabelShown) ...[
          Space.fromSpacingVertical(Spacing.s2),
          CompanyRichText(
            CompanyRichTextModel(
              text: l10n
                  .creditAutoPayMinimumPaymentToAvoidFees(minimumPaymentAmount),
              highlightedTextModels: [
                HighlightedTextModel(minimumPaymentAmount),
              ],
              normalTextColor: CompanyColorPointer.secondary4,
              accentTextColor: CompanyColorPointer.secondary4,
              normalStyle: CompanyTextStylePointer.b4,
              accentStyle: CompanyTextStylePointer.b4medium,
            ),
          ),
        ],
      ],
    );
  }

  void _onValuePressed(int index, CreditDashboardCubit cubit) {
    final isEditAutopayTapped = index == 1;
    final isFeeTapped = index == 3;

    if (isFeeTapped) {
      return cubit.showAutoPayFeeDetailsBottomSheet();
    }

    if (isEditAutopayTapped) {
      return cubit.onEditAutoPay();
    }
  }
}

class _AutopaySingleDetail {
  final String title;
  final String value;
  final CompanyIconPointer? icon;
  final bool isValueLabeled;
  final CompanyTextStylePointer? textStyle;
  final CompanyColorPointer? textColor;

  const _AutopaySingleDetail({
    required this.title,
    required this.value,
    this.icon,
    this.isValueLabeled = false,
    this.textStyle,
    this.textColor,
  });

  ListDetailsModel get listDetailsModel {
    return ListDetailsModel(
      textLabelModel: ListDetailsTextLabelModel(
        text: title,
        textColor: CompanyColorPointer.secondary5,
      ),
      valueModel: _value,
    );
  }

  ListDetailsModel listDetailsModelWithValueTab(String tabLabel) {
    return ListDetailsModel(
      textLabelModel: ListDetailsTextLabelModel(
        text: title,
        textColor: CompanyColorPointer.secondary5,
      ),
      valueModel: ListDetailsValueModel.textTab(
        textModel: _valueText,
        tabLabel: tabLabel,
      ),
    );
  }

  ListDetailsValueModel get _value {
    if (icon != null) {
      return _valueWithIcon;
    } else {
      return ListDetailsValueModel.text(
        textModel: isValueLabeled ? _valueLabel : _valueText,
      );
    }
  }

  ListDetailsValueModel get _valueWithIcon {
    return ListDetailsValueModel.textIcon(
      textModel: isValueLabeled ? _valueLabel : _valueText,
      iconPointer: icon!,
      colorPointer: CompanyColorPointer.primary1,
    );
  }

  ListDetailValueTextModel get _valueLabel {
    return ListDetailValueTextModel.companyLabel(
      labelModel: CompanyLabelModel(
        text: value,
        textStyle: CompanyTextStylePointer.b5medium,
        color: CompanyColorPointer.secondary3,
        backgroundColor: CompanyColorPointer.surface13,
        icon: CompanyIconPointer.action.toGraphicAsset(),
      ),
    );
  }

  ListDetailValueTextModel get _valueText {
    return ListDetailValueTextModel.label(
      content: value,
      textStyle: textStyle ?? CompanyTextStylePointer.b3,
      textColor: textColor ?? CompanyColorPointer.secondary1,
    );
  }
}

class _SectionTitle extends StatelessWidget {
  final String title;

  const _SectionTitle({
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Label(
      model: LabelModel(
        text: title,
        textStyle: CompanyTextStylePointer.h4,
        color: CompanyColorPointer.primary3,
      ),
    );
  }
}
