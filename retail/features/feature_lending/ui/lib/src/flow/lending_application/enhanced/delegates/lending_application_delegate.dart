import 'dart:async';

import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:retail_api/provider/dashboard_navigation_provider.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_ui/feature_lending_ui.dart';
import 'package:wio_feature_lending_ui/src/common/utils/loan_partner_name_parser.dart';
import 'package:wio_feature_lending_ui/src/flow/lending_application/enhanced/flow/order_states/flow_order_state.dart';
import 'package:wio_feature_lending_ui/src/navigation/configs/bottom_sheets/exit_confirmation_bottom_sheet_navigation_config.dart';
import 'package:wio_feature_lending_ui/src/screens/submit_personal_loan_application/flow/submit_personal_loan_application_router.dart';
import 'package:wio_feature_status_view_api/feature_status_view.dart';

part 'auto_loan_application_delegate.dart';
part 'credit_card_application_delegate.dart';
part 'credit_card_application_from_onboarding_delegate.dart';
part 'credit_card_limit_increase_application_delegate.dart';
part 'lending_application_delegate_base.dart';
part 'personal_loan_application_delegate.dart';

abstract interface class LendingApplicationDelegate {
  /// Handles the completion of the credit application.
  ///
  /// Submits the credit agreement and handles aftremath actions.
  Future<void> handleApplicationCompletion(LendingApplication application);

  /// Submits the credit application.
  Future<LendingApplication> submitApplication();

  /// Handles the successful submission of the credit application.
  Future<void> handleSuccessfulApplicationSubmission();

  /// Returns [FlowOrderState] that determined by several different
  /// [LendingApplication] fields.
  ///
  /// That can be productType, creditDecisions and onboardungFlowControls.
  ///
  /// In several cases, the product type is taken from different sources.
  FlowOrderState determineFlowOrderState(
    LendingApplication application,
  );

  Future<void> runExitFlow();
}
