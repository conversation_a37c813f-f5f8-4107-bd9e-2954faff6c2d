// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_limit_page_navigation_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditLimitConfig {
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)
        $default, {
    required TResult Function(
            Money maxAmount, Money minAmount, String loanAccountId)
        reduce,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)?
        $default, {
    TResult? Function(Money maxAmount, Money minAmount, String loanAccountId)?
        reduce,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)?
        $default, {
    TResult Function(Money maxAmount, Money minAmount, String loanAccountId)?
        reduce,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CreditLimitConfig value) $default, {
    required TResult Function(_ReduceCreditLimitConfig value) reduce,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CreditLimitConfig value)? $default, {
    TResult? Function(_ReduceCreditLimitConfig value)? reduce,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CreditLimitConfig value)? $default, {
    TResult Function(_ReduceCreditLimitConfig value)? reduce,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditLimitConfigCopyWith<$Res> {
  factory $CreditLimitConfigCopyWith(
          CreditLimitConfig value, $Res Function(CreditLimitConfig) then) =
      _$CreditLimitConfigCopyWithImpl<$Res, CreditLimitConfig>;
}

/// @nodoc
class _$CreditLimitConfigCopyWithImpl<$Res, $Val extends CreditLimitConfig>
    implements $CreditLimitConfigCopyWith<$Res> {
  _$CreditLimitConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CreditLimitConfigImplCopyWith<$Res> {
  factory _$$CreditLimitConfigImplCopyWith(_$CreditLimitConfigImpl value,
          $Res Function(_$CreditLimitConfigImpl) then) =
      __$$CreditLimitConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {LendingApplication application,
      OnboardingStatus nextStatus,
      bool closeOnDone});

  $LendingApplicationCopyWith<$Res> get application;
}

/// @nodoc
class __$$CreditLimitConfigImplCopyWithImpl<$Res>
    extends _$CreditLimitConfigCopyWithImpl<$Res, _$CreditLimitConfigImpl>
    implements _$$CreditLimitConfigImplCopyWith<$Res> {
  __$$CreditLimitConfigImplCopyWithImpl(_$CreditLimitConfigImpl _value,
      $Res Function(_$CreditLimitConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? application = null,
    Object? nextStatus = null,
    Object? closeOnDone = null,
  }) {
    return _then(_$CreditLimitConfigImpl(
      application: null == application
          ? _value.application
          : application // ignore: cast_nullable_to_non_nullable
              as LendingApplication,
      nextStatus: null == nextStatus
          ? _value.nextStatus
          : nextStatus // ignore: cast_nullable_to_non_nullable
              as OnboardingStatus,
      closeOnDone: null == closeOnDone
          ? _value.closeOnDone
          : closeOnDone // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LendingApplicationCopyWith<$Res> get application {
    return $LendingApplicationCopyWith<$Res>(_value.application, (value) {
      return _then(_value.copyWith(application: value));
    });
  }
}

/// @nodoc

class _$CreditLimitConfigImpl extends _CreditLimitConfig {
  const _$CreditLimitConfigImpl(
      {required this.application,
      required this.nextStatus,
      this.closeOnDone = false})
      : super._();

  @override
  final LendingApplication application;
  @override
  final OnboardingStatus nextStatus;

  /// Whether to pop the page after submitting a limit.
  @override
  @JsonKey()
  final bool closeOnDone;

  @override
  String toString() {
    return 'CreditLimitConfig(application: $application, nextStatus: $nextStatus, closeOnDone: $closeOnDone)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditLimitConfigImpl &&
            (identical(other.application, application) ||
                other.application == application) &&
            (identical(other.nextStatus, nextStatus) ||
                other.nextStatus == nextStatus) &&
            (identical(other.closeOnDone, closeOnDone) ||
                other.closeOnDone == closeOnDone));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, application, nextStatus, closeOnDone);

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditLimitConfigImplCopyWith<_$CreditLimitConfigImpl> get copyWith =>
      __$$CreditLimitConfigImplCopyWithImpl<_$CreditLimitConfigImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)
        $default, {
    required TResult Function(
            Money maxAmount, Money minAmount, String loanAccountId)
        reduce,
  }) {
    return $default(application, nextStatus, closeOnDone);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)?
        $default, {
    TResult? Function(Money maxAmount, Money minAmount, String loanAccountId)?
        reduce,
  }) {
    return $default?.call(application, nextStatus, closeOnDone);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)?
        $default, {
    TResult Function(Money maxAmount, Money minAmount, String loanAccountId)?
        reduce,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(application, nextStatus, closeOnDone);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CreditLimitConfig value) $default, {
    required TResult Function(_ReduceCreditLimitConfig value) reduce,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CreditLimitConfig value)? $default, {
    TResult? Function(_ReduceCreditLimitConfig value)? reduce,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CreditLimitConfig value)? $default, {
    TResult Function(_ReduceCreditLimitConfig value)? reduce,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _CreditLimitConfig extends CreditLimitConfig {
  const factory _CreditLimitConfig(
      {required final LendingApplication application,
      required final OnboardingStatus nextStatus,
      final bool closeOnDone}) = _$CreditLimitConfigImpl;
  const _CreditLimitConfig._() : super._();

  LendingApplication get application;
  OnboardingStatus get nextStatus;

  /// Whether to pop the page after submitting a limit.
  bool get closeOnDone;

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditLimitConfigImplCopyWith<_$CreditLimitConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReduceCreditLimitConfigImplCopyWith<$Res> {
  factory _$$ReduceCreditLimitConfigImplCopyWith(
          _$ReduceCreditLimitConfigImpl value,
          $Res Function(_$ReduceCreditLimitConfigImpl) then) =
      __$$ReduceCreditLimitConfigImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Money maxAmount, Money minAmount, String loanAccountId});
}

/// @nodoc
class __$$ReduceCreditLimitConfigImplCopyWithImpl<$Res>
    extends _$CreditLimitConfigCopyWithImpl<$Res, _$ReduceCreditLimitConfigImpl>
    implements _$$ReduceCreditLimitConfigImplCopyWith<$Res> {
  __$$ReduceCreditLimitConfigImplCopyWithImpl(
      _$ReduceCreditLimitConfigImpl _value,
      $Res Function(_$ReduceCreditLimitConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxAmount = null,
    Object? minAmount = null,
    Object? loanAccountId = null,
  }) {
    return _then(_$ReduceCreditLimitConfigImpl(
      maxAmount: null == maxAmount
          ? _value.maxAmount
          : maxAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      minAmount: null == minAmount
          ? _value.minAmount
          : minAmount // ignore: cast_nullable_to_non_nullable
              as Money,
      loanAccountId: null == loanAccountId
          ? _value.loanAccountId
          : loanAccountId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ReduceCreditLimitConfigImpl extends _ReduceCreditLimitConfig {
  const _$ReduceCreditLimitConfigImpl(
      {required this.maxAmount,
      required this.minAmount,
      required this.loanAccountId})
      : super._();

  @override
  final Money maxAmount;
  @override
  final Money minAmount;
  @override
  final String loanAccountId;

  @override
  String toString() {
    return 'CreditLimitConfig.reduce(maxAmount: $maxAmount, minAmount: $minAmount, loanAccountId: $loanAccountId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReduceCreditLimitConfigImpl &&
            (identical(other.maxAmount, maxAmount) ||
                other.maxAmount == maxAmount) &&
            (identical(other.minAmount, minAmount) ||
                other.minAmount == minAmount) &&
            (identical(other.loanAccountId, loanAccountId) ||
                other.loanAccountId == loanAccountId));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, maxAmount, minAmount, loanAccountId);

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReduceCreditLimitConfigImplCopyWith<_$ReduceCreditLimitConfigImpl>
      get copyWith => __$$ReduceCreditLimitConfigImplCopyWithImpl<
          _$ReduceCreditLimitConfigImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)
        $default, {
    required TResult Function(
            Money maxAmount, Money minAmount, String loanAccountId)
        reduce,
  }) {
    return reduce(maxAmount, minAmount, loanAccountId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)?
        $default, {
    TResult? Function(Money maxAmount, Money minAmount, String loanAccountId)?
        reduce,
  }) {
    return reduce?.call(maxAmount, minAmount, loanAccountId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(LendingApplication application,
            OnboardingStatus nextStatus, bool closeOnDone)?
        $default, {
    TResult Function(Money maxAmount, Money minAmount, String loanAccountId)?
        reduce,
    required TResult orElse(),
  }) {
    if (reduce != null) {
      return reduce(maxAmount, minAmount, loanAccountId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_CreditLimitConfig value) $default, {
    required TResult Function(_ReduceCreditLimitConfig value) reduce,
  }) {
    return reduce(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_CreditLimitConfig value)? $default, {
    TResult? Function(_ReduceCreditLimitConfig value)? reduce,
  }) {
    return reduce?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_CreditLimitConfig value)? $default, {
    TResult Function(_ReduceCreditLimitConfig value)? reduce,
    required TResult orElse(),
  }) {
    if (reduce != null) {
      return reduce(this);
    }
    return orElse();
  }
}

abstract class _ReduceCreditLimitConfig extends CreditLimitConfig {
  const factory _ReduceCreditLimitConfig(
      {required final Money maxAmount,
      required final Money minAmount,
      required final String loanAccountId}) = _$ReduceCreditLimitConfigImpl;
  const _ReduceCreditLimitConfig._() : super._();

  Money get maxAmount;
  Money get minAmount;
  String get loanAccountId;

  /// Create a copy of CreditLimitConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReduceCreditLimitConfigImplCopyWith<_$ReduceCreditLimitConfigImpl>
      get copyWith => throw _privateConstructorUsedError;
}
