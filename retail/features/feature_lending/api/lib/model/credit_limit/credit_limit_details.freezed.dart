// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_limit_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditLimitDetails {
  /// Holds the information about the credit limit
  LimitDetails get limitDetails => throw _privateConstructorUsedError;

  /// The request made for increasing limit.
  InProgressRequest? get inProgressRequest =>
      throw _privateConstructorUsedError;

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditLimitDetailsCopyWith<CreditLimitDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditLimitDetailsCopyWith<$Res> {
  factory $CreditLimitDetailsCopyWith(
          CreditLimitDetails value, $Res Function(CreditLimitDetails) then) =
      _$CreditLimitDetailsCopyWithImpl<$Res, CreditLimitDetails>;
  @useResult
  $Res call({LimitDetails limitDetails, InProgressRequest? inProgressRequest});

  $LimitDetailsCopyWith<$Res> get limitDetails;
  $InProgressRequestCopyWith<$Res>? get inProgressRequest;
}

/// @nodoc
class _$CreditLimitDetailsCopyWithImpl<$Res, $Val extends CreditLimitDetails>
    implements $CreditLimitDetailsCopyWith<$Res> {
  _$CreditLimitDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limitDetails = null,
    Object? inProgressRequest = freezed,
  }) {
    return _then(_value.copyWith(
      limitDetails: null == limitDetails
          ? _value.limitDetails
          : limitDetails // ignore: cast_nullable_to_non_nullable
              as LimitDetails,
      inProgressRequest: freezed == inProgressRequest
          ? _value.inProgressRequest
          : inProgressRequest // ignore: cast_nullable_to_non_nullable
              as InProgressRequest?,
    ) as $Val);
  }

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LimitDetailsCopyWith<$Res> get limitDetails {
    return $LimitDetailsCopyWith<$Res>(_value.limitDetails, (value) {
      return _then(_value.copyWith(limitDetails: value) as $Val);
    });
  }

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InProgressRequestCopyWith<$Res>? get inProgressRequest {
    if (_value.inProgressRequest == null) {
      return null;
    }

    return $InProgressRequestCopyWith<$Res>(_value.inProgressRequest!, (value) {
      return _then(_value.copyWith(inProgressRequest: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreditLimitDetailsImplCopyWith<$Res>
    implements $CreditLimitDetailsCopyWith<$Res> {
  factory _$$CreditLimitDetailsImplCopyWith(_$CreditLimitDetailsImpl value,
          $Res Function(_$CreditLimitDetailsImpl) then) =
      __$$CreditLimitDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({LimitDetails limitDetails, InProgressRequest? inProgressRequest});

  @override
  $LimitDetailsCopyWith<$Res> get limitDetails;
  @override
  $InProgressRequestCopyWith<$Res>? get inProgressRequest;
}

/// @nodoc
class __$$CreditLimitDetailsImplCopyWithImpl<$Res>
    extends _$CreditLimitDetailsCopyWithImpl<$Res, _$CreditLimitDetailsImpl>
    implements _$$CreditLimitDetailsImplCopyWith<$Res> {
  __$$CreditLimitDetailsImplCopyWithImpl(_$CreditLimitDetailsImpl _value,
      $Res Function(_$CreditLimitDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limitDetails = null,
    Object? inProgressRequest = freezed,
  }) {
    return _then(_$CreditLimitDetailsImpl(
      limitDetails: null == limitDetails
          ? _value.limitDetails
          : limitDetails // ignore: cast_nullable_to_non_nullable
              as LimitDetails,
      inProgressRequest: freezed == inProgressRequest
          ? _value.inProgressRequest
          : inProgressRequest // ignore: cast_nullable_to_non_nullable
              as InProgressRequest?,
    ));
  }
}

/// @nodoc

class _$CreditLimitDetailsImpl extends _CreditLimitDetails {
  const _$CreditLimitDetailsImpl(
      {required this.limitDetails, this.inProgressRequest})
      : super._();

  /// Holds the information about the credit limit
  @override
  final LimitDetails limitDetails;

  /// The request made for increasing limit.
  @override
  final InProgressRequest? inProgressRequest;

  @override
  String toString() {
    return 'CreditLimitDetails(limitDetails: $limitDetails, inProgressRequest: $inProgressRequest)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditLimitDetailsImpl &&
            (identical(other.limitDetails, limitDetails) ||
                other.limitDetails == limitDetails) &&
            (identical(other.inProgressRequest, inProgressRequest) ||
                other.inProgressRequest == inProgressRequest));
  }

  @override
  int get hashCode => Object.hash(runtimeType, limitDetails, inProgressRequest);

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditLimitDetailsImplCopyWith<_$CreditLimitDetailsImpl> get copyWith =>
      __$$CreditLimitDetailsImplCopyWithImpl<_$CreditLimitDetailsImpl>(
          this, _$identity);
}

abstract class _CreditLimitDetails extends CreditLimitDetails {
  const factory _CreditLimitDetails(
      {required final LimitDetails limitDetails,
      final InProgressRequest? inProgressRequest}) = _$CreditLimitDetailsImpl;
  const _CreditLimitDetails._() : super._();

  /// Holds the information about the credit limit
  @override
  LimitDetails get limitDetails;

  /// The request made for increasing limit.
  @override
  InProgressRequest? get inProgressRequest;

  /// Create a copy of CreditLimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditLimitDetailsImplCopyWith<_$CreditLimitDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LimitDetails {
  /// The minimum credit limit that can be selected
  Money get minLimit => throw _privateConstructorUsedError;

  /// The maximum credit limit approved for the user
  Money get maxLimit => throw _privateConstructorUsedError;

  /// Parameters for increasing credit limit
  LimitParameters get increaseLimitParameters =>
      throw _privateConstructorUsedError;

  /// Parameters for reducing credit limit
  LimitParameters get reduceLimitParameters =>
      throw _privateConstructorUsedError;

  /// Indicates whether the user can request to increase the limit.
  bool get canRequestToIncreaseLimit => throw _privateConstructorUsedError;

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LimitDetailsCopyWith<LimitDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LimitDetailsCopyWith<$Res> {
  factory $LimitDetailsCopyWith(
          LimitDetails value, $Res Function(LimitDetails) then) =
      _$LimitDetailsCopyWithImpl<$Res, LimitDetails>;
  @useResult
  $Res call(
      {Money minLimit,
      Money maxLimit,
      LimitParameters increaseLimitParameters,
      LimitParameters reduceLimitParameters,
      bool canRequestToIncreaseLimit});

  $LimitParametersCopyWith<$Res> get increaseLimitParameters;
  $LimitParametersCopyWith<$Res> get reduceLimitParameters;
}

/// @nodoc
class _$LimitDetailsCopyWithImpl<$Res, $Val extends LimitDetails>
    implements $LimitDetailsCopyWith<$Res> {
  _$LimitDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minLimit = null,
    Object? maxLimit = null,
    Object? increaseLimitParameters = null,
    Object? reduceLimitParameters = null,
    Object? canRequestToIncreaseLimit = null,
  }) {
    return _then(_value.copyWith(
      minLimit: null == minLimit
          ? _value.minLimit
          : minLimit // ignore: cast_nullable_to_non_nullable
              as Money,
      maxLimit: null == maxLimit
          ? _value.maxLimit
          : maxLimit // ignore: cast_nullable_to_non_nullable
              as Money,
      increaseLimitParameters: null == increaseLimitParameters
          ? _value.increaseLimitParameters
          : increaseLimitParameters // ignore: cast_nullable_to_non_nullable
              as LimitParameters,
      reduceLimitParameters: null == reduceLimitParameters
          ? _value.reduceLimitParameters
          : reduceLimitParameters // ignore: cast_nullable_to_non_nullable
              as LimitParameters,
      canRequestToIncreaseLimit: null == canRequestToIncreaseLimit
          ? _value.canRequestToIncreaseLimit
          : canRequestToIncreaseLimit // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LimitParametersCopyWith<$Res> get increaseLimitParameters {
    return $LimitParametersCopyWith<$Res>(_value.increaseLimitParameters,
        (value) {
      return _then(_value.copyWith(increaseLimitParameters: value) as $Val);
    });
  }

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LimitParametersCopyWith<$Res> get reduceLimitParameters {
    return $LimitParametersCopyWith<$Res>(_value.reduceLimitParameters,
        (value) {
      return _then(_value.copyWith(reduceLimitParameters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LimitDetailsImplCopyWith<$Res>
    implements $LimitDetailsCopyWith<$Res> {
  factory _$$LimitDetailsImplCopyWith(
          _$LimitDetailsImpl value, $Res Function(_$LimitDetailsImpl) then) =
      __$$LimitDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Money minLimit,
      Money maxLimit,
      LimitParameters increaseLimitParameters,
      LimitParameters reduceLimitParameters,
      bool canRequestToIncreaseLimit});

  @override
  $LimitParametersCopyWith<$Res> get increaseLimitParameters;
  @override
  $LimitParametersCopyWith<$Res> get reduceLimitParameters;
}

/// @nodoc
class __$$LimitDetailsImplCopyWithImpl<$Res>
    extends _$LimitDetailsCopyWithImpl<$Res, _$LimitDetailsImpl>
    implements _$$LimitDetailsImplCopyWith<$Res> {
  __$$LimitDetailsImplCopyWithImpl(
      _$LimitDetailsImpl _value, $Res Function(_$LimitDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minLimit = null,
    Object? maxLimit = null,
    Object? increaseLimitParameters = null,
    Object? reduceLimitParameters = null,
    Object? canRequestToIncreaseLimit = null,
  }) {
    return _then(_$LimitDetailsImpl(
      minLimit: null == minLimit
          ? _value.minLimit
          : minLimit // ignore: cast_nullable_to_non_nullable
              as Money,
      maxLimit: null == maxLimit
          ? _value.maxLimit
          : maxLimit // ignore: cast_nullable_to_non_nullable
              as Money,
      increaseLimitParameters: null == increaseLimitParameters
          ? _value.increaseLimitParameters
          : increaseLimitParameters // ignore: cast_nullable_to_non_nullable
              as LimitParameters,
      reduceLimitParameters: null == reduceLimitParameters
          ? _value.reduceLimitParameters
          : reduceLimitParameters // ignore: cast_nullable_to_non_nullable
              as LimitParameters,
      canRequestToIncreaseLimit: null == canRequestToIncreaseLimit
          ? _value.canRequestToIncreaseLimit
          : canRequestToIncreaseLimit // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$LimitDetailsImpl implements _LimitDetails {
  const _$LimitDetailsImpl(
      {required this.minLimit,
      required this.maxLimit,
      required this.increaseLimitParameters,
      required this.reduceLimitParameters,
      this.canRequestToIncreaseLimit = false});

  /// The minimum credit limit that can be selected
  @override
  final Money minLimit;

  /// The maximum credit limit approved for the user
  @override
  final Money maxLimit;

  /// Parameters for increasing credit limit
  @override
  final LimitParameters increaseLimitParameters;

  /// Parameters for reducing credit limit
  @override
  final LimitParameters reduceLimitParameters;

  /// Indicates whether the user can request to increase the limit.
  @override
  @JsonKey()
  final bool canRequestToIncreaseLimit;

  @override
  String toString() {
    return 'LimitDetails(minLimit: $minLimit, maxLimit: $maxLimit, increaseLimitParameters: $increaseLimitParameters, reduceLimitParameters: $reduceLimitParameters, canRequestToIncreaseLimit: $canRequestToIncreaseLimit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LimitDetailsImpl &&
            (identical(other.minLimit, minLimit) ||
                other.minLimit == minLimit) &&
            (identical(other.maxLimit, maxLimit) ||
                other.maxLimit == maxLimit) &&
            (identical(
                    other.increaseLimitParameters, increaseLimitParameters) ||
                other.increaseLimitParameters == increaseLimitParameters) &&
            (identical(other.reduceLimitParameters, reduceLimitParameters) ||
                other.reduceLimitParameters == reduceLimitParameters) &&
            (identical(other.canRequestToIncreaseLimit,
                    canRequestToIncreaseLimit) ||
                other.canRequestToIncreaseLimit == canRequestToIncreaseLimit));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      minLimit,
      maxLimit,
      increaseLimitParameters,
      reduceLimitParameters,
      canRequestToIncreaseLimit);

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LimitDetailsImplCopyWith<_$LimitDetailsImpl> get copyWith =>
      __$$LimitDetailsImplCopyWithImpl<_$LimitDetailsImpl>(this, _$identity);
}

abstract class _LimitDetails implements LimitDetails {
  const factory _LimitDetails(
      {required final Money minLimit,
      required final Money maxLimit,
      required final LimitParameters increaseLimitParameters,
      required final LimitParameters reduceLimitParameters,
      final bool canRequestToIncreaseLimit}) = _$LimitDetailsImpl;

  /// The minimum credit limit that can be selected
  @override
  Money get minLimit;

  /// The maximum credit limit approved for the user
  @override
  Money get maxLimit;

  /// Parameters for increasing credit limit
  @override
  LimitParameters get increaseLimitParameters;

  /// Parameters for reducing credit limit
  @override
  LimitParameters get reduceLimitParameters;

  /// Indicates whether the user can request to increase the limit.
  @override
  bool get canRequestToIncreaseLimit;

  /// Create a copy of LimitDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LimitDetailsImplCopyWith<_$LimitDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$LimitParameters {
  /// Indicates whether the user is able to change credit limit
  bool get isAllowed => throw _privateConstructorUsedError;

  /// The date at which the limit change option will expire.
  DateTime? get expiryDate => throw _privateConstructorUsedError;

  /// Create a copy of LimitParameters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LimitParametersCopyWith<LimitParameters> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LimitParametersCopyWith<$Res> {
  factory $LimitParametersCopyWith(
          LimitParameters value, $Res Function(LimitParameters) then) =
      _$LimitParametersCopyWithImpl<$Res, LimitParameters>;
  @useResult
  $Res call({bool isAllowed, DateTime? expiryDate});
}

/// @nodoc
class _$LimitParametersCopyWithImpl<$Res, $Val extends LimitParameters>
    implements $LimitParametersCopyWith<$Res> {
  _$LimitParametersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LimitParameters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAllowed = null,
    Object? expiryDate = freezed,
  }) {
    return _then(_value.copyWith(
      isAllowed: null == isAllowed
          ? _value.isAllowed
          : isAllowed // ignore: cast_nullable_to_non_nullable
              as bool,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LimitParametersImplCopyWith<$Res>
    implements $LimitParametersCopyWith<$Res> {
  factory _$$LimitParametersImplCopyWith(_$LimitParametersImpl value,
          $Res Function(_$LimitParametersImpl) then) =
      __$$LimitParametersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isAllowed, DateTime? expiryDate});
}

/// @nodoc
class __$$LimitParametersImplCopyWithImpl<$Res>
    extends _$LimitParametersCopyWithImpl<$Res, _$LimitParametersImpl>
    implements _$$LimitParametersImplCopyWith<$Res> {
  __$$LimitParametersImplCopyWithImpl(
      _$LimitParametersImpl _value, $Res Function(_$LimitParametersImpl) _then)
      : super(_value, _then);

  /// Create a copy of LimitParameters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAllowed = null,
    Object? expiryDate = freezed,
  }) {
    return _then(_$LimitParametersImpl(
      isAllowed: null == isAllowed
          ? _value.isAllowed
          : isAllowed // ignore: cast_nullable_to_non_nullable
              as bool,
      expiryDate: freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$LimitParametersImpl implements _LimitParameters {
  const _$LimitParametersImpl({required this.isAllowed, this.expiryDate});

  /// Indicates whether the user is able to change credit limit
  @override
  final bool isAllowed;

  /// The date at which the limit change option will expire.
  @override
  final DateTime? expiryDate;

  @override
  String toString() {
    return 'LimitParameters(isAllowed: $isAllowed, expiryDate: $expiryDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LimitParametersImpl &&
            (identical(other.isAllowed, isAllowed) ||
                other.isAllowed == isAllowed) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isAllowed, expiryDate);

  /// Create a copy of LimitParameters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LimitParametersImplCopyWith<_$LimitParametersImpl> get copyWith =>
      __$$LimitParametersImplCopyWithImpl<_$LimitParametersImpl>(
          this, _$identity);
}

abstract class _LimitParameters implements LimitParameters {
  const factory _LimitParameters(
      {required final bool isAllowed,
      final DateTime? expiryDate}) = _$LimitParametersImpl;

  /// Indicates whether the user is able to change credit limit
  @override
  bool get isAllowed;

  /// The date at which the limit change option will expire.
  @override
  DateTime? get expiryDate;

  /// Create a copy of LimitParameters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LimitParametersImplCopyWith<_$LimitParametersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$InProgressRequest {
  /// Identifier for the request
  String get id => throw _privateConstructorUsedError;

  /// The amount of the request
  Money get amount => throw _privateConstructorUsedError;

  /// The date at which the request was made.
  DateTime get requestDate => throw _privateConstructorUsedError;

  /// Create a copy of InProgressRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InProgressRequestCopyWith<InProgressRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InProgressRequestCopyWith<$Res> {
  factory $InProgressRequestCopyWith(
          InProgressRequest value, $Res Function(InProgressRequest) then) =
      _$InProgressRequestCopyWithImpl<$Res, InProgressRequest>;
  @useResult
  $Res call({String id, Money amount, DateTime requestDate});
}

/// @nodoc
class _$InProgressRequestCopyWithImpl<$Res, $Val extends InProgressRequest>
    implements $InProgressRequestCopyWith<$Res> {
  _$InProgressRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InProgressRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? amount = null,
    Object? requestDate = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
      requestDate: null == requestDate
          ? _value.requestDate
          : requestDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InProgressRequestImplCopyWith<$Res>
    implements $InProgressRequestCopyWith<$Res> {
  factory _$$InProgressRequestImplCopyWith(_$InProgressRequestImpl value,
          $Res Function(_$InProgressRequestImpl) then) =
      __$$InProgressRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, Money amount, DateTime requestDate});
}

/// @nodoc
class __$$InProgressRequestImplCopyWithImpl<$Res>
    extends _$InProgressRequestCopyWithImpl<$Res, _$InProgressRequestImpl>
    implements _$$InProgressRequestImplCopyWith<$Res> {
  __$$InProgressRequestImplCopyWithImpl(_$InProgressRequestImpl _value,
      $Res Function(_$InProgressRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of InProgressRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? amount = null,
    Object? requestDate = null,
  }) {
    return _then(_$InProgressRequestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as Money,
      requestDate: null == requestDate
          ? _value.requestDate
          : requestDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$InProgressRequestImpl implements _InProgressRequest {
  const _$InProgressRequestImpl(
      {required this.id, required this.amount, required this.requestDate});

  /// Identifier for the request
  @override
  final String id;

  /// The amount of the request
  @override
  final Money amount;

  /// The date at which the request was made.
  @override
  final DateTime requestDate;

  @override
  String toString() {
    return 'InProgressRequest(id: $id, amount: $amount, requestDate: $requestDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InProgressRequestImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.requestDate, requestDate) ||
                other.requestDate == requestDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, amount, requestDate);

  /// Create a copy of InProgressRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InProgressRequestImplCopyWith<_$InProgressRequestImpl> get copyWith =>
      __$$InProgressRequestImplCopyWithImpl<_$InProgressRequestImpl>(
          this, _$identity);
}

abstract class _InProgressRequest implements InProgressRequest {
  const factory _InProgressRequest(
      {required final String id,
      required final Money amount,
      required final DateTime requestDate}) = _$InProgressRequestImpl;

  /// Identifier for the request
  @override
  String get id;

  /// The amount of the request
  @override
  Money get amount;

  /// The date at which the request was made.
  @override
  DateTime get requestDate;

  /// Create a copy of InProgressRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InProgressRequestImplCopyWith<_$InProgressRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
