// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'lending_file.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LendingFile {
  /// The blob of the agreement file.
  Uint8List get data => throw _privateConstructorUsedError;

  /// The format of the file.
  LendingFileFormat get format => throw _privateConstructorUsedError;

  /// Create a copy of LendingFile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LendingFileCopyWith<LendingFile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LendingFileCopyWith<$Res> {
  factory $LendingFileCopyWith(
          LendingFile value, $Res Function(LendingFile) then) =
      _$LendingFileCopyWithImpl<$Res, LendingFile>;
  @useResult
  $Res call({Uint8List data, LendingFileFormat format});
}

/// @nodoc
class _$LendingFileCopyWithImpl<$Res, $Val extends LendingFile>
    implements $LendingFileCopyWith<$Res> {
  _$LendingFileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LendingFile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? format = null,
  }) {
    return _then(_value.copyWith(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Uint8List,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as LendingFileFormat,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LendingFileImplCopyWith<$Res>
    implements $LendingFileCopyWith<$Res> {
  factory _$$LendingFileImplCopyWith(
          _$LendingFileImpl value, $Res Function(_$LendingFileImpl) then) =
      __$$LendingFileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Uint8List data, LendingFileFormat format});
}

/// @nodoc
class __$$LendingFileImplCopyWithImpl<$Res>
    extends _$LendingFileCopyWithImpl<$Res, _$LendingFileImpl>
    implements _$$LendingFileImplCopyWith<$Res> {
  __$$LendingFileImplCopyWithImpl(
      _$LendingFileImpl _value, $Res Function(_$LendingFileImpl) _then)
      : super(_value, _then);

  /// Create a copy of LendingFile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
    Object? format = null,
  }) {
    return _then(_$LendingFileImpl(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Uint8List,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as LendingFileFormat,
    ));
  }
}

/// @nodoc

class _$LendingFileImpl implements _LendingFile {
  const _$LendingFileImpl(
      {required this.data, this.format = LendingFileFormat.pdf});

  /// The blob of the agreement file.
  @override
  final Uint8List data;

  /// The format of the file.
  @override
  @JsonKey()
  final LendingFileFormat format;

  @override
  String toString() {
    return 'LendingFile(data: $data, format: $format)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LendingFileImpl &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.format, format) || other.format == format));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(data), format);

  /// Create a copy of LendingFile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LendingFileImplCopyWith<_$LendingFileImpl> get copyWith =>
      __$$LendingFileImplCopyWithImpl<_$LendingFileImpl>(this, _$identity);
}

abstract class _LendingFile implements LendingFile {
  const factory _LendingFile(
      {required final Uint8List data,
      final LendingFileFormat format}) = _$LendingFileImpl;

  /// The blob of the agreement file.
  @override
  Uint8List get data;

  /// The format of the file.
  @override
  LendingFileFormat get format;

  /// Create a copy of LendingFile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LendingFileImplCopyWith<_$LendingFileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
