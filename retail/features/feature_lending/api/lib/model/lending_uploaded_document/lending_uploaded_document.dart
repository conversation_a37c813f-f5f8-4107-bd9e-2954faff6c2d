import 'package:freezed_annotation/freezed_annotation.dart';

enum LendingUploadedDocumentType {
  salaryStatement,
}

@immutable

/// Represents a lending document like salary certificate/statement
class LendingUploadedDocument {
  final String id;
  final String fileName;
  final LendingUploadedDocumentType type;
  final DateTime uploadedAt;
  final String contentType;

  /// Default constructor for the lending document
  const LendingUploadedDocument({
    required this.id,
    required this.fileName,
    required this.type,
    required this.uploadedAt,
    required this.contentType,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is LendingUploadedDocument &&
        other.id == id &&
        other.fileName == fileName &&
        other.uploadedAt == uploadedAt &&
        other.contentType == contentType &&
        other.type == type;
  }

  @override
  int get hashCode => Object.hash(
        runtimeType,
        id,
        fileName,
        uploadedAt,
        contentType,
        type,
      );
}
