// ignore_for_file: avoid_implementing_value_types

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

part 'credit_dashboard_navigation_config.freezed.dart';

@freezed
class CreditDashboardConfig with _$CreditDashboardConfig {
  const factory CreditDashboardConfig({
    required String accountId,
    required LendingDashboardNavigationEntryPoint entryPoint,
    @Default(false) bool isEasyCash,
  }) = _CreditDashboardConfig;
}

@immutable
class CreditDashboardScreenNavigationConfig extends ScreenNavigationConfig {
  static const productIdentifer = LoanProductIdentifier.retailCreditCard;
  static final screenName = '${productIdentifer.productName}_dashboard';

  final CreditDashboardConfig config;

  CreditDashboardScreenNavigationConfig(
    this.config,
  ) : super(
          id: screenName,
          feature: LendingFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'CreditDashboardScreenNavigationConfig';

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        other is CreditDashboardScreenNavigationConfig &&
            runtimeType == other.runtimeType &&
            config == other.config;
  }

  @override
  int get hashCode => config.hashCode ^ runtimeType.hashCode;
}
