import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';

class ResumeApplicationNavigationConfig extends ScreenNavigationConfig {
  static const screenName = 'resume_application_screen';

  final LendingApplication application;
  final ProductType productType;

  const ResumeApplicationNavigationConfig({
    required this.application,
    required this.productType,
  }) : super(id: screenName, feature: LendingFeatureNavigationConfig.name);

  @override
  String toString() => 'ResumeApplicationNavigationConfig';
}
