import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:uuid/uuid.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_impl/src/data/mappers/lending_mapper.dart';
import 'package:wio_feature_lending_impl/src/data/mappers/loan_mapper.dart';
import 'package:wio_feature_lending_impl/src/data/repositories/resolvers/lending_error_mapping_handler.dart';
import 'package:wio_feature_lending_impl/src/data/services/personal_loan_service.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

class PersonalLoanRepositoryImpl implements LoanRepository {
  static const _uuid = Uuid();

  @visibleForTesting
  String idempotencyKey = _uuid.v4();
  final PersonalLoanService _service;
  final LendingMapper _mapper;
  final LendingErrorMappingHandler _errorHandler;
  final LoanMapper _loanMapper;

  PersonalLoanRepositoryImpl({
    required PersonalLoanService service,
    required LendingMapper mapper,
    required LendingErrorMappingHandler errorHandler,
    required LoanMapper loanMapper,
  })  : _service = service,
        _mapper = mapper,
        _errorHandler = errorHandler,
        _loanMapper = loanMapper;

  @override
  Future<Schedule> evaluateRepayment({
    required LoanRepaymentEvaluationRequest request,
  }) async {
    final scheduleDto = await _errorHandler.handle(
      _service.evaluateInstallments(
        _mapper.mapToEvalutateRepaymentInstallmentsDto(
          request: request,
          productType: ProductType.personalLoan,
        ),
      ),
    );

    return _mapper.mapToSchedule(scheduleDto);
  }

  @override
  Future<void> makeRepayment({
    required String loanAccountId,
    required String depositAccountId,
    required Money amount,
    bool isRetry = false,
  }) {
    if (!isRetry) {
      idempotencyKey = _uuid.v4();
    }
    final requestDto = _mapper.mapToLoanRepaymentCommand(
      depositAccountId,
      loanAccountId,
      amount,
    );

    return _service.repayCreditAccount(
      dto: requestDto,
      idempotencyKey: idempotencyKey,
    );
  }

  @override
  Future<Schedule> evaluateDisbursement({
    required LoanDisbursementEvaluationRequest request,
  }) async {
    final scheduleDto = await _errorHandler.handle(
      _service.evaluateInstallments(
        _mapper.mapToEvalutateDisbursementInstallmentsDto(request: request),
      ),
    );

    return _mapper.mapToSchedule(scheduleDto);
  }

  @override
  Future<FileData> getAmortizationSchedule({required String accountId}) async {
    final dto = await _errorHandler
        .handle(_service.getAmortizationSchedule(accountId: accountId));

    if (dto.pdfString?.isEmpty ?? false) {
      throw NoFileDataRecievedException();
    }

    return _loanMapper.mapAmortizationSchedule(dto);
  }

  @override
  Future<Money> checkCreditLimit({
    required LoanProductIdentifier productIdentifier,
    required Money amount,
  }) {
    throw UnimplementedError('Implement after retail is ready');
  }

  @override
  Future<ProductCodeStaticDetails> getProductInfo({
    required LoanProductIdentifier productIdentifier,
  }) {
    throw UnimplementedError('Implement after retail is ready');
  }

  @override
  Future<Schedule> evaluateCalculator({
    required LoanCalculatorEvaluationRequest request,
  }) async {
    final scheduleDto = await _errorHandler.handle(
      _service.evaluateInstallments(
        _mapper.mapToEvalutateLoanCalculatorDto(request: request),
      ),
    );

    return _mapper.mapToSchedule(scheduleDto);
  }
}
