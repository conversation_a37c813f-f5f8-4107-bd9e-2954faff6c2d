import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';

/// Represents a cache for fetched lending applications.
abstract class LendingApplicationStorage implements Clearable {
  Future<Applications?> getApplications();

  Future<void> saveApplications({required Applications applications});

  void updateApplicationInCache(LendingApplication application);
}

class InMemoryApplicationStorageImpl implements LendingApplicationStorage {
  Applications? _cachedApplications;

  @override
  Future<void> clear() async => _cachedApplications = null;

  @override
  Future<Applications?> getApplications() => Future.value(_cachedApplications);

  @override
  Future<void> saveApplications({
    required Applications applications,
  }) async =>
      _cachedApplications = applications;

  @override
  void updateApplicationInCache(LendingApplication application) {
    final applications = _cachedApplications;

    if (applications != null) {
      final oldApplications =
          applications.items.where((a) => a.id != application.id).toList();

      final items = [...oldApplications, application];

      _cachedApplications = applications.copyWith(
        items: items,
        total: items.length,
      );
    }
  }
}
