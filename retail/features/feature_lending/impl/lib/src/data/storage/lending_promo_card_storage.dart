import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_lending_impl/src/data/storage/dismissable_state_storage_mixin.dart';

abstract class LendingPromoCardStorage {
  Future<bool> isLendingPromoCardDismissed();

  Future<void> dismissLendingPromoCard();
}

class LendingPromoCardStorageImpl
    with DismissableStateStorageMixin
    implements LendingPromoCardStorage {
  static const _lendingPromoCardDismissedKey = 'lending_promo_card_dismissed';
  final KeyValueStorage _storage;

  const LendingPromoCardStorageImpl({
    required KeyValueStorage storage,
  }) : _storage = storage;

  @override
  KeyValueStorage get dismissableStorage => _storage;

  @override
  Future<bool> isLendingPromoCardDismissed() =>
      isDismissed(_lendingPromoCardDismissedKey);

  @override
  Future<void> dismissLendingPromoCard() =>
      dismiss(_lendingPromoCardDismissedKey);
}
