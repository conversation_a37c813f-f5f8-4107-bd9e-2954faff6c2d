import 'package:common_feature_toggle_api/common_feature_toggle_api.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/money.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_impl/src/domain/loan_account_interactor.dart';
import 'package:wio_feature_loan_api/loan_api.dart';

import '../mocks.dart';

class MockLoanAccountRepository extends Mock implements LoanAccountRepository {}

void main() {
  late LoanAccountInteractor interactor;
  late MockLoanAccountRepository repository;
  late LoanRepository loanRepository;
  late FeatureToggleProvider featureToggleProvider;
  late SplitTransactionsRepository splitTransactionsRepository;

  setUp(() {
    repository = MockLoanAccountRepository();
    loanRepository = MockPersonalLoanRepository();
    featureToggleProvider = MockFeatureToggleProvider();
    splitTransactionsRepository = MockSplitTransactionsRepository();

    interactor = LoanAccountInteractorImpl(
      repository: repository,
      loanRepository: loanRepository,
      featureToggleProvider: featureToggleProvider,
      splitTransactionsRepository: splitTransactionsRepository,
    );

    when(
      () => featureToggleProvider.get(
        LendingFeatureToggles.isNewCreditCardPaybackEndpointEnabled,
      ),
    ).thenReturn(false);
  });

  group('LoanAccountInteractorImpl', () {
    test('getLoanAccount should call repository.getLoanAccount', () async {
      // Arrange
      const id = 'loanAccountId';
      final expectedLoanAccount = MockValues.loanAccount;
      when(() => repository.getLoanAccount(id, calculateMinLimit: false))
          .justAnswerAsync(expectedLoanAccount);

      // Act
      final result = await interactor.getLoanAccount(id);

      // Assert
      expect(result, equals(expectedLoanAccount));
      verify(() => repository.getLoanAccount(id, calculateMinLimit: false))
          .calledOnce;
    });

    test('getLoanAccount with minLimit', () async {
      // Arrange
      const id = 'loanAccountId';
      final expectedLoanAccount = MockValues.loanAccount
          .copyWith(minLimit: Money.fromNumWithCurrency(10, Currency.aed));
      when(() => repository.getLoanAccount(id, calculateMinLimit: true))
          .justAnswerAsync(expectedLoanAccount);

      // Act
      final result =
          await interactor.getLoanAccount(id, calculateMinLimit: true);

      // Assert
      expect(result, equals(expectedLoanAccount));
      verify(() => repository.getLoanAccount(id, calculateMinLimit: true))
          .calledOnce;
    });

    test('getLoanAccounts should call repository.getLoanAccounts', () async {
      // Arrange
      final expectedLoanAccounts = [MockValues.loanAccount];
      when(() => repository.getLoanAccounts())
          .justAnswerAsync(expectedLoanAccounts);

      // Act
      final result = await interactor.getLoanAccounts();

      // Assert
      expect(result, equals(expectedLoanAccounts));
      verify(() => repository.getLoanAccounts()).calledOnce;
    });

    test('observeAccounts should call repository.observeAccounts', () {
      // Arrange
      final expectedData = Data<LoanDetails>(content: MockValues.loanDetails);
      when(() => repository.observeLoanDetails()).justAnswerAsync(expectedData);

      // Act
      final result = interactor.observeLoanDetails();

      // Assert
      expect(result, emits(expectedData));
      verify(() => repository.observeLoanDetails()).calledOnce;
    });

    test('refreshAccounts should call repository.refreshAccounts', () async {
      // Arrange
      const accountId = 'loanAccountId';
      when(() => repository.refreshAccounts(accountId: accountId))
          .justCompleteAsync();

      // Act
      await interactor.refreshAccounts(accountId: accountId);

      // Assert
      verify(() => repository.refreshAccounts(accountId: accountId)).calledOnce;
    });

    test(
      'updateMonthlyPaymentPercentage should call '
      'repository.updateMonthlyPaymentPercentage and refreshSummary',
      () async {
        // Arrange
        const repaymentPercentage = 50;
        final expectedLoanAccount = MockValues.loanAccount;
        when(
          () => repository.updateMonthlyPaymentPercentage(
            accountId: any(named: 'accountId'),
            repaymentPercentage: repaymentPercentage,
          ),
        ).justAnswerAsync(expectedLoanAccount);
        when(() => repository.refreshSummary(any())).justCompleteAsync();

        // Act
        final result = await interactor.updateMonthlyPaymentPercentage(
          accountId: 'accountId',
          repaymentPercentage: repaymentPercentage,
        );
        await flushFutures();

        // Assert
        expect(result, equals(expectedLoanAccount));
        verify(
          () => repository.updateMonthlyPaymentPercentage(
            accountId: any(named: 'accountId'),
            repaymentPercentage: repaymentPercentage,
          ),
        ).calledOnce;
        verify(() => repository.refreshSummary(any())).calledOnce;
      },
    );

    test('moveMoney should call repository.moveMoney', () async {
      // Arrange
      final amount = Money.fromNumWithCurrency(100, Currency.aed);
      const fromAccountId = 'fromAccountId';
      const toAccountId = 'toAccountId';
      when(
        () => repository.moveMoney(
          amount: amount,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
        ),
      ).justCompleteAsync();
      when(() => repository.refreshSummary(toAccountId)).justCompleteAsync();
      when(() => repository.refreshAccounts()).justCompleteAsync();
      when(() => splitTransactionsRepository.refreshInstallments())
          .justCompleteAsync();
      when(
        () => splitTransactionsRepository.clearCachedInstallmentsData(),
      ).justCompleteAsync();

      // Act
      await interactor.moveMoney(
        amount: amount,
        fromAccountId: fromAccountId,
        toAccountId: toAccountId,
      );
      await flushFutures();

      // Assert
      verify(
        () => repository.moveMoney(
          amount: amount,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
        ),
      ).calledOnce;
    });

    test(
      'moveMoney should call loanRepository.makeRepayment '
      'if FF isNewCreditCardPaybackEndpointEnabled enabled',
      () async {
        // Arrange
        final amount = Money.fromNumWithCurrency(100, Currency.aed);
        const fromAccountId = 'fromAccountId';
        const toAccountId = 'toAccountId';
        when(
          () => featureToggleProvider.get(
            LendingFeatureToggles.isNewCreditCardPaybackEndpointEnabled,
          ),
        ).thenReturn(true);
        when(
          () => splitTransactionsRepository.clearCachedInstallmentsData(),
        ).justCompleteAsync();
        when(
          () => repository.moveMoney(
            amount: amount,
            fromAccountId: fromAccountId,
            toAccountId: toAccountId,
          ),
        ).justCompleteAsync();
        when(
          () => loanRepository.makeRepayment(
            loanAccountId: toAccountId,
            depositAccountId: fromAccountId,
            amount: amount,
          ),
        ).justCompleteAsync();
        when(() => repository.refreshSummary(toAccountId)).justCompleteAsync();
        when(() => repository.refreshAccounts()).justCompleteAsync();
        when(() => splitTransactionsRepository.refreshInstallments())
            .justCompleteAsync();

        // Act
        await interactor.moveMoney(
          amount: amount,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
        );
        await flushFutures();

        // Assert
        verifyNever(
          () => repository.moveMoney(
            amount: amount,
            fromAccountId: fromAccountId,
            toAccountId: toAccountId,
          ),
        );
        verify(
          () => loanRepository.makeRepayment(
            loanAccountId: toAccountId,
            depositAccountId: fromAccountId,
            amount: amount,
          ),
        ).calledOnce;
      },
    );

    test(
      'getAutoPaymentInfo should call repository.getAutoPaymentInfo',
      () async {
        // Arrange
        const accountId = 'loanAccountId';
        final expectedAutoPayment = MockValues.autoPayment;
        when(() => repository.getAutoPaymentInfo(accountId))
            .justAnswerAsync(expectedAutoPayment);

        // Act
        final result = await interactor.getAutoPaymentInfo(accountId);

        // Assert
        expect(result, equals(expectedAutoPayment));
        verify(() => repository.getAutoPaymentInfo(accountId)).calledOnce;
      },
    );

    test(
      'observeLoanAccountSummary should call repository.observeAccountSummary',
      () {
        // Arrange
        const loanAccountId = 'loanAccountId';
        final expectedData = Data<LoanAccountSummary>(
          content: MockValues.loanAccountSummary,
        );
        when(() => repository.observeAccountSummary(loanAccountId))
            .thenAnswer((_) => Stream.value(expectedData));

        // Act
        final result = interactor.observeLoanAccountSummary(loanAccountId);

        // Assert
        expect(result, emits(expectedData));
        verify(() => repository.observeAccountSummary(loanAccountId))
            .calledOnce;
      },
    );

    test('refreshSummary should call repository.refreshSummary', () async {
      // Arrange
      const accountId = 'loanAccountId';
      when(() => repository.refreshSummary(accountId)).justCompleteAsync();

      // Act
      await interactor.refreshSummary(accountId);

      // Assert
      verify(() => repository.refreshSummary(accountId)).calledOnce;
    });

    test(
      'getLastAutopayment should call repository.getLastAutopayment',
      () async {
        // Arrange
        const accountId = 'loanAccountId';
        final expectedLastAutoPayment = MockValues.lastAutoPayment;
        when(() => repository.getLastAutopayment(accountId))
            .justAnswerAsync(expectedLastAutoPayment);

        // Act
        final result = await interactor.getLastAutopayment(accountId);

        // Assert
        expect(result, equals(expectedLastAutoPayment));
        verify(() => repository.getLastAutopayment(accountId)).calledOnce;
      },
    );

    test(
      'closeAccount should call repository.closeAccount and refreshAccounts',
      () async {
        // Arrange
        const accountId = 'loanAccountId';
        when(() => repository.closeAccount(accountId)).justCompleteAsync();
        when(() => repository.refreshAccounts()).justCompleteAsync();
        when(() => splitTransactionsRepository.refreshInstallments())
            .justCompleteAsync();

        // Act
        await interactor.closeAccount(accountId);

        // Assert
        verify(() => repository.closeAccount(accountId)).calledOnce;
        verify(() => repository.refreshAccounts()).calledOnce;
        verify(() => splitTransactionsRepository.refreshInstallments())
            .calledOnce;
      },
    );

    test('updateLoanAmount should call repository.updateLoanAmount', () async {
      // Arrange
      const accountId = 'loanAccountId';
      const amount = 1000;
      when(() => repository.updateLoanAmount(any(), any()))
          .justAnswerAsync(MockValues.loanAccount);

      // Act
      await interactor.updateLoanAmount(accountId: accountId, amount: amount);

      // Assert
      verify(() => repository.updateLoanAmount(any(), any())).calledOnce;
    });

    test('getCreditLimitDetails should call repository.getCreditLimitDetails',
        () async {
      // Arrange
      const accountId = 'loanAccountId';
      when(() => repository.getCreditLimitDetails(accountId: accountId))
          .justAnswerAsync(MockValues.creditLimitDetailsDomain);

      // Act
      await interactor.getCreditLimitDetails(accountId: accountId);

      // Assert
      verify(() => repository.getCreditLimitDetails(accountId: accountId))
          .calledOnce;
    });

    test('getLoanDetails should call repository.getLoanDetails', () async {
      // Arrange
      when(() => repository.getLoanDetails())
          .justAnswerAsync(MockValues.loanDetails);

      // Act
      await interactor.getLoanDetails();

      // Assert
      verify(() => repository.getLoanDetails()).calledOnce;
    });
  });
}
