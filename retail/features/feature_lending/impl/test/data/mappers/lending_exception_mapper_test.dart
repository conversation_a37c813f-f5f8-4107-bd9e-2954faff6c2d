// ignore_for_file: inference_failure_on_instance_creation

import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_impl/src/data/mappers/lending_exception_mapper.dart';
import 'package:wio_feature_lending_impl/src/data/models/lending.enums.swagger.dart';

import '../../mocks.dart';

void main() {
  late LendingExceptionMapperImpl lendingExceptionMapper;
  late MockRestApiException mockRestApiException;

  setUp(() {
    lendingExceptionMapper = const LendingExceptionMapperImpl();
    mockRestApiException = MockRestApiException();
  });

  group('LendingExceptionMapperImpl >', () {
    group('mapLocalException >', () {
      test(
        'should return LendingException with unknownError code '
        'if exception is not RestApiException',
        () {
          // Arrange
          final exception = Exception();

          // Act
          final result = lendingExceptionMapper.mapLocalException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unknownError,
            ),
          );
        },
      );

      test(
        'should return LendingException with unexpectedError code '
        'if exception response data is not a Map',
        () {
          // Arrange
          final exception = mockRestApiException;
          when(() => exception.response?.data).thenReturn(null);

          // Act
          final result = lendingExceptionMapper.mapLocalException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unexpectedError,
            ),
          );
        },
      );

      test(
        'should map local error without code properly to unexpectedError',
        () {
          // Arrange
          final exception = RestApiException(
            response: RestApiResponse<Map<String, Map<String, String>>>(
              data: {
                'properties': {},
              },
            ),
          );

          // Act
          final result = lendingExceptionMapper.mapLocalException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unexpectedError,
            ),
          );
        },
      );

      test(
        'should map local error without properties properly to unexpectedError',
        () {
          // Arrange
          final exception = RestApiException(
            response: RestApiResponse<Map<String, Map<String, String>>>(
              data: {},
            ),
          );

          // Act
          final result = lendingExceptionMapper.mapLocalException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unexpectedError,
            ),
          );
        },
      );
      group('succesful cases >', () {
        LendingExceptionCode mapToLendingExceptionCode(
          ApiV1StaticExceptionCodesGet$Response dtoCode,
        ) {
          switch (dtoCode) {
            case ApiV1StaticExceptionCodesGet$Response.modifyUndisbursedAccount:
              return LendingExceptionCode.modifyUndisbursedAccount;
            case ApiV1StaticExceptionCodesGet$Response.otpRetriesExhausted:
              return LendingExceptionCode.otpRetriesExhausted;
            case ApiV1StaticExceptionCodesGet$Response.otpNotVerified:
              return LendingExceptionCode.otpNotVerified;
            case ApiV1StaticExceptionCodesGet$Response.applicationNotFound:
              return LendingExceptionCode.applicationNotFound;
            case ApiV1StaticExceptionCodesGet$Response
                  .applicationCustomerMismatch:
              return LendingExceptionCode.applicationCustomerMismatch;
            case ApiV1StaticExceptionCodesGet$Response.noOffersFound:
              return LendingExceptionCode.noOffersFound;
            case ApiV1StaticExceptionCodesGet$Response.invalidIban:
              return LendingExceptionCode.invalidIban;
            case ApiV1StaticExceptionCodesGet$Response
                  .creditDecisionIncompleteInput:
              return LendingExceptionCode.creditDecisionIncompleteInput;
            case ApiV1StaticExceptionCodesGet$Response
                  .creditAgreementIncompleteInput:
              return LendingExceptionCode.creditAgreementIncompleteInput;
            case ApiV1StaticExceptionCodesGet$Response
                  .creditLimitGreaterThanApprovedLimit:
              return LendingExceptionCode.creditLimitGreaterThanApprovedLimit;
            case ApiV1StaticExceptionCodesGet$Response
                  .signCreditAgreementInvalidOperation:
              return LendingExceptionCode.signCreditAgreementInvalidOperation;
            case ApiV1StaticExceptionCodesGet$Response
                  .submitCreditAgreementInvalidState:
              return LendingExceptionCode.submitCreditAgreementInvalidState;
            case ApiV1StaticExceptionCodesGet$Response
                  .creditDecisionInvalidState:
              return LendingExceptionCode.creditDecisionInvalidState;
            case ApiV1StaticExceptionCodesGet$Response.accountNotFound:
              return LendingExceptionCode.accountNotFound;
            case ApiV1StaticExceptionCodesGet$Response.invalidInput:
              return LendingExceptionCode.invalidInput;
            case ApiV1StaticExceptionCodesGet$Response.customerDetailsInvalid:
              return LendingExceptionCode.customerDetailsInvalid;
            case ApiV1StaticExceptionCodesGet$Response.productNotFound:
              return LendingExceptionCode.productNotFound;
            case ApiV1StaticExceptionCodesGet$Response
                  .acceptedOfferExceededProductLimit:
              return LendingExceptionCode.acceptedOfferExceededProductLimit;
            case ApiV1StaticExceptionCodesGet$Response
                  .acceptedOfferExceededApprovedOffer:
              return LendingExceptionCode.acceptedOfferExceededApprovedOffer;
            case ApiV1StaticExceptionCodesGet$Response.agreementNotFound:
              return LendingExceptionCode.agreementNotFound;
            case ApiV1StaticExceptionCodesGet$Response.approvedOfferNotFound:
              return LendingExceptionCode.approvedOfferNotFound;
            case ApiV1StaticExceptionCodesGet$Response.activeEasyCashAccount:
              return LendingExceptionCode.activeEasyCashAccount;
            case ApiV1StaticExceptionCodesGet$Response.activeSplitAccount:
              return LendingExceptionCode.activeSplitAccount;
            case ApiV1StaticExceptionCodesGet$Response
                  .repaymentRestrictionPeriod:
              return LendingExceptionCode.repaymentRestriced;
            default:
              return LendingExceptionCode.unrecognizedError;
          }
        }

        void testLendingExceptionCode(
          String code,
          LendingExceptionCode lendingExceptionCode,
        ) {
          test(
            'should map local error with code $code to $lendingExceptionCode',
            () {
              // Arrange
              final exception = RestApiException(
                response: RestApiResponse<Map<String, Map<String, String>>>(
                  data: {
                    'properties': {
                      'code': code,
                    },
                  },
                ),
              );

              // Act
              final result =
                  lendingExceptionMapper.mapLocalException(exception);

              // Assert
              expect(
                result,
                LendingException(
                  originalError: exception,
                  code: lendingExceptionCode,
                ),
              );
              expect(result.properties, {
                'code': code,
              });
            },
          );
        }

        for (final exceptionCode
            in ApiV1StaticExceptionCodesGet$Response.values) {
          final code = exceptionCode.value;
          if (code != null) {
            testLendingExceptionCode(
              code,
              mapToLendingExceptionCode(exceptionCode),
            );
          }
        }
      });
    });

    group('mapForeignException >', () {
      test(
        'should return LendingException with unknownError code '
        'if exception is not RestApiException',
        () {
          // Arrange
          final exception = Exception();

          // Act
          final result = lendingExceptionMapper.mapForeignException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unknownError,
            ),
          );
        },
      );

      test(
        'should return LendingException with unexpectedError code '
        'if exception response data is not a Map',
        () {
          // Arrange
          final exception = mockRestApiException;
          when(() => exception.response?.data).thenReturn(null);

          // Act
          final result = lendingExceptionMapper.mapForeignException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unexpectedError,
            ),
          );
        },
      );

      test('should map foreign error with code properly', () {
        // Arrange
        final exception = RestApiException(
          response: RestApiResponse<Map<String, List<Map<String, String>>>>(
            data: {
              'details': [
                {
                  'detailCode': 'LENDING_ACCOUNT_INVALID_STATE',
                },
              ],
            },
          ),
        );

        // Act
        final result = lendingExceptionMapper.mapForeignException(exception);

        // Assert
        expect(
          result,
          LendingException(
            originalError: exception,
            code: LendingExceptionCode.lendingAccountInvalidState,
          ),
        );
      });

      test(
        'should map foreign error with code lendingAccountInvalidType properly',
        () {
          // Arrange
          final exception = RestApiException(
            response: RestApiResponse<Map<String, List<Map<String, String>>>>(
              data: {
                'details': [
                  {
                    'detailCode': 'LENDING_ACCOUNT_INVALID_TYPE',
                  },
                ],
              },
            ),
          );

          // Act
          final result = lendingExceptionMapper.mapForeignException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.lendingAccountInvalidType,
            ),
          );
        },
      );

      test(
        'should map foreign error without '
        'detailCode properly to unexpectedError',
        () {
          // Arrange
          final exception = RestApiException(
            response: RestApiResponse<Map<String, List<Map<String, String>>>>(
              data: {
                'detail': [{}],
              },
            ),
          );

          // Act
          final result = lendingExceptionMapper.mapForeignException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unexpectedError,
            ),
          );
        },
      );

      test(
        'should map foreign error with empty details '
        'properly to unexpectedError',
        () {
          // Arrange
          final exception = RestApiException(
            response: RestApiResponse<Map<String, List<Map<String, String>>>>(
              data: {
                'detail': [],
              },
            ),
          );

          // Act
          final result = lendingExceptionMapper.mapForeignException(exception);

          // Assert
          expect(
            result,
            LendingException(
              originalError: exception,
              code: LendingExceptionCode.unexpectedError,
            ),
          );
        },
      );

      group('succesful cases >', () {
        LendingExceptionCode mapForeignExceptionCodeToLendingCode(
          String foreignExceptionCode,
        ) {
          switch (foreignExceptionCode) {
            case 'LENDING_ACCOUNT_OUTSTANDING_BALANCE_EXCEEDED':
              return LendingExceptionCode
                  .lendingAccountOutstandingBalanceExceeded;
            case 'LENDING_ACCOUNT_INVALID_STATE':
              return LendingExceptionCode.lendingAccountInvalidState;
            case 'LENDING_ACCOUNT_INVALID_TYPE':
              return LendingExceptionCode.lendingAccountInvalidType;
            default:
              return LendingExceptionCode.unrecognizedError;
          }
        }

        void testLendingForeignExceptionCode(
          String code,
          LendingExceptionCode lendingExceptionCode,
        ) {
          test(
            'should map foreign error with code $code to $lendingExceptionCode',
            () {
              // Arrange
              final exception = RestApiException(
                response:
                    RestApiResponse<Map<String, List<Map<String, String>>>>(
                  data: {
                    'details': [
                      {
                        'detailCode': code,
                      },
                    ],
                  },
                ),
              );

              // Act
              final result =
                  lendingExceptionMapper.mapForeignException(exception);

              // Assert
              expect(
                result,
                LendingException(
                  originalError: exception,
                  code: lendingExceptionCode,
                ),
              );
            },
          );
        }

        for (final exceptionCode in [
          'LENDING_ACCOUNT_OUTSTANDING_BALANCE_EXCEEDED',
          'LENDING_ACCOUNT_INVALID_STATE',
          'LENDING_ACCOUNT_INVALID_TYPE',
        ]) {
          testLendingForeignExceptionCode(
            exceptionCode,
            mapForeignExceptionCodeToLendingCode(exceptionCode),
          );
        }
      });
    });
  });
}
