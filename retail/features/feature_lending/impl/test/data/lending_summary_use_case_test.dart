import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_lending_impl/src/domain/use_cases/lending_summary_use_case.dart';

import '../mocks.dart';

void main() {
  const productType = ProductType.creditCard;
  late LendingRepository lendingRepository;
  late LoanAccountRepository loanAccountRepository;

  late LendingSummaryUseCase lendingSummaryUseCase;
  late ErrorReporter errorReporter;

  setUpAll(registerFallbackValues);

  setUp(() {
    lendingRepository = MockLendingRepository();
    loanAccountRepository = MockLoanAccountRepository();
    errorReporter = MockErrorReporter();

    lendingSummaryUseCase = LendingSummaryUseCaseImpl(
      lendingRepository: lendingRepository,
      loanAccountRepository: loanAccountRepository,
      errorReporter: errorReporter,
    );
  });

  test('Observe lending summary calls expected repos', () async {
    // Arrange

    when(() => lendingRepository.observeApplications())
        .justAnswerData(const Applications(total: 0));
    when(() => loanAccountRepository.observeLoanDetails())
        .justAnswerData(MockValues.loanDetails);
    when(() => lendingRepository.getOffers())
        .justAnswerAsync(MockValues.offers);
    when(() => lendingRepository.getLendingConfiguration(any()))
        .justAnswerAsync(MockValues.configuration);
    when(
      () => lendingRepository.observeOffers(),
    ).justAnswerData(MockValues.offers);
    when(() => loanAccountRepository.getAutoPaymentInfo(any()))
        .justAnswerAsync(MockValues.autoPayment);

    // Act
    await lendingSummaryUseCase(productType).first;

    // Assert
    verify(
      () => lendingRepository.observeApplications(),
    ).calledOnce;
    verify(() => loanAccountRepository.observeLoanDetails()).calledOnce;
    verify(() => lendingRepository.observeOffers()).calledOnce;
  });
}
