import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:wio_common_feature_fraud_api/fraud_api.dart';
import 'package:wio_common_feature_transaction_api/model/transaction.dart';
import 'package:wio_feature_fraud_impl/src/domain/fraud_interactor.dart';

import '../mocks.dart';
import '../test_entities.dart';

void main() {
  late MockFraudRepositoryImpl mockRepository;
  late FraudInteractorImpl interactor;

  // Set up the mock repository and interactor before each test.
  setUp(() {
    mockRepository = MockFraudRepositoryImpl();
    interactor = FraudInteractorImpl(repository: mockRepository);
  });

  group('getDisputesById', () {
    test('should delegate call and return a UserDisputeModel', () async {
      // Arrange
      const testId = 'test-id';
      final dummyDispute = UserDisputeModel(
        id: 'dummy-id',
        status: DisputeStatus.reported,
        transactionDetails: [],
        reportedDate: DateTime(2020),
        caseId: '223232',
      );
      when(() => mockRepository.getDisputesById(testId))
          .thenAnswer((_) async => dummyDispute);

      // Act
      final result = await interactor.getDisputesById(testId);

      // Assert
      expect(result, dummyDispute);
      verify(() => mockRepository.getDisputesById(testId)).called(1);
    });
  });

  group('getDispute', () {
    test('should delegate call and return repository value', () async {
      // Arrange
      // Assuming repository.getDispute returns a List<UserDisputeModel>.
      final dummyList = <UserDisputeModel>[
        UserDisputeModel(
          id: 'dummy-id',
          status: DisputeStatus.reported,
          transactionDetails: [],
          reportedDate: DateTime(2020),
          caseId: '23323',
        ),
      ];
      when(() => mockRepository.getDispute())
          .thenAnswer((_) async => dummyList);

      // Act
      final result = await interactor.getDispute();

      // Assert
      expect(result, dummyList);
      verify(() => mockRepository.getDispute()).called(1);
    });
  });

  group('getDisputes', () {
    test('should delegate call and return a list of UserDisputeModel',
        () async {
      // Arrange
      final dummyList = <UserDisputeModel>[
        UserDisputeModel(
          id: 'dummy-id',
          status: DisputeStatus.reported,
          transactionDetails: [],
          reportedDate: DateTime(2020),
          caseId: '121212',
        ),
      ];
      when(() => mockRepository.getDisputes())
          .thenAnswer((_) async => dummyList);

      // Act
      final result = await interactor.getDisputes();

      // Assert
      expect(result, dummyList);
      verify(() => mockRepository.getDisputes()).called(1);
    });
  });

  group('reportDisputes', () {
    test('should delegate call and return a UserDisputeModel', () async {
      // Arrange
      final transactions = <Transaction>{TestEntities.transaction};
      final dummyDispute = UserDisputeModel(
        id: 'dummy-id',
        status: DisputeStatus.reported,
        transactionDetails: [],
        reportedDate: DateTime(2020),
        caseId: '125535',
      );
      when(() => mockRepository.reportDisputes(transactions))
          .thenAnswer((_) async => dummyDispute);

      // Act
      final result = await interactor.reportDisputes(transactions);

      // Assert
      expect(result, dummyDispute);
      verify(() => mockRepository.reportDisputes(transactions)).called(1);
    });
  });
}
