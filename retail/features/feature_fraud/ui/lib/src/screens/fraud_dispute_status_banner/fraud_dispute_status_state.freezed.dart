// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fraud_dispute_status_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FraudDisputeStatusState {
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IdleFraudDisputeStatusState value) idle,
    required TResult Function(ProcessingFraudDisputeStatusState value)
        processing,
    required TResult Function(FailedFraudDisputeStatusState value) failed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IdleFraudDisputeStatusState value)? idle,
    TResult? Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult? Function(FailedFraudDisputeStatusState value)? failed,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IdleFraudDisputeStatusState value)? idle,
    TResult Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult Function(FailedFraudDisputeStatusState value)? failed,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FraudDisputeStatusStateCopyWith<$Res> {
  factory $FraudDisputeStatusStateCopyWith(FraudDisputeStatusState value,
          $Res Function(FraudDisputeStatusState) then) =
      _$FraudDisputeStatusStateCopyWithImpl<$Res, FraudDisputeStatusState>;
}

/// @nodoc
class _$FraudDisputeStatusStateCopyWithImpl<$Res,
        $Val extends FraudDisputeStatusState>
    implements $FraudDisputeStatusStateCopyWith<$Res> {
  _$FraudDisputeStatusStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FraudDisputeStatusState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$IdleFraudDisputeStatusStateImplCopyWith<$Res> {
  factory _$$IdleFraudDisputeStatusStateImplCopyWith(
          _$IdleFraudDisputeStatusStateImpl value,
          $Res Function(_$IdleFraudDisputeStatusStateImpl) then) =
      __$$IdleFraudDisputeStatusStateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserDisputeModel dispute});
}

/// @nodoc
class __$$IdleFraudDisputeStatusStateImplCopyWithImpl<$Res>
    extends _$FraudDisputeStatusStateCopyWithImpl<$Res,
        _$IdleFraudDisputeStatusStateImpl>
    implements _$$IdleFraudDisputeStatusStateImplCopyWith<$Res> {
  __$$IdleFraudDisputeStatusStateImplCopyWithImpl(
      _$IdleFraudDisputeStatusStateImpl _value,
      $Res Function(_$IdleFraudDisputeStatusStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FraudDisputeStatusState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dispute = null,
  }) {
    return _then(_$IdleFraudDisputeStatusStateImpl(
      dispute: null == dispute
          ? _value.dispute
          : dispute // ignore: cast_nullable_to_non_nullable
              as UserDisputeModel,
    ));
  }
}

/// @nodoc

class _$IdleFraudDisputeStatusStateImpl extends IdleFraudDisputeStatusState {
  const _$IdleFraudDisputeStatusStateImpl({required this.dispute}) : super._();

  @override
  final UserDisputeModel dispute;

  @override
  String toString() {
    return 'FraudDisputeStatusState.idle(dispute: $dispute)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdleFraudDisputeStatusStateImpl &&
            (identical(other.dispute, dispute) || other.dispute == dispute));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dispute);

  /// Create a copy of FraudDisputeStatusState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdleFraudDisputeStatusStateImplCopyWith<_$IdleFraudDisputeStatusStateImpl>
      get copyWith => __$$IdleFraudDisputeStatusStateImplCopyWithImpl<
          _$IdleFraudDisputeStatusStateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IdleFraudDisputeStatusState value) idle,
    required TResult Function(ProcessingFraudDisputeStatusState value)
        processing,
    required TResult Function(FailedFraudDisputeStatusState value) failed,
  }) {
    return idle(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IdleFraudDisputeStatusState value)? idle,
    TResult? Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult? Function(FailedFraudDisputeStatusState value)? failed,
  }) {
    return idle?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IdleFraudDisputeStatusState value)? idle,
    TResult Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult Function(FailedFraudDisputeStatusState value)? failed,
    required TResult orElse(),
  }) {
    if (idle != null) {
      return idle(this);
    }
    return orElse();
  }
}

abstract class IdleFraudDisputeStatusState extends FraudDisputeStatusState {
  const factory IdleFraudDisputeStatusState(
          {required final UserDisputeModel dispute}) =
      _$IdleFraudDisputeStatusStateImpl;
  const IdleFraudDisputeStatusState._() : super._();

  UserDisputeModel get dispute;

  /// Create a copy of FraudDisputeStatusState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdleFraudDisputeStatusStateImplCopyWith<_$IdleFraudDisputeStatusStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProcessingFraudDisputeStatusStateImplCopyWith<$Res> {
  factory _$$ProcessingFraudDisputeStatusStateImplCopyWith(
          _$ProcessingFraudDisputeStatusStateImpl value,
          $Res Function(_$ProcessingFraudDisputeStatusStateImpl) then) =
      __$$ProcessingFraudDisputeStatusStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ProcessingFraudDisputeStatusStateImplCopyWithImpl<$Res>
    extends _$FraudDisputeStatusStateCopyWithImpl<$Res,
        _$ProcessingFraudDisputeStatusStateImpl>
    implements _$$ProcessingFraudDisputeStatusStateImplCopyWith<$Res> {
  __$$ProcessingFraudDisputeStatusStateImplCopyWithImpl(
      _$ProcessingFraudDisputeStatusStateImpl _value,
      $Res Function(_$ProcessingFraudDisputeStatusStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FraudDisputeStatusState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ProcessingFraudDisputeStatusStateImpl
    extends ProcessingFraudDisputeStatusState {
  const _$ProcessingFraudDisputeStatusStateImpl() : super._();

  @override
  String toString() {
    return 'FraudDisputeStatusState.processing()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProcessingFraudDisputeStatusStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IdleFraudDisputeStatusState value) idle,
    required TResult Function(ProcessingFraudDisputeStatusState value)
        processing,
    required TResult Function(FailedFraudDisputeStatusState value) failed,
  }) {
    return processing(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IdleFraudDisputeStatusState value)? idle,
    TResult? Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult? Function(FailedFraudDisputeStatusState value)? failed,
  }) {
    return processing?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IdleFraudDisputeStatusState value)? idle,
    TResult Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult Function(FailedFraudDisputeStatusState value)? failed,
    required TResult orElse(),
  }) {
    if (processing != null) {
      return processing(this);
    }
    return orElse();
  }
}

abstract class ProcessingFraudDisputeStatusState
    extends FraudDisputeStatusState {
  const factory ProcessingFraudDisputeStatusState() =
      _$ProcessingFraudDisputeStatusStateImpl;
  const ProcessingFraudDisputeStatusState._() : super._();
}

/// @nodoc
abstract class _$$FailedFraudDisputeStatusStateImplCopyWith<$Res> {
  factory _$$FailedFraudDisputeStatusStateImplCopyWith(
          _$FailedFraudDisputeStatusStateImpl value,
          $Res Function(_$FailedFraudDisputeStatusStateImpl) then) =
      __$$FailedFraudDisputeStatusStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FailedFraudDisputeStatusStateImplCopyWithImpl<$Res>
    extends _$FraudDisputeStatusStateCopyWithImpl<$Res,
        _$FailedFraudDisputeStatusStateImpl>
    implements _$$FailedFraudDisputeStatusStateImplCopyWith<$Res> {
  __$$FailedFraudDisputeStatusStateImplCopyWithImpl(
      _$FailedFraudDisputeStatusStateImpl _value,
      $Res Function(_$FailedFraudDisputeStatusStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FraudDisputeStatusState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FailedFraudDisputeStatusStateImpl
    extends FailedFraudDisputeStatusState {
  const _$FailedFraudDisputeStatusStateImpl() : super._();

  @override
  String toString() {
    return 'FraudDisputeStatusState.failed()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailedFraudDisputeStatusStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IdleFraudDisputeStatusState value) idle,
    required TResult Function(ProcessingFraudDisputeStatusState value)
        processing,
    required TResult Function(FailedFraudDisputeStatusState value) failed,
  }) {
    return failed(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IdleFraudDisputeStatusState value)? idle,
    TResult? Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult? Function(FailedFraudDisputeStatusState value)? failed,
  }) {
    return failed?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IdleFraudDisputeStatusState value)? idle,
    TResult Function(ProcessingFraudDisputeStatusState value)? processing,
    TResult Function(FailedFraudDisputeStatusState value)? failed,
    required TResult orElse(),
  }) {
    if (failed != null) {
      return failed(this);
    }
    return orElse();
  }
}

abstract class FailedFraudDisputeStatusState extends FraudDisputeStatusState {
  const factory FailedFraudDisputeStatusState() =
      _$FailedFraudDisputeStatusStateImpl;
  const FailedFraudDisputeStatusState._() : super._();
}
