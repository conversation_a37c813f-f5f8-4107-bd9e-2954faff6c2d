import 'package:di/di.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_fraud_api/fraud_api.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_feature_fraud_ui/feature_fraud_mobile_ui.dart';
import 'package:wio_feature_fraud_ui/src/screens/proof_of_contact/cubit/upload_proof_of_contact_cubit.dart';
import 'package:wio_feature_fraud_ui/src/screens/proof_of_contact/cubit/upload_proof_of_contact_state.dart';

class UploadProofOfContactPage extends StatelessWidget {
  final Transaction transaction;
  final UserFlow userFlow;

  const UploadProofOfContactPage({
    required this.transaction,
    required this.userFlow,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider<UploadProofOfContactCubit>(
      create: (context) => DependencyProvider.get<UploadProofOfContactCubit>(),
      child: _Body(
        transaction: transaction,
        userFlow: userFlow,
      ),
    );
  }
}

class _Body extends StatelessWidget {
  final Transaction transaction;
  final UserFlow userFlow;

  const _Body({
    required this.transaction,
    required this.userFlow,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = FraudLocalizations.of(context);
    final cubit = context.read<UploadProofOfContactCubit>();

    return Scaffold(
      appBar: TopNavigation(
        onRightIconPressed: () => cubit.onClosePressed(),
        TopNavigationModel(
          rightAccessory: TopNavigationIconRightAccessoryModel(
            icon: const GraphicAssetPointer.icon(CompanyIconPointer.close),
          ),
          state: TopNavigationState.positive,
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 8),
              Label(
                model: LabelModel(
                  text: l10n.didYouReachOutToTheMerchant,
                  color: CompanyColorPointer.primary3,
                  textStyle: CompanyTextStylePointer.h2medium,
                ),
              ),
              const SizedBox(height: 12),
              Label(
                model: LabelModel(
                  text: l10n.uploadDocumentDetail,
                  color: CompanyColorPointer.secondary3,
                  textStyle: CompanyTextStylePointer.b2,
                ),
              ),
              const SizedBox(height: 12),
              ...[
                l10n.emailToTheMerchant,
                l10n.chatTranscripts,
                l10n.callLogsWithDate,
                l10n.caseOrTicketNumbers,
                l10n.receiptsWithNotes,
                l10n.submittedContactForms,
              ].map(
                (text) => _BulletPointLabel(text: text),
              ),
              const SizedBox(height: 24),
              BlocBuilder<UploadProofOfContactCubit, UploadProofOfContactState>(
                builder: (context, state) {
                  return FileUploadListItem(
                    onClicked: cubit.openFilePicker,
                    model: _buildFileUploadModel(state, l10n),
                    onDeleted: cubit.removeDocument,
                  );
                },
              ),
              const Spacer(),
              InkWell(
                onTap: cubit.openContactMerchantInfo,
                child: Label(
                  model: LabelModel(
                    textAlign: LabelTextAlign.center,
                    text: l10n.iDidNotContactTheMerchant,
                    textStyle: CompanyTextStylePointer.b2,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              BlocBuilder<UploadProofOfContactCubit, UploadProofOfContactState>(
                builder: (_, state) {
                  return Button(
                    model: ButtonModel(
                      title: l10n.fraudTransactionSelectionNextButton,
                    ),
                    onPressed: state.status == DocumentStatus.uploaded
                        ? () => cubit.onNextTap(transaction, userFlow)
                        : null,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  PreviewDocumentType _getPreviewDocumentType(UploadDocumentType type) {
    return switch (type) {
      UploadDocumentType.image => PreviewDocumentType.image,
      UploadDocumentType.pdf => PreviewDocumentType.pdf,
    };
  }

  FileUploadListItemModel _buildFileUploadModel(
    UploadProofOfContactState state,
    FraudLocalizations l10n,
  ) {
    final file = state.files;
    final isFileLoaded = file != null;

    if (!isFileLoaded) {
      return FileUploadListItemModel.initial(
        title: l10n.uploadProofOfContact,
      );
    }

    switch (state.status) {
      case DocumentStatus.uploading:
        return FileUploadListItemModel.uploading(
          title: file.fileName,
          progress: state.progress,
        );
      case DocumentStatus.uploaded:
        return FileUploadListItemModel.populated(
          title: l10n.proofOfContact,
          previewContainerModel: PreviewContainerModel(
            fileName: file.fileName,
            type: _getPreviewDocumentType(file.type),
            fileData: file.fileData,
          ),
          suffix: CompanyIconPointer.delete,
        );

      case DocumentStatus.failed:
        return FileUploadListItemModel.populated(
          title: l10n.proofOfContact,
          previewContainerModel: PreviewContainerModel(
            fileName: file.fileName,
            type: _getPreviewDocumentType(file.type),
            fileData: file.fileData,
          ),
          suffix: CompanyIconPointer.delete,
          prefix: CompanyIconPointer.failure,
          subTitle: l10n.failed,
        );

      default:
        return FileUploadListItemModel.populated(
          title: l10n.proofOfContact,
          previewContainerModel: PreviewContainerModel(
            fileName: file.fileName,
            type: _getPreviewDocumentType(file.type),
            fileData: file.fileData,
          ),
          suffix: CompanyIconPointer.delete,
        );
    }
  }
}

class _BulletPointLabel extends StatelessWidget {
  final String text;

  const _BulletPointLabel({required this.text});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.only(end: 8),
      child: Row(
        textDirection: Directionality.of(context),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.only(top: 8, start: 8, end: 8),
            child: Container(
              width: 4,
              height: 4,
              decoration: BoxDecoration(
                color: context.colorStyling
                    .fromPointer(CompanyColorPointer.primary3),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Expanded(
            child: Label(
              model: LabelModel(
                text: text,
                textStyle: CompanyTextStylePointer.b2,
                color: CompanyColorPointer.secondary3,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
