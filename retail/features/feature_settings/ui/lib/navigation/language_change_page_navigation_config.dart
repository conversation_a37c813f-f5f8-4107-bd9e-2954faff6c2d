import 'package:feature_settings_api/navigation/settings_feature_navigation_config.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';

class LanguageChangePageNavigationConfig extends ScreenNavigationConfig {
  static const screenId = 'language_change_page';

  const LanguageChangePageNavigationConfig()
      : super(
          id: LanguageChangePageNavigationConfig.screenId,
          feature: SettingsFeatureNavigationConfig.name,
        );

  @override
  String toString() => 'LanguageChangePageNavigationConfig';
}
