import 'package:feature_auth_api/navigation/sof_bottomsheet_navigation_config.dart';
import 'package:feature_settings_api/feature_settings_api.dart';
import 'package:feature_settings_ui/navigation/employee_type_edit_navigation_config.dart';
import 'package:feature_settings_ui/navigation/language_change_page_navigation_config.dart';
import 'package:feature_settings_ui/navigation/personal_details_page_navigation_config.dart';
import 'package:feature_settings_ui/navigation/sof_screen_navigation_config.dart';
import 'package:feature_settings_ui/navigation/update_tax_info_screen_config.dart';
import 'package:feature_settings_ui/screens/language_change_screen/language_change_page.dart';
import 'package:feature_settings_ui/screens/participant_settings_screen/participant_settings_page.dart';
import 'package:feature_settings_ui/screens/personal_details_screen/personal_details_page.dart';
import 'package:feature_settings_ui/screens/personal_details_screen/screens/employment_type/employment_type_page.dart';
import 'package:feature_settings_ui/screens/personal_details_screen/screens/signature_update/signature_update_page.dart';
import 'package:feature_settings_ui/screens/personal_details_screen/screens/sof_page/sof_page.dart';
import 'package:feature_settings_ui/screens/settings_screen/settings_page.dart';
import 'package:feature_settings_ui/screens/tax_info_screen/update_tax_info_page.dart';
import 'package:flutter/widgets.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';

class SettingsRouter extends NavigationRouter {
  const SettingsRouter();

  @override
  Route<Object?> getScreenRoute(RouteSettings settings) {
    final Object? config;
    if (settings.arguments is SettingsFeatureNavigationConfig) {
      final featureConfig =
          settings.arguments as SettingsFeatureNavigationConfig;
      config = featureConfig.destination ??
          SettingsPageNavigationConfig(
            deepLinkType: featureConfig.deepLinkType,
          );
    } else {
      config = settings.arguments;
    }

    final currentScreenConfigSettings = RouteSettings(
      name: settings.name,
      arguments: config,
    );

    if (config is OwnerSettingsPageNavigationConfig) {
      return toRoute(
        SettingsPage(deepLinkType: config.deepLinkType),
        currentScreenConfigSettings,
      );
    }

    if (config is ParticipantSettingsPageNavigationConfig) {
      return toRoute(
        const ParticipantSettingsPage(),
        currentScreenConfigSettings,
      );
    }

    if (config is LanguageChangePageNavigationConfig) {
      return toRoute(
        const LanguageChangePage(),
        currentScreenConfigSettings,
      );
    }

    if (config is PersonalDetailsPageNavigationConfig) {
      return toRoute(
        const PersonalDetailsPage(),
        currentScreenConfigSettings,
      );
    }

    if (config is SofScreenNavigationConfig) {
      return toRoute(
        SofPage(
          screenId: config.flowType?.value ?? config.id,
          employmentType: config.employmentType,
          selectedCodes: config.selectedCodes,
          requiredActionScreenConfig: config.requiredActionScreenConfig,
        ),
        currentScreenConfigSettings,
      );
    } else if (config is EmployeeTypeEditNavigationConfig) {
      return toRoute(
        EmploymentTypePage(
          flowType: config.flowType,
          question: config.info,
          selectedIds: [if (config.type != null) config.type!.serverName],
          onSubmit: (options) =>
              (config as EmployeeTypeEditNavigationConfig).onSubmit(
            EmploymentInfo.values.firstWhere(
              (info) => options.any((option) => option.id == info.serverName),
            ),
          ),
        ),
        settings,
      );
    }

    if (config is UpdateTaxInfoScreenConfig) {
      return toRoute(
        UpdateTaxInfoPage(
          config: config.config,
          requiredActionScreenConfig: config.requiredActionScreenConfig,
        ),
        settings,
      );
    }

    if (config is SignatureUpdateScreenNavigationConfig) {
      return toRoute(
        SignatureUpdatePage(url: config.url),
        settings,
      );
    }

    throw Exception('Unknown $config for the $runtimeType');
  }

  @override
  Future<T?> showBottomSheet<T>(
    BuildContext context,
    BottomSheetNavigationConfig<T> config,
    RouteSettings routeSettings,
  ) {
    if (config is SofBottomSheetNavigationConfig) {
      final args = config as SofBottomSheetNavigationConfig;
      return CompanyBottomSheet.showModal(
        context: context,
        routeSettings: routeSettings,
        builder: (context) => SofBottomSheet(
          model: SofBottomSheetModel(
            title: args.title,
            description: args.description,
            url: args.url,
          ),
        ),
      );
    }

    return super.showBottomSheet(context, config, routeSettings);
  }
}
