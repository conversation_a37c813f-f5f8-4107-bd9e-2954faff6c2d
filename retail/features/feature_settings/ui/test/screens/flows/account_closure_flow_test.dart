import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_initialization_api/navigation/initialization_feature_navigation_config.dart';
import 'package:feature_settings_ui/flows/account_closure_flow.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_account_closure_ui/flows/account_closure_flow.dart';

import '../../feature_settings_ui_mocks.dart';

void main() {
  late NavigationProvider navigator;
  late AuthInteractor authInteractor;
  late Logger logger;
  late AccountClosureFlow accountClosureFlow;

  setUp(() {
    navigator = MockNavigationProvider();
    authInteractor = MockAuthInteractor();
    logger = MockLogger();
    accountClosureFlow = AccountClosureFlowImpl(
      navigator: navigator,
      authInteractor: authInteractor,
      logger: logger,
    );
  });

  test('logout tests', () async {
    // Arrange
    when(() => authInteractor.logout()).justCompleteAsync();

    // Act
    await accountClosureFlow.logout();

    // Assert
    verify(() => logger.debug(any())).calledOnce;
    verifyNever(
      () => logger.error(
        any(),
        error: any(named: 'error'),
        stackTrace: any(named: 'stackTrace'),
      ),
    );
    verify(() => authInteractor.logout()).calledOnce;
    verify(
      () => navigator.removeStackAndPush(
        const InitializationFeatureNavigationConfig(),
      ),
    ).calledOnce;
  });
}
