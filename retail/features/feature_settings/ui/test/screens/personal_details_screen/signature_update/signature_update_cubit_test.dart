import 'dart:io';
import 'dart:typed_data';
import 'dart:ui';

import 'package:feature_remote_file_api/remote_file.dart';
import 'package:feature_settings_ui/l10n/settings_localization.g.dart';
import 'package:feature_settings_ui/screens/personal_details_screen/screens/signature_update/signature_update_cubit.dart';
import 'package:mime/mime.dart';
import 'package:mocktail/mocktail.dart';
import 'package:tests/tests.dart';
import 'package:tests_ui/mocks/mocks.dart';
import 'package:wio_app_core_api/flutter_index.dart';
import 'package:wio_common_file_picker_api/model/file_picker_bottom_sheet_model.dart';
import 'package:wio_common_file_picker_api/navigation/file_picker_bottom_sheet_config.dart';
import 'package:wio_feature_kyc_api/feature_kyc_api.dart';

import '../../../feature_settings_ui_mocks.dart';

void main() {
  late SignatureUpdateCubit cubit;
  late MockDocumentUpdateInteractor documentUpdateInteractor;
  late MockNavigationProvider navigationProvider;
  late MockKycInteractor kycInteractor;
  late SettingsLocalizations localizations;
  late MockToastMessageProvider toastMessageProvider;
  late MockLogger logger;

  setUpAll(() async {
    await OtaLocalizationImpl().initMock();
    localizations = await SettingsLocalizations.load(const Locale('en'));
  });

  setUp(() async {
    documentUpdateInteractor = MockDocumentUpdateInteractor();
    navigationProvider = MockNavigationProvider();
    kycInteractor = MockKycInteractor();
    toastMessageProvider = MockToastMessageProvider();
    logger = MockLogger();
    cubit = SignatureUpdateCubit(
      documentUpdateInteractor: documentUpdateInteractor,
      navigationProvider: navigationProvider,
      kycInteractor: kycInteractor,
      localizations: localizations,
      toastMessageProvider: toastMessageProvider,
      logger: logger,
    );

    registerFallbackValue(
      const FilePickerBottomSheetConfig(
        filePickerModel: FilePickerBottomSheetModel(
          bottomSheetTitle: '',
        ),
      ),
    );

    registerFallbackValue(
      UploadDocumentInput(
        documentCategory: 'Retail',
        documentType: 'KYC',
        journeyCode: 'Onboarding',
      ),
    );
  });

  group('SignatureUpdateCubit', () {
    test('init with provided url downloads file and sets fileModel', () async {
      final fileBytes = Uint8List.fromList([1]);
      when(() => documentUpdateInteractor.downloadFile(any()))
          .thenAnswer((_) async => fileBytes);
      await cubit.init('testUrl.jpg');
      final expectedFileModel = UploadFileModel(
        id: 1,
        fileData: fileBytes,
        uploadedPath: 'testUrl.jpg',
        fileName: 'testUrl.jpg',
        mimeType: lookupMimeType('testUrl.jpg') ?? '',
        status: FileUploadStatus.completed,
      );
      expect(cubit.state.fileModel, equals(expectedFileModel));
      expect(cubit.state.isFileLoading, isFalse);
    });

    test('init with null url and empty signatureUrl sets default fileModel',
        () async {
      when(() => kycInteractor.getCustomerScreeningInfo()).thenAnswer(
        (_) async => const CustomerScreeningInfo(
          signatureUrl: '',
          screeningStatusOnPassport: PassportVerificationStatus.approved,
          addressVerified: true,
        ),
      );
      await cubit.init(null);
      expect(cubit.state.fileModel, equals(const UploadFileModel(id: 1)));
      expect(cubit.state.isFileLoading, isFalse);
    });

    test('submit calls goBack on successful submission', () async {
      final mockFile = MockFile('testUrl.pdf');
      final fileBytes = Uint8List.fromList([1]);

      when(() => mockFile.readAsBytesSync()).thenReturn(fileBytes);
      when(() => navigationProvider.showBottomSheet<File>(any()))
          .thenAnswer((_) async => mockFile);
      when(
        () => documentUpdateInteractor.uploadFile(
          fileBytes: any(named: 'fileBytes'),
          uploadDocumentInput: any(named: 'uploadDocumentInput'),
        ),
      ).thenAnswer((_) async => 'submittedUrl');
      when(() => documentUpdateInteractor.downloadFile(any()))
          .thenAnswer((_) async => fileBytes);
      when(() => kycInteractor.getCustomerScreeningInfo()).thenAnswer(
        (_) async => const CustomerScreeningInfo(
          signatureUrl: 'existingUrl',
          screeningStatusOnPassport: PassportVerificationStatus.approved,
          addressVerified: true,
        ),
      );
      await cubit.init(null);
      final initialFileModel = UploadFileModel(
        id: 1,
        fileData: fileBytes,
        uploadedPath: 'submittedUrl',
        fileName: 'testUrl.pdf',
        status: FileUploadStatus.completed,
        mimeType: 'application/pdf',
      );
      await cubit.uploadDocument(
        fileModel: initialFileModel,
        onUploadStatusChange: (_) {},
      );
      when(
        () => kycInteractor.submitSignature(
          documentKey: initialFileModel.uploadedPath,
          documentName: initialFileModel.fileName,
          mimeType: initialFileModel.mimeType,
          documentSide: 'Front',
        ),
      ).thenAnswer((_) async {
        return;
      });
      when(() => navigationProvider.navigateTo(any())).thenAnswer((_) async {
        return null;
      });
      await cubit.submit();
      verify(() => navigationProvider.goBack(initialFileModel.uploadedPath))
          .called(1);
    });

    test('submit handles error and shows error status view', () async {
      final mockFile = MockFile('testUrl.pdf');
      final fileBytes = Uint8List.fromList([1]);

      when(() => mockFile.readAsBytesSync()).thenReturn(fileBytes);
      when(() => navigationProvider.showBottomSheet<File>(any()))
          .justAnswerAsync(mockFile);
      when(
        () => documentUpdateInteractor.uploadFile(
          fileBytes: any(named: 'fileBytes'),
          uploadDocumentInput: any(named: 'uploadDocumentInput'),
        ),
      ).thenAnswer((_) async => 'submittedUrl');
      when(() => documentUpdateInteractor.downloadFile(any()))
          .thenAnswer((_) async => fileBytes);
      when(() => kycInteractor.getCustomerScreeningInfo()).thenAnswer(
        (_) async => const CustomerScreeningInfo(
          signatureUrl: 'existingUrl',
          screeningStatusOnPassport: PassportVerificationStatus.approved,
          addressVerified: true,
        ),
      );
      await cubit.init(null);
      final initialFileModel = UploadFileModel(
        id: 1,
        fileData: fileBytes,
        uploadedPath: 'submittedUrl',
        fileName: 'testUrl.pdf',
        status: FileUploadStatus.completed,
        mimeType: 'application/pdf',
      );
      await cubit.uploadDocument(
        fileModel: initialFileModel,
        onUploadStatusChange: (_) {},
      );
      when(
        () => kycInteractor.submitSignature(
          documentKey: initialFileModel.uploadedPath,
          documentName: initialFileModel.fileName,
          mimeType: initialFileModel.mimeType,
          documentSide: 'Front',
        ),
      ).thenThrow(Exception('Submission error'));
      when(() => navigationProvider.navigateTo(any())).thenAnswer((_) async {
        return null;
      });
      await cubit.submit();
      verify(
        () => logger.error(
          any(),
          error: any(named: 'error'),
        ),
      ).called(1);
      verify(() => navigationProvider.navigateTo(any())).called(1);
    });
  });
}
