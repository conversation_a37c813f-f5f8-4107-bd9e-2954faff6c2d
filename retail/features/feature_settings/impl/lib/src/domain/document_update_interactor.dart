import 'dart:typed_data';

import 'package:feature_remote_file_api/remote_file.dart';
import 'package:feature_settings_api/domain/document_update_interactor.dart';

class DocumentUpdateInteractorImpl implements DocumentUpdateInteractor {
  final RemoteFileRepository _remoteFileRepository;

  DocumentUpdateInteractorImpl({
    required RemoteFileRepository remoteFileRepository,
  }) : _remoteFileRepository = remoteFileRepository;

  @override
  Future<Uint8List> downloadFile(String url) {
    return _remoteFileRepository.downloadFile(url);
  }

  @override
  Future<String> uploadFile({
    required List<int> fileBytes,
    required UploadDocumentInput uploadDocumentInput,
  }) {
    return _remoteFileRepository.uploadBinary(
      fileBytes: fileBytes,
      documentInput: uploadDocumentInput,
    );
  }
}
