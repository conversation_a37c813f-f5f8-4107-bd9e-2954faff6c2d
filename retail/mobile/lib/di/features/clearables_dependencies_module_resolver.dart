import 'package:analyst_ratings_impl/data/analyst_ratings_repository.dart';
import 'package:common_feature_fx_api/feature_fx_api.dart';
import 'package:di/di.dart';
import 'package:feature_auth_api/feature_auth_api.dart';
import 'package:feature_dashboard_api/feature_dashboard_api.dart';
import 'package:feature_neobroker_onboarding_api/domain/feature_eligibility_provider.dart';
import 'package:feature_todo_actions_api/feature_todo_actions_api.dart';
import 'package:instruments_impl/index.dart';
import 'package:portfolio_api/index.dart';
import 'package:retail_api/index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_app_core_api/rest_network_manager.dart';
import 'package:wio_common_feature_payments_api/wio_common_feature_payments_api.dart';
import 'package:wio_common_feature_transaction_api/index.dart';
import 'package:wio_digital_trust_api/feature_digital_trust_api.dart';
import 'package:wio_feature_balance_privacy_api/balance_privacy_api.dart';
import 'package:wio_feature_carbon_calculator_api/state_controller/carbon_calculator_state_controller.dart';
import 'package:wio_feature_cards_impl/data/repositories/card_repository.dart';
import 'package:wio_feature_cards_impl/data/repositories/cards_guide_repository.dart';
import 'package:wio_feature_cards_impl/data/repositories/order_card_repository.dart';
import 'package:wio_feature_email_api/domain/email_repository.dart';
import 'package:wio_feature_family_banking_api/family_banking_api.dart';
import 'package:wio_feature_lending_api/lending_api.dart';
import 'package:wio_feature_loan_api/loan_api.dart';
import 'package:wio_feature_money_movement_api/data/money_movement_repository.dart';
import 'package:wio_feature_pricing_plan_api/index.dart';
import 'package:wio_feature_product_hub_api/index.dart';
import 'package:wio_feature_rewards_impl/feature_rewards_impl.dart';
import 'package:wio_feature_saving_spaces_api/saving_spaces_api.dart';
import 'package:wio_feature_spend_host_impl/data/spend_host_repository.dart';
import 'package:wio_feature_user_api/data/user_repository.dart';
import 'package:wio_feature_wealth_management_impl/data/wealth_management_repository.dart';

/// Contain all [Clearable] that requires to clean after logout
class ClearablesDependenciesModuleResolver {
  static void register() {
    DependencyProvider.registerLazySingleton<ClearableManager>(
      () => ClearableManagerImpl(
        clearableProvider: () {
          return [
            DependencyProvider.get<EmailRepository>(),
            DependencyProvider.get<UserRepository>(),
            DependencyProvider.get<OnboardingStageRepository>(),
            DependencyProvider.get<CustomerIdRepository>(),
            DependencyProvider.get<RefreshSessionManager>(),
            DependencyProvider.get<TodoActionsRepository>(),
            DependencyProvider.get<SavingSpacesInteractor>(),
            DependencyProvider.get<TokenRepository>(),
            DependencyProvider.get<CookieManager>(),
            DependencyProvider.get<CardRepository>(),
            DependencyProvider.get<TransactionsRepository>(),
            DependencyProvider.get<OrderCardRepository>(),
            DependencyProvider.get<LendingRepository>(),
            DependencyProvider.get<LoanAccountRepository>(),
            DependencyProvider.get<SplitTransactionsRepository>(),
            DependencyProvider.get<FxStateRepository>(),
            DependencyProvider.get<AppDeepLinkRepository>(),
            DependencyProvider.get<CashbackSettingsRepository>(),
            DependencyProvider.get<CarbonCalculatorStateController>(),
            DependencyProvider.get<EducationalInfoRepository>(),
            DependencyProvider.get<EducationalInfoHandler>(),
            DependencyProvider.get<DioClient>(),
            DependencyProvider.get<InstrumentsRepository>(),
            DependencyProvider.get<SpendHostRepository>(),
            DependencyProvider.get<PricingPlanInteractor>(),
            DependencyProvider.get<LastProductRepository>(),
            DependencyProvider.get<BrokerProductRepository>(),
            DependencyProvider.get<BeneficiaryStreamRepository>(),
            DependencyProvider.get<NPSSInteractor>(),
            DependencyProvider.get<BalancePrivacyInteractor>(),
            DependencyProvider.get<CardsGuideRepository>(),
            DependencyProvider.get<BrokerToDoInteractor>(),
            DependencyProvider.get<AnalystRatingsRepository>(),
            DependencyProvider.get<NPSSRepository>(),
            DependencyProvider.get<FamilyBankingInteractor>(),
            DependencyProvider.get<FamilyBankingGuideInteractor>(),
            DependencyProvider.get<CardPaymentsRepository>(),
            DependencyProvider.get<DashboardNavigationProvider>(),
            DependencyProvider.get<FeatureEligibilityProvider>(),
            DependencyProvider.get<PortfolioProvider>(),
            DependencyProvider.get<DigitalTrustRepository>(),
            DependencyProvider.get<WealthManagementRepository>(),
            DependencyProvider.get<DirectDebitsInteractor>(),
            DependencyProvider.get<MoneyMovementRepository>(),
          ];
        },
      ),
    );
  }
}
