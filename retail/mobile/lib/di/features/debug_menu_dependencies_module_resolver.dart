import 'package:di/di.dart';
import 'package:feature_debug_menu_ui/index.dart';
import 'package:wio_app_core_api/index.dart';
import 'package:wio_core_navigation_api/wio_core_navigation_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_feature_debug_menu_api/feature_debug_menu_api.dart';
import 'package:wio_feature_proxy_impl/index.dart';
import 'package:wio_feature_proxy_ui/di/proxy_feature_dependency_module_resolver.dart';

class DebugMenuDependenciesModuleResolver {
  static void register(AppEnvironment env) {
    if (env.isDebug) {
      DebugMenuFeatureDependencyModuleResolver.register();

      ///Temporary solution, we will register router with other routers
      ///when split env launchings
      ///Env will be provided by flavor in main_pre or main_dev files
      DependencyProvider.get<ApplicationNavigatorConfig>(
        instanceName: ApplicationType.retail.id,
      ).features[DebugMenuFeatureNavigationConfig.name] =
          DependencyProvider.get<NavigationRouter>(
        instanceName: DebugMenuFeatureNavigationConfig.name,
      );
    }

    _registerProxyFeature();

    return;
  }

  static void _registerProxyFeature() {
    ProxyFeatureDependencyModuleResolver.register();
    ProxyDomainDependencyModuleResolver.register();
  }
}
