import 'dart:async';

import 'package:feature_auth_api/domain/auth_interactor.dart';
import 'package:feature_initialization_api/navigation/initialization_feature_navigation_config.dart';
import 'package:wio_app_core_api/logging_api.dart';
import 'package:wio_core_navigation_ui/wio_core_navigation_ui.dart';
import 'package:wio_digital_trust_api/feature_digital_trust_api.dart';

class DigitalTrustActiveDefenseWorkDelegate
    implements DigitalTrustActiveDefenseDelegate {
  final AuthInteractor _authInteractor;
  final NavigationProvider _navigationProvider;
  final Logger _logger;

  DigitalTrustActiveDefenseWorkDelegate({
    required AuthInteractor authInteractor,
    required NavigationProvider navigationProvider,
    required Logger logger,
  })  : _authInteractor = authInteractor,
        _navigationProvider = navigationProvider,
        _logger = logger;

  @override
  Future<bool> preProcessActiveDefenseAction() async {
    try {
      await _authInteractor.logout();
      _navigationProvider.clearStackAndReplace(
        const InitializationFeatureNavigationConfig(),
      );
      return true;
    } on Object catch (e, _) {
      _logger.warning('Active Defence Pre Action Failed with error: $e');
      return false;
    }
  }
}
