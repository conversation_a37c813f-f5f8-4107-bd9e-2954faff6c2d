import 'package:integration_test_core/integration_test_core.dart';
import 'package:ui_kit_legacy_core/ui_kit_core.dart';
import 'package:ui_kit_legacy_mobile/ui_kit_mobile.dart';
import 'package:wio_common_feature_payments_ui/src/screens/transaction_limit_setting/widgets/transfer_remaining_limits_widget.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/currency_selection/currency_selection_page.dart';
import 'package:wio_feature_payments_v2_ui/src/screens/payment_creation_flow/payment_creation_flow.dart';

class RetailCurrencySelectionScreen {
  final body = find.byKey(PaymentCreationFlow.currencySelectionPageKey);
  final numpadEntryInputCell =
      find.byKey(CurrencySelectionPage.numpadEntryInputCellKey);
  final textField = find.byKey(CurrencySelectionPage.textFieldKey);
  final expandCurrencyListIcon = find.byIcon(CompanyIcons.chevron_down_mini);
  final selectedCurrency = find.byKey(NumpadEntryInputCell.currencyCodeKey);
  final remainingLimitLabel = find
      .byKey(TransferRemainingDailyLimitsWidget.transferRemainingLimitLabelKey);
  final remainingLimitValue = find
      .byKey(TransferRemainingDailyLimitsWidget.transferRemainingLimitValueKey);
  final totalLimit =
      find.byKey(TransferRemainingDailyLimitsWidget.transferDailyLimitKey);
}
