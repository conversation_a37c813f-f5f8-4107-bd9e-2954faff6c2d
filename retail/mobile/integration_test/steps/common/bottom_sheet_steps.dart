import 'package:integration_test_core/integration_test_core.dart';

import '../../screens/index.dart';

class BottomSheetSteps extends RetailTestScreenSet {
  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        when<ScenarioContext>(
          RegExp(r'I close the bottom sheet by tapping on an empty space$'),
          (context) async {
            await context.tester.tapAt(const Offset(5.0, 5.0));
          },
        ),
      ];
}
