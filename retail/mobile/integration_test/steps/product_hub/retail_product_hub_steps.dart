import 'package:integration_test_core/integration_test_core.dart';

import '../../screens/index.dart';

class RetailProductHubSteps extends RetailTestScreenSet {
  static const waitAnimationTimeout = 5000;

  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        and<ScenarioContext>(
          RegExp(r'I click on the Broker button$'),
          (context) async {
            await context.tester.pumpAndSettle();
            await productHubScreen.brokerProductButton.click();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I click on the IPO button$'),
          (context) async {
            await context.tester.pumpAndSettle();
            await productHubScreen.ipoProductButton.click();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I click on the Retail button$'),
          (context) async {
            await context.tester.pumpAndSettle();
            await productHubScreen.retailProductButton.click();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I click on the Lets start button$'),
          (context) async {
            await productHubScreen.wioInvestPopupButton.click();
            await productHubScreen.wioInvestPopupButton.shouldNotBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
      ];
}
