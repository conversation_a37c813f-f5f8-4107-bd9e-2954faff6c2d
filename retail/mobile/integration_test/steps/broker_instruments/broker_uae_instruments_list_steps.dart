import 'package:flutter/cupertino.dart';
import 'package:integration_test_core/integration_test_core.dart';

import '../../screens/retail_test_screen_set.dart';

class BrokerUaeInstrumentsListSteps extends RetailTestScreenSet {
  Iterable<StepDefinitionGeneric<ScenarioContext>> get steps => [
        and<ScenarioContext>(
          RegExp(r'I see UAE Instruments List screen$'),
          (context) async {
            await brokerUaeInstrumentsListScreen.body.shouldBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(
            r'I scroll to last instrument on UAE Instruments List screen$',
          ),
          (context) async {
            await brokerUaeInstrumentsListScreen.scrollableList
                .scrollUntilVisible(
              target: brokerUaeInstrumentsListScreen.instrumentItem.last,
              direction: ScrollDirection.down,
            );
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I see UAE Instruments List header$'),
          (context) async {
            await brokerUaeInstrumentsListScreen.header.shouldBeVisible();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
        and<ScenarioContext>(
          RegExp(r'I pull down UAE Instruments screen to update$'),
          (context) async {
            await Future<void>.delayed(const Duration(seconds: 2));
            final buildContext = await brokerUaeInstrumentsListScreen
                .uaeInstrumentsScreenRefresher
                .buildContext();
            final swipeHeight = MediaQuery.of(buildContext).size.height * 0.5;
            final offset = Offset(0, swipeHeight);

            await context.tester.timedDrag(
              brokerUaeInstrumentsListScreen.uaeInstrumentsScreenRefresher,
              offset,
              const Duration(milliseconds: 250),
            );
            await context.tester.pumpUntilSettled();
            await AttachScreenshotHook.takeScreenshot();
          },
        ),
      ];
}
